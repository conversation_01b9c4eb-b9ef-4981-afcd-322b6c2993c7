#ddev-generated
# MinIO addon for DDEV - Base file
services:
  minio:
    container_name: ddev-${DDEV_SITENAME}-minio
    # image: minio/minio:latest
    build:
      context: .minioImageBuild
    hostname: ddev-${DDEV_SITENAME}-minio
    environment:
      MINIO_VOLUMES: "/data"
      MC_CONFIG_DIR: "/root/.mc"
      MINIO_UID: $DDEV_UID
      MINIO_GID: $DDEV_GID
      MINIO_ROOT_USER: ddevminio
      MINIO_ROOT_PASSWORD: ddevminio
      MINIO_SERVER_URL: ${DDEV_PRIMARY_URL}:10101
      MINIO_BROWSER_REDIRECT_URL: ${DDEV_PRIMARY_URL}:9090
      VIRTUAL_HOST: $DDEV_HOSTNAME
      HTTP_EXPOSE: "9089:9090,10100:10101"
      HTTPS_EXPOSE: "9090:9090,10101:10101"
    command: >
      server
      --console-address
      :9090
      --address
      :10101
    volumes:
      - "minio:/data"
      - ".:/mnt/ddev_config"
      - ddev-global-cache:/mnt/ddev-global-cache
      - "./minio/mc-config.json:/root/.mc/config.json"
    expose:
      - "9090"
      - "10101"
    networks:
      - default
      - ddev_default
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: ${DDEV_APPROOT}
    external_links:
      - ddev-router:${DDEV_SITENAME}.${DDEV_TLD}
    deploy:
      resources:
        limits:
          cpus: "2.5"
          memory: "1024M"
        reservations:
          cpus: "1.5"
          memory: "512M"
    restart: "no"
    healthcheck:
      interval: 120s
      timeout: 2s
      retries: 1

volumes:
  minio:
    name: ddev-${DDEV_SITENAME}-minio
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
