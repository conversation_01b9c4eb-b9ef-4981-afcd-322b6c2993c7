#ddev-generated
# Remove the line above if you don't want this file to be overwritten
# when you run ddev get ddev/ddev-redis-commander
services:
  redis-commander:
    container_name: ddev-${DDEV_SITENAME}-redis-commander
    hostname: ${DDEV_SITENAME}-redis-commander
    image: ghcr.io/joeferner/redis-commander:latest
    expose:
      - 1358
    labels:
      com.ddev.site-name: ${DDEV_SITENAME}
      com.ddev.approot: $DDEV_APPROOT
    environment:
      - VIRTUAL_HOST=$DDEV_HOSTNAME
      - HTTP_EXPOSE=1358
      - HTTPS_EXPOSE=1359:1358
      - REDIS_PORT=6379
      - REDIS_HOST=redis
      - PORT=1358
    volumes:
      - ".:/mnt/ddev_config"
    depends_on:
      - redis
    healthcheck:
      disable: true
