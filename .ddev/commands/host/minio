#!/bin/bash

## #ddev-generated: If you want to edit and own this file, remove this line.
## Description: Launch a browser with Minio console
## Usage: minio
## Example: "ddev minio"

DDEV_MINIOCONSOLE_PORT=9089
DDEV_MINIOCONSOLE_HTTPS_PORT=9090

FULLURL=${DDEV_PRIMARY_URL}
HTTPS=""
if [ "${DDEV_PRIMARY_URL%://*}" = "https" ]; then HTTPS=true; fi

if [[ -n "${GITPOD_INSTANCE_ID}" ]] || [[ "${CODESPACES}" == "true" ]]; then
    FULLURL="${FULLURL/-${DDEV_HOST_WEBSERVER_PORT}/-${DDEV_MINIOCONSOLE_PORT}}"
else
    if [ "${HTTPS}" = "" ]; then
        FULLURL="${FULLURL%:[0-9]*}:${DDEV_MINIOCONSOLE_PORT}"
    else
        FULLURL="${FULLURL%:[0-9]*}:${DDEV_MINIOCONSOLE_HTTPS_PORT}"
    fi
fi

if [ -n "${1:-}" ] ; then
  if [[ ${1::1} != "/" ]] ; then
    FULLURL="${FULLURL}/";
  fi

  FULLURL="${FULLURL}${1}";
fi

if [ "${DDEV_DEBUG:-}" = "true" ]; then
    printf "FULLURL %s\n" "$FULLURL" && exit 0
fi

case $OSTYPE in
  linux-gnu)
    if [[ -n "${GITPOD_INSTANCE_ID}" ]]; then
        gp preview "${FULLURL}"
    else
        xdg-open "${FULLURL}"
    fi
    ;;
  "darwin"*)
    open "${FULLURL}"
    ;;
  "win*"* | "msys"*)
    start "${FULLURL}"
    ;;
esac
