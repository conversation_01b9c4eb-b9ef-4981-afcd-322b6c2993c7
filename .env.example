APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=attendance
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=minio
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MINIO_ENDPOINT=https://attendance.ddev.site:10101
MINIO_ACCESS_KEY_ID=ddevminio
MINIO_SECRET_ACCESS_KEY=ddevminio
MINIO_DEFAULT_REGION=us-east-1
MINIO_BUCKET=drive

GOOGLE_MAPS_KEY=

GOTENBERG_SERVICE_URL=

# used for Attendace seeder to spicify a team to seed
LOCAL_SEEDER_TEAM_ID=

JWT_GUARD_AUTH_SERVER_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
<key>
-----END PUBLIC KEY-----"

AMQP_HOST=rabbitmq
AMQP_PORT=5672
AMQP_USER=rabbitmq
AMQP_PASSWORD=rabbitmq
AMQP_VHOST=/

STREAM_REDIS_HOST=ddev-nawart-portal-backend-redis
STREAM_REDIS_PASSWORD=null
STREAM_REDIS_PORT=6379
STREAM_REDIS_DB=0

ONESIGNAL_ENABLED=false
