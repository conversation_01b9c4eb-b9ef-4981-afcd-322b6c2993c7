<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Email Template Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used by email templates
    |
    */

    'weekly_summary.title' => 'Weekly Summary',
    'weekly_summary.heading' => 'Weekly Update for :name',
    'weekly_summary.attendanceSummary.header.heading' => 'Your Attendance This Week',
    'weekly_summary.attendanceSummary.header.present' => 'Present: :count',
    'weekly_summary.attendanceSummary.header.absent' => 'Absent: :count',
    'weekly_summary.attendanceSummary.body.lastWeekGraph.noData' =>
        'There is not enough data to compare to last week',
    'weekly_summary.attendanceSummary.body.lastWeekGraph.Data.present' => 'Present',
    'weekly_summary.attendanceSummary.body.lastWeekGraph.Data.absent' => 'Absent',
    'weekly_summary.attendanceSummary.body.averageHours' => 'average working hours',
    'weekly_summary.attendanceSummary.body.percentageChange.percentageText' => ':percentage %',
    'weekly_summary.attendanceSummary.body.percentageChange.increase.alt' => 'upward arrow',
    'weekly_summary.attendanceSummary.body.percentageChange.increase.description' =>
        'more than last week',
    'weekly_summary.attendanceSummary.body.percentageChange.decrease.alt' => 'downward arrow',
    'weekly_summary.attendanceSummary.body.percentageChange.decrease.description' =>
        'less than last week',
    'weekly_summary.attendanceSummary.body.percentageChange.same.description' => 'No change',
    'weekly_summary.attendanceDetails.header' => 'Your Attendance In Detail',
    'weekly_summary.attendanceDetails.body.table.heading.day' => 'Day',
    'weekly_summary.attendanceDetails.body.table.heading.firstIn' => 'First In',
    'weekly_summary.attendanceDetails.body.table.heading.lastOut' => 'Last Out',
    'weekly_summary.attendanceDetails.body.table.heading.totalHours' => 'Total Hours',
    'weekly_summary.attendanceDetails.body.table.body.noData' =>
        'There is not enough data for last week.',
    'weekly_summary.attendanceDetails.body.table.body.Data.absent' => 'ABSENT',
    'weekly_summary.pastThreeYears.header.heading' => '3 Month History',
    'weekly_summary.pastThreeYears.header.fewerHours' => 'Fewer Hours',
    'weekly_summary.pastThreeYears.header.moreHours' => 'More Hours',
];
