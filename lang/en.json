{"The :attribute must contain at least one letter.": "The :attribute must contain at least one letter.", "The :attribute must contain at least one number.": "The :attribute must contain at least one number.", "The :attribute must contain at least one symbol.": "The :attribute must contain at least one symbol.", "The :attribute must contain at least one uppercase and one lowercase letter.": "The :attribute must contain at least one uppercase and one lowercase letter.", "The given :attribute has appeared in a data leak. Please choose a different :attribute.": "The given :attribute has appeared in a data leak. Please choose a different :attribute.", "mood report description": "Feelings and mood play an important role in the enthusiasm and workings of an employee, directly affecting the organizational culture and goals", "additional_resource.documentation.title": "Documentation", "additional_resource.documentation.description": "Learn how to integrate our tools with your app", "additional_resource.APIReference.title": "API Reference", "additional_resource.APIReference.description": "A complete API reference for our libraries", "additional_resource.guide.title": "Guides", "additional_resource.guide.description": "Installation guides that cover popular setups", "additional_resource.blog.title": "Blog", "additional_resource.blog.description": "Read our latest news and articles", "shift info message": "If the shift ends on the next day, please enable Next Day Checkout and set a time to Prevent Checkout After.", "Approval request policy": "regularization request policy", "department.deleted": "The department names :name has been deleted.", "employee.deleted": "The employee names :name has been deleted.", "location.deleted": "The location names :name has been deleted.", "shift.updated": "The shift names :name has been updated.", "shift.deleted": "The shift names :name has been deleted.", "report date range exceed limit": "You can't export a report for more than 6 months", "Device added successfully": "Device added successfully", "Device updated successfully": "Device updated successfully", "Device deleted successfully": "<PERSON>ce deleted successfully", "check out radius should be greater than 0 if automatic checkin is true": "check out radius should be greater than 0 if automatic checkin is enabled", "Success": "Success", "Failed": "Failed", "Pending": "Pending", "permission requests are disabled": "permission requests are disabled", "daily or monthly limit of permission request hours exceeded": "daily or monthly limit of permission request hours exceeded", "there should not be more than one permanent shift": "there should not be more than one permanent shift", "shifts should not be overlapping": "shifts should not be overlapping", "there should be one or more permanent locations": "there should be one or more permanent locations", "very-happy": "happy", "happy": "happy", "neutral": "neutral", "sad": "sad", "very-sad": "sad", "New shift assigned": "New shift assigned", "Temporary shift is scheduled from :from to :to": "Temporary shift is scheduled from :from to :to", "permanent shift is scheduled": "permanent shift is scheduled", "permanent shift has been changed": "permanent shift has been changed", "temporary shift has been changed": "temporary shift has been changed", "Temporary shift is rescheduled": "Temporary shift is rescheduled"}