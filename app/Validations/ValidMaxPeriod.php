<?php

namespace App\Validations;

use Carbon\CarbonInterval;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Support\Carbon;

class ValidMaxPeriod
{
    public function __construct(
        protected CarbonInterval $maxLimit,
        protected string $startDateKey,
        protected string $endDateKey,
        protected ?string $message = null
    ) {
    }

    public function __invoke(Validator $validator): void
    {
        if ($validator->failed()) {
            return;
        }

        $validatedData = $validator->valid();

        $interval = CarbonInterval::instance(
            Carbon::parse($validatedData[$this->startDateKey])->diff(
                date: $validatedData[$this->endDateKey] ?? null,
                absolute: true
            )
        );

        if ($interval->greaterThan($this->maxLimit)) {
            $validator
                ->errors()
                ->add($this->endDateKey, $this->message ?? __('Period limit is exceeded'));
        }
    }
}
