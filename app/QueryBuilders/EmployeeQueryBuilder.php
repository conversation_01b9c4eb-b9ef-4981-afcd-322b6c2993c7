<?php

namespace App\QueryBuilders;

use App\Models\Employee;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use function request;

class EmployeeQueryBuilder
{
    public static function query(Builder|Relation|string $query = null): QueryBuilder
    {
        request()->validate([
            'filter.search' => ['nullable', 'string'],
            'filter.ids' => ['nullable', 'array'],
            'filter.direct_managers' => ['nullable', 'array'],
            'filter.direct_managers.*' => ['integer'],
            'filter.departments' => ['nullable', 'array'],
            'filter.departments.*' => ['integer'],
            'filter.locations' => ['nullable', 'array'],
            'filter.locations.*' => ['integer'],
            'filter.shifts' => ['nullable', 'array'],
            'filter.shifts.*' => ['integer'],
            'filter.tags' => ['nullable', 'array'],
            'filter.tags.*' => ['integer'],
            'filter.exclude' => ['nullable', 'integer'],
            'filter.is_active' => ['nullable', 'boolean'],
            'filter.is_direct_manager' => ['nullable', 'boolean'],
        ]);

        return QueryBuilder::for($query ?? Employee::class)
            ->allowedFilters([
                AllowedFilter::callback('ids', fn($q, $ids) => $q->whereIn('id', $ids)),
                AllowedFilter::callback(
                    'direct_managers',
                    fn($q, $directManagersIDs) => $q->whereIn('manager_id', $directManagersIDs)
                ),
                AllowedFilter::callback(
                    'is_direct_manager',
                    fn($query, bool $isDirectManager) => $isDirectManager
                        ? $query->has('directlyManagedEmployees')
                        : $query->doesntHave('directlyManagedEmployees')
                ),
                AllowedFilter::exact('department_id'), // deprecated, use `departments` filter instead
                AllowedFilter::exact('is_active'),
                AllowedFilter::callback('search', fn($q, $value) => $q->search($value)),
                AllowedFilter::callback('departments', fn($q, $l) => $q->filterByDepartments($l)),
                AllowedFilter::callback('locations', fn($q, $l) => $q->filterByLocations($l)),
                AllowedFilter::callback('shifts', fn($q, $s) => $q->filterByShifts($s)),
                AllowedFilter::callback('tags', fn($q, $tags) => $q->filterByTags($tags)),
                AllowedFilter::callback('exclude', fn($q, $value) => $q->where('id', '!=', $value)),
            ])
            ->allowedIncludes([
                'department',
                'shift',
                'locations',
                'tags',
                'activeLocations',
                'directManager',
            ])
            ->orderBy('is_active', 'desc');
    }
}
