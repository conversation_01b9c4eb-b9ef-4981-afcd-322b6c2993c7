<?php

namespace App\QueryBuilders;

use App\Enums\RequestStatus;
use App\Models\Leave;
use App\Support\AllowedFilterEmployeesDepartments;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use function request;

class LeaveQueryBuilder
{
    public static function query(Builder|Relation|string $query = null): QueryBuilder
    {
        request()->validate([
            'filter.date' => ['nullable', 'date:Y-m-d'], // deprecated
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d'],
            'filter.status' => ['nullable', 'array'],
            'filter.status.*' => ['nullable', Rule::enum(RequestStatus::class)],
            'filter.employees' => ['nullable', 'array'],
            'filter.departments' => ['nullable', 'array'],
            'filter.tags' => ['nullable', 'array'],
            'per_page' => ['nullable'],
        ]);

        $from = request()->input('filter.from');
        $to = request()->input('filter.to');

        return QueryBuilder::for($query ?? Leave::class)
            ->allowedFilters([
                ...AllowedFilterEmployeesDepartments::filters(),
                AllowedFilter::callback(
                    // Deprecated
                    'date',
                    fn($query, $date) => $query->whereDate('created_at', $date)
                ),
                AllowedFilter::callback('status', fn($q, $s) => $q->whereIn('status', $s)),
                AllowedFilter::callback(
                    'tags',
                    fn($q, $tags) => $q->whereHas('employee', fn($q) => $q->filterByTags($tags))
                ),
            ])
            ->when($from && $to, fn($q) => $q->date(from: $from, to: $to))
            ->allowedSorts(['from_date', 'to_date', 'status', 'created_at'])
            ->allowedIncludes([
                'employee',
                'employee.manager',
                'employee.tags',
                'department',
                'decisions',
            ])
            ->defaultSort('-created_at');
    }
}
