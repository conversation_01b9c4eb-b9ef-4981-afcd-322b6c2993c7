<?php

namespace App\QueryBuilders;

use App\Models\ApprovalRequest;
use App\Support\AllowedFilterFromToCreatedAt;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use function request;

class ApprovalRequestQueryBuilder
{
    public static function query(Builder|Relation|string $query = null): QueryBuilder
    {
        request()->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d', 'after_or_equal:filter.from'],
            'filter.status' => ['nullable', 'array'],
            'filter.status.*' => [
                'nullable',
                Rule::in([
                    ApprovalRequest::PENDING,
                    ApprovalRequest::APPROVED,
                    ApprovalRequest::REJECTED,
                ]),
            ],
            'filter.type' => ['nullable', 'array'],
            'filter.type.*' => [
                'nullable',
                Rule::in([
                    ApprovalRequest::PERMISSION,
                    ApprovalRequest::REMOTE_WORK,
                    ApprovalRequest::REGULARIZATION,
                ]),
            ],
            'per_page' => ['nullable'],
        ]);

        return QueryBuilder::for($query ?? ApprovalRequest::class)
            ->allowedFilters([
                ...AllowedFilterFromToCreatedAt::filters(),
                AllowedFilter::callback('status', fn($q, $s) => $q->whereIn('status', $s)),
                AllowedFilter::callback('type', fn($q, $t) => $q->whereIn('type', $t)),
                AllowedFilter::callback(
                    'departments',
                    fn($q, $d) => $q->whereHas(
                        'employee',
                        fn($q) => $q->whereIn('department_id', $d)
                    )
                ),
                AllowedFilter::callback('employees', fn($q, $e) => $q->whereIn('employee_id', $e)),
            ])
            ->allowedIncludes([
                'employee',
                'employee.department',
                'employee.manager',
                'employee.tags',
                'decisions',
            ])
            ->allowedSorts(['status', 'created_at'])
            ->defaultSort('-created_at');
    }
}
