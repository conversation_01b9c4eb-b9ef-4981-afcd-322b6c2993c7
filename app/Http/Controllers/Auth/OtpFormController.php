<?php

namespace App\Http\Controllers\Auth;

use App\Authenticators\OneTimePasswordAuthenticator;
use App\Http\Controllers\Controller;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class OtpFormController extends Controller
{
    public function __construct(private readonly OneTimePasswordAuthenticator $authenticator)
    {
    }

    public function create()
    {
        return Inertia::render('Login/OtpForm', [
            'identifier' => [
                'type' => loginSession()->resolveIdentifier()->type(),
                'value' => loginSession()->resolveIdentifier()->masked(),
            ],
            'secondsToExpire' => now()->diffInSeconds(
                loginSession()->resolveOtp()->sent_at?->addMinutes(2)
            ),
            'allowSms' => loginSession()->resolveTenant()->allow_sms,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'otp' => ['required', 'string'],
        ]);

        $this->ensureIsNotRateLimited();

        $tenant = loginSession()->resolveTenant();
        $identifier = loginSession()->resolveIdentifier();

        $this->authenticate();

        RateLimiter::clear($this->throttleKey($identifier));

        $this->authenticator->invalidateOtp($tenant, $identifier);

        loginSession()->clear();

        return Inertia::location(session('url.intended', config('app.frontend_url')));
    }

    protected function ensureIsNotRateLimited(): void
    {
        $identifier = loginSession()->resolveIdentifier();

        if (!RateLimiter::tooManyAttempts($this->throttleKey($identifier), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey($identifier));

        throw ValidationException::withMessages([
            'otp' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    protected function throttleKey($email): string
    {
        return Str::lower($email) . '|' . request()->ip();
    }

    protected function authenticate(): void
    {
        $identifier = loginSession()->resolveIdentifier();
        $tenant = loginSession()->resolveTenant();

        if (
            !$this->authenticator->authenticate(
                identifier: $identifier,
                tenant: $tenant,
                password: request('otp')
            )
        ) {
            RateLimiter::hit($this->throttleKey($identifier));

            throw ValidationException::withMessages([
                'otp' => $this->authenticator->errors(),
            ]);
        }

        session()->regenerate();
    }
}
