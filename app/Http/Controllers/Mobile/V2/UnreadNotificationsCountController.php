<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Employee - Notifications
 */
class UnreadNotificationsCountController extends Controller
{
    /**
     * Unread notifications count
     * @response {
     *   "data": {
     *     "count": 1
     *   }
     * }
     */
    public function __invoke(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: [
                'count' => auth()->user()->unreadNotifications()->count(),
            ]
        );
    }
}
