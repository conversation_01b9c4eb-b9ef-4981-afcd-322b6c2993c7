<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\EmployeeResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @group Mobile
 * @subgroup Employee - Language
 * @deprecated
 */
class SwitchLanguageController extends Controller
{
    /**
     * Switch Language
     *
     * @bodyParam preferred_language string required The language to be selected. Example: ar
     *
     * @responseFile 200 storage/responses/employee.language.json
     *
     * @response 422 {"message":"The preferred language must be one of the following: ar, en."}
     */
    public function __invoke(Request $request): ApiResponse
    {
        $request->validate([
            'preferred_language' => ['required', Rule::in(['ar', 'en'])],
        ]);

        $request->user()->update([
            'preferred_language' => $request->preferred_language,
        ]);

        return new ApiResponse(data: new EmployeeResource($request->user()));
    }
}
