<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Support\ApiResponse;
use App\Support\EmployeeAttendanceSummary;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @deprecated
 * @subgroup Employee - Attendances
 */
class SummaryOfMonthController extends Controller
{
    /**
     * Monthly Attendance Summary
     *
     * @bodyParam The date needed to be returned. Example: 2023-01-15
     *
     * @responseFile 200 storage/responses/attendance.monthly.json
     */
    public function __invoke(Request $request): ApiResponse
    {
        $request->validate([
            'date' => ['nullable', 'date_format:Y-m'],
        ]);

        $employee = auth()->user();

        if (!empty($request->date)) {
            $startOfMonth = Carbon::parse($request->date)->startOfMonth();
            $endOfMonth = Carbon::parse($request->date)->endOfMonth();
        } else {
            $startOfMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();
        }

        $employeeSummary = EmployeeAttendanceSummary::get($employee, $startOfMonth, $endOfMonth);

        $checkInOutSummary = $employeeSummary->checkInOutSummary();
        $committedHours = $employeeSummary->committedHours();
        $approvedRejectedRequests = $employeeSummary->approvalRequests();

        $responsePayload = [
            'total_checkins' => 0,
            'total_hours' => 0,
            'average_check_in_time' => 0,
            'average_check_out_time' => 0,
            'average_check_in_mood' => 0,
            'average_check_out_mood' => 0,
            'total_commited_hours' => 0,
            'total_working_days' => 0,
            'total_requests' => 0,
            'total_approved_requests' => 0,
            'total_rejected_requests' => 0,
        ];

        if ($checkInOutSummary['total_checkins'] > 0) {
            $responsePayload = [
                'total_checkins' => $checkInOutSummary['total_checkins'],
                'total_hours' => $checkInOutSummary['total_hours'],
                'average_check_in_time' => Carbon::parse(
                    $checkInOutSummary['average_check_in']
                )->format('h:i a'),
                'average_check_out_time' => Carbon::parse(
                    $checkInOutSummary['average_check_out']
                )->format('h:i a'),
                'average_check_in_mood' => $employeeSummary->getAvgCheckInMood(),
                'average_check_out_mood' => $employeeSummary->getAvgCheckOutMood(),
                'total_commited_hours' => $committedHours['total_committed_hours'],
                'total_working_days' => $committedHours['total_working_days'],
                'total_requests' => $approvedRejectedRequests['total_requests'],
                'total_approved_requests' => $approvedRejectedRequests['total_approved_requests'],
                'total_rejected_requests' => $approvedRejectedRequests['total_rejected_requests'],
            ];
        }

        return new ApiResponse(data: $responsePayload, spreadResponseContent: true);
    }
}
