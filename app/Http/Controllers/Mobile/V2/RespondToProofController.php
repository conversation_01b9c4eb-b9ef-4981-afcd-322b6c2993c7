<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Enums\ProofStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\RespondToProofRequest;
use App\Models\Activity;
use App\Support\ApiResponse;
use Illuminate\Http\Response;

/**
 * @group Mobile
 * @subgroup Employee - Proofs
 */
class RespondToProofController extends Controller
{
    /**
     * Proof Response
     *
     * @bodyParam notification_id uuid required the ID notification. Example: 8703357c-5da4-4790-bf9a-eedd00ab1e81
     * @bodyParam lat float required the latitude of the device. Example: 24.7023808
     * @bodyParam lng float required the longitude of the device. Example: 46.6617784
     *
     * @response 202 {"status":"ACCEPTED"}
     * @response 406 {"status":"REJECTED"}
     * @response 408 {"status":"MISSED"}
     * @response 409 {"status":"ALREADY_RESPONDED"}
     */
    public function __invoke(RespondToProofRequest $request): ApiResponse
    {
        $employee = $request->user();

        $proof = $employee
            ->proofs()
            ->where('notification_id', $request['notification_id'])
            ->firstOrFail();
        $score = 0;

        if ($proof->status === ProofStatus::Accepted || $proof->status === ProofStatus::Rejected) {
            return new ApiResponse(
                status: 'ALREADY_RESPONDED',
                message: __('Proof of attendance already responded'),
                code: Response::HTTP_CONFLICT
            );
        }

        if ($proof->status === ProofStatus::Sent) {
            $insideLocation = $employee->checkLocation($request['lat'], $request['lng']);
            if ($insideLocation) {
                $status = ProofStatus::Accepted;
                $score = 10;
            } else {
                if ($employee->remote_work_policy === 'not_allowed') {
                    $status = ProofStatus::Rejected;
                    $score = 2;
                } else {
                    $status = ProofStatus::Accepted;
                    $score = 9;
                }
            }
        } elseif ($proof->status === ProofStatus::Missed) {
            $insideLocation = $employee->checkLocation($request['lat'], $request['lng']);
            $status = ProofStatus::Missed;
            if ($insideLocation) {
                $score = 5;
            }
        }

        $proof->update([
            'status' => $status,
            'location_id' => $insideLocation ?: null,
            'lat' => $request['lat'],
            'lng' => $request['lng'],
            'responded_at' => now(),
            'score' => $score,
        ]);

        $employee->activities()->create([
            'team_id' => $employee->team_id,
            'action' => Activity::PROOF_OF_ATTENDANCE,
            'lat' => $request['lat'],
            'lng' => $request['lng'],
            'location_id' => $insideLocation ?: null,
            'payload' => ['method' => $proof->method, 'status' => $status],
        ]);

        if ($status === ProofStatus::Accepted) {
            return new ApiResponse(status: 'ACCEPTED', message: __('Proof of attendance verified'));
        }

        if ($status === ProofStatus::Rejected) {
            return new ApiResponse(
                status: 'REJECTED',
                message: __('you are not allowed to proof your attendance outside the zone'),
                code: Response::HTTP_NOT_ACCEPTABLE
            );
        }

        return new ApiResponse(
            status: 'MISSED',
            message: __('Proof of attendance has expired'),
            code: Response::HTTP_REQUEST_TIMEOUT
        );
    }
}
