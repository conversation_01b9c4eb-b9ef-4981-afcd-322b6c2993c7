<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Config
 */
class MobileVersionController extends Controller
{
    /**
     * Get the minimum mobile app version.
     * @response { "ios": "1.0.0", "android": "1.0.0" }
     */
    public function __invoke(Request $request): JsonResponse
    {
        return response()->json([
            'ios' => config('mobile.ios_minimum_version'),
            'android' => config('mobile.android_minimum_version'),
        ]);
    }
}
