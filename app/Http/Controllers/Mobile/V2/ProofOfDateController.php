<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\ProofOfDateRequest;
use App\Http\Resources\ProofResource;
use App\Support\AllowedFilterFromToCreatedAt;
use App\Support\ApiResponse;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Mobile
 * @subgroup Employee - Proofs
 */
class ProofOfDateController extends Controller
{
    /**
     * List proofs
     * @apiResource App\Http\Resources\ProofResource
     * @apiResourceModel App\Models\Proof
     */
    public function index(ProofOfDateRequest $request): ApiResponse
    {
        return new ApiResponse(
            data: ProofResource::collection(
                QueryBuilder::for(auth()->user()->proofs())
                    ->allowedFilters([
                        ...AllowedFilterFromToCreatedAt::filters(),
                        AllowedFilter::callback(
                            'status',
                            fn($q, $proof) => $q->whereIn('status', $proof)
                        ),
                    ])
                    ->paginate($request->validated('per_page'))
                    ->withQueryString()
            )
        );
    }
}
