<?php

namespace App\Http\Controllers\Mobile\V2\Leaves;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\LeaveResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @deprecated
 * @subgroup Employee - Leaves
 */
class GetMyLeaveRequestsController extends Controller
{
    /**
     * Get my leave requests
     */
    public function __invoke(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: LeaveResource::collection(
                auth()
                    ->user()
                    ->leaves()
                    ->with(['employee.manager', 'department'])
                    ->get()
            )
        );
    }
}
