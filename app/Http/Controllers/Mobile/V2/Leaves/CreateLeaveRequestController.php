<?php

namespace App\Http\Controllers\Mobile\V2\Leaves;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\LeaveStoreRequest;
use App\Http\Resources\Mobile\LeaveResource;
use App\Models\Leave;
use App\Notifications\NewLeaveRequestNotification;
use App\Support\ApiResponse;
use Carbon\Carbon;

/**
 * @group Mobile
 * @deprecated
 * @subgroup Employee - Leaves
 */
class CreateLeaveRequestController extends Controller
{
    /**
     * Create a leave request
     */
    public function __invoke(LeaveStoreRequest $request): ApiResponse
    {
        $leave = Leave::create([
            'team_id' => $request->user()->team_id,
            'employee_id' => $request->user()->id,
            'department_id' => $request->user()->department_id,
            'reason' => $request->reason,
            'from_date' => Carbon::parse($request->from_date),
            'to_date' => Carbon::parse($request->to_date),
        ]);

        $request
            ->user()
            ->manager?->receiverOfLeaveRequest()
            ->notify(new NewLeaveRequestNotification($leave));

        return new ApiResponse(
            data: new LeaveResource($leave->loadMissing(['employee.manager', 'department'])),
            status: 'LEAVE_REQUEST_CREATED'
        );
    }
}
