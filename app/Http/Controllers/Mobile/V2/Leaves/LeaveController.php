<?php

namespace App\Http\Controllers\Mobile\V2\Leaves;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\LeaveStoreRequest;
use App\Http\Resources\Mobile\V3\LeaveResource;
use App\Models\Attendance;
use App\Notifications\NewLeaveRequestNotification;
use App\Services\CheckEmployeeStatementDurationForPreventingRequests;
use App\Support\ApiResponse;
use Carbon\CarbonPeriod;

/**
 * @group Mobile
 * @subgroup Employee - Leaves
 */
class LeaveController extends Controller
{
    /**
     * Request a leave.
     * @apiResource App\Http\Resources\Mobile\V3\LeaveResource
     * @apiResourceModel App\Models\Leave
     */
    public function store(LeaveStoreRequest $request): ApiResponse
    {
        $employee = auth()->user();

        (new CheckEmployeeStatementDurationForPreventingRequests(
            employee: $employee,
            period: CarbonPeriod::create($request->from, $request->to)
        ))->validate();

        $leave = $employee->leaves()->create([
            'department_id' => $employee->department_id,
            'from_date' => $request->from,
            'to_date' => $request->to,
            'reason' => $request->reason,
            'attachment' => $request->attachment,
        ]);

        $leave
            ->attendances()
            ->with('employeeStatement')
            ->each(
                fn(Attendance $attendance) => $attendance->employeeStatement
                    ?->requestable()
                    ->associate($leave)
                    ->save()
            );

        $employee->manager
            ?->receiverOfLeaveRequest()
            ->notify(new NewLeaveRequestNotification($leave));

        return new ApiResponse(data: new LeaveResource($leave));
    }
}
