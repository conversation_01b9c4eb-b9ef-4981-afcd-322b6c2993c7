<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Enums\AttendanceDayType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\ScheduledShiftsRequest;
use App\Http\Resources\ShiftResource;
use App\Services\AttendanceService;
use App\Support\ApiResponse;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;

class ScheduledShiftsController extends Controller
{
    public function index(
        ScheduledShiftsRequest $request,
        AttendanceService $attendanceService
    ): ApiResponse {
        $validated = $request->validated();
        $employee = $request->user();

        $period = CarbonPeriodImmutable::dates($validated['from'], $validated['to']);

        $days = collect($period)->map(function (CarbonImmutable $day) use (
            $attendanceService,
            $employee
        ) {
            $dayShift = $attendanceService->dayShift($employee, $day);

            if (is_null($dayShift)) {
                return [
                    'date' => $day->toDateString(),
                    'shift' => null,
                ];
            }

            $weekday = $dayShift->getDayShift($day);
            $shiftOutput = match ($attendanceService->dayType($dayShift, $employee, $day)) {
                AttendanceDayType::WEEKDAY => [
                    'shift' => ShiftResource::make($dayShift),
                    'status' => AttendanceDayType::WEEKDAY->value,
                    'from' => Carbon::parse($weekday['from'])->toTimeString(),
                    'to' => Carbon::parse($weekday['to'])->toTimeString(),
                    'flexible_hours' => (int) $dayShift['working_hours']['flexible_hours'],
                ],
                AttendanceDayType::WEEKEND => [
                    'shift' => ShiftResource::make($dayShift),
                    'status' => AttendanceDayType::WEEKEND->value,
                    'from' => null,
                    'to' => null,
                    'flexible_hours' => null,
                ],
                AttendanceDayType::LEAVE => [
                    'shift' => ShiftResource::make($dayShift),
                    'status' => AttendanceDayType::LEAVE->value,
                    'from' => null,
                    'to' => null,
                    'flexible_hours' => null,
                ],
                AttendanceDayType::HOLIDAY => [
                    'shift' => ShiftResource::make($dayShift),
                    'status' => AttendanceDayType::HOLIDAY->value,
                    'from' => null,
                    'to' => null,
                    'flexible_hours' => null,
                ],
            };

            return [
                'date' => $day->toDateString(),
                'shift' => $shiftOutput,
            ];
        });

        return new ApiResponse($days);
    }
}
