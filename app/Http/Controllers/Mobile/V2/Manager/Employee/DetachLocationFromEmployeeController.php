<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Support\ApiResponse;
use App\Support\AuthorizeManagerToEmployee;

class DetachLocationFromEmployeeController extends Controller
{
    public function __invoke(Employee $employee, int $locationableID): ApiResponse
    {
        AuthorizeManagerToEmployee::authorize(employee: $employee, manager: request()->user());

        $employee->locations()->wherePivot('id', $locationableID)->firstOrFail();

        $employee->locations()->wherePivot('id', $locationableID)->detach();

        return new ApiResponse();
    }
}
