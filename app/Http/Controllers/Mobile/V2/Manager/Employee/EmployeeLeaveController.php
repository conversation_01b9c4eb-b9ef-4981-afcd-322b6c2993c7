<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\V3\LeaveResource;
use App\Models\Employee;
use App\QueryBuilders\LeaveQueryBuilder;
use App\Support\ApiResponse;
use App\Support\AuthorizeManagerToEmployee;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Manager - Employee Leaves
 */
class EmployeeLeaveController extends Controller
{
    /**
     * List leaves
     * @apiResource App\Http\Resources\Mobile\V3\LeaveResource
     * @apiResourceModel App\Models\Leave
     * @queryParam filter.from date Filter by from date. Example: 2022-01-01
     * @queryParam filter.to date Filter by to date. Example: 2022-01-01
     * @queryParam filter.status array Filter by status. Example: ["PENDING", "APPROVED", "REJECTED"]
     * @queryParam filter.employees array Filter by employees. Example: [1, 2]
     * @queryParam filter.departments array Filter by departments. Example: [1, 2]
     * @queryParam filter.tags array Filter by tags. Example: [1, 2]
     * @queryParam per_page integer Number of items per page. Example: 10
     */
    public function index(Request $request, Employee $employee): ApiResponse
    {
        AuthorizeManagerToEmployee::authorize($employee);

        return new ApiResponse(
            data: LeaveResource::collection(
                LeaveQueryBuilder::query($employee->leaves())
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
