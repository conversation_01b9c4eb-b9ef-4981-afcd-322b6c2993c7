<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Support\ApiResponse;
use App\Support\AuthorizeManagerToEmployee;
use App\Support\EmployeeAttendanceSummary;
use Illuminate\Http\Request;
use Knuckles\Scribe\Attributes\Response;

/**
 * @group Mobile
 * @subgroup Manager - Employees
 */
class EmployeeSummaryController extends Controller
{
    #[
        Response(
            content: [
                'total_checkins' => 1,
                'total_hours' => 1,
                'average_check_in_time' => '00:00',
                'average_check_out_time' => '00:00',
                'average_check_in_mood' => 1,
                'average_check_out_mood' => 1,
                'total_committed_hours' => 1,
                'total_working_days' => 1,
                'total_requests' => 1,
                'total_approved_requests' => 1,
                'total_rejected_requests' => 1,
            ]
        )
    ]
    public function __invoke(Request $request, Employee $employee): ApiResponse
    {
        $request->validate([
            'date' => ['nullable', 'date_format:Y-m'],
        ]);

        AuthorizeManagerToEmployee::authorize($employee);

        $date = $request->date('date') ?? now();

        $employeeSummary = EmployeeAttendanceSummary::get(
            $employee,
            $date->startOfMonth(),
            $date->endOfMonth()
        );

        return new ApiResponse(data: $employeeSummary->formatForResponse());
    }
}
