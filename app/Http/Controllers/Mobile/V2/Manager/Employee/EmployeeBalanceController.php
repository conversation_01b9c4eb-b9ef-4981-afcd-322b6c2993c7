<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Calculations\EmployeeBalanceCalculator;
use App\Http\Controllers\Controller;
use App\Support\ApiResponse;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Manager - Employees
 */
class EmployeeBalanceController extends Controller
{
    /**
     * Get employee balance
     * @response {
     *     "data": {
     *         "remote_work: 0,
     *        "permission: 0,
     *        "regularization: 0,
     *        "leave: 0,
     *    }
     * }
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d'],
        ]);

        $from = $request->date('filter.from') ?? now()->startOfMonth();
        $to = $request->date('filter.to') ?? now()->endOfMonth();

        return new ApiResponse(
            data: (new EmployeeBalanceCalculator(
                auth()->user(),
                CarbonPeriod::create($from, $to)
            ))->calculate()
        );
    }
}
