<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\V3\ApprovalRequestResource;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Support\ApiResponse;
use App\Support\AuthorizeManagerToEmployee;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Mobile
 * @subgroup Manager - Employee Approval Requests
 */
class EmployeeApprovalRequestController extends Controller
{
    /**
     * List current employee approval requests.
     *
     * @apiResource App\Http\Resources\Mobile\V3\ApprovalRequestResource
     * @apiResourceModel App\Models\ApprovalRequest with=employee,employee.department,employee.tags
     */
    public function index(Request $request, Employee $employee): ApiResponse
    {
        AuthorizeManagerToEmployee::authorize($employee);

        $request->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d', 'after_or_equal:filter.from'],
            'filter.status' => ['nullable', 'array'],
            'filter.status.*' => [
                'nullable',
                Rule::in([
                    ApprovalRequest::PENDING,
                    ApprovalRequest::APPROVED,
                    ApprovalRequest::REJECTED,
                ]),
            ],
            'filter.type' => ['nullable', 'array'],
            'filter.type.*' => [
                'nullable',
                Rule::in([
                    ApprovalRequest::PERMISSION,
                    ApprovalRequest::REMOTE_WORK,
                    ApprovalRequest::REGULARIZATION,
                ]),
            ],
            'per_page' => ['nullable'],
        ]);

        $from = $request->input('filter.from');
        $to = $request->input('filter.to');

        return new ApiResponse(
            data: ApprovalRequestResource::collection(
                QueryBuilder::for($employee->approvalRequests())
                    ->allowedFilters([
                        AllowedFilter::callback('status', fn($q, $s) => $q->whereIn('status', $s)),
                        AllowedFilter::callback('type', fn($q, $t) => $q->whereIn('type', $t)),
                        AllowedFilter::callback(
                            'departments',
                            fn($q, $d) => $q->whereIn('department_id', $d)
                        ),
                        AllowedFilter::callback(
                            'employees',
                            fn($q, $e) => $q->whereIn('employee_id', $e)
                        ),
                    ])
                    ->when($from && $to, fn($q) => $q->date(from: $from, to: $to))
                    ->allowedIncludes(['employee', 'employee.department', 'employee.tags'])
                    ->allowedSorts(['status', 'created_at'])
                    ->defaultSort('-created_at')
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
