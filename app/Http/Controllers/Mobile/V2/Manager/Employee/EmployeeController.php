<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\V2\EmployeeResource;
use App\Models\Employee;
use App\QueryBuilders\EmployeeQueryBuilder;
use App\Support\ApiResponse;
use App\Support\AuthorizeManagerToEmployee;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Manager - Employees
 */
class EmployeeController extends Controller
{
    /**
     * List employees
     * @apiResource App\Http\Resources\Mobile\V2\EmployeeResource
     * @apiResourceModel App\Models\Employee
     * @queryParam filter.search string Search by name, email, or phone number.
     * @queryParam filter.ids array Filter by employee ids.
     * @queryParam filter.departments array Filter by department ids.
     * @queryParam filter.locations array Filter by location ids.
     * @queryParam filter.shifts array Filter by shift ids.
     * @queryParam filter.tags array Filter by tag ids.
     * @queryParam filter.exclude integer Exclude employee by id.
     * @queryParam filter.is_active boolean Filter by active status.
     * @queryParam include string Include related resources. E.g. include=department,shift,locations,tags
     * @queryParam per_page integer Number of items per page.
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'per_page' => ['nullable', 'integer'],
        ]);

        return new ApiResponse(
            EmployeeResource::collection(
                EmployeeQueryBuilder::query(auth()->user()->managedEmployees()->active())
                    // for mobile app, we need to eager load for `status` in employee resource
                    ->with('activeAttendanceRecord')
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Show employees
     * @apiResource App\Http\Resources\Mobile\V2\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function show(Employee $employee): ApiResponse
    {
        AuthorizeManagerToEmployee::authorize($employee);

        return new ApiResponse(
            data: EmployeeResource::make(
                $employee->load([
                    'department',
                    'upcomingShifts',
                    'activeLocations',
                    'activeAttendanceRecord',
                ])
            )
        );
    }
}
