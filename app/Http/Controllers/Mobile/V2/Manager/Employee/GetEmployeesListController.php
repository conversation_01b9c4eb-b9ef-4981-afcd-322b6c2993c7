<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\EmployeeResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @deprecated
 * @subgroup Manager - Employees
 */
class GetEmployeesListController extends Controller
{
    /**
     * Get employees list
     *
     * @queryParam search string nullable for searching in managed employees
     * @queryParam per_page number nullable the total number of employees per page
     * @queryParam page the page number
     */
    public function __invoke(Request $request): ApiResponse
    {
        $employees = $request
            ->user()
            ->managedEmployees()
            ->active()
            ->search($request->search)
            ->paginate($request->per_page);

        return new ApiResponse(data: EmployeeResource::collection($employees));
    }
}
