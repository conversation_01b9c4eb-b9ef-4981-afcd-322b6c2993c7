<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Enums\AttendanceStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\V3\AttendanceResource;
use App\Models\Attendance;
use App\Models\Employee;
use App\Support\ApiResponse;
use App\Support\AuthorizeManagerToEmployee;
use App\Support\EmployeeAttendanceSummary;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Mobile
 * @subgroup Manager - Employee Attendances
 */
class EmployeeAttendanceController extends Controller
{
    #[
        ResponseFromApiResource(
            name: AttendanceResource::class,
            model: Attendance::class,
            description: 'List employee attendance',
            with: ['employee'],
            additional: [
                'summary' => [
                    'total_checkins' => 1,
                    'total_hours' => 1,
                    'average_check_in_time' => '00:00',
                    'average_check_out_time' => '00:00',
                    'average_check_in_mood' => 1,
                    'average_check_out_mood' => 1,
                    'total_committed_hours' => 1,
                    'total_working_days' => 1,
                    'total_requests' => 1,
                    'total_approved_requests' => 1,
                    'total_rejected_requests' => 1,
                ],
            ]
        )
    ]
    public function index(Request $request, Employee $employee): ApiResponse
    {
        AuthorizeManagerToEmployee::authorize($employee);

        $request->validate([
            //todo: limit by ValidMaxPeriod validation
            'filter.from' => ['required', 'date:Y-m-d'],
            'filter.to' => ['required', 'date:Y-m-d'],
            'filter.status' => ['nullable', Rule::enum(AttendanceStatus::class)],
        ]);

        $employeeSummary = EmployeeAttendanceSummary::get(
            $employee,
            $request->date('filter.from'),
            $request->date('filter.to')
        );

        return new ApiResponse(
            data: AttendanceResource::collection(
                QueryBuilder::for($employee->attendances())
                    ->allowedFilters([
                        AllowedFilter::exact('status'),
                        AllowedFilter::callback('from', fn($q, $v) => $q->where('date', '>=', $v)),
                        AllowedFilter::callback('to', fn($q, $v) => $q->where('date', '<=', $v)),
                    ])
                    ->get()
            )->additional([
                'summary' => $employeeSummary->formatForResponse(),
            ])
        );
    }
}
