<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\Summary\AttendanceSummaryResource;
use App\Scopes\TenantScope;
use App\Support\ApiResponse;
use App\Support\EmployeeAttendanceSummary;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @deprecated use EmployeeSummaryController instead
 * @subgroup Manager - Employees
 */
class GetEmployeeSummaryController extends Controller
{
    /**
     * Get employee summary
     */
    public function __invoke(Request $request): ApiResponse
    {
        $request->validate([
            'employee_id' => ['required'],
            'date' => ['nullable', 'date_format:Y-m'],
        ]);

        $employee = auth()
            ->user()
            ->managedEmployees()
            ->active()
            ->where('employees.id', $request->employee_id)
            ->withoutGlobalScope(TenantScope::class)
            ->firstOrFail();

        if (!empty($request->date)) {
            $startOfMonth = Carbon::parse($request->date)->startOfMonth();
            $endOfMonth = Carbon::parse($request->date)->endOfMonth();
        } else {
            $startOfMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();
        }

        $employeeSummary = EmployeeAttendanceSummary::get($employee, $startOfMonth, $endOfMonth);

        $checkInOutSummary = $employeeSummary->checkInOutSummary();
        $committedHours = $employeeSummary->committedHours();
        $approvedRejectedRequests = $employeeSummary->approvalRequests();

        $responsePayload = [
            'total_checkins' => $checkInOutSummary['total_checkins'],
            'total_hours' => $checkInOutSummary['total_hours'],
            'average_check_in_time' => Carbon::parse(
                $checkInOutSummary['average_check_in']
            )->format('h:i a'),
            'average_check_out_time' => Carbon::parse(
                $checkInOutSummary['average_check_out']
            )->format('h:i a'),
            'average_check_in_mood' => $employeeSummary->getAvgCheckInMood(),
            'average_check_out_mood' => $employeeSummary->getAvgCheckOutMood(),
            'total_committed_hours' => $committedHours['total_committed_hours'],
            'total_working_days' => $committedHours['total_working_days'],
            'total_requests' => $approvedRejectedRequests['total_requests'],
            'total_approved_requests' => $approvedRejectedRequests['total_approved_requests'],
            'total_rejected_requests' => $approvedRejectedRequests['total_rejected_requests'],
            'attendances' => AttendanceSummaryResource::collection($employeeSummary->attendance()),
        ];

        return new ApiResponse(data: $responsePayload, spreadResponseContent: true);
    }
}
