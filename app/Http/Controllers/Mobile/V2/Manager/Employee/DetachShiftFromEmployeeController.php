<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Employee;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Support\ApiResponse;
use App\Support\AuthorizeManagerToEmployee;

class DetachShiftFromEmployeeController extends Controller
{
    public function __invoke(Employee $employee, int $employeeShiftID): ApiResponse
    {
        AuthorizeManagerToEmployee::authorize(employee: $employee, manager: request()->user());

        $employee->shifts()->wherePivot('id', $employeeShiftID)->firstOrFail();

        $employee->shifts()->wherePivot('id', $employeeShiftID)->detach();

        return new ApiResponse();
    }
}
