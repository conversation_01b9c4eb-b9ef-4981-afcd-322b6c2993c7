<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Leaves;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\LeaveResource;
use App\Queries\ManagerEmployeesLeaveQuery;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @deprecated
 * @subgroup Manager - Leaves
 */
class GetPendingLeaveRequestsController extends Controller
{
    /**
     * Get pending leave requests
     */
    public function __invoke(Request $request): ApiResponse
    {
        $leaveQuery = ManagerEmployeesLeaveQuery::build(auth()->user());

        return new ApiResponse(
            data: LeaveResource::collection(
                $leaveQuery
                    ->with(['employee.manager', 'department'])
                    ->pending()
                    ->get()
            )
        );
    }
}
