<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Leaves;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\BulkLeaveUpdateRequest;
use App\Models\Leave;
use App\Queries\ManagerEmployeesLeaveQuery;
use App\Services\RejectLeaveService;
use App\Support\ApiResponse;
use DB;

/**
 * @group Mobile
 * @subgroup Manager - Leaves
 */
class RejectLeaveController extends Controller
{
    /**
     * Reject Leave
     */
    public function __invoke(BulkLeaveUpdateRequest $request): ApiResponse
    {
        $request->validate([
            'rejection_reason' => ['required', 'string', 'max:255'],
        ]);

        $query = ManagerEmployeesLeaveQuery::build(auth()->user())->with([
            // load for the approval process
            'team',
            'employee',
        ]);

        $query->when(
            $request->boolean('all'),
            fn($query) => $query->pending(),
            fn($query) => $query->whereIn('id', $request->input('leaves'))
        );

        if ($query->clone()->notPending()->exists()) {
            return new ApiResponse(
                status: 'ONLY_PENDING_REQUESTS_CAN_BE_REJECTED',
                message: __('only pending requests can be accepted or rejected'),
                code: 400
            );
        }

        DB::transaction(function () use ($request, $query) {
            $query->each(function (Leave $leave) use ($request) {
                (new RejectLeaveService(
                    leave: $leave,
                    rejectionReason: $request->input('rejection_reason'),
                    decider: auth()->user()
                ))->handle();
            });
        });

        return new ApiResponse(message: __('Request rejected successfully'));
    }
}
