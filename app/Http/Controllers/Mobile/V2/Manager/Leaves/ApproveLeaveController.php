<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Leaves;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\BulkLeaveUpdateRequest;
use App\Models\Leave;
use App\Queries\ManagerEmployeesLeaveQuery;
use App\Services\ApproveLeaveService;
use App\Support\ApiResponse;
use DB;

/**
 * @group Mobile
 * @subgroup Manager - Leaves
 */
class ApproveLeaveController extends Controller
{
    /**
     * Approve Leave
     */
    public function __invoke(BulkLeaveUpdateRequest $request): ApiResponse
    {
        $query = ManagerEmployeesLeaveQuery::build(auth()->user())
            // load for the approval process
            ->with(['team', 'employee'])
            ->pending()
            ->when(
                $request->boolean('all'),
                fn($query) => $query,
                fn($query) => $query->whereIn('id', $request->input('leaves'))
            );

        DB::transaction(function () use ($request, $query) {
            $query->each(
                fn(Leave $leave) => (new ApproveLeaveService($leave, auth()->user()))->handle()
            );
        });

        return new ApiResponse(message: __('Request approved successfully'));
    }
}
