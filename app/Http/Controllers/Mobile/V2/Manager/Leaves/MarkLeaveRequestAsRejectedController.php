<?php

namespace App\Http\Controllers\Mobile\V2\Manager\Leaves;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\LeaveResource;
use App\Models\Leave;
use App\Support\ApiResponse;
use Illuminate\Http\Response;

/**
 * @group Mobile
 * @subgroup Manager - Leaves
 * @deprecated
 */
class MarkLeaveRequestAsRejectedController extends Controller
{
    /**
     * Mark leave request as rejected
     */
    public function __invoke(Leave $leave): ApiResponse
    {
        $this->authorize('reject', $leave);

        if ($leave->status !== RequestStatus::Pending) {
            return new ApiResponse(
                status: 'ONLY_PENDING_REQUESTS_CAN_BE_APPROVED',
                message: __('only pending requests can be accepted or rejected'),
                code: Response::HTTP_CONFLICT
            );
        }
        $leave->update(['status' => RequestStatus::Rejected]);

        //TODO
        //Notifications

        return new ApiResponse(
            data: new LeaveResource($leave->loadMissing(['employee.manager', 'department']))
        );
    }
}
