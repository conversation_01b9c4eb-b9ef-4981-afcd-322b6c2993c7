<?php

namespace App\Http\Controllers\Mobile\V2\Manager;

use App\DTOs\AttachEmployeesToShiftLocationData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\AttachEmployeesToLocationRequest;
use App\Jobs\EmployeeLocationJob;
use App\Models\Location;
use App\Support\ApiResponse;

/**
 * @group Mobile
 * @subgroup Manager - Employees
 */
class AttachLocationToEmployeesController extends Controller
{
    /**
     * Attach employees to a location
     * @queryParam filter.search string Search by name, email, or phone number.
     * @queryParam filter.ids array Filter by employee ids.
     * @queryParam filter.departments array Filter by department ids.
     * @queryParam filter.locations array Filter by location ids.
     * @queryParam filter.shifts array Filter by shift ids.
     * @queryParam filter.tags array Filter by tag ids.
     * @queryParam filter.exclude integer Exclude employee by id.
     * @queryParam filter.is_active boolean Filter by active status.
     * @queryParam include string Include related resources. E.g. include=department,shift,locations,tags
     * @queryParam per_page integer Number of items per page.
     */
    public function __invoke(AttachEmployeesToLocationRequest $request, Location $location = null)
    {
        $employeeLocationJob = new EmployeeLocationJob(
            data: AttachEmployeesToShiftLocationData::fromRequest(),
            location: $location,
            manager: auth()->user()
        );

        return new ApiResponse(message: $employeeLocationJob->queueIfNeededAndReturnMessage());
    }
}
