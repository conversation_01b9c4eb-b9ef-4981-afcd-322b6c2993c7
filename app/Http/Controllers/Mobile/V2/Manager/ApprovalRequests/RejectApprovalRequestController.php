<?php

namespace App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\BulkApprovalRequestUpdateRequest;
use App\Models\ApprovalRequest;
use App\Queries\ManagerEmployeesApprovalRequestQuery;
use App\Services\RejectApprovalRequestService;
use App\Support\ApiResponse;

/**
 * @group Mobile
 * @subgroup Manager - ApprovalRequests
 */
class RejectApprovalRequestController extends Controller
{
    /**
     * Reject ApprovalRequest
     */
    public function __invoke(BulkApprovalRequestUpdateRequest $request): ApiResponse
    {
        $request->validate([
            'rejection_reason' => ['required', 'string', 'max:255'],
        ]);

        $query = ManagerEmployeesApprovalRequestQuery::build(auth()->user())->with([
            // load for the rejection process
            'team',
            'employee',
        ]);

        $query->when(
            $request->boolean('all'),
            fn($query) => $query->pending(),
            fn($query) => $query->whereIn('id', $request->input('approval_requests'))
        );

        if ($query->clone()->notPending()->exists()) {
            return new ApiResponse(
                status: 'ONLY_PENDING_REQUESTS_CAN_BE_REJECTED',
                message: __('only pending requests can be accepted or rejected'),
                code: 400
            );
        }

        $query->each(function (ApprovalRequest $approvalRequest) use ($request) {
            (new RejectApprovalRequestService(
                approvalRequest: $approvalRequest,
                rejectionReason: $request->input('rejection_reason'),
                decider: auth()->user()
            ))->handle();
        });

        return new ApiResponse(message: __('Request approved successfully'));
    }
}
