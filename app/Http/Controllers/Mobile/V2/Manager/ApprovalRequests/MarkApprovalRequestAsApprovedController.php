<?php

namespace App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\ApprovalRequestResource;
use App\Models\ApprovalRequest;
use App\Services\ApproveApprovalRequestService;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * @group Mobile
 * @subgroup Manager - Approval Requests
 * @deprecated
 */
class MarkApprovalRequestAsApprovedController extends Controller
{
    /**
     * Approve approval request
     * @response 409 {"status": "ONLY_PENDING_REQUESTS_CAN_BE_APPROVED", "message": "only pending requests can be accepted or rejected"}
     */
    public function __invoke(Request $request, ApprovalRequest $approvalRequest): ApiResponse
    {
        $this->authorize('approve', $approvalRequest);

        if ($approvalRequest->status !== ApprovalRequest::PENDING) {
            return new ApiResponse(
                status: 'ONLY_PENDING_REQUESTS_CAN_BE_ACCEPTED',
                message: __('only pending requests can be accepted'),
                code: Response::HTTP_CONFLICT
            );
        }

        (new ApproveApprovalRequestService(
            approvalRequest: $approvalRequest,
            decider: auth()->user()
        ))->handle();

        return new ApiResponse(
            data: new ApprovalRequestResource(
                $approvalRequest->refresh()->loadMissing(['employee.manager', 'department'])
            )
        );
    }
}
