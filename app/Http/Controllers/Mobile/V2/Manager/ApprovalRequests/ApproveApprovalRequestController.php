<?php

namespace App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\BulkApprovalRequestUpdateRequest;
use App\Models\ApprovalRequest;
use App\Queries\ManagerEmployeesApprovalRequestQuery;
use App\Services\ApproveApprovalRequestService;
use App\Support\ApiResponse;

/**
 * @group Mobile
 * @subgroup Manager - ApprovalRequests
 */
class ApproveApprovalRequestController extends Controller
{
    /**
     * Approve ApprovalRequest
     */
    public function __invoke(BulkApprovalRequestUpdateRequest $request): ApiResponse
    {
        $query = ManagerEmployeesApprovalRequestQuery::build(auth()->user())
            // load for the approval process
            ->with(['team', 'employee'])
            ->pending()
            ->when(
                $request->boolean('all'),
                fn($query) => $query,
                fn($query) => $query->whereIn('id', $request->input('approval_requests'))
            );

        $query->each(function (ApprovalRequest $approvalRequest) {
            (new ApproveApprovalRequestService(
                approvalRequest: $approvalRequest,
                decider: auth()->user()
            ))->handle();
        });

        return new ApiResponse(message: __('Request approved successfully'));
    }
}
