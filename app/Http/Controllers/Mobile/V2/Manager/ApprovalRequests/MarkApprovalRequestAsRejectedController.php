<?php

namespace App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests;

use App\Events\ApprovalRequestRejected;
use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\ApprovalRequestResource;
use App\Models\ApprovalRequest;
use App\Notifications\ApprovalRequestRejectedNotification;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * @group Mobile
 * @subgroup Manager - Approval Requests
 * @deprecated
 */
class MarkApprovalRequestAsRejectedController extends Controller
{
    /**
     * Reject approval request
     * @response 409 {"status": "ONLY_PENDING_REQUESTS_CAN_BE_REJECTED", "message": "only pending requests can be accepted or rejected"}
     */
    public function __invoke(Request $request, ApprovalRequest $approvalRequest): ApiResponse
    {
        $this->authorize('reject', $approvalRequest);

        if ($approvalRequest->status !== ApprovalRequest::PENDING) {
            return new ApiResponse(
                status: 'ONLY_PENDING_REQUESTS_CAN_BE_REJECTED',
                message: __('only pending requests can be accepted or rejected'),
                code: Response::HTTP_CONFLICT
            );
        }

        $approvalRequest->update(['status' => ApprovalRequest::REJECTED]);

        $approvalRequest->employee->notify(
            new ApprovalRequestRejectedNotification($approvalRequest)
        );

        event(new ApprovalRequestRejected($approvalRequest));

        return new ApiResponse(
            data: new ApprovalRequestResource(
                $approvalRequest->loadMissing(['employee.manager', 'department'])
            )
        );
    }
}
