<?php

namespace App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\ApprovalRequestResource;
use App\Queries\ManagerEmployeesApprovalRequestQuery;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Manager - Approval Requests
 * @deprecated
 */
class GetPendingApprovalRequestsController extends Controller
{
    /**
     * List pending approval requests
     */
    public function __invoke(Request $request): ApiResponse
    {
        $approvalRequestQuery = ManagerEmployeesApprovalRequestQuery::build(auth()->user());

        return new ApiResponse(
            data: ApprovalRequestResource::collection(
                $approvalRequestQuery
                    ->with(['employee.manager', 'department'])
                    ->pending()
                    ->get()
            )
        );
    }
}
