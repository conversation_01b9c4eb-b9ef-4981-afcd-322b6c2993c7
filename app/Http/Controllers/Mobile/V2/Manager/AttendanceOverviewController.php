<?php

namespace App\Http\Controllers\Mobile\V2\Manager;

use App\Calculations\AverageWorkingHoursCalculator;
use App\Calculations\TodayAttendanceCalculator;
use App\Http\Controllers\Controller;
use App\Models\Attendance;
use App\Scopes\DateFilter;
use App\Support\ApiResponse;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Manager - Attendances
 */
class AttendanceOverviewController extends Controller
{
    /**
     * Get the attendance overview.
     * @response {
     *     "data": {
     *        "today": {
     *           "total": 2,
     *          "average_working_hours": 8.5
     *       }
     * }
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d'],
        ]);

        $period = CarbonPeriod::create(
            $request->date('filter.from') ?? now()->startOfMonth(),
            $request->date('filter.to') ?? now()->endOfMonth()
        );

        $employeesQuery = auth()->user()->managedEmployees()->active();

        $attendanceQuery = Attendance::query()
            ->ofEmployeesOfManager(auth()->user())
            ->tap(new DateFilter($period, fromColumn: 'date'))
            ->present();

        return new ApiResponse(
            data: [
                'today' => (new TodayAttendanceCalculator())->calculate($employeesQuery),
                'average_working_hours' => (new AverageWorkingHoursCalculator())->calculateStatsByDays(
                    $attendanceQuery
                ),
            ]
        );
    }
}
