<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\InMoodRequest;
use App\Http\Requests\Mobile\OutMoodRequest;
use App\Http\Requests\Mobile\SummaryRequest;
use App\Http\Resources\Mobile\AttendanceResource;
use App\Models\Attendance;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use function now;

/**
 * @group Mobile
 * @subgroup Employee - Attendances
 */
class AttendanceController extends Controller
{
    /**
     * Update In Mood
     * @bodyParam value int required Must be at least 1. Must not be greater than 5. Example: 3
     *
     * @response 200
     */
    public function updateInMood(InMoodRequest $request): ApiResponse
    {
        $attendance = $request->user()->activeAttendanceRecord;

        if (!$attendance) {
            return new ApiResponse(
                status: 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD',
                message: __('Employee does not have active attendance record'),
                code: Response::HTTP_EXPECTATION_FAILED
            );
        }

        $attendance->update(['in_mood' => $request->value]);

        return new ApiResponse(data: new AttendanceResource($attendance));
    }

    /**
     * Update Out Mood
     *
     *
     * @bodyParam value int required Must be at least 1. Must not be greater than 5. Example: 3
     *
     * @response 200
     */
    public function updateOutMood(OutMoodRequest $request): ApiResponse
    {
        $employee = $request->user();
        $attendance = $employee->activeAttendanceRecord;

        if (!$attendance) {
            return new ApiResponse(
                status: 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD',
                message: __('Employee does not have active attendance record'),
                code: Response::HTTP_EXPECTATION_FAILED
            );
        }

        $attendance->update(['out_mood' => $request->value]);

        return new ApiResponse(data: new AttendanceResource($attendance));
    }

    /**
     * Weekly Attendance Summary
     * @deprecated
     * @responseFile  200 storage/responses/attendance.weekly.json
     */
    public function weeklyAttendanceSummary(Request $request): ApiResponse
    {
        $employee = $request->user();

        $date = now()
            ->startOfWeek(Carbon::SUNDAY)
            ->startOfDay();

        return new ApiResponse(
            data: [
                'summary' => Attendance::query()
                    ->select(['net_hours', 'date'])
                    ->where('employee_id', $employee->id)
                    ->where('status', Attendance::PRESENT)
                    ->where('date', '>=', $date)
                    ->get()
                    ->append('day_of_week'),
                'last_check_in_mood' => $employee?->last_attendance?->in_mood ?: 0,
                'last_check_out_mood' => $employee?->last_attendance?->out_mood ?: 0,
            ],
            spreadResponseContent: true
        );
    }

    /**
     * Days Attendance Summary
     *
     * @deprecated
     * @bodyParam from date required the target date to start with. Example: 2023-01-01
     * @bodyParam to date required the target date to end with. Example: 2023-02-01
     *
     * @responseFile 200 storage/responses/attendance.days.summary.json
     */
    public function daysAttendanceSummary(SummaryRequest $request): ApiResponse
    {
        return new ApiResponse(
            data: AttendanceResource::collection(
                $request
                    ->user()
                    ->attendances()
                    ->whereBetween('date', [
                        Carbon::parse($request->from),
                        Carbon::parse($request->to),
                    ])
                    ->get()
            )
        );
    }
}
