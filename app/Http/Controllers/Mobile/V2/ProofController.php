<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProofResource;
use App\Support\ApiResponse;

/**
 * @group Mobile
 * @subgroup Employee - Proofs
 */
class ProofController extends Controller
{
    /**
     * List proofs
     * @apiResource App\Http\Resources\ProofResource
     * @apiResourceModel App\Models\Proof
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(
            data: ProofResource::collection(
                auth()->user()->proofs()->where('status', 'SENT')->get()
            )
        );
    }
}
