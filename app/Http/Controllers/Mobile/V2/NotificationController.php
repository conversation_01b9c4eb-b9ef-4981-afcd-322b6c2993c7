<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\NotificationResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Employee - Notifications
 */
class NotificationController extends Controller
{
    /**
     * List notifications
     * @apiResource App\Http\Resources\NotificationResource
     * @apiResourceModel Illuminate\Notifications\DatabaseNotification
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: NotificationResource::collection(
                auth()
                    ->user()
                    ->notifications()
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
