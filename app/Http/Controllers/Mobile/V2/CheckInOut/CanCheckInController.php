<?php

namespace App\Http\Controllers\Mobile\V2\CheckInOut;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\CheckInRequest;
use App\Models\Activity;
use App\Support\ApiResponse;
use Illuminate\Http\Response;

/**
 * @group Mobile
 * @subgroup Employee - Check In/Out
 */
class CanCheckInController extends Controller
{
    /**
     * Check if the employee can check in.
     * @bodyParam lat float required The latitude of the location. Example: 31.5204
     * @bodyParam lng float required The longitude of the location. Example: 74.3587
     * @bodyParam automatic boolean required Whether the check-in is automatic. Example: true
     * @response status=200 scenario=ALLOWED
     * @response status=200 scenario=APPROVED
     * @response status=200 scenario=ATTENDANCE_REQUEST_CREATED
     * @response status=417 scenario=EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD
     * @response status=417 scenario=NOT_ALLOWED
     * @response status=417 scenario=REACHED_REMOTE_WORK_DAYS_MONTHLY_LIMITS
     */
    public function __invoke(CheckInRequest $request): ApiResponse
    {
        $activeAttendanceRecord = auth()->user()->firstOrCreateActiveAttendanceRecord();

        if (!$activeAttendanceRecord) {
            return new ApiResponse(
                status: 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD',
                message: __('Employee does not have active attendance record'),
                code: Response::HTTP_EXPECTATION_FAILED
            );
        }

        if (
            auth()
                ->user()
                ->checkLocation($request['lat'], $request['lng'])
        ) {
            return new ApiResponse(status: 'ALLOWED');
        }

        if (auth()->user()->remote_work_policy === 'allowed') {
            return new ApiResponse(status: 'APPROVED');
        }

        if (auth()->user()->remote_work_policy === 'allowed_with_approval') {
            return auth()->user()->reachedRemoteWorkDaysLimits()
                ? new ApiResponse(
                    status: 'REACHED_REMOTE_WORK_DAYS_MONTHLY_LIMITS',
                    message: __('Reached remote work days monthly limits.'),
                    code: Response::HTTP_EXPECTATION_FAILED
                )
                : new ApiResponse(
                    status: 'ATTENDANCE_REQUEST_CREATED',
                    message: __('Attendance request created'),
                    // todo: why 417 not 200?
                    code: Response::HTTP_EXPECTATION_FAILED
                );
        }

        auth()
            ->user()
            ->activities()
            ->create([
                'action' => $request->automatic
                    ? Activity::OUT_OF_ZONE_AUTOMATIC_CHECK_IN_LOG
                    : Activity::OUT_OF_ZONE_CHECK_IN_LOG,
                'lat' => $request->lat,
                'lng' => $request->lng,
                'location_id' => null,
            ]);

        return new ApiResponse(
            status: 'NOT_ALLOWED',
            message: __('Not allowed'),
            code: Response::HTTP_EXPECTATION_FAILED
        );
    }
}
