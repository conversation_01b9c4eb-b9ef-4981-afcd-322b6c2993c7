<?php

namespace App\Http\Controllers\Mobile\V2\CheckInOut;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\CheckOutRequest;
use App\Models\Activity;
use App\Support\ApiResponse;
use Illuminate\Http\Response;

/**
 * @group Mobile
 * @subgroup Employee - Check In/Out
 */
class CanCheckoutController extends Controller
{
    /**
     * Check if the employee can check out.
     *
     * @bodyParam lat float required The latitude of the location. Example: 31.5204
     * @bodyParam lng float required The longitude of the location. Example: 74.3587
     * @bodyParam automatic boolean required Whether the check-out is automatic. Example: true
     *
     * @response status=200 scenario=ALLOWED
     * @response status=200 scenario=APPROVED
     * @response status=200 scenario=ATTENDANCE_REQUEST_CREATED
     * @response status=417 scenario=NOT_CHECKED_IN_YET
     * @response status=417 scenario=NOT_ALLOWED
     */
    public function __invoke(CheckOutRequest $request): ApiResponse
    {
        $employee = $request->user();

        if (!$employee->isCheckedIn()) {
            return new ApiResponse(
                status: 'NOT_CHECKED_IN_YET',
                message: __('Not checked in yet'),
                code: Response::HTTP_EXPECTATION_FAILED
            );
        }

        if ($employee->checkLocation($request['lat'], $request['lng'], $request['automatic'])) {
            return new ApiResponse(status: 'ALLOWED');
        }
        //TODO: check for free checkout configuration

        if ($employee->remote_work_policy === 'allowed') {
            return new ApiResponse(status: 'APPROVED');
        }

        if ($employee->remote_work_policy === 'allowed_with_approval') {
            return new ApiResponse(
                status: 'ATTENDANCE_REQUEST_CREATED',
                message: __('Attendance request created'),
                code: Response::HTTP_EXPECTATION_FAILED
            );
        }

        $employee->activities()->create([
            'team_id' => $employee->team_id,
            'action' => $request['automatic']
                ? Activity::OUT_OF_ZONE_AUTOMATIC_CHECK_OUT_LOG
                : Activity::OUT_OF_ZONE_CHECK_OUT_LOG,
            'lat' => $request['lat'],
            'lng' => $request['lng'],
            'location_id' => null,
        ]);

        return new ApiResponse(
            status: 'NOT_ALLOWED',
            message: __('Not allowed'),
            code: Response::HTTP_EXPECTATION_FAILED
        );
    }
}
