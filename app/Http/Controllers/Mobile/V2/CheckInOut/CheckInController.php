<?php

namespace App\Http\Controllers\Mobile\V2\CheckInOut;

use App\Events\EmployeeCheckedIn;
use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\CheckInRequest;
use App\Http\Resources\Mobile\ActivityResource;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Support\ApiResponse;
use Illuminate\Http\Response;
use function now;

/**
 * @group Mobile
 * @subgroup Employee - Check In/Out
 */
class CheckInController extends Controller
{
    /**
     * Check in the employee.
     *
     * @bodyParam lat float required The latitude of the location. Example: 31.5204
     * @bodyParam lng float required The longitude of the location. Example: 74.3587
     * @bodyParam automatic boolean required Whether the check-in is automatic. Example: true
     *
     * @apiResource App\Http\Resources\Mobile\ActivityResource
     * @apiResourceModel App\Models\Activity
     * @response status=409 scenario=ALREADY_CHECKED_IN
     * @response status=406 scenario=OUT_OF_ZONE
     * @response status=417 scenario=EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD
     * @response status=417 scenario=REACHED_REMOTE_WORK_DAYS_MONTHLY_LIMITS
     * @response status=417 scenario=ALREADY_HAS_REMOTE_WORK_APPROVAL_REQUEST
     */
    public function __invoke(CheckInRequest $request): ApiResponse
    {
        /** @var Employee $employee */
        $employee = auth()->user();

        $activeAttendanceRecord = $employee->firstOrCreateActiveAttendanceRecord();

        if (!$activeAttendanceRecord) {
            return new ApiResponse(
                status: 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD',
                message: __('Employee does not have active attendance record'),
                code: Response::HTTP_EXPECTATION_FAILED
            );
        }

        if ($employee->isCheckedIn()) {
            return new ApiResponse(
                status: 'ALREADY_CHECKED_IN',
                message: __('Already checked in'),
                code: Response::HTTP_CONFLICT
            );
        }

        $insideLocation = $employee->checkLocation($request['lat'], $request['lng']);

        if (!$insideLocation && $employee->remote_work_policy === 'allowed_with_approval') {
            if ($employee->reachedRemoteWorkDaysLimits()) {
                return new ApiResponse(
                    status: 'REACHED_REMOTE_WORK_DAYS_MONTHLY_LIMITS',
                    message: __('Reached remote work days monthly limits.'),
                    code: Response::HTTP_EXPECTATION_FAILED
                );
            }

            if ($employee->hasPendingRemoteWorkApprovalRequest(ApprovalRequest::CHECK_IN)) {
                return new ApiResponse(
                    status: 'ALREADY_HAS_REMOTE_WORK_APPROVAL_REQUEST',
                    message: __('Already has remote work approval request'),
                    code: Response::HTTP_EXPECTATION_FAILED
                );
            }

            ApprovalRequest::create([
                'team_id' => $employee->team_id,
                'employee_id' => $employee->id,
                'department_id' => $employee->department_id,
                'reason' => 'checkin and out of the zone',
                'type' => ApprovalRequest::REMOTE_WORK,
                'status' => ApprovalRequest::PENDING,
                'from_datetime' => now(),
                'to_datetime' => now(),
                'attendance_type' => ApprovalRequest::CHECK_IN,
            ]);
        }

        if (!$insideLocation && $employee->remote_work_policy === 'not_allowed') {
            $employee->activities()->create([
                'team_id' => $employee->team_id,
                'action' => $request['automatic']
                    ? Activity::OUT_OF_ZONE_AUTOMATIC_CHECK_IN_LOG
                    : Activity::OUT_OF_ZONE_CHECK_IN_LOG,
                'lat' => $request['lat'],
                'lng' => $request['lng'],
                'location_id' => null,
            ]);

            return new ApiResponse(
                status: 'OUT_OF_ZONE',
                message: __('Out of zone'),
                code: Response::HTTP_NOT_ACCEPTABLE
            );
        }

        $activity = $employee->activities()->create([
            'team_id' => $employee->team_id,
            'action' => $insideLocation
                ? ($request['automatic']
                    ? Activity::AUTOMATIC_CHECK_IN
                    : Activity::CHECK_IN)
                : ($employee->remote_work_policy === 'allowed'
                    ? Activity::REMOTE_CHECK_IN_APPROVED
                    : Activity::REMOTE_CHECK_IN),
            'lat' => $request['lat'],
            'lng' => $request['lng'],
            'location_id' => $insideLocation ?: null,
        ]);

        event(new EmployeeCheckedIn($activity));

        return new ApiResponse(data: new ActivityResource($activity));
    }
}
