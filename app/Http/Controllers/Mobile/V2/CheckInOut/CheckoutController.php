<?php

namespace App\Http\Controllers\Mobile\V2\CheckInOut;

use App\Events\EmployeeCheckedOut;
use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\CheckOutRequest;
use App\Http\Resources\Mobile\ActivityResource;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Support\ApiResponse;
use Illuminate\Http\Response;
use function now;

/**
 * @group Mobile
 * @subgroup Employee - Check In/Out
 */
class CheckoutController extends Controller
{
    /**
     * Check out the employee.
     *
     * @bodyParam lat float required The latitude of the location. Example: 31.5204
     * @bodyParam lng float required The longitude of the location. Example: 74.3587
     * @bodyParam automatic boolean required Whether the check-out is automatic. Example: true
     *
     * @apiResource App\Http\Resources\Mobile\ActivityResource
     * @apiResourceModel App\Models\Activity
     * @response status=406 scenario=OUT_OF_ZONE
     * @response status=406 scenario=OUT_OF_ZONE_AUTOMATIC_CHECK_OUT_LOG
     * @response status=200 scenario=CHECK_OUT
     * @response status=200 scenario=AUTOMATIC_CHECK_OUT
     * @response status=200 scenario=REMOTE_CHECK_OUT
     * @response status=200 scenario=REMOTE_CHECK_OUT_APPROVED
     * @response status=200 scenario=OUT_OF_ZONE_CHECK_OUT_LOG
     * @response status=200 scenario=OUT_OF_ZONE_AUTOMATIC_CHECK_OUT_LOG
     * @response status=409 scenario=ALREADY_HAS_REMOTE_WORK_APPROVAL_REQUEST
     * @response status=406 scenario=OUT_OF_ZONE
     * @response status=417 scenario=OUT_OF_ZONE
     * @response status=417 scenario=OUT_OF_ZONE_AUTOMATIC_CHECK_OUT_LOG
     * @response status=417 scenario=OUT_OF_ZONE_CHECK_OUT_LOG
     */
    public function __invoke(CheckOutRequest $request): ApiResponse
    {
        $employee = $request->user();

        if (!$employee->isCheckedIn()) {
            return new ApiResponse(
                status: 'NOT_CHECKED_IN_YET',
                message: __('Not checked in yet'),
                code: Response::HTTP_EXPECTATION_FAILED
            );
        }

        $insideLocation = $employee->checkLocation(
            $request['lat'],
            $request['lng'],
            $request['automatic']
        );

        if (!$insideLocation && $employee->remote_work_policy === 'allowed_with_approval') {
            if ($employee->hasPendingRemoteWorkApprovalRequest(ApprovalRequest::CHECK_OUT)) {
                return new ApiResponse(
                    status: 'ALREADY_HAS_REMOTE_WORK_APPROVAL_REQUEST',
                    message: __('Already has remote work approval request'),
                    code: Response::HTTP_EXPECTATION_FAILED
                );
            }

            ApprovalRequest::create([
                'team_id' => $employee->team_id,
                'employee_id' => $employee->id,
                'department_id' => $employee->department_id,
                'reason' => 'checkout and out of the zone',
                'type' => ApprovalRequest::REMOTE_WORK,
                'status' => ApprovalRequest::PENDING,
                'from_datetime' => now(),
                'to_datetime' => now(),
                'attendance_type' => ApprovalRequest::CHECK_OUT,
            ]);
        }

        if (!$insideLocation && $employee->remote_work_policy === 'not_allowed') {
            $employee->activities()->create([
                'team_id' => $employee->team_id,
                'action' => $request['automatic']
                    ? Activity::OUT_OF_ZONE_AUTOMATIC_CHECK_OUT_LOG
                    : Activity::OUT_OF_ZONE_CHECK_OUT_LOG,
                'lat' => $request['lat'],
                'lng' => $request['lng'],
                'location_id' => null,
            ]);

            return new ApiResponse(
                status: 'OUT_OF_ZONE',
                message: __('Out of zone'),
                code: Response::HTTP_NOT_ACCEPTABLE
            );
        }

        $activity = $employee->activities()->create([
            'team_id' => $employee->team_id,
            'action' => $insideLocation
                ? ($request['automatic']
                    ? Activity::AUTOMATIC_CHECK_OUT
                    : Activity::CHECK_OUT)
                : ($employee->remote_work_policy === 'allowed'
                    ? Activity::REMOTE_CHECK_OUT_APPROVED
                    : Activity::REMOTE_CHECK_OUT),
            'lat' => $request['lat'],
            'lng' => $request['lng'],
            'location_id' => $insideLocation ?: null,
        ]);

        event(new EmployeeCheckedOut($activity));

        return new ApiResponse(data: new ActivityResource($activity));
    }
}
