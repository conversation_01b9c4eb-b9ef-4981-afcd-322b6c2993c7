<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\LocationResource;
use App\Models\Location;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Mobile
 * @subgroup Config - Locations
 */
class LocationController extends Controller
{
    /**
     * Get Locations
     * @apiResource App\Http\Resources\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.name' => ['nullable', 'string'],
        ]);

        return new ApiResponse(
            data: LocationResource::collection(
                QueryBuilder::for(Location::class)
                    ->allowedFilters(['name'])
                    ->paginateOrGet()
            )
        );
    }
}
