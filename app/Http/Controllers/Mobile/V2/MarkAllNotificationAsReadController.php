<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Employee - Notifications
 */
class MarkAllNotificationAsReadController extends Controller
{
    /**
     * Mark all notifications as read
     */
    public function __invoke(Request $request): ApiResponse
    {
        auth()->user()->unreadNotifications()->get()->markAsRead();

        return new ApiResponse();
    }
}
