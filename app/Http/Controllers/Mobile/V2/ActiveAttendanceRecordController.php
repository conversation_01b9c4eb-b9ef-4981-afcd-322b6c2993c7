<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\V2\AttendanceResource;
use App\Support\ApiResponse;

/**
 * @group Mobile
 * @subgroup Employee - Attendances
 */
class ActiveAttendanceRecordController extends Controller
{
    /**
     * Get the active attendance record.
     */
    public function __invoke(): ApiResponse
    {
        return new ApiResponse(
            data: auth()->user()->activeAttendanceRecord
                ? new AttendanceResource(auth()->user()->activeAttendanceRecord)
                : null
        );
    }
}
