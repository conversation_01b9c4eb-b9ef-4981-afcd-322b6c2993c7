<?php

namespace App\Http\Controllers\Mobile\V2\ApprovalRequests;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\ApprovalRequestResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @deprecated
 * @subgroup Employee - Approval Requests
 */
class GetMyApprovalRequestsController extends Controller
{
    /**
     * Get my approval requests
     */
    public function __invoke(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: ApprovalRequestResource::collection(
                auth()
                    ->user()
                    ->approvalRequests()
                    ->with(['employee.manager', 'department'])
                    ->get()
            )
        );
    }
}
