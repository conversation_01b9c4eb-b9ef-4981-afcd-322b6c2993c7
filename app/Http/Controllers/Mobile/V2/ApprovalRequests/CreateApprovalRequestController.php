<?php

namespace App\Http\Controllers\Mobile\V2\ApprovalRequests;

use App\Calculations\EmployeeBalanceCalculator;
use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateApprovalRequest;
use App\Http\Resources\Mobile\ApprovalRequestResource;
use App\Models\ApprovalRequest;
use App\Services\CheckEmployeeStatementDurationForPreventingRequests;
use App\Support\ApiResponse;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Symfony\Component\HttpFoundation\Response;

/**
 * @group Mobile
 * @subgroup Employee - Approval Requests
 */
class CreateApprovalRequestController extends Controller
{
    /**
     * Create approval request
     * @apiResource App\Http\Resources\Mobile\ApprovalRequestResource
     * @apiResourceModel App\Models\ApprovalRequest with=employee.manager,department
     * @response status=400 scenario=NO_ATTENDANCE_RECORD_FOUND_IN_REQUEST_DATE
     * @response status=400 scenario=REGULARIZATION_REQUEST_ALREADY_EXISTS_IN_REGULATED_DATE
     * @response status=400 scenario=REACHED_MAXIMUM_NUMBER_OF_APPROVAL_REQUESTS
     * @response status=400 scenario=PERMISSION_REQUESTS_ARE_DISABLED
     * @response status=400 scenario=DAILY_OR_MONTHLY_LIMIT_EXCEEDED
     */
    public function __invoke(CreateApprovalRequest $request): ApiResponse
    {
        $employee = auth()->user();

        (new CheckEmployeeStatementDurationForPreventingRequests(
            employee: $employee,
            period: CarbonPeriod::create($request->from, $request->to)
        ))->validate();

        $attendance = $employee
            ->attendances()
            ->date($request->date('from'))
            ->with('employeeStatement')
            ->first();

        // approval requests shouldn’t be created in day when no attendance record for the employee exists
        if (!$attendance) {
            return new ApiResponse(
                status: 'NO_ATTENDANCE_RECORD_FOUND_IN_REQUEST_DATE',
                message: __('no attendance record found in request date'),
                code: Response::HTTP_BAD_REQUEST
            );
        }

        if ($request->validated('type') === ApprovalRequest::REGULARIZATION) {
            // employee can't have more than one pending or approved regularization requests per date
            if (
                $employee
                    ->approvalRequests()
                    ->regularization()
                    ->whereIn('status', [RequestStatus::Pending, RequestStatus::Approved])
                    ->whereDate('from_datetime', $request->date('from'))
                    ->exists()
            ) {
                return new ApiResponse(
                    status: 'REGULARIZATION_REQUEST_ALREADY_EXISTS_IN_REGULATED_DATE',
                    message: __(
                        'pending or approved regularization request in the regulated date already exists'
                    ),
                    code: Response::HTTP_BAD_REQUEST
                );
            }

            // employee can't exceed the maximum number of regulation requests allowed
            $balance = new EmployeeBalanceCalculator(
                $employee,
                CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth())
            );

            if ($balance->remainingRequests($request->type) <= 0) {
                return new ApiResponse(
                    status: 'REACHED_MAXIMUM_NUMBER_OF_APPROVAL_REQUESTS',
                    message: __('reached maximum number of approval requests'),
                    code: Response::HTTP_BAD_REQUEST
                );
            }
        } elseif ($request->validated('type') === ApprovalRequest::PERMISSION) {
            // permission requests must be enabled in the tenant
            if ($employee->team->permission_request === false) {
                return new ApiResponse(
                    status: 'PERMISSION_REQUESTS_ARE_DISABLED',
                    message: __('permission requests are disabled'),
                    code: Response::HTTP_BAD_REQUEST
                );
            }

            // check limits
            $balance = (new EmployeeBalanceCalculator(
                $employee,
                CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth())
            ))->remainingPermissionRequests()->currentBalance;

            //    balance is null means no limit enforced
            $newRequestInterval = Carbon::parse($request->validated('from'))->diffAsCarbonInterval(
                $request->validated('to')
            );

            if (
                !is_null($balance) &&
                $balance->sub($newRequestInterval)->lessThan(CarbonInterval::second(0))
            ) {
                return new ApiResponse(
                    status: 'DAILY_OR_MONTHLY_LIMIT_EXCEEDED',
                    message: __('daily or monthly limit of permission request hours exceeded'),
                    code: Response::HTTP_BAD_REQUEST
                );
            }
        }

        $approvalRequest = ApprovalRequest::create([
            'team_id' => $employee->team_id,
            'employee_id' => $employee->id,
            'department_id' => $employee->department_id,
            'reason' => $request->reason,
            'type' => $request->type,
            'from_datetime' => $request->from,
            'to_datetime' => $request->to,
            'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
            'status' => ApprovalRequest::PENDING,
        ]);

        $attendance->employeeStatement?->requestable()->associate($approvalRequest)->save();

        return new ApiResponse(
            data: new ApprovalRequestResource(
                $approvalRequest->loadMissing(['employee.manager', 'department'])
            )
        );
    }
}
