<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\ShiftResource;
use App\Models\Shift;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Mobile
 * @subgroup Config - Shift
 */
class ShiftController extends Controller
{
    /**
     * List Shifts
     * @apiResource App\Http\Resources\ShiftResource
     * @apiResourceModel App\Models\Shift
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.name' => ['nullable', 'string'],
            'per_page' => ['nullable', 'integer'],
        ]);

        return new ApiResponse(
            data: ShiftResource::collection(
                QueryBuilder::for(Shift::class)
                    ->allowedFilters(['name'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
