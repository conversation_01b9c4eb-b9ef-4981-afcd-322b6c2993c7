<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\Mobile\V2\ScheduledLocationsRequest;
use App\Http\Resources\LocationResource;
use App\Models\Location;
use App\Support\ApiResponse;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;

class ScheduledLocationsController extends Controller
{
    public function index(ScheduledLocationsRequest $request): ApiResponse
    {
        $validated = $request->validated();
        $employee = $request->user();

        $period = CarbonPeriodImmutable::dates($validated['from'], $validated['to']);

        $days = collect($period)->map(function (CarbonImmutable $day) use ($employee) {
            $locations = $employee->upcomingLocations;

            $dayPeriod = CarbonPeriodImmutable::dates($day, $day);

            $dayLocations = $locations
                ->filter(function (Location $location) use ($locations, $dayPeriod) {
                    return $location->pivot->permanent ||
                        (!$location->pivot->permanent &&
                            $dayPeriod->overlaps(
                                Carbon::parse($location->pivot->start_date)->subDay(),
                                Carbon::parse($location->pivot->end_date)->addDay()
                            ));
                })
                ->unique('id');

            if ($dayLocations->isEmpty()) {
                $dayLocations = $employee->team->locations;
            }

            return [
                'date' => $day->toDateString(),
                'locations' => LocationResource::collection($dayLocations),
            ];
        });

        return new ApiResponse($days);
    }
}
