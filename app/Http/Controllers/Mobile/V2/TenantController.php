<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\Mobile\TeamResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Mobile
 * @subgroup Employee - Tenant
 */
class TenantController extends Controller
{
    /**
     * Get tenant
     * @apiResource App\Http\Resources\Mobile\TeamResource
     * @apiResourceModel App\Models\Team
     */
    public function __invoke(Request $request): ApiResponse
    {
        return new ApiResponse(data: new TeamResource($request->user()->team));
    }
}
