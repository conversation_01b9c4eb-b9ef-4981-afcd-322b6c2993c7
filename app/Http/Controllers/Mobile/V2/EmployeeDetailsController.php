<?php

namespace App\Http\Controllers\Mobile\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\ManagerDelegationsResource;
use App\Http\Resources\Mobile\V2\EmployeeResource;
use App\Support\ApiResponse;

/**
 * @group Mobile
 * @subgroup Current Employee
 */
class EmployeeDetailsController extends Controller
{
    /**
     * Get the current employee details.
     * @apiResource App\Http\Resources\Mobile\V2\EmployeeResource
     * @apiResourceModel App\Models\Employee with=department,activeLocations,shift,employeeDelegations
     */
    public function __invoke(): ApiResponse
    {
        $employee = auth()->user();

        return new ApiResponse(
            data: EmployeeResource::make(
                $employee
                    ->load([
                        'department',
                        'activeLocations',
                        'shift',
                        'employeeDelegations',
                        'manager',
                    ])
                    ->loadExists(['managedEmployees' => fn($q) => $q->active()])
            )->additional([
                'manager_delegations' => $employee->manager
                    ? new ManagerDelegationsResource($employee->manager->loadDelegated())
                    : [],
            ])
        );
    }
}
