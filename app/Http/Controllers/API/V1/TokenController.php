<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\External\StoreAuthTokenRequest;
use App\Http\Resources\TokenResource;
use App\Support\ApiResponse;
use Lara<PERSON>\Sanctum\PersonalAccessToken;

/**
 * @group Frontend
 * @subgroup Token
 */
class TokenController extends Controller
{
    /**
     * List
     * @response {"data":[{"id":1,"name":"token","last_used_at":null,"tokenable_id":1}]}
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(
            data: TokenResource::collection(currentTenant()->personalAccessTokens()->paginate())
        );
    }

    /**
     * Create
     * @response {"message":"Your Access Token is: 1|2|3"}
     */
    public function store(StoreAuthTokenRequest $request): ApiResponse
    {
        return new ApiResponse(
            message: __('Your Access Token is: :token', [
                'token' => currentTenant()->createToken($request->name, ['*'], now()->addYear())
                    ->plainTextToken,
            ])
        );
    }

    /**
     * Destroy
     */
    public function destroy($token): ApiResponse
    {
        PersonalAccessToken::find($token)->delete();

        return new ApiResponse(message: __('token has been deleted successfully'));
    }
}
