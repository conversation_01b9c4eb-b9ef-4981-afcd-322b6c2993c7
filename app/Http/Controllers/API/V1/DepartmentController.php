<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\NewDepartmentRequest;
use App\Http\Requests\UpdateDepartmentRequest;
use App\Http\Resources\DepartmentResource;
use App\Models\Department;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Knuckles\Scribe\Attributes\Response;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Department
 */
class DepartmentController extends Controller
{
    /**
     * List
     * @apiResource App\Http\Resources\DepartmentResource
     * @apiResourceModel App\Models\Department with=manager,parent
     */
    public function index(Request $request)
    {
        return new ApiResponse(
            data: DepartmentResource::collection(
                QueryBuilder::for(currentTenant()->departments())
                    ->allowedFilters([
                        'name',
                        'id',
                        'parent_id',
                        AllowedFilter::callback(
                            'exclude',
                            fn($query, $value) => $query->where('id', '!=', $value)
                        ),
                    ])
                    ->allowedSorts(['updated_at', 'created_at'])
                    ->defaultSort('-created_at')
                    ->with(['manager', 'parent'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Create
     * @apiResource App\Http\Resources\DepartmentResource
     * @apiResourceModel App\Models\Department
     */
    public function store(NewDepartmentRequest $request)
    {
        $data = $request->validated();
        $data['tenant_id'] = currentTenant()->id;
        $department = Department::create($data);

        return new ApiResponse(
            data: new DepartmentResource($department),
            message: __('The Department has been created successfully.')
        );
    }

    /**
     * Show
     * @apiResource App\Http\Resources\DepartmentResource
     * @apiResourceModel App\Models\Department with=manager,parent
     */
    public function show($departmentId)
    {
        return new ApiResponse(
            data: new DepartmentResource(
                currentTenant()
                    ->departments()
                    ->with(['manager', 'parent'])
                    ->where('id', $departmentId)
                    ->firstOrFail()
            )
        );
    }

    /**
     * Update
     * @apiResource App\Http\Resources\DepartmentResource
     * @apiResourceModel App\Models\Department with=manager,parent
     */
    public function update(UpdateDepartmentRequest $request, string $departmentId)
    {
        $department = currentTenant()->departments()->where('id', $departmentId)->firstOrFail();

        $department->update([...$request->validated(), 'tenant_id' => currentTenant()->id]);

        return new ApiResponse(
            data: new DepartmentResource($department->load(['manager', 'parent'])),
            message: __('The department has been updated successfully.')
        );
    }

    /**
     * Destroy
     */
    #[
        Response(
            content: ['message' => 'The department has been deleted successfully.'],
            status: 200,
            description: 'Success'
        )
    ]
    #[
        Response(
            content: ['message' => 'Department that has employees cannot be deleted'],
            status: 422,
            description: 'Department that has employees cannot be deleted'
        )
    ]
    #[
        Response(
            content: ['message' => 'Department that has sub-departments cannot be deleted'],
            status: 422,
            description: 'Department that has sub-departments cannot be deleted'
        )
    ]
    public function destroy($departmentId)
    {
        $department = currentTenant()->departments()->findOrFail($departmentId);

        if (currentTenant()->employees()->where('department_id', $departmentId)->exists()) {
            return new ApiResponse(
                message: __('Department that has employees cannot be deleted'),
                code: 422
            );
        }

        if (currentTenant()->departments()->where('parent_id', $departmentId)->exists()) {
            return new ApiResponse(
                message: __('Department that has sub-departments cannot be deleted'),
                code: 422
            );
        }

        $department->delete();

        return new ApiResponse(message: __('The department has been deleted successfully.'));
    }
}
