<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\EmployeeResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Employees
 */
class GetCurrentEmployeeController extends Controller
{
    /**
     * Current Employee
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=tenant,department,roles
     */
    public function __invoke(Request $request)
    {
        return new ApiResponse(
            data: new EmployeeResource($request->user()->load(['tenant', 'department', 'roles']))
        );
    }
}
