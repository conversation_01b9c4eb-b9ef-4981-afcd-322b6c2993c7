<?php

namespace App\Http\Controllers;

use App\Http\Requests\WorkdayRequest;
use App\Http\Resources\Frontend\WorkdayResource;
use App\Models\Workday;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use <PERSON>tie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class WorkdayController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'filter.search' => ['nullable', 'string'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ]);

        return new ApiResponse(
            data: WorkdayResource::collection(
                QueryBuilder::for(Workday::class)
                    ->allowedFilters([
                        AllowedFilter::callback('search', fn($q, $s) => $q->search($s)),
                    ])
                    ->defaultSort('-created_at')
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    public function show(Workday $workday)
    {
        return new ApiResponse(data: new WorkdayResource($workday));
    }

    public function store(WorkdayRequest $request)
    {
        $workday = Workday::create($request->validated());

        return new ApiResponse(
            data: new WorkdayResource($workday),
            message: __('The workday has been created successfully')
        );
    }

    public function update(WorkdayRequest $request, Workday $workday)
    {
        $workday->update($request->validated());

        return new ApiResponse(
            data: new WorkdayResource($workday),
            message: __('The workday has been updated successfully')
        );
    }

    public function destroy(Workday $workday)
    {
        $workday->delete();

        return new ApiResponse(message: __('The workday has been deleted successfully'));
    }
}
