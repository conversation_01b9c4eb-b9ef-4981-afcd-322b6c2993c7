<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\TokenResource;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use <PERSON>vel\Sanctum\PersonalAccessToken;
use <PERSON><PERSON>\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Token
 */
class TokenController extends Controller
{
    /**
     * List tokens
     * @apiResource App\Http\Resources\TokenResource
     * @apiResourceModel Laravel\Sanctum\PersonalAccessToken
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: TokenResource::collection(
                QueryBuilder::for(auth()->user()->team->tokens())
                    ->allowedFilters(['name'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Create token
     * @apiResource App\Http\Resources\TokenResource
     * @apiResourceModel Laravel\Sanctum\PersonalAccessToken
     */
    public function store(Request $request): ApiResponse
    {
        return new ApiResponse(
            message: __('Your Access Token is: :token', [
                'token' => auth()
                    ->user()
                    ->team->createToken($request->name, ['*'], now()->addYear())->plainTextToken,
            ])
        );
    }

    /**
     * Delete token
     */
    public function destroy(PersonalAccessToken $token): ApiResponse
    {
        $token->delete();

        return new ApiResponse(message: __('The token has been deleted successfully'));
    }
}
