<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\WebhookStoreRequest;
use App\Http\Requests\WebhookUpdateRequest;
use App\Http\Resources\WebhookResource;
use App\Models\Webhook;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Webhook
 */
class WebhookController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Webhook::class, 'webhook');
    }

    /**
     * List webhooks
     * @queryParam per_page int The number of items per page. Example: 10
     * @queryParam name string Filter by webhook name. Example: test
     * @apiResource App\Http\Resources\WebhookResource
     * @apiResourceModel App\Models\Webhook
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: WebhookResource::collection(
                QueryBuilder::for(Webhook::class)
                    ->allowedFilters(['name'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Create webhook
     * @apiResource App\Http\Resources\WebhookResource
     * @apiResourceModel App\Models\Webhook
     */
    public function store(WebhookStoreRequest $request): ApiResponse
    {
        Webhook::create($request->validated());

        return new ApiResponse(message: __('The webhook has been added successfully'));
    }

    /**
     * Get a webhook by id
     * @apiResource App\Http\Resources\WebhookResource
     * @apiResourceModel App\Models\Webhook
     */
    public function show(Webhook $webhook): ApiResponse
    {
        return new ApiResponse(data: new WebhookResource($webhook));
    }

    /**
     * Update a webhook
     * @apiResource App\Http\Resources\WebhookResource
     * @apiResourceModel App\Models\Webhook
     */
    public function update(WebhookUpdateRequest $request, Webhook $webhook): ApiResponse
    {
        $webhook->update($request->validated());

        return new ApiResponse(message: __('The webhook has been updated successfully'));
    }

    /**
     * Delete a webhook
     */
    public function destroy(Webhook $webhook): ApiResponse
    {
        $webhook->delete();

        return new ApiResponse(message: __('The webhook has been deleted successfully'));
    }
}
