<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Support\ApiResponse;
use Knuckles\Scribe\Attributes\Response;

/**
 * @group Frontend
 * @subgroup Config
 */
class PolicyController extends Controller
{
    #[
        Response(
            content: [
                'data' => [
                    'remote_work_policy' => [
                        [
                            'title' => 'Inherited',
                            'description' => 'This policy will be inherited from the top level.',
                            'value' => 'inherited',
                        ],
                        [
                            'title' => 'Allowed',
                            'description' =>
                                'This allows check-in and check-out outside the organization’s locations.',
                            'value' => 'allowed',
                        ],
                        [
                            'title' => 'Allowed with approval',
                            'description' =>
                                'This allows check-in and check-out outside the organization’s locations; a request will be sent to the direct manager later on for approval.',
                            'value' => 'allowed_with_approval',
                        ],
                        [
                            'title' => 'Not allowed',
                            'description' =>
                                'This does not allow check-in or check-out from outside the organization’s locations.',
                            'value' => 'not_allowed',
                        ],
                    ],
                    'random_notification_policy' => [
                        [
                            'title' => 'Inherited',
                            'description' => 'This policy will be inherited from the top level.',
                            'value' => 'inherited',
                        ],
                        [
                            'title' => 'Enabled',
                            'description' =>
                                'This will verify employee attendance with random alerts during work hours, ensuring accurate records and reducing the possibility of time theft.',
                            'value' => 'enabled',
                        ],
                        [
                            'title' => 'Disabled',
                            'description' =>
                                'This will no longer send random alerts to verify employee attendance during work hours. This may affect the accuracy of attendance records and increase the risk of time theft.',
                            'value' => 'disabled',
                        ],
                    ],
                ],
            ],
            description: 'List policies'
        )
    ]
    public function __invoke()
    {
        return new ApiResponse(
            data: [
                'remote_work_policies' => collect(config('lookups.remote_work_policy'))->map(
                    fn($item) => [
                        'title' => __($item['title']),
                        'description' => __($item['description']),
                        'value' => $item['value'],
                    ]
                ),
                'random_notification_policies' => collect(
                    config('lookups.random_notification_policy')
                )->map(
                    fn($item) => [
                        'title' => __($item['title']),
                        'description' => __($item['description']),
                        'value' => $item['value'],
                    ]
                ),
            ]
        );
    }
}
