<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\LocationSelection;
use App\Http\Controllers\Controller;
use App\Http\Requests\DeviceRequest;
use App\Http\Resources\DeviceResource;
use App\Models\Device;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Devices
 */
class DeviceController extends Controller
{
    /**
     * List devices
     * @apiResource App\Http\Resources\DeviceResource
     * @apiResourceModel App\Models\Device with=location
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate(['filter.search' => ['nullable', 'string']]);

        return new ApiResponse(
            DeviceResource::collection(
                QueryBuilder::for(Device::class)
                    ->allowedFilters([AllowedFilter::scope('search')])
                    ->with('location')
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Add device
     * @apiResource App\Http\Resources\DeviceResource
     * @apiResourceModel App\Models\Device with=location
     */
    public function store(DeviceRequest $request): ApiResponse
    {
        $validated = $request->validated();

        if ($validated['location_selection'] === LocationSelection::DeviceLocation->value) {
            $validated['location_id'] = null;
        }

        $device = Device::create($validated);

        return new ApiResponse(
            data: new DeviceResource($device->load('location')),
            message: __('Device added successfully')
        );
    }

    /**
     * Show device
     * @apiResource App\Http\Resources\DeviceResource
     * @apiResourceModel App\Models\Device
     */
    public function show(Device $device): ApiResponse
    {
        return new ApiResponse(new DeviceResource($device));
    }

    /**
     * Update device
     * @apiResource App\Http\Resources\DeviceResource
     * @apiResourceModel App\Models\Device with=location
     */
    public function update(DeviceRequest $request, Device $device): ApiResponse
    {
        $validated = $request->validated();

        if ($validated['location_selection'] === LocationSelection::DeviceLocation->value) {
            $validated['location_id'] = null;
        }

        $device->update($validated);

        return new ApiResponse(
            data: new DeviceResource($device->load('location')),
            message: __('Device updated successfully')
        );
    }

    /**
     * Delete device
     */
    public function destroy(Device $device): ApiResponse
    {
        $device->delete();

        return new ApiResponse(message: __('Device deleted successfully'));
    }
}
