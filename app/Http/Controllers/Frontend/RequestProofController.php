<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\ProofMethod;
use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Notifications\ProofAttendanceRequested;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Employees
 */
class RequestProofController extends Controller
{
    /**
     * Request proof of attendance
     * @response {"message":"If the employee present the request has been sent to the employee device."}
     */
    public function __invoke(Employee $employee)
    {
        $employee->notify(new ProofAttendanceRequested(ProofMethod::Manual));

        return new ApiResponse(
            message: __('If the employee present the request has been sent to the employee device.')
        );
    }
}
