<?php

namespace App\Http\Controllers\Frontend;

use App\Events\DepartmentUpdated;
use App\Http\Controllers\Controller;
use App\Http\Requests\DepartmentUpdateRequest;
use App\Http\Resources\Frontend\DepartmentResource;
use App\Models\Department;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Departments
 */
class DepartmentController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Department::class, 'department');
    }

    /**
     * List departments
     * @apiResource App\Http\Resources\Frontend\DepartmentResource
     * @apiResourceModel App\Models\Department with=parent,manager
     * @queryParam filter.name string Filter by department name.
     * @queryParam filter.exclude integer Exclude department by id.
     * @queryParam per_page integer Number of items per page.
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: DepartmentResource::collection(
                QueryBuilder::for(Department::class)
                    ->allowedFilters([
                        'name',
                        AllowedFilter::callback(
                            'exclude',
                            fn($query, $value) => $query->where('id', '!=', $value)
                        ),
                    ])
                    ->with(['parent', 'manager'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Update department
     * @apiResource App\Http\Resources\Frontend\DepartmentResource
     * @apiResourceModel App\Models\Department with=parent,manager
     */
    public function update(DepartmentUpdateRequest $request, Department $department): ApiResponse
    {
        $department->update($request->validated());

        event(new DepartmentUpdated($department));

        return new ApiResponse(
            data: new DepartmentResource($department),
            message: __('The department has been updated.')
        );
    }

    /**
     * Show department
     * @apiResource App\Http\Resources\Frontend\DepartmentResource
     * @apiResourceModel App\Models\Department with=parent,manager
     */
    public function show(Department $department): ApiResponse
    {
        return new ApiResponse(
            data: new DepartmentResource($department->load(['manager', 'parent']))
        );
    }
}
