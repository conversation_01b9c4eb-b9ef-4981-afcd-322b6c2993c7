<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\AuditAgent;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuditResource;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Tag;
use App\Models\Team;
use App\Support\ApiResponse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use OwenIt\Auditing\Models\Audit;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class AuditController extends Controller
{
    public function __invoke(Request $request)
    {
        $request->validate([
            'agent' => ['nullable', Rule::in(AuditAgent::cases())],
            'per_page' => ['nullable', 'integer'],
        ]);

        return new ApiResponse(
            data: AuditResource::collection(
                QueryBuilder::for(
                    Audit::query()
                        ->where('team_id', currentTeam()->id)
                        ->whereIn('auditable_type', [
                            Employee::class,
                            Department::class,
                            Team::class,
                            Tag::class,
                            Shift::class,
                            Holiday::class,
                            Location::class,
                        ])
                )
                    ->allowedFilters([
                        AllowedFilter::callback(
                            'search',
                            fn(Builder $q, $value) => $q->where(
                                fn(Builder $query) => $query
                                    ->whereHasMorph(
                                        'user',
                                        Employee::class,
                                        fn(Builder $q) => $q->search($value)
                                    )
                                    ->orWhere('new_values', 'like', "%$value%")
                                    ->orWhere('old_values', 'like', "%$value%")
                            )
                        ),
                        AllowedFilter::exact('agent'),
                    ])
                    ->with(['user', 'auditable'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
