<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\TagResource;
use App\Models\Tag;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Tags
 */
class TagController extends Controller
{
    /**
     * List tags
     * @apiResource App\Http\Resources\TagResource
     * @apiResourceModel App\Models\Tag
     */
    public function __invoke(Request $request): ApiResponse
    {
        $request->validate([
            'filter.search' => ['nullable', 'string'],
        ]);

        return new ApiResponse(
            data: TagResource::collection(
                QueryBuilder::for(Tag::class)
                    ->allowedFilters([AllowedFilter::partial('search', 'name')])
                    ->paginateOrGet()
            )
        );
    }
}
