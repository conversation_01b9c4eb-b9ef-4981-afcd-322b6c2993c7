<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\DelegationType;
use App\Http\Controllers\Controller;
use App\Http\Requests\EmployeeUpdateRequest;
use App\Http\Resources\EmployeeResource;
use App\Http\Resources\Frontend\ManagerDelegationsResource;
use App\Models\Employee;
use App\QueryBuilders\EmployeeQueryBuilder;
use App\Services\UpdateDelegationsService;
use App\Services\UpdateEmployeeService;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

/**
 * @group Frontend
 * @subgroup Employees
 */
class EmployeeController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Employee::class, 'employee');
    }

    /**
     * List employees
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     * @queryParam filter.search string Search by name, email, or phone number.
     * @queryParam filter.ids array Filter by employee ids.
     * @queryParam filter.direct_managers array Filter by direct manager ids.
     * @queryParam filter.is_direct_managers boolean Filter by is employee a direct manager.
     * @queryParam filter.departments array Filter by department ids.
     * @queryParam filter.locations array Filter by location ids.
     * @queryParam filter.shifts array Filter by shift ids.
     * @queryParam filter.tags array Filter by tag ids.
     * @queryParam filter.exclude integer Exclude employee by id.
     * @queryParam filter.is_active boolean Filter by active status.
     * @queryParam include string Include related resources. E.g. include=department,shift,locations,tags
     * @queryParam per_page integer Number of items per page.
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: EmployeeResource::collection(
                EmployeeQueryBuilder::query()
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Show employee
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=department,shift,shifts,locations,tags,directManager
     */
    public function show(Employee $employee): ApiResponse
    {
        return new ApiResponse(
            data: EmployeeResource::make(
                $employee->load([
                    'department',
                    'shift',
                    'shifts',
                    'locations',
                    'tags',
                    'directManager',
                ])
            )->additional([
                'delegations' => new ManagerDelegationsResource($employee->loadDelegated()),
            ])
        );
    }

    /**
     * Update employee
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function update(EmployeeUpdateRequest $request, Employee $employee): ApiResponse
    {
        UpdateEmployeeService::handle(
            $employee,
            Arr::except($request->validated(), DelegationType::requestInputs())
        );

        (new UpdateDelegationsService())->handle($employee, $request->delegationData());

        return new ApiResponse(
            data: new EmployeeResource($employee->load(['department', 'shifts', 'locations'])),
            message: __('The employee has been updated.')
        );
    }
}
