<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\ProofStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\ProofResource;
use App\Models\Proof;
use App\Support\AllowedFilterEmployeesDepartments;
use App\Support\AllowedFilterFromToCreatedAt;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Proofs
 */
class ProofController extends Controller
{
    /**
     * List proofs
     * @apiResource App\Http\Resources\Frontend\ProofResource
     * @apiResourceModel App\Models\Proof with=employee,department
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d', 'after_or_equal:filter.from'],
            'filter.status' => ['nullable', Rule::enum(ProofStatus::class)],
            'filter.employees' => ['nullable', 'array'],
            'filter.departments' => ['nullable', 'array'],
            'per_page' => ['nullable'],
        ]);

        return new ApiResponse(
            data: ProofResource::collection(
                QueryBuilder::for(Proof::class)
                    ->allowedFilters([
                        ...AllowedFilterFromToCreatedAt::filters(),
                        ...AllowedFilterEmployeesDepartments::filters(),
                        AllowedFilter::exact('status'),
                    ])
                    ->allowedIncludes(['employee', 'department'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
