<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\AttendanceStatus;
use App\Enums\ProofStatus;
use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Proof;
use App\Support\ApiResponse;
use Arr;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Dashboard
 */
class DashboardStatsController extends Controller
{
    /**
     * Get dashboard stats
     * @response {
     *     "data": {
     *         "employees_count": 1,
     *         "attendance_status": {
     *             "checked_in": 1,
     *             "checked_in_compared_to_yesterday_percentage": 0,
     *             "remote_workers": 0,
     *             "remote_workers_compared_to_yesterday_percentage": 0,
     *             "absent": 0,
     *             "absent_compared_to_yesterday_percentage": 0
     *         },
     *         "attendance_rate": {
     *             "1": {
     *                 "checked_in_percentage": 100,
     *                 "remote_workers_percentage": 0,
     *                 "absent_percentage": 0
     *             },
     *             "2": {
     *                 "checked_in_percentage": 100,
     *                 "remote_workers_percentage": 0,
     *                 "absent_percentage": 0
     *             },
     *             "3": {
     *                 "checked_in_percentage": 100,
     *                 "remote_workers_percentage": 0,
     *                 "absent_percentage": 0
     *             },
     *             "4": {
     *                 "checked_in_percentage": 100,
     *                 "remote_workers_percentage": 0,
     *                 "absent_percentage": 0
     *             },
     *             "5": {
     *                 "checked_in_percentage": 100,
     *                 "remote_workers_percentage": 0,
     *                 "absent_percentage": 0
     *             }
     *         },
     *         "commitment_rate_percentage": 0,
     *         "commitment_rate_compared_to_last_week_percentage": 0,
     *         "employees_feeling_percentage": 0
     *     }
     * }
     */
    public function __invoke()
    {
        $commitmentRate = $this->getCommitmentRate(now());
        $commitmentRatePrevWeek = $this->getCommitmentRate(now()->subWeek());

        return new ApiResponse(
            data: [
                'employees_count' => $this->getEmployeesCount(),
                'attendance_status' => $this->getAttendanceStatus(),
                'attendance_rate' => $this->getAttendanceRate()->toArray(),
                'commitment_rate_percentage' => $commitmentRate ? round($commitmentRate) : null,
                'commitment_rate_compared_to_last_week_percentage' =>
                    $commitmentRate && $commitmentRatePrevWeek
                        ? round($commitmentRatePrevWeek - $commitmentRate)
                        : null,
                'employees_feeling_percentage' => $this->getEmployeeFeeling(),
            ]
        );
    }

    protected function getCommitmentRate(Carbon $date): ?float
    {
        $startOfWeek = $date->copy()->startOfWeek()->subDay()->startOfDay();
        $endOfWeek = $date->copy()->endOfWeek()->subDay()->endOfDay();

        $acceptedProofs = QueryBuilder::for(Proof::class)
            ->allowedFilters([
                AllowedFilter::callback('departments', function (
                    Builder $query,
                    array|string $value
                ) {
                    $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    );
                }),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::callback('shifts', function (Builder $query, array|string $value) {
                    $query->whereHas(
                        'employee.shift',
                        fn(Builder $query) => $query->whereIn('id', Arr::wrap($value))
                    );
                }),
                AllowedFilter::exact('locations', 'location_id'),
            ])
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
            ->where('status', ProofStatus::Missed)
            ->count();

        $totalProofs = QueryBuilder::for(Proof::class)
            ->allowedFilters([
                AllowedFilter::callback('departments', function (
                    Builder $query,
                    array|string $value
                ) {
                    $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    );
                }),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::callback('shifts', function (Builder $query, array|string $value) {
                    $query->whereHas(
                        'employee.shift',
                        fn(Builder $query) => $query->whereIn('id', Arr::wrap($value))
                    );
                }),
                AllowedFilter::exact('locations', 'location_id'),
            ])
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
            ->count();

        return $totalProofs > 0 ? $this->ratio($totalProofs, $acceptedProofs) : null;
    }

    /**
     * assumes that total is bigger than zero
     */
    protected function ratio(int|float $total, int|float $fraction): float
    {
        return ($fraction / $total) * 100;
    }

    protected function getEmployeeFeeling(): ?float
    {
        $count = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->whereDate('date', now())
            ->count();

        if ($count < 2) {
            return null;
        }

        $moodAverage = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->select([
                DB::raw('avg(in_mood) as average_in_mood'),
                DB::raw('avg(out_mood) as average_out_mood'),
            ])
            ->whereDate('date', now())
            ->get()
            ->select('average_in_mood', 'average_out_mood')
            ->flatten()
            ->avg();

        return $moodAverage ? round($this->ratio(5, $moodAverage)) : null;
    }

    protected function getAttendanceRate(): Collection
    {
        return QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->select(['date', 'status', 'in_type'])
            ->whereBetween('date', [
                today()->startOfWeek()->subDay(),
                today()->endOfWeek()->subDay(),
            ])
            ->get()
            ->groupBy('date')
            ->mapWithKeys(function ($attendance, string $key) {
                $total = $attendance->count();

                $checkInCount = $attendance
                    ->where('status', AttendanceStatus::PRESENT->value)
                    ->where('in_type', Activity::CHECK_IN)
                    ->count();
                $remoteWorkersCount = $attendance
                    ->where('status', AttendanceStatus::PRESENT->value)
                    ->whereIn('in_type', [
                        Activity::REMOTE_CHECK_IN_APPROVED,
                        Activity::REMOTE_CHECK_IN,
                    ])
                    ->count();
                $absentCount = $attendance
                    ->where('status', AttendanceStatus::ABSENT->value)
                    ->count();

                return [
                    Carbon::parse($key)->dayOfWeek => [
                        'checked_in_percentage' =>
                            $total > 0 ? round($this->ratio($total, $checkInCount), 2) : null,
                        'remote_workers_percentage' =>
                            $total > 0 ? round($this->ratio($total, $remoteWorkersCount), 2) : null,
                        'absent_percentage' =>
                            $total > 0 ? round($this->ratio($total, $absentCount), 2) : null,
                    ],
                ];
            });
    }

    protected function getAttendanceStatus(): array
    {
        $checkedInCount = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->date(now())
            ->where('status', AttendanceStatus::PRESENT->value)
            ->where('in_type', Activity::CHECK_IN)
            ->count();

        $remoteWorkCount = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->date(now())
            ->where('status', AttendanceStatus::PRESENT->value)
            ->whereIn('in_type', [Activity::REMOTE_CHECK_IN_APPROVED, Activity::REMOTE_CHECK_IN])
            ->count();

        $absentCount = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->date(now())
            ->where('status', AttendanceStatus::ABSENT->value)
            ->count();

        $totalToday = $checkedInCount + $remoteWorkCount + $absentCount;

        $checkedInPreviousDayCount = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->date(now()->subDay())
            ->where('status', AttendanceStatus::PRESENT->value)
            ->where('in_type', Activity::CHECK_IN)
            ->count();

        $remoteWorkersPreviousDayCount = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->date(now()->subDay())
            ->where('status', AttendanceStatus::PRESENT->value)
            ->whereIn('in_type', [Activity::REMOTE_CHECK_IN_APPROVED, Activity::REMOTE_CHECK_IN])
            ->count();

        $absentPreviousDayCount = QueryBuilder::for(Attendance::class)
            ->allowedFilters([
                AllowedFilter::callback(
                    'departments',
                    fn(Builder $query, array|string $value) => $query->whereHas(
                        'employee',
                        fn(Builder $query) => $query->whereIn('department_id', Arr::wrap($value))
                    )
                ),
                AllowedFilter::exact('employees', 'employee_id'),
                AllowedFilter::exact('shifts', 'shift_id'),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    return $query->where(
                        fn(Builder $query) => $query
                            ->whereIn('check_in_location_id', Arr::wrap($value))
                            ->orWhereIn('check_out_location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->date(now()->subDay())
            ->where('status', AttendanceStatus::ABSENT->value)
            ->count();

        $totalPreviousDay =
            $checkedInPreviousDayCount + $remoteWorkersPreviousDayCount + $absentPreviousDayCount;

        return [
            'checked_in' => $checkedInCount,
            'checked_in_compared_to_yesterday_percentage' =>
                $totalPreviousDay > 0 && $totalToday > 0
                    ? round(
                        $this->ratio($totalPreviousDay, $checkedInPreviousDayCount) -
                            $this->ratio($totalToday, $checkedInCount)
                    )
                    : 0,
            'remote_workers' => $remoteWorkCount,
            'remote_workers_compared_to_yesterday_percentage' =>
                $totalPreviousDay > 0 && $totalToday > 0
                    ? round(
                        $this->ratio($totalPreviousDay, $remoteWorkersPreviousDayCount) -
                            $this->ratio($totalToday, $remoteWorkCount)
                    )
                    : 0,
            'absent' => $absentCount,
            'absent_compared_to_yesterday_percentage' =>
                $totalPreviousDay > 0 && $totalToday > 0
                    ? round(
                        $this->ratio($totalPreviousDay, $absentPreviousDayCount) -
                            $this->ratio($totalToday, $absentCount)
                    )
                    : 0,
        ];
    }

    protected function getEmployeesCount(): int
    {
        return QueryBuilder::for(Employee::class)
            ->allowedFilters([
                AllowedFilter::exact('departments', 'department_id'),
                AllowedFilter::exact('employees', 'id'),
                AllowedFilter::callback('shifts', function (Builder $query, array|string $value) {
                    $query->whereHas(
                        'shift',
                        fn(Builder $query) => $query->whereIn('shift_id', Arr::wrap($value))
                    );
                }),
                AllowedFilter::callback('locations', function (
                    Builder $query,
                    array|string $value
                ) {
                    $query->whereHas(
                        'locations',
                        fn(Builder $query) => $query->whereIn('location_id', Arr::wrap($value))
                    );
                }),
            ])
            ->count();
    }
}
