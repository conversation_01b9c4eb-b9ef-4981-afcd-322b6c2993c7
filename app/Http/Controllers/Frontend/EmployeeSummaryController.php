<?php

namespace App\Http\Controllers\Frontend;

use App\Calculations\EmployeeBalanceCalculator;
use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Support\ApiResponse;
use App\Support\EmployeeAttendanceSummary;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Employees
 */
class EmployeeSummaryController extends Controller
{
    /**
     * Employee summary
     * @response {
     *     "total_committed_hours": 40,
     *     "total_working_days": 5,
     *     "total_absent_days": 1,
     *     "total_present_days": 4,
     *     "total_requests": 2,
     *     "remaining_remote_days": 2,
     *     "early_in": "00:10:00",
     *     "late_in": "00:20:00"
     * }
     */
    public function index(Request $request, Employee $employee): ApiResponse
    {
        $request->validate([
            'filter.from' => ['required', 'date:Y-m-d'],
            'filter.to' => ['required', 'date:Y-m-d'],
        ]);

        $employeeSummary = EmployeeAttendanceSummary::get(
            $employee,
            $request->date('filter.from'),
            $request->date('filter.to')
        );

        $committedHours = $employeeSummary->committedHours();

        return new ApiResponse(
            data: [
                'total_committed_hours' => $committedHours['total_committed_hours'],
                'total_working_days' => $committedHours['total_working_days'],
                'total_absent_days' => $employeeSummary->totalAbsentDays(),
                'total_present_days' => $employeeSummary->totalPresentDays(),
                'total_requests' => $employeeSummary->approvalRequests()['total_requests'],
                'remaining_remote_days' => EmployeeBalanceCalculator::remoteWorkBalance(
                    $employee,
                    CarbonPeriod::create($request->date('filter.from'), $request->date('filter.to'))
                ),
                'early_in' => $employeeSummary->calculateEarlyIn(),
                'late_in' => $employeeSummary->calculateLateIn(),
            ]
        );
    }
}
