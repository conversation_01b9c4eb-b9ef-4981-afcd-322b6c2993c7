<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\AttendanceStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\AttendanceResource;
use App\Models\Attendance;
use App\Support\AllowedFilterEmployeesDepartments;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedInclude;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Attendances
 */
class AttendanceController extends Controller
{
    /**
     * List attendance
     * @apiResource App\Http\Resources\Frontend\AttendanceResource
     * @apiResourceModel App\Models\Attendance with=checkInLocation,checkoutLocation
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d', 'after_or_equal:filter.from'],
            'filter.status' => ['nullable', Rule::enum(AttendanceStatus::class)],
            'filter.employees' => ['nullable', 'array'],
            'filter.employee_id' => ['nullable', Rule::existsTenant('employees', 'id')],
            'per_page' => ['nullable'],
        ]);

        return new ApiResponse(
            data: AttendanceResource::collection(
                QueryBuilder::for(Attendance::class)
                    ->allowedFilters([
                        ...AllowedFilterEmployeesDepartments::filters(),
                        AllowedFilter::exact('employee_id'), // deprecated, use `employees` filter instead
                        AllowedFilter::callback('from', fn($q, $v) => $q->where('date', '>=', $v)),
                        AllowedFilter::callback('to', fn($q, $v) => $q->where('date', '<=', $v)),
                        AllowedFilter::callback(
                            'done',
                            fn($q) => $q->where('status', '!=', Attendance::YET)
                        ),
                    ])
                    ->allowedIncludes([
                        AllowedInclude::relationship('check_in_location', 'checkInLocation'),
                        AllowedInclude::relationship('check_out_location', 'checkoutLocation'),
                    ])
                    ->latest('date')
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
