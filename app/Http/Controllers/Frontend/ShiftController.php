<?php

namespace App\Http\Controllers\Frontend;

use App\Events\ShiftAdded;
use App\Events\ShiftDeleted;
use App\Events\ShiftUpdated;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\ShiftStoreRequest;
use App\Http\Requests\Frontend\ShiftUpdateRequest;
use App\Http\Resources\ShiftResource;
use App\Models\Shift;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Shifts
 */
class ShiftController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Shift::class, 'shift');
    }

    /**
     * List shifts
     * @apiResource App\Http\Resources\ShiftResource
     * @apiResourceModel App\Models\Shift
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.name' => ['nullable', 'string'],
            'filter.employees' => ['nullable', 'array'],
            'per_page' => ['nullable', 'integer'],
        ]);

        return new ApiResponse(
            data: ShiftResource::collection(
                QueryBuilder::for(Shift::class)
                    ->allowedFilters([
                        AllowedFilter::partial('name'),
                        AllowedFilter::callback(
                            'employees',
                            fn($q, $employees) => $q->filterByEmployees($employees)
                        ),
                    ])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Get shift
     * @apiResource App\Http\Resources\ShiftResource
     * @apiResourceModel App\Models\Shift
     */
    public function show(Shift $shift): ApiResponse
    {
        return new ApiResponse(data: new ShiftResource($shift));
    }

    /**
     * Create shift
     * @apiResource App\Http\Resources\ShiftResource
     * @apiResourceModel App\Models\Shift
     */
    public function store(ShiftStoreRequest $request): ApiResponse
    {
        $fields = $request->validated();

        $isDefault = $fields['is_default'] ?? false;

        if ($isDefault) {
            Shift::where('is_default', true)->update(['is_default' => false]);
        }

        $shift = Shift::create([
            'name' => $fields['name'],
            'is_default' => $isDefault,
            'force_checkout' => $fields['force_checkout'],
            'working_hours' => [
                'flexible_hours' => $fields['flexible_hours'],
                'weekdays' => collect($fields['weekdays'])->mapWithKeys(
                    fn($day) => [
                        $day['value'] => $day['enabled']
                            ? [
                                'from' => $day['from'],
                                'to' => $day['to'],
                                'next_day_checkout' => $day['next_day_checkout'] ?? false,
                                'prevent_checkout_after' => $day['prevent_checkout_after'] ?? null,
                            ]
                            : false,
                    ]
                ),
            ],
        ]);

        event(new ShiftAdded($shift));

        return new ApiResponse(
            data: new ShiftResource($shift),
            message: __('The shift has been added successfully')
        );
    }

    /**
     * Update shift
     * @apiResource App\Http\Resources\ShiftResource
     * @apiResourceModel App\Models\Shift
     */
    public function update(ShiftUpdateRequest $request, Shift $shift): ApiResponse
    {
        $fields = $request->validated();

        $isDefault = $fields['is_default'] ?? false;

        if ($isDefault) {
            Shift::where('is_default', true)->update(['is_default' => false]);
        }

        $shift->update([
            'name' => $fields['name'],
            'is_default' => $isDefault,
            'force_checkout' => $fields['force_checkout'],
            'working_hours' => [
                'flexible_hours' => $fields['flexible_hours'],
                'weekdays' => collect($fields['weekdays'])->mapWithKeys(
                    fn($day) => [
                        $day['value'] => $day['enabled']
                            ? [
                                'from' => $day['from'],
                                'to' => $day['to'],
                                'next_day_checkout' => $day['next_day_checkout'] ?? false,
                                'prevent_checkout_after' => $day['prevent_checkout_after'] ?? null,
                            ]
                            : false,
                    ]
                ),
            ],
        ]);

        event(new ShiftUpdated($shift));

        return new ApiResponse(
            data: new ShiftResource($shift),
            message: __('shift.updated', ['name' => $shift->name])
        );
    }

    /**
     * Delete shift
     * @response 422 {"message":"The default shift cannot be deleted"}
     * @response 200 {"message":"The shift has been deleted successfully"}
     */
    public function destroy(Shift $shift): ApiResponse
    {
        if ($shift->is_default) {
            return new ApiResponse(message: __('The default shift cannot be deleted'), code: 422);
        }

        $shift->delete();

        event(new ShiftDeleted($shift));

        return new ApiResponse(message: __('The shift has been deleted successfully'));
    }
}
