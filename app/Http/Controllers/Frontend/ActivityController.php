<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\ActivityResource;
use App\Models\Activity;
use App\Support\AllowedFilterFromToCreatedAt;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Activities
 */
class ActivityController extends Controller
{
    /**
     * List activities
     * @apiResource App\Http\Resources\Frontend\ActivityResource
     * @apiResourceModel App\Models\Activity with=employee
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d', 'after_or_equal:filter.from'],
            'filter.employees' => ['nullable', 'array'],
            'per_page' => ['nullable'],
        ]);

        return new ApiResponse(
            data: ActivityResource::collection(
                QueryBuilder::for(Activity::class)
                    ->allowedFilters([
                        ...AllowedFilterFromToCreatedAt::filters(),
                        AllowedFilter::callback(
                            'employees',
                            fn($q, $employees) => $q->whereIn('activities.employee_id', $employees)
                        ),
                    ])
                    ->allowedSorts(['created_at'])
                    ->allowedIncludes(['employee'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
