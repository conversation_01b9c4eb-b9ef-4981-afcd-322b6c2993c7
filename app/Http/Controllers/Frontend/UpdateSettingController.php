<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\SettingRequest;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Tenant
 */
class UpdateSettingController extends Controller
{
    /**
     * Update settings
     */
    public function __invoke(SettingRequest $request)
    {
        currentTeam()->update($request->validated());

        return new ApiResponse(message: __('Settings have been updated successfully'));
    }
}
