<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\ShiftResource;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Shifts
 */
class DefaultShiftController extends Controller
{
    /**
     * Get default shift
     * @apiResource App\Http\Resources\ShiftResource
     * @apiResourceModel App\Models\Shift
     */
    public function __invoke()
    {
        return new ApiResponse(data: new ShiftResource(currentTeam()->firstOrCreateDefaultShift()));
    }
}
