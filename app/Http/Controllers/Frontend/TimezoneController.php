<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Config
 */
class TimezoneController extends Controller
{
    /**
     * List timezones
     * @response {"label":"(GMT+03:00) Riyadh","value": "Asia/Riyadh"}
     */
    public function __invoke(): ApiResponse
    {
        return new ApiResponse(
            data: collect(config('lookups.timezones'))
                ->map(fn($value, $key) => ['label' => $key, 'value' => $value])
                ->values()
        );
    }
}
