<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Employees
 */
class RevokeEmployeeDeviceIDController extends Controller
{
    /**
     * Revoke employee device ID
     * @urlParam employee required Employee ID
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=department,shift,locations
     */
    public function __invoke(Request $request, Employee $employee)
    {
        $employee->update([
            'device_id' => null,
            'device_name' => null,
            'device_os' => null,
        ]);

        return new ApiResponse(
            data: new EmployeeResource($employee->load(['department', 'shift', 'locations'])),
            message: __('The device has been revoked.')
        );
    }
}
