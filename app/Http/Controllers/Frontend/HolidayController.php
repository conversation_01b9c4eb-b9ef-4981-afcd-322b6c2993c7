<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreHolidayRequest;
use App\Http\Requests\UpdateHolidayRequest;
use App\Http\Resources\Frontend\HolidayResource;
use App\Models\Holiday;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Holidays
 */
class HolidayController extends Controller
{
    /**
     * List holidays
     * @apiResource App\Http\Resources\Frontend\HolidayResource
     * @apiResourceModel App\Models\Holiday
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: HolidayResource::collection(
                QueryBuilder::for(Holiday::class)
                    ->allowedFilters(['name'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Show holiday
     * @apiResource App\Http\Resources\Frontend\HolidayResource
     * @apiResourceModel App\Models\Holiday
     */
    public function show(Holiday $holiday): ApiResponse
    {
        return new ApiResponse(data: new HolidayResource($holiday));
    }

    /**
     * Add holiday
     * @apiResource App\Http\Resources\Frontend\HolidayResource
     * @apiResourceModel App\Models\Holiday
     */
    public function store(StoreHolidayRequest $request): ApiResponse
    {
        $holiday = Holiday::create($request->validated());

        return new ApiResponse(
            data: new HolidayResource($holiday),
            message: __('The Holiday has been created successfully.')
        );
    }

    /**
     * Update holiday
     * @apiResource App\Http\Resources\Frontend\HolidayResource
     * @apiResourceModel App\Models\Holiday
     * @response 422 {"message":"you can't update holidays that has already started or already finished"} when the holiday has already started or already finished
     */
    public function update(UpdateHolidayRequest $request, Holiday $holiday): ApiResponse
    {
        if (!Carbon::parse($holiday->start_date)->isFuture()) {
            return new ApiResponse(
                message: __(
                    'you can\'t update holidays that has already started or already finished'
                ),
                code: 422
            );
        }

        $holiday->update($request->validated());

        return new ApiResponse(
            data: new HolidayResource($holiday),
            message: __('The Holiday has been updated successfully.')
        );
    }

    /**
     * Delete holiday
     * @response 422 {"message":"you can't delete holidays that has already started or already finished"} when the holiday has already started or already finished
     */
    public function destroy(Holiday $holiday): ApiResponse
    {
        if (!Carbon::parse($holiday->start_date)->isFuture()) {
            return new ApiResponse(
                message: __(
                    'you can\'t delete holidays that has already started or already finished'
                ),
                code: 422
            );
        }

        $holiday->delete();

        return new ApiResponse(message: __('The Holiday has been deleted successfully.'));
    }
}
