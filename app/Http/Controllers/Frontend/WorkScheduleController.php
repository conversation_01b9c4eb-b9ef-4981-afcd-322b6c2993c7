<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\WorkScheduleStoreRequest;
use App\Http\Requests\Frontend\WorkScheduleUpdateRequest;
use App\Http\Resources\Frontend\WorkScheduleResource;
use App\Models\WorkSchedule;
use App\Services\CreateWorkScheduleService;
use App\Services\UpdateWorkScheduleService;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Work Schedules
 */
class WorkScheduleController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->authorizeResource(WorkSchedule::class, "workSchedule");
    }

    /**
     * List work schedules
     *
     * Returns a paginated list of work schedules with filtering options.
     *
     * @apiResource App\Http\Resources\Frontend\WorkScheduleResource
     * @apiResourceModel App\Models\WorkSchedule
     * @queryParam filter.search string Search by name.
     * @queryParam filter.type string Filter by type (fixed, rotational).
     * @queryParam filter.include string Include related resources. E.g. include=workdays
     * @queryParam per_page integer Number of items per page. Default is 15.
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.search' => ['nullable', 'string'],
            'filter.type' => ['nullable', 'string', 'in:fixed,rotational'],
            'filter.include' => ['nullable', 'string'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ]);

        return new ApiResponse(
            data: WorkScheduleResource::collection(
                QueryBuilder::for(WorkSchedule::class)
                    ->allowedFilters([
                        AllowedFilter::callback('search', fn($q, $s) => $q->search($s)),
                        AllowedFilter::exact('type'),
                    ])
                    ->allowedIncludes(['workdays'])
                    ->defaultSort('-created_at')
                    ->paginate($request->per_page ?? 15)
                    ->withQueryString()
            )
        );
    }

    /**
     * Create work schedule
     *
     * Store a new work schedule in the database.
     *
     * @apiResource App\Http\Resources\Frontend\WorkScheduleResource
     * @apiResourceModel App\Models\WorkSchedule
     */
    public function store(WorkScheduleStoreRequest $request): ApiResponse
    {
        $workSchedule = CreateWorkScheduleService::handle($request->validated());

        return new ApiResponse(
            data: new WorkScheduleResource($workSchedule->load('workdays')),
            message: __("The work schedule has been created successfully.")
        );
    }

    /**
     * Show work schedule
     *
     * Display detailed information about a specific work schedule.
     *
     * @apiResource App\Http\Resources\Frontend\WorkScheduleResource
     * @apiResourceModel App\Models\WorkSchedule with=workdays
     */
    public function show(WorkSchedule $workSchedule, Request $request): ApiResponse
    {
        $workSchedule->load($request->input("include", ["workdays"]));

        return new ApiResponse(data: new WorkScheduleResource($workSchedule));
    }

    /**
     * Update work schedule
     *
     * Update an existing work schedule with new information.
     *
     * @apiResource App\Http\Resources\Frontend\WorkScheduleResource
     * @apiResourceModel App\Models\WorkSchedule
     */
    public function update(WorkScheduleUpdateRequest $request, WorkSchedule $workSchedule): ApiResponse
    {
        UpdateWorkScheduleService::handle($workSchedule, $request->validated());

        return new ApiResponse(
            data: new WorkScheduleResource($workSchedule->fresh()->load('workdays')),
            message: __("The work schedule has been updated successfully.")
        );
    }

    /**
     * Delete work schedule
     *
     * Remove a work schedule from the system.
     */
    public function destroy(WorkSchedule $workSchedule): ApiResponse
    {
        $workSchedule->delete();

        return new ApiResponse(message: __("The work schedule has been deleted successfully."));
    }
}
