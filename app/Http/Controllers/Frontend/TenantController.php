<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\TeamResource;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Tenant
 */
class TenantController extends Controller
{
    /**
     * Get tenant
     * @apiResource App\Http\Resources\TeamResource
     * @apiResourceModel App\Models\Team
     */
    public function __invoke()
    {
        return new ApiResponse(data: new TeamResource(auth()->user()->team));
    }
}
