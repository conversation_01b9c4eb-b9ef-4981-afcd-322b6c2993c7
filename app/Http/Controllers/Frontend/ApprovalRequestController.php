<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\ApprovalRequestResource;
use App\QueryBuilders\ApprovalRequestQueryBuilder;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Approval Requests
 */
class ApprovalRequestController extends Controller
{
    /**
     * List approval requests
     * @apiResource App\Http\Resources\Frontend\ApprovalRequestResource
     * @apiResourceModel App\Models\ApprovalRequest
     * @queryParam filter.from date Filter by from date. Example: 2022-01-01
     * @queryParam filter.to date Filter by to date. Example: 2022-01-31
     * @queryParam filter.status array Filter by status. Example: pending,approved
     * @queryParam filter.type array Filter by type. Example: permission,remote_work
     * @queryParam filter.departments array Filter by departments. Example: 1,2
     * @queryParam filter.employees array Filter by employees. Example: 1,2
     * @queryParam per_page integer Number of items per page.
     * @apiResource App\Http\Resources\Frontend\ApprovalRequestResource
     * @apiResourceModel App\Models\ApprovalRequest
     */
    public function index(Request $request): ApiResponse
    {
        return new ApiResponse(
            data: ApprovalRequestResource::collection(
                ApprovalRequestQueryBuilder::query()
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
