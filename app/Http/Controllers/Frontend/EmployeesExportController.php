<?php

namespace App\Http\Controllers\Frontend;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\EmployeesExportRequest;
use App\Http\Resources\ReportTaskResource;
use App\Jobs\GenerateEmployeeExcelJob;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Scopes\EmployeesFilters;
use App\Support\ApiResponse;
use Illuminate\Support\Carbon;

class EmployeesExportController extends Controller
{
    public function __invoke(EmployeesExportRequest $request)
    {
        $data = $request->validated();
        $data['start_date'] = Carbon::now();

        $reportData = new ReportData($data);

        if (Employee::query()->tap(new EmployeesFilters($reportData))->doesntExist()) {
            return new ApiResponse(message: __('No employees to export'), code: 422);
        }

        $reportTask = ReportTask::createFromData(
            reportData: $reportData,
            reportName: ReportName::Employees,
            team: auth()->user()->team,
            createdBy: auth()->user()
        );

        GenerateEmployeeExcelJob::dispatch(employee: auth()->user(), reportTask: $reportTask);

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
