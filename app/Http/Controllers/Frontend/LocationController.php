<?php

namespace App\Http\Controllers\Frontend;

use App\Events\LocationAdded;
use App\Events\LocationDeleted;
use App\Events\LocationUpdated;
use App\Http\Controllers\Controller;
use App\Http\Requests\LocationStoreRequest;
use App\Http\Requests\LocationUpdateRequest;
use App\Http\Resources\LocationResource;
use App\Models\Location;
use App\Support\ApiResponse;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Locations
 */
class LocationController extends Controller
{
    public function __construct()
    {
        $this->authorizeResource(Location::class, 'location');
    }

    /**
     * List locations
     * @apiResource App\Http\Resources\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(
            data: LocationResource::collection(
                QueryBuilder::for(Location::class)
                    ->allowedFilters(['name'])
                    ->paginateOrGet()
            )
        );
    }

    /**
     * Show location
     * @apiResource App\Http\Resources\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function show(Location $location): ApiResponse
    {
        return new ApiResponse(data: new LocationResource($location));
    }

    /**
     * Add location
     * @apiResource App\Http\Resources\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function store(LocationStoreRequest $request): ApiResponse
    {
        $attributes = $request->validated();

        if ($attributes['is_default']) {
            Location::clearCurrentDefaultLocation();
        }

        $location = Location::create($request->validated());

        event(new LocationAdded($location));

        return new ApiResponse(
            data: new LocationResource($location),
            message: __('The location has been added successfully')
        );
    }

    /**
     * Update location
     * @apiResource App\Http\Resources\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function update(LocationUpdateRequest $request, Location $location): ApiResponse
    {
        $attributes = $request->validated();

        if ($attributes['is_default']) {
            Location::setAsDefaultLocation($location);
        }

        $location->update($request->validated());

        event(new LocationUpdated($location));

        return new ApiResponse(
            data: new LocationResource($location),
            message: __('The location has been updated successfully')
        );
    }

    /**
     * Delete location
     */
    public function destroy(Location $location): ApiResponse
    {
        if ($location->is_default) {
            return new ApiResponse(
                message: __('The default location cannot be deleted'),
                code: 422
            );
        }

        $location->delete();

        event(new LocationDeleted($location));

        return new ApiResponse(message: __('The location has been deleted successfully'));
    }
}
