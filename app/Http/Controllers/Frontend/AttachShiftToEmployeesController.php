<?php

namespace App\Http\Controllers\Frontend;

use App\DTOs\AttachEmployeesToShiftLocationData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\AttachEmployeesToShiftRequest;
use App\Http\Resources\EmployeeResource;
use App\Jobs\EmployeeShiftJob;
use App\Models\Shift;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Employees
 */
class AttachShiftToEmployeesController extends Controller
{
    /**
     * Attach employees to shift
     * @queryParam filter.search string Search by name, email, or phone number.
     * @queryParam filter.ids array Filter by employee ids.
     * @queryParam filter.departments array Filter by department ids.
     * @queryParam filter.locations array Filter by location ids.
     * @queryParam filter.shifts array Filter by shift ids.
     * @queryParam filter.tags array Filter by tag ids.
     * @queryParam filter.exclude integer Exclude employee by id.
     * @queryParam filter.is_active boolean Filter by active status.
     * @queryParam include string Include related resources. E.g. include=department,shift,locations,tags
     * @queryParam per_page integer Number of items per page.
     * @response 422 {"data":[{"id":1,"name":"John Doe","email":""}],"message":"Some employees is already assigned to another temporary shift at the same time"} when some employees are already assigned to another temporary shift at the same time
     */
    public function __invoke(AttachEmployeesToShiftRequest $request, Shift $shift)
    {
        $employeeShiftJob = new EmployeeShiftJob(
            shift: $shift,
            data: AttachEmployeesToShiftLocationData::fromRequest()
        );

        if ($invalidEmployees = $employeeShiftJob->hasInvalidEmployees()) {
            return new ApiResponse(
                data: EmployeeResource::collection($invalidEmployees),
                message: __(
                    'Some employees is already assigned to another temporary shift at the same time'
                ),
                code: 422
            );
        }

        return new ApiResponse(message: $employeeShiftJob->queueIfNeededAndReturnMessage());
    }
}
