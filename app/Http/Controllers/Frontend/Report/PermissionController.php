<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\ApprovalRequestResource;
use App\Models\ApprovalRequest;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Reports
 */
class PermissionController extends Controller
{
    /**
     * List permission
     * @apiResource App\Http\Resources\Frontend\ApprovalRequestResource
     * @apiResourceModel App\Models\ApprovalRequest with=employee.department
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'filter.date' => ['nullable', 'date'],
            'filter.departments' => ['nullable', 'array'],
            'per_page' => ['nullable'],
        ]);

        return new ApiResponse(
            data: ApprovalRequestResource::collection(
                QueryBuilder::for(ApprovalRequest::class)
                    ->allowedFilters([
                        AllowedFilter::callback(
                            'date',
                            fn($query, $date) => $query->whereDate('created_at', $date)
                        ),
                        AllowedFilter::callback(
                            'departments',
                            fn($query, $departments) => $query->whereHas(
                                'employee.department',
                                fn($query) => $query->whereIn('departments.id', $departments)
                            )
                        ),
                    ])
                    ->with('employee.department')
                    ->permission()
                    ->when(
                        $request->department_id,
                        fn($query) => $query->whereRelation(
                            'employee.department',
                            'id',
                            $request->department_id
                        )
                    )
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
