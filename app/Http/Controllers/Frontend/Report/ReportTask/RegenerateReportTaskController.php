<?php

namespace App\Http\Controllers\Frontend\Report\ReportTask;

use App\Http\Controllers\Controller;
use App\Http\Resources\ReportTaskResource;
use App\Models\ReportTask;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Reports Tasks
 */
class RegenerateReportTaskController extends Controller
{
    /**
     * Regenerate report task
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask
     */
    public function __invoke(ReportTask $reportTask)
    {
        $reportTask->regenerate();

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
