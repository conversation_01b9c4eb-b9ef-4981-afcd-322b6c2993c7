<?php

namespace App\Http\Controllers\Frontend\Report\ReportTask;

use App\Http\Controllers\Controller;
use App\Mail\ReportMail;
use App\Models\ReportTask;
use App\Support\ApiResponse;
use Mail;

/**
 * @group Frontend
 * @subgroup Reports Tasks
 */
class SendReportTaskController extends Controller
{
    /**
     * Send report task
     * @response 404 {"message": "No report task available"} when the report task is not available yet
     * @response {
     *   "message": "The report will be sent to your email soon"
     * }
     */
    public function __invoke(ReportTask $reportTask)
    {
        abort_if(!$reportTask->available(), 404);

        Mail::to(auth()->user()->email)->send(
            new ReportMail(employee: auth()->user(), reportTask: $reportTask)
        );

        return new ApiResponse(message: __('The report will be sent to your email soon'));
    }
}
