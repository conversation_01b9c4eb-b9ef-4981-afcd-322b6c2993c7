<?php

namespace App\Http\Controllers\Frontend\Report\ReportTask;

use App\Http\Controllers\Controller;
use App\Http\Resources\ReportTaskResource;
use App\Models\ReportTask;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Reports Tasks
 */
class ReportTaskController extends Controller
{
    /**
     * List report task
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask with=employee,report
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'per_page' => ['nullable'],
        ]);

        return new ApiResponse(
            data: ReportTaskResource::collection(
                QueryBuilder::for(ReportTask::createdByEmployee())
                    ->allowedIncludes(['employee', 'report'])
                    ->allowedSorts(['status', 'created_at'])
                    ->defaultSort('-created_at')
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }

    /**
     * Show report task
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask with=createdBy,report
     */
    public function show(ReportTask $reportTask): ApiResponse
    {
        return new ApiResponse(
            data: new ReportTaskResource($reportTask->load(['createdBy', 'report']))
        );
    }
}
