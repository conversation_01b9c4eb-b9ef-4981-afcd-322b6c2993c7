<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\ActivityResource;
use App\Models\Activity;
use App\Support\ApiResponse;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Reports
 */
class GeospatialAttendanceController extends Controller
{
    /**
     * List geospatial attendance
     * @apiResource App\Http\Resources\Frontend\ActivityResource
     * @apiResourceModel App\Models\Activity with=employee
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'filter.date' => ['required', 'date'],
            'filter.search' => ['nullable', 'string'],
            'per_page' => ['nullable'],
        ]);

        return new ApiResponse(
            data: ActivityResource::collection(
                QueryBuilder::for(Activity::class)
                    ->allowedFilters([
                        AllowedFilter::callback(
                            'date',
                            fn($q, $date) => $q->whereBetween('created_at', [
                                Carbon::parse($date)->startOfDay(),
                                Carbon::parse($date)->endOfDay(),
                            ])
                        ),
                        AllowedFilter::callback(
                            'search',
                            fn($q, $s) => $q->whereHas('employee', fn($q) => $q->search($s))
                        ),
                    ])
                    ->allowedIncludes(['employee'])
                    ->whereIn('action', Activity::checkInOutActivities())
                    ->paginateOrGet()
            )
        );
    }
}
