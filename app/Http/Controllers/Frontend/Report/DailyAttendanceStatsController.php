<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Models\Attendance;
use App\Support\ApiResponse;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Reports
 */
class DailyAttendanceStatsController extends Controller
{
    /**
     * List daily attendance stats
     * @response {
     *   "data": {
     *     "total": 1,
     *     "present": 1,
     *     "absent": 0,
     *     "weekend": 0
     *   }
     * }
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'filter.date' => ['nullable', 'date'],
        ]);

        $date = Carbon::parse($request->input('filter.date', now()));

        $attendances = Attendance::query()
            ->date($date)
            ->where('status', '!=', Attendance::YET)
            ->get();

        return new ApiResponse(
            data: [
                'total' => $attendances->count(),
                'present' => $attendances->where('status', Attendance::PRESENT)->count(),
                'absent' => $attendances->where('status', Attendance::ABSENT)->count(),
                'weekend' => $attendances->where('status', Attendance::WEEKEND)->count(),
            ]
        );
    }
}
