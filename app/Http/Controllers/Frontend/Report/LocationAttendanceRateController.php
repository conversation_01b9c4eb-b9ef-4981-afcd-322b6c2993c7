<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Enums\AttendanceStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\LocationResource;
use App\Models\Location;
use App\Support\ApiResponse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Reports
 */
class LocationAttendanceRateController extends Controller
{
    /**
     * List location attendance rate
     * @apiResource App\Http\Resources\LocationResource
     * @apiResourceModel App\Models\Location with=employees
     */
    public function index(Request $request): ApiResponse
    {
        $request->validate([
            'date' => ['required', 'date', 'date_format:Y-m-d'],
            'filter.search' => ['nullable', 'string'],
        ]);

        # only not yet, absent and present-in-location are counted in employees' total of location.
        # present-in-location employee is only counted in the total of the location he is present in.

        $locations = QueryBuilder::for(Location::class)
            ->allowedFilters([AllowedFilter::partial('search', 'name')])
            ->withCount([
                'employees as total_employees' => fn($q) => $q->whereHas(
                    'attendances',
                    fn(Builder $query) => $query
                        ->date($request->date)
                        ->where(
                            fn($q) => $q
                                ->whereIn('status', [
                                    AttendanceStatus::YET,
                                    AttendanceStatus::ABSENT,
                                ])
                                ->orWhereColumn('check_in_location_id', 'locations.id')
                        )
                ),
                'employees as present_employees' => fn($q) => $q->whereHas(
                    'attendances',
                    fn(Builder $query) => $query
                        ->date($request->date)
                        ->present()
                        ->whereColumn('check_in_location_id', 'locations.id')
                ),
            ])
            ->get();

        return new ApiResponse(data: LocationResource::collection($locations));
    }
}
