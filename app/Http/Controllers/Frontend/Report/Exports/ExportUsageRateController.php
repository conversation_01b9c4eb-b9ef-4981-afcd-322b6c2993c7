<?php

namespace App\Http\Controllers\Frontend\Report\Exports;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Http\Controllers\Controller;
use App\Http\Resources\ReportTaskResource;
use App\Jobs\GenerateUsageRateExcelJob;
use App\Models\ReportTask;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @group Frontend
 * @subgroup Reports Exports
 */
class ExportUsageRateController extends Controller
{
    /**
     * Export usage rate report
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask
     */
    public function __invoke(Request $request): ApiResponse
    {
        $data = $request->validate([
            'departments_ids' => ['nullable', 'array', Rule::existsTenant('departments', 'id')],
            'employees_ids' => ['nullable', 'array', Rule::existsTenant('employees', 'id')],
            'shifts' => ['nullable', 'array', Rule::existsTenant('shifts', 'id')],
            'locations' => ['nullable', 'array', Rule::existsTenant('locations', 'id')],
            'tags' => ['nullable', 'array', Rule::existsTenant('tags', 'id')],
            'usage_status' => ['nullable', 'boolean'],
        ]);

        $reportTask = ReportTask::createFromData(
            reportData: new ReportData($data),
            reportName: ReportName::UsageRate,
            team: auth()->user()->team,
            createdBy: auth()->user()
        );

        GenerateUsageRateExcelJob::dispatch(employee: auth()->user(), reportTask: $reportTask);

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
