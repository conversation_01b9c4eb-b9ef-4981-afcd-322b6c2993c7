<?php

namespace App\Http\Controllers\Frontend\Report\Exports;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Http\Controllers\Controller;
use App\Http\Requests\ExportReportRequest;
use App\Http\Resources\ReportTaskResource;
use App\Jobs\GenerateLeaveExcelJob;
use App\Models\ReportTask;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Reports Exports
 */
class ExportLeaveController extends Controller
{
    /**
     * Export leave report
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask
     */
    public function __invoke(ExportReportRequest $request): ApiResponse
    {
        $reportTask = ReportTask::createFromData(
            reportData: new ReportData($request->validated()),
            reportName: ReportName::Leave,
            team: auth()->user()->team,
            createdBy: auth()->user()
        );

        GenerateLeaveExcelJob::dispatch(employee: auth()->user(), reportTask: $reportTask);

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
