<?php

namespace App\Http\Controllers\Frontend\Report\Exports;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Http\Controllers\Controller;
use App\Http\Requests\ExportReportRequest;
use App\Http\Resources\ReportTaskResource;
use App\Jobs\GeneratePresentAbsentExcelJob;
use App\Models\ReportTask;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Reports Exports
 */
class ExportPresentAbsentController extends Controller
{
    /**
     * Export present absent report
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask
     */
    public function __invoke(ExportReportRequest $request): ApiResponse
    {
        $reportTask = ReportTask::createFromData(
            reportData: new ReportData($request->validated()),
            reportName: ReportName::PresentAbsent,
            team: auth()->user()->team,
            createdBy: auth()->user()
        );

        GeneratePresentAbsentExcelJob::dispatch(employee: auth()->user(), reportTask: $reportTask);

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
