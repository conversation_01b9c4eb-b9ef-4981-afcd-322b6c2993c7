<?php

namespace App\Http\Controllers\Frontend\Report\Exports;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Http\Controllers\Controller;
use App\Http\Resources\ReportTaskResource;
use App\Jobs\GenerateMoodExcelJob;
use App\Models\ReportTask;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Reports Exports
 */
class ExportMoodController extends Controller
{
    /**
     * Export mood report
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask
     */
    public function __invoke(Request $request): ApiResponse
    {
        $data = $request->validate([
            'start_date' => ['required', 'date'],
            'end_date' => [
                'required_if:type,monthly',
                'exclude_if:type,daily',
                'date',
                'after:start_date',
            ],
            'type' => ['required', 'in:monthly,daily'],
        ]);

        $reportData = new ReportData($data);

        $reportTask = ReportTask::createFromData(
            reportData: $reportData,
            reportName: ReportName::Mood,
            team: auth()->user()->team,
            createdBy: auth()->user()
        );

        GenerateMoodExcelJob::dispatch(employee: auth()->user(), reportTask: $reportTask);

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
