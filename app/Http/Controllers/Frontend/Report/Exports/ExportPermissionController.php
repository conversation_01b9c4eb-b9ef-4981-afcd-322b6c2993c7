<?php

namespace App\Http\Controllers\Frontend\Report\Exports;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Http\Controllers\Controller;
use App\Http\Requests\ExportReportRequest;
use App\Http\Resources\ReportTaskResource;
use App\Jobs\GeneratePermissionExcelJob;
use App\Models\ReportTask;
use App\Support\ApiResponse;

/**
 * @group Frontend
 * @subgroup Reports Exports
 */
class ExportPermissionController extends Controller
{
    /**
     * Export permission report
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask
     */
    public function __invoke(ExportReportRequest $request): ApiResponse
    {
        $reportTask = ReportTask::createFromData(
            reportData: new ReportData($request->validated()),
            reportName: ReportName::Permission,
            team: auth()->user()->team,
            createdBy: auth()->user()
        );

        GeneratePermissionExcelJob::dispatch(employee: auth()->user(), reportTask: $reportTask);

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
