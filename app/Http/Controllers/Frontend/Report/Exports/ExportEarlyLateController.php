<?php

namespace App\Http\Controllers\Frontend\Report\Exports;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Http\Controllers\Controller;
use App\Http\Requests\EarlyLateReportRequest;
use App\Http\Resources\ReportTaskResource;
use App\Models\Attendance;
use App\Models\ReportTask;
use App\Scopes\EarlyLateFilters;
use App\Support\ApiResponse;
use App\Workflows\GenerateEarlyLateReportWorkflow;

/**
 * @group Frontend
 * @subgroup Reports Exports
 */
class ExportEarlyLateController extends Controller
{
    /**
     * Export early late report
     * @apiResource App\Http\Resources\ReportTaskResource
     * @apiResourceModel App\Models\ReportTask
     * @response 422 {"message": "No employees to export"} when there are no employees to export
     */
    public function __invoke(EarlyLateReportRequest $request): ApiResponse
    {
        $reportData = new ReportData($request->validated());

        if (Attendance::query()->tap(new EarlyLateFilters($reportData))->doesntExist()) {
            return new ApiResponse(message: __('No employees to export'), code: 422);
        }

        $reportTask = ReportTask::createFromData(
            reportData: $reportData,
            reportName: ReportName::EarlyLate,
            team: auth()->user()->team,
            createdBy: auth()->user()
        );

        GenerateEarlyLateReportWorkflow::start($reportTask);

        return new ApiResponse(data: new ReportTaskResource($reportTask));
    }
}
