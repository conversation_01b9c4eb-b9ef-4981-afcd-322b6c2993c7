<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use App\Support\ApiResponse;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * @group Frontend
 * @subgroup Reports
 */
class PresentAbsentController extends Controller
{
    /**
     * List present absent
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=attendances,department
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'filter.date' => ['nullable', 'date'],
            'department_id' => ['nullable', Rule::existsTenant('departments', 'id')],
            'per_page' => ['nullable'],
        ]);

        $date = CarbonImmutable::parse($request->input('filter.date', now()));

        return new ApiResponse(
            data: EmployeeResource::collection(
                Employee::with([
                    'attendances' => fn($query) => $query
                        ->whereBetween('date', [$date->startOfMonth(), $date->endOfMonth()])
                        ->orderBy('date'),
                    'department',
                ])
                    ->when(
                        $request->department_id,
                        fn($query) => $query->where('department_id', $request->department_id)
                    )
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
