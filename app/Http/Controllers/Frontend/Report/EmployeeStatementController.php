<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\EmployeeStatementResource;
use App\Models\EmployeeStatement;
use App\Support\AllowedFilterEmployeesDepartments;
use App\Support\AllowedFilterFromToCreatedAt;
use App\Support\ApiResponse;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class EmployeeStatementController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'filter.from' => ['nullable', 'date:Y-m-d'],
            'filter.to' => ['nullable', 'date:Y-m-d'],
            'per_page' => ['nullable'],
            'filter.employees' => ['nullable', 'array'],
            'filter.departments' => ['nullable', 'array'],
            'filter.shifts' => ['nullable', 'array'],
            'filter.locations' => ['nullable', 'array'],
            'filter.tags' => ['nullable', 'array'],
        ]);

        return new ApiResponse(
            data: EmployeeStatementResource::collection(
                QueryBuilder::for(EmployeeStatement::class)
                    ->allowedFilters([
                        ...AllowedFilterFromToCreatedAt::filters(),
                        ...AllowedFilterEmployeesDepartments::filters(),
                        AllowedFilter::callback('shifts', function (Builder $query, array $shifts) {
                            $query->where(
                                fn(Builder $query) => $query
                                    ->whereHas(
                                        'employee',
                                        fn(Builder $query) => $query->filterByShifts($shifts)
                                    )
                                    ->orWhereHas(
                                        'attendance',
                                        fn(Builder $query) => $query->filterByShifts($shifts)
                                    )
                            );
                        }),
                        AllowedFilter::callback('locations', function (
                            Builder $query,
                            array $locations
                        ) {
                            $query->where(
                                fn(Builder $query) => $query
                                    ->whereHas(
                                        'employee',
                                        fn(Builder $query) => $query->filterByLocations($locations)
                                    )
                                    ->orWhereHas(
                                        'attendance',
                                        fn(Builder $query) => $query->filterByLocations($locations)
                                    )
                            );
                        }),
                        AllowedFilter::callback(
                            'tags',
                            fn(Builder $query, array $tags) => $query->whereHas(
                                'employee',
                                fn(Builder $query) => $query->filterByTags($tags)
                            )
                        ),
                    ])
                    ->allowedIncludes([
                        'employee',
                        'employee.locations',
                        'employee.department',
                        'employee.department.parent',
                        'employee.tags',
                        'attendance',
                        'attendance.shift',
                        'attendance.checkInLocation',
                        'attendance.checkoutLocation',
                        'attendance.checkInDevice',
                        'attendance.checkoutDevice',
                        'requestable',
                    ])
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
