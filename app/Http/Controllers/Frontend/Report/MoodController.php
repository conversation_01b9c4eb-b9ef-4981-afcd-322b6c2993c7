<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Models\Attendance;
use App\Support\ApiResponse;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * @group Frontend
 * @subgroup Reports
 */
class MoodController extends Controller
{
    /**
     * List mood
     * @response {
     *   "data": {
     *     "total": 1,
     *     "in_mood": 1,
     *     "out_mood": 0,
     *     "mood_per_department": [
     *       {
     *         "department_name": "IT",
     *         "in_mood": 1,
     *         "out_mood": 0
     *       }
     *     ],
     *     "mood_per_shift": [
     *       {
     *         "shift_name": "Morning",
     *         "in_mood": 1,
     *         "out_mood": 0
     *       }
     *     ]
     *   }
     * }
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'filter.date' => ['nullable', 'date'],
        ]);

        $date = Carbon::parse($request->input('filter.date', now()));

        $attendances = Attendance::query()
            ->select('id', 'team_id', 'employee_id', 'in_mood', 'out_mood', 'shift_id')
            ->with(
                'employee:id,department_id,first_name,last_name',
                'employee.department:id,name',
                'shift'
            )
            ->date($date)
            ->where('status', '!=', Attendance::YET)
            ->get();

        return new ApiResponse(
            data: [
                'total' => $attendances->count(),
                'in_mood' => $attendances->countBy('in_mood'),
                'out_mood' => $attendances->countBy('out_mood'),
                'mood_per_department' => $attendances->groupBy('employee.department_id')->map(
                    fn($attendance) => [
                        'department_name' => $attendance->first()->employee->department?->name,
                        'in_mood' => $attendance->avg('in_mood'),
                        'out_mood' => $attendance->avg('out_mood'),
                    ]
                ),
                'mood_per_shift' => $attendances->groupBy('shift_id')->map(
                    fn($attendance) => [
                        'shift_name' => $attendance->first()->shift?->name,
                        'in_mood' => $attendance->avg('in_mood'),
                        'out_mood' => $attendance->avg('out_mood'),
                    ]
                ),
            ]
        );
    }
}
