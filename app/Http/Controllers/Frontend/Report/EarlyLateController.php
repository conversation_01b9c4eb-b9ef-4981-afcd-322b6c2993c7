<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\AttendanceResource;
use App\Models\Attendance;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Reports
 */
class EarlyLateController extends Controller
{
    /**
     * List early late
     * @apiResource App\Http\Resources\Frontend\AttendanceResource
     * @apiResourceModel App\Models\Attendance with=employee,shift
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'filter.date' => ['nullable', 'date'],
            'filter.department_id' => ['nullable', Rule::existsTenant('departments', 'id')],
            'department_id' => ['nullable', Rule::existsTenant('departments', 'id')],
            'per_page' => ['nullable'],
        ]);

        return new ApiResponse(
            data: AttendanceResource::collection(
                QueryBuilder::for(Attendance::class)
                    ->allowedFilters([
                        AllowedFilter::scope('date'),
                        AllowedFilter::exact('department_id'),
                    ])
                    ->where('status', Attendance::PRESENT)
                    ->with(['employee' => ['department', 'team'], 'shift'])
                    ->when(
                        $request->department_id,
                        fn($query) => $query->whereRelation(
                            'employee.department',
                            'id',
                            $request->department_id
                        )
                    )
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
