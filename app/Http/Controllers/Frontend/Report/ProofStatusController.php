<?php

namespace App\Http\Controllers\Frontend\Report;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\ProofResource;
use App\Models\Proof;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group Frontend
 * @subgroup Reports
 * @deprecated, use Frontend/ProofController instead
 */
class ProofStatusController extends Controller
{
    /**
     * Get the proof status.
     * @apiResource App\Http\Resources\Frontend\ProofResource
     * @apiResourceModel App\Models\Proof
     */
    public function __invoke(Request $request)
    {
        return new ApiResponse(
            data: ProofResource::collection(
                QueryBuilder::for(Proof::class)
                    ->allowedFilters([
                        AllowedFilter::callback(
                            'date',
                            fn($query, $date) => $query->whereDate('created_at', $date)
                        ),
                        AllowedFilter::exact('department_id'),
                    ])
                    ->with('employee.department')
                    ->when(
                        $request->department_id,
                        fn($query) => $query->whereRelation(
                            'employee.department',
                            'id',
                            $request->department_id
                        )
                    )
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
