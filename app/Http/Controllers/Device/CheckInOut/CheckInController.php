<?php

namespace App\Http\Controllers\Device\CheckInOut;

use App\Events\EmployeeCheckedIn;
use App\Http\Controllers\Controller;
use App\Http\Requests\Device\CheckInOutRequest;
use App\Models\Activity;
use App\Support\ApiResponse;

/**
 * @group Device
 * @subgroup CheckInOut
 */
class CheckInController extends Controller
{
    /**
     * Check in
     */
    public function __invoke(CheckInOutRequest $request): ApiResponse
    {
        $employee = $request->employee();

        $activeAttendanceRecord = $employee->firstOrCreateActiveAttendanceRecord();

        if (!$activeAttendanceRecord) {
            return new ApiResponse(
                status: 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD',
                message: __('Employee does not have active attendance record'),
                code: 422
            );
        }

        if ($employee->isCheckedIn()) {
            return new ApiResponse(
                status: 'ALREADY_CHECKED_IN',
                message: __('Already checked in'),
                code: 422
            );
        }

        $activity = $employee->activities()->create([
            'action' => Activity::CHECK_IN,
            'device_id' => $request->device()->id,
            'location_id' => $request->device()->location_id,
            ...$request->device()->latLng(),
        ]);

        event(new EmployeeCheckedIn($activity));

        return new ApiResponse(message: __('Checked in successfully'));
    }
}
