<?php

namespace App\Http\Controllers\Device\CheckInOut;

use App\Events\EmployeeCheckedOut;
use App\Http\Controllers\Controller;
use App\Http\Requests\Device\CheckInOutRequest;
use App\Models\Activity;
use App\Support\ApiResponse;

/**
 * @group Device
 * @subgroup CheckInOut
 */
class CheckoutController extends Controller
{
    /**
     * Check out
     */
    public function __invoke(CheckInOutRequest $request): ApiResponse
    {
        $employee = $request->employee();

        if (!$employee->isCheckedIn()) {
            return new ApiResponse(
                status: 'NOT_CHECKED_IN_YET',
                message: __('Not checked in yet'),
                code: 422
            );
        }

        $activity = $employee->activities()->create([
            'action' => Activity::CHECK_OUT,
            'device_id' => $request->device()->id,
            'location_id' => $request->device()->location_id,
            ...$request->device()->latLng(),
        ]);

        event(new EmployeeCheckedOut($activity));

        return new ApiResponse(message: __('Checked out successfully'));
    }
}
