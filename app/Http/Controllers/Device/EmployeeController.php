<?php

namespace App\Http\Controllers\Device;

use App\Http\Controllers\Controller;
use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * @group Device
 * @subgroup Employees
 */
class EmployeeController extends Controller
{
    /**
     * Show by identifier
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee
     */
    public function __invoke(Request $request, string $identifier): ApiResponse
    {
        $employee = Employee::whereAny(['email', 'number'], $identifier)->first();

        if (!$employee) {
            throw ValidationException::withMessages([
                'identifier' => __('This employee does not exists'),
            ]);
        }

        return new ApiResponse(
            data: new EmployeeResource($employee->load('activeAttendanceRecord'))
        );
    }
}
