<?php

namespace App\Http\Controllers\Device\Auth;

use App\Enums\LocationSelection;
use App\Http\Controllers\Controller;
use App\Http\Resources\DeviceResource;
use App\Models\Device;
use App\Support\ApiResponse;
use Hash;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * @group Device
 * @subgroup Auth
 */
class LoginController extends Controller
{
    /**
     * Login
     * @apiResource App\Http\Resources\DeviceResource
     * @apiResourceModel App\Models\Device
     */
    public function __invoke(Request $request)
    {
        $request->validate([
            'username' => 'required',
            'secret_key' => 'required',
        ]);

        $device = Device::firstWhere('username', $request->username);

        if (!$device) {
            throw ValidationException::withMessages([
                'username' => __('This device does not exists'),
            ]);
        }

        if (!Hash::check($request->secret_key, $device->secret_key)) {
            throw ValidationException::withMessages([
                'secret_key' => __('auth.password'),
            ]);
        }

        if ($device->location_selection === LocationSelection::DeviceLocation) {
            $data = $request->validate([
                'lat' => ['required', 'numeric'],
                'lng' => ['required', 'numeric'],
            ]);

            $device->update($data);
        }

        return new ApiResponse(
            data: [
                'device' => new DeviceResource($device),
                'token' => $device->createToken(name: 'device', expiresAt: now()->addYear())
                    ->plainTextToken,
            ]
        );
    }
}
