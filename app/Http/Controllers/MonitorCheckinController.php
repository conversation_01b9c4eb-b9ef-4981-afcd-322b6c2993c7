<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Device\CheckInOut\CheckInController as CheckInControllerDevice;
use App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController as CheckInControllerMobile;
use App\Http\Requests\Device\CheckInOutRequest;
use App\Http\Requests\Mobile\CheckInRequest;
use App\Models\Employee;
use App\Services\OnboardEmployee;
use App\Support\ApiResponse;
use Illuminate\Http\Request;

class MonitorCheckinController extends Controller
{
    public function __invoke(Request $request)
    {
        $testEmployee = Employee::firstWhere('email', '<EMAIL>');

        (new OnboardEmployee())->attachDefaultShiftLocation($testEmployee);

        $testEmployee->activeAttendanceRecord->resetCheckinState();

        $location = $testEmployee->activeLocations()->first();

        auth()->login($testEmployee);

        $mobileResponse = (new CheckInControllerMobile())(
            new CheckInRequest(
                query: [
                    'lat' => $location->lat,
                    'lng' => $location->lng,
                ]
            )
        );

        if ($mobileResponse->code() !== 200) {
            return $mobileResponse;
        }

        if ($device = $testEmployee->team->devices()->first()) {
            $testEmployee->activeAttendanceRecord->refresh()->resetCheckinState();

            auth()->login($device);

            $deviceResponse = (new CheckInControllerDevice())(
                new CheckInOutRequest(query: ['identifier' => $testEmployee->email])
            );

            if ($deviceResponse->code() !== 200) {
                return $deviceResponse;
            }
        }

        return new ApiResponse();
    }
}
