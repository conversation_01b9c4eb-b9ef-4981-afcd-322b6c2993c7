<?php

namespace App\Http\Controllers\External;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\External\Leaves\CreateLeaveRequest;
use App\Http\Requests\External\Leaves\DeleteLeaveRequest;
use App\Http\Requests\External\Leaves\UpdateLeaveRequest;
use App\Http\Resources\External\LeaveResource;
use App\Models\Employee;
use App\Models\Leave;
use App\Services\SyncLeaveAttendanceService;
use App\Support\ApiResponse;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use function abort_if;
use function auth;
use function strtolower;

/**
 * @group External
 *
 * @subgroup Leaves
 */
class LeaveController extends Controller
{
    /**
     * Show the leaves of an employee by ID or Email
     * @urlParam employeeNawartUuid string The Nawart UUID of the employee. Example: 123456
     * @queryParam email string The email of the employee. Example:
     * @queryParam per_page integer The number of items per page. Example: 10
     * @apiResource App\Http\Resources\External\LeaveResource
     * @apiResourceModel App\Models\Leave with=department,employee
     */
    public function index(Request $request, ?string $employeeNawartUuid = null): ApiResponse
    {
        $employee = $employeeNawartUuid
            ? Employee::findByNawartUuid($employeeNawartUuid)
            : Employee::where('email', $request->email)->firstOrFail();

        abort_if(!$employee, 404);

        return new ApiResponse(
            data: LeaveResource::collection(
                $employee
                    ->leaves()
                    ->with(['department', 'employee'])
                    ->paginate($request->per_page)
                    ->withQueryString()
            ),
            message: __('Employee Leaves retrieved successfully')
        );
    }

    /**
     * Create a leave request
     * @apiResource App\Http\Resources\External\LeaveResource
     * @apiResourceModel App\Models\Leave with=department
     */
    public function store(CreateLeaveRequest $request): ApiResponse
    {
        $leave = DB::transaction(function () use ($request) {
            $employee = Employee::where('email', strtolower($request->email))->firstOrFail();

            $leave = Leave::create([
                'team_id' => auth()->id(),
                'employee_id' => $employee->id,
                'department_id' => $employee->department_id,
                'reason' => $request->reason,
                'from_date' => Carbon::parse($request->from_date),
                'to_date' => Carbon::parse($request->to_date),
                'status' => RequestStatus::Approved,
            ]);

            (new SyncLeaveAttendanceService())->setAttendanceToLeave($leave);

            return $leave;
        });

        return new ApiResponse(
            data: new LeaveResource($leave->load('department')),
            message: "Leave $leave->id request created successfully"
        );
    }

    /**
     * Update a leave request
     */
    public function update(UpdateLeaveRequest $request, ?Leave $leave = null): ApiResponse
    {
        $leave = DB::transaction(function () use ($leave, $request) {
            $leave ??= Employee::where('email', $request->email)
                ->firstOrFail()
                ->leaves()
                ->whereDate('from_date', $request->old_from_date)
                ->whereDate('to_date', $request->old_to_date)
                ->firstOrFail();

            $syncLeaveService = new SyncLeaveAttendanceService();

            $syncLeaveService->revertAttendanceToAbsent($leave);

            $leave->update([
                'reason' => $request->reason ?? $leave->reason,
                'from_date' => $request->new_from_date,
                'to_date' => $request->new_to_date,
            ]);

            $syncLeaveService->setAttendanceToLeave($leave);

            return $leave;
        });

        return new ApiResponse(
            data: new LeaveResource($leave->load('department')),
            message: "Leave $leave->id request updated successfully"
        );
    }

    /**
     * Delete a leave request
     */
    public function destroy(DeleteLeaveRequest $request, ?Leave $leave = null): ApiResponse
    {
        $leave = DB::transaction(function () use ($request, $leave) {
            $leave ??= Employee::where('email', $request->email)
                ->firstOrFail()
                ->leaves()
                ->whereDate('from_date', $request->from_date)
                ->whereDate('to_date', $request->to_date)
                ->firstOrFail();

            (new SyncLeaveAttendanceService())->revertAttendanceToAbsent($leave);

            $leave->delete();

            return $leave;
        });

        return new ApiResponse(message: "Leave $leave->id request deleted successfully");
    }
}
