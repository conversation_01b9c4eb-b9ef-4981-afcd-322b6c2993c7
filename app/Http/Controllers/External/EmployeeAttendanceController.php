<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\AttendanceResource;
use App\Services\GetEmployeeFromRequestService;
use App\Support\ApiResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

/**
 * @group External
 * @subgroup Attendance
 */
class EmployeeAttendanceController extends Controller
{
    /**
     * List employee attendances
     * @apiResource App\Http\Resources\Frontend\AttendanceResource
     * @apiResourceModel App\Models\Attendance
     */
    public function __invoke(Request $request)
    {
        $employee = (new GetEmployeeFromRequestService())->handle();

        $request->validate([
            'from_date' => ['nullable', 'date:Y-m-d', 'before:to_date'],
            'to_date' => ['nullable', 'date:Y-m-d', 'after:from_date'],
            'per_page' => ['nullable', 'integer'],
        ]);

        return new ApiResponse(
            data: AttendanceResource::collection(
                QueryBuilder::for($employee->attendances())
                    ->whereBetween('date', [
                        $request->input('from_date', now()->subMonth()),
                        $request->input('to_date', now()),
                    ])
                    ->latest()
                    ->paginate($request->per_page)
                    ->withQueryString()
            )
        );
    }
}
