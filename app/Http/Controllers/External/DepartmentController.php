<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Http\Resources\DepartmentResource;
use App\Models\Department;
use App\Support\ApiResponse;

/**
 * @group External
 *
 * @subgroup Departments
 */
class DepartmentController extends Controller
{
    /**
     * List departments
     * @apiResource App\Http\Resources\DepartmentResource
     * @apiResourceModel App\Models\Department
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(data: DepartmentResource::collection(Department::all()));
    }
}
