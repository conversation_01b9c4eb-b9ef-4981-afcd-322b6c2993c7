<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Http\Requests\External\StoreDepartmentRequest;
use App\Http\Requests\External\UpdateDepartmentRequest;
use App\Http\Resources\DepartmentResource;
use App\Models\Department;
use App\Support\ApiResponse;

/**
 * @group External
 * @subgroup Department
 */
class DepartmentController extends Controller
{
    /**
     * List
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(
            data: DepartmentResource::collection(
                currentTenant()
                    ->departments()
                    ->with(['parent', 'manager'])
                    ->get()
            )
        );
    }

    /**
     * Create
     */
    public function store(StoreDepartmentRequest $request): ApiResponse
    {
        $department = Department::create([
            ...$request->validated(),
            'manager_id' => $request->manager_id,
            'parent_id' => $request->parent_id,
            'tenant_id' => auth()->id(),
        ]);

        return new ApiResponse(
            data: new DepartmentResource($department->load(['manager', 'parent'])),
            message: 'The Department has been created successfully.'
        );
    }

    /**
     * Show
     */
    public function show($id): ApiResponse
    {
        $department = currentTenant()->departments()->findOrFail($id);

        return new ApiResponse(
            data: new DepartmentResource($department->load(['parent', 'manager']))
        );
    }

    /**
     * Update
     */
    public function update(UpdateDepartmentRequest $request, $id): ApiResponse
    {
        $department = currentTenant()->departments()->findOrFail($id);

        $department->update([
            ...$request->validated(),
            'manager_id' => $request->manager_id,
            'parent_id' => $request->parent_id,
        ]);

        return new ApiResponse(
            data: new DepartmentResource($department->load(['manager', 'parent'])),
            message: 'department updated successfully'
        );
    }

    /**
     * Destroy
     */
    public function destroy($id)
    {
        $department = currentTenant()->departments()->findOrFail($id);

        if (currentTenant()->employees()->where('department_id', $id)->exists()) {
            return new ApiResponse(
                message: 'Department that has employees cannot be deleted',
                code: 422
            );
        }

        if (currentTenant()->departments()->where('parent_id', $id)->exists()) {
            return new ApiResponse(
                message: 'Department that has sub-departments cannot be deleted',
                code: 422
            );
        }

        $department->delete();

        return new ApiResponse(message: 'The department has been deleted successfully.');
    }
}
