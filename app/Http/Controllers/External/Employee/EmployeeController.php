<?php

namespace App\Http\Controllers\External\Employee;

use App\Http\Controllers\Controller;
use App\Http\Requests\External\Employee\EmployeeShowRequest;
use App\Http\Requests\External\Employee\EmployeeUpdateRequest;
use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use App\Services\UpdateEmployeeService;
use App\Support\ApiResponse;

/**
 * @group External
 * @subgroup Employees
 */
class EmployeeController extends Controller
{
    /**
     * List employees
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=department,shift,locations,tags
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(
            data: EmployeeResource::collection(Employee::with(['locations', 'tags'])->get())
        );
    }

    /**
     * Get an employee by id, email or employee id
     * @urlParam id integer The ID of the employee. Example: 1
     * @queryParam email string The email of the employee. Example:
     * @queryParam employee_id string The employee ID of the employee. Example:
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=department,shift,locations,tags
     */
    public function show(EmployeeShowRequest $request): ApiResponse
    {
        $employee = Employee::query()
            ->when($request->id, fn($q) => $q->where('id', $request->id))
            ->when($request->email, fn($q) => $q->where('email', $request->email))
            ->when($request->employee_id, fn($q) => $q->where('number', $request->employee_id))
            ->firstOrFail();

        return new ApiResponse(
            data: new EmployeeResource(
                $employee->load(['department', 'shift', 'locations', 'tags'])
            )
        );
    }

    /**
     * Update an employee
     * @apiResource App\Http\Resources\EmployeeResource
     * @apiResourceModel App\Models\Employee with=department,shift,locations,tags
     */
    public function update(EmployeeUpdateRequest $request, Employee $employee): ApiResponse
    {
        UpdateEmployeeService::handle($employee, $request->validated());

        return new ApiResponse(
            data: new EmployeeResource($employee->load(['department', 'shift', 'locations'])),
            message: __('The employee has been updated.')
        );
    }
}
