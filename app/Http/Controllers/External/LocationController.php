<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Http\Resources\LocationResource;
use App\Models\Location;
use App\Support\ApiResponse;

/**
 * @group External
 * @subgroup Locations
 */
class LocationController extends Controller
{
    /**
     * List locations
     * @apiResource App\Http\Resources\LocationResource
     * @apiResourceModel App\Models\Location
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(data: LocationResource::collection(Location::all()));
    }
}
