<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Http\Resources\ShiftResource;
use App\Models\Shift;
use App\Support\ApiResponse;

/**
 * @group External
 *
 * @subgroup Shift
 */
class ShiftController extends Controller
{
    /**
     * List shifts
     * @apiResource App\Http\Resources\ShiftResource
     * @apiResourceModel App\Models\Shift
     */
    public function index(): ApiResponse
    {
        return new ApiResponse(data: ShiftResource::collection(Shift::all()));
    }
}
