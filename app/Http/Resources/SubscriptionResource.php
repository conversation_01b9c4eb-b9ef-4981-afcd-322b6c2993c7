<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'total_employees' => $this->total_employees,
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'tenant_id' => $this->tenant_id,
            'plan' => $this->whenLoaded('plan'),
            'price' => $this->price,
            'items' => $this->whenLoaded('items'),
        ];
    }
}
