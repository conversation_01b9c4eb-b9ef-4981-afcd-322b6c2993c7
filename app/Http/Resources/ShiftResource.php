<?php

namespace App\Http\Resources;

use App\Models\EmployeeShift;
use App\Models\Shift;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Shift */
class ShiftResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'is_default' => $this->is_default,
            'name' => $this->name,
            'force_checkout' => $this->force_checkout?->format('Y-m-d H:i:s'),
            'working_hours' => $this->working_hours,
            'assignment_id' => $this->whenPivotLoaded(
                EmployeeShift::class,
                fn() => $this->pivot->id
            ),
            'permanent' => $this->whenPivotLoaded(
                EmployeeShift::class,
                fn() => $this->pivot->permanent
            ),
            'start_at' => $this->whenPivotLoaded(
                EmployeeShift::class,
                fn() => $this->pivot->start_at
            ),
            'end_at' => $this->whenPivotLoaded(EmployeeShift::class, fn() => $this->pivot->end_at),
        ];
    }
}
