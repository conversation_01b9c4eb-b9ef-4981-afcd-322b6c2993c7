<?php

namespace App\Http\Resources;

use App\Models\Location;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Location */
class LocationResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'lat' => $this->lat,
            'lng' => $this->lng,
            'radius' => $this->radius,
            'timezone' => $this->timezone,
            'is_default' => $this->is_default,
            'automatic' => $this->automatic,
            'check_out_radius' => $this->check_out_radius,
            'employees_count' => $this->whenHas('total_employees'),
            'present_count' => $this->whenHas('present_employees'),
            'assignment_id' => $this->whenPivotLoaded('locationables', fn() => $this->pivot->id),
            'permanent' => $this->whenPivotLoaded('locationables', fn() => $this->pivot->permanent),
            'start_date' => $this->whenPivotLoaded(
                'locationables',
                fn() => $this->pivot->start_date
            ),
            'end_date' => $this->whenPivotLoaded('locationables', fn() => $this->pivot->end_date),
        ];
    }
}
