<?php

namespace App\Http\Resources;

use App\Models\Department;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Department */
class DepartmentResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'remote_work' => $this->remote_work,
            'random_notification' => $this->random_proof_notification_config?->oldRandomNotificationValue(),
            'random_proof_notification_config' => $this->random_proof_notification_config,
            'parent' => $this->whenLoaded('parent', fn() => new DepartmentResource($this->parent)),
            'manager' => $this->whenLoaded('manager', fn() => new EmployeeResource($this->manager)),
        ];
    }
}
