<?php

namespace App\Http\Resources\Frontend;

use App\Http\Resources\EmployeeResource;
use App\Http\Resources\Mobile\V3\AttendanceResource;
use App\Http\Resources\RequestableResource;
use App\Models\EmployeeStatement;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin EmployeeStatement */
class EmployeeStatementResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            'attendance' => new AttendanceResource($this->whenLoaded('attendance')),
            'employee' => new EmployeeResource($this->whenLoaded('employee')),
            'requestable' => new RequestableResource($this->whenLoaded('requestable')),
        ];
    }
}
