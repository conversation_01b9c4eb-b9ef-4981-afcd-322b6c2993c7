<?php

namespace App\Http\Resources\Frontend;

use App\Http\Resources\EmployeeResource;
use App\Models\Department;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Department */
class DepartmentResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'nawart_uuid' => $this->nawart_uuid,
            'name' => $this->name,
            'remote_work' => $this->remote_work,
            'parent' => $this->whenLoaded('parent', fn() => new DepartmentResource($this->parent)),
            'manager' => $this->whenLoaded('manager', fn() => new EmployeeResource($this->manager)),
            'random_proof_notification_config' => $this->random_proof_notification_config,
        ];
    }
}
