<?php

namespace App\Http\Resources\Frontend;

use App\Http\Resources\EmployeeResource;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin ApprovalRequest */
class ApprovalRequestResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'employee' => $this->whenLoaded(
                'employee',
                fn(Employee $employee) => new EmployeeResource($employee)
            ),
            'from_datetime' => $this->from_datetime,
            'to_datetime' => $this->to_datetime,
            'type' => $this->type,
            'reason' => $this->reason,
            'rejection_reason' => $this->rejection_reason,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'decisions' => DecisionResource::collection($this->whenLoaded('decisions')),
        ];
    }
}
