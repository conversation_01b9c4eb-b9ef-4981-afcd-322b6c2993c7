<?php

namespace App\Http\Resources\Frontend;

use App\Http\Resources\EmployeeResource;
use App\Models\Proof;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Proof */
class ProofResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'employee' => $this->whenLoaded(
                'employee',
                fn() => new EmployeeResource($this->employee->load('department'))
            ),
            'status' => $this->status,
            'method' => $this->method,
            'created_at' => $this->created_at,
        ];
    }
}
