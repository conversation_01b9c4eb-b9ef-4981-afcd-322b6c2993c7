<?php

namespace App\Http\Resources\Frontend;

use App\Models\WorkSchedule;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin WorkSchedule */
class WorkScheduleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type->value,
            'name' => $this->name,
            'work_and_off_days_distribution_type' =>
                $this->work_and_off_days_distribution_type->value,
            'specific_days' => $this->specific_days,
            'off_days_after_each_repetition' => $this->off_days_after_each_repetition,
            'start_date' => $this->start_date->format('Y-m-d'),
            'assigned_employees_count' => $this->assignedEmployeesCount(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relationships
            'workdays' => WorkdayResource::collection($this->whenLoaded('workdays')),
            'assignments' => $this->whenLoaded('assignments', function () {
                return $this->assignments->groupBy('type')->map(function ($assignments, $type) {
                    return $assignments->pluck('value')->flatten()->unique()->values();
                });
            }),
        ];
    }
}
