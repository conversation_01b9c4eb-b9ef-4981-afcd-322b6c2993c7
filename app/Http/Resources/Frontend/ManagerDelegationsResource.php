<?php

namespace App\Http\Resources\Frontend;

use App\Http\Resources\EmployeeResource;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Employee */
class ManagerDelegationsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'remote_work_request_delegated' => new EmployeeResource(
                $this->whenLoaded('remoteWorkDelegatedEmployee')
            ),
            'leave_request_delegated' => new EmployeeResource(
                $this->whenLoaded('leaveDelegatedEmployee')
            ),
            'regularization_request_delegated' => new EmployeeResource(
                $this->whenLoaded('regularizationDelegatedEmployee')
            ),
            'permission_request_delegated' => new EmployeeResource(
                $this->whenLoaded('permissionDelegatedEmployee')
            ),
            'periodical_early_late_report_delegated' => new EmployeeResource(
                $this->whenLoaded('earlyLateDelegatedEmployee')
            ),
        ];
    }
}
