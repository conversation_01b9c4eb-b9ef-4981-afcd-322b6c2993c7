<?php

namespace App\Http\Resources\Frontend;

use App\Http\Resources\DepartmentResource;
use App\Http\Resources\EmployeeResource;
use App\Models\Leave;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Leave */
class LeaveResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'department' => new DepartmentResource($this->whenLoaded('department')),
            'employee' => new EmployeeResource($this->whenLoaded('employee')),
            'from_date' => $this->from_date->format('Y-m-d'),
            'to_date' => $this->to_date->format('Y-m-d'),
            'reason' => $this->reason,
            'rejection_reason' => $this->rejection_reason,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'total_days' => $this->total_days,
            'attachment_url' => $this->attachment_url,
            'decisions' => DecisionResource::collection($this->whenLoaded('decisions')),
        ];
    }
}
