<?php

namespace App\Http\Resources\Frontend;

use App\Http\Resources\EmployeeResource;
use App\Http\Resources\LocationResource;
use App\Http\Resources\ShiftResource;
use App\Models\Attendance;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Attendance */
class AttendanceResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'date' => $this->date->format('Y-m-d'),
            'in_type' => $this->in_type,
            'out_type' => $this->out_type,
            'early_in' => $this->early_in,
            'late_in' => $this->late_in,
            'early_out' => $this->early_out,
            'late_out' => $this->late_out,
            'is_adjusted' => $this->is_adjusted,
            'status' => $this->status,
            'status_name' => $this->status_name,
            'check_in' => $this->check_in,
            'check_out' => $this->check_out,
            'shift_from' => $this->shift_from,
            'shift_to' => $this->shift_to,
            'net_hours' => $this->net_hours,
            'on_duty' => $this->on_duty,
            'employee' => new EmployeeResource($this->whenLoaded('employee')),
            'shift' => new ShiftResource($this->whenLoaded('shift')),
            'check_in_location' => new LocationResource($this->whenLoaded('checkInLocation')),
            'check_out_location' => new LocationResource($this->whenLoaded('checkoutLocation')),
        ];
    }
}
