<?php

namespace App\Http\Resources;

use App\Models\ReportTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin ReportTask */
class ReportTaskResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'display_status' => $this->status?->displayName(),
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_path_url' => $this->file_path_url,
            'data' => $this->data,
            'start_date' => $this->start_date?->format('Y-m-d H:i:s'),
            'end_date' => $this->end_date?->format('Y-m-d H:i:s'),
            'completed_at' => $this->completed_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'created_by' => new EmployeeResource($this->whenLoaded('createdBy')),
            'report' => new ReportResource($this->whenLoaded('report')),
        ];
    }
}
