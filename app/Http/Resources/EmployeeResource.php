<?php

namespace App\Http\Resources;

use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Employee */
class EmployeeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'full_name' => $this->name, // todo: deprecated
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'phone' => $this->phone,
            'position' => $this->position,
            'number' => $this->number,
            'is_active' => $this->is_active,
            'preferred_language' => $this->preferred_language,
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'tags' => TagResource::collection($this->whenLoaded('tags')),
            'department' => $this->whenLoaded('department'),
            'manager' => $this->whenLoaded('manager'),
            'tenant' => $this->whenLoaded('tenant'),
            'direct_manager' => EmployeeResource::make($this->whenLoaded('directManager')),
        ];
    }
}
