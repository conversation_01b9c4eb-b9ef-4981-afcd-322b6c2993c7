<?php

namespace App\Http\Resources;

use App\Http\Resources\Frontend\AttendanceResource;
use App\Models\Employee;
use App\Models\Location;
use Arr;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Employee */
class EmployeeResource extends JsonResource
{
    protected bool $useNawartUUidAsId = false;

    public function useNawartUUidAsId(): static
    {
        $this->useNawartUUidAsId = true;

        return $this;
    }

    public function toArray($request): array
    {
        $remoteWork = Arr::first(
            config('lookups.remote_work_policy'),
            fn($p) => $p['value'] === $this->whenHas('remote_work')
        );

        $randomNotification = Arr::first(
            config('lookups.random_notification_policy'),
            fn($r) => $r['value'] ===
                $this->random_proof_notification_config?->oldRandomNotificationValue()
        );

        return [
            'id' => $this->useNawartUUidAsId ? $this->nawart_uuid : $this->id,
            'nawart_uuid' => $this->nawart_uuid,
            'name' => $this->name,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'mobile' => $this->mobile,
            'position' => $this->position,
            'number' => (string) $this->number,
            'is_active' => $this->is_active,
            'device_id' => $this->device_id,
            'random_notification' => $randomNotification // deprecated
                ? [
                    'title' => __($randomNotification['title']),
                    'description' => __($randomNotification['description']),
                    'value' => $randomNotification['value'],
                ]
                : null,
            'random_proof_notification_config' => $this->random_proof_notification_config,
            'remote_work' => $remoteWork
                ? [
                    'title' => __($remoteWork['title']),
                    'description' => __($remoteWork['description']),
                    'value' => $remoteWork['value'],
                ]
                : null,
            'email' => $this->email,
            'locations' => $this->whenLoaded(
                'locations',
                fn() => $this->locations->map(
                    fn(Location $location) => [
                        'id' => $location->id,
                        'name' => $location->name,
                        'permanent' => $location->pivot->permanent,
                        'start_date' => $location->pivot->start_date,
                        'end_date' => $location->pivot->end_date,
                        'latitude' => $location->lat,
                        'longitude' => $location->lng,
                        'radius' => $location->radius,
                        'check_out_radius' => $location->check_out_radius,
                        'automatic' => $location->automatic,
                    ]
                )
            ),
            'department' => DepartmentResource::make($this->whenLoaded('department')),
            'shift' => ShiftResource::make($this->whenLoaded('shift')),
            'shifts' => ShiftResource::collection($this->whenLoaded('shifts')),
            'attendances' => AttendanceResource::collection($this->whenLoaded('attendances')),
            'tags' => TagResource::collection($this->whenLoaded('tags')),
            'direct_manager' => EmployeeResource::make($this->whenLoaded('directManager')),
            'random_proof_of_attendance_count' => $this->random_proof_notification_config?->count, // deprecated
            'active_attendance_record' => AttendanceResource::make(
                $this->whenLoaded('activeAttendanceRecord')
            ),
            'last_activity_at' => $this->last_activity_at,
            'first_login_at' => $this->first_login_at,
            'device_name' => $this->device_name,
            'device_os' => $this->device_os,
        ];
    }
}
