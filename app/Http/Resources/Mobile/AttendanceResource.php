<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @deprecated
 * */
class AttendanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'employee' => new EmployeeResource(
                $this->employee->load(['managedDepartments', 'activeLocations', 'shift'])
            ), // FIXME: this should be removed but before check with mobile team
            'force_checkout' => $this->employee->shift?->force_checkout,
            'managed_departments' => $this->employee->managedDepartments,
            // FIXME: this should be removed, but before check with mobile team
            'last_check_in_time' => $this->check_in,
            'check_out_time' => $this->check_out,
            'working_hours' => $this->net_hours->format('H:i'),
            'working_time_in_sec' => $this->getActualHoursInSecAttribute(),
            'created_at' => $this->created_at,
            'deleted_at' => $this->deleted_at,
            'actual_hours_pct' => $this->actual_hours_pct,
            'is_weekend' => $this->is_weekend,
            'status' => $this->status,
            'today_attendance' => $this->employee->activeAttendanceRecord,
        ];
    }
}
