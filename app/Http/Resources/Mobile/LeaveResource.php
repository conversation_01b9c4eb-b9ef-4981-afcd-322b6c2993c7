<?php

namespace App\Http\Resources\Mobile;

use App\Models\Leave;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Leave
 * @deprecated
 */
class LeaveResource extends JsonResource
{
    // TODO: should we load delegated employee instead of manager when exists?
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'employee_name' => $this->whenLoaded('employee', fn() => $this->employee->name),
            'manager_name' => $this->whenLoaded(
                'employee.manager',
                fn() => $this->employee->manager->name
            ),
            'department_name' => $this->whenLoaded('department', fn() => $this->department->name),
            'from_date' => Carbon::parse($this->from_date),
            'to_date' => Carbon::parse($this->to_date),
            'reason' => $this->reason,
            'status' => $this->status,
            'request_id' => $this->id,
            'created_at' => $this->created_at,
            'total_days' => $this->total_days,
        ];
    }
}
