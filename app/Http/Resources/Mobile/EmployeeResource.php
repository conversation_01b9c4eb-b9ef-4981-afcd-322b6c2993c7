<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

/**
 * @deprecated
 */
class EmployeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'mobile' => $this->mobile,
            'status' => $this->status,
            'remote_work' => $this->remote_work,
            'position' => $this->position,
            'department' => [
                'id' => $this?->department?->id,
                'name' => $this?->department?->name,
            ],
            'managed_departments' => $this->managedDepartments,
            'location_names' => $this->whenLoaded('activeLocations', function () {
                $locations_of_employee = [];
                foreach ($this->locations as $location) {
                    $locations_of_employee[$location->id] = $location->name;
                }

                return Arr::join(Arr::flatten($locations_of_employee), ', ');
            }),
            'shift' => $this->whenLoaded('shift', function () {
                $shifts = $this->shift['working_hours']['weekdays'];

                return Arr::map($shifts, function (array|bool $item, string $key) {
                    if ($item) {
                        $item['from'] = Carbon::createFromFormat('H:i', $item['from'])->format(
                            'h:i a'
                        );
                        $item['to'] = Carbon::createFromFormat('H:i', $item['to'])->format('h:i a');
                        unset($item['next_day_checkout']);
                    }

                    return $item;
                });
            }),
            'is_active' => $this->is_active,
        ];
    }
}
