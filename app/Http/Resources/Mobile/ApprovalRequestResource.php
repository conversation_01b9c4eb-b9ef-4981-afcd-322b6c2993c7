<?php

namespace App\Http\Resources\Mobile;

use App\Models\ApprovalRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ApprovalRequest
 * @deprecated
 */
class ApprovalRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'employee_name' => $this->whenLoaded('employee', fn() => $this->employee->name),
            'manager_name' => $this->whenLoaded(
                'employee.manager',
                fn() => $this->employee->manager->name
            ),
            'department_name' => $this->whenLoaded('department', fn() => $this->department->name),
            'from_datetime' => $this->from_datetime->format('Y-m-d H:i:s'),
            'to_datetime' => $this->to_datetime->format('Y-m-d H:i:s'),
            'type' => $this->type,
            'reason' => $this->reason,
            'status' => $this->status,
            'request_id' => $this->id,
            'created_at' => $this->created_at,
        ];
    }
}
