<?php

namespace App\Http\Resources\Mobile\V2;

use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @deprecated
 * @mixin Attendance
 */
class AttendanceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'last_check_in_time' => $this->check_in,
            'check_out_time' => $this->check_out,
            'working_hours' => $this->net_hours->format('H:i'),
            'working_time_in_sec' => $this->actual_hours_in_sec,
            'actual_hours_pct' => $this->actual_hours_pct,
            'is_weekend' => $this->is_weekend,
            'status' => $this->status,
            'in_mood' => $this->in_mood,
            'out_mood' => $this->out_mood,
            'date' => $this->date,
            'committed_hours' => $this->committed_hours,
            'flexible_hours' => $this->flexible_hours,
        ];
    }
}
