<?php

namespace App\Http\Resources\Mobile\V2;

use App\Http\Resources\LocationResource;
use App\Http\Resources\Mobile\V3\AttendanceResource;
use App\Http\Resources\ShiftResource;
use App\Http\Resources\TagResource;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**@mixin Employee */
class EmployeeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'mobile' => $this->mobile,
            'status' => $this->status,
            'remote_work' => $this->remote_work,
            'position' => $this->position,
            'department' => $this->whenLoaded('department'),
            //todo: deprecated, should be removed in the future
            'managed_departments' => $this->whenHas(
                attribute: 'managed_employees_exists',
                value: [1],
                default: []
            ),
            // the mobile check if the array is empty
            'is_manager' => $this->when(
                $this->hasAttribute('managed_employees_exists'),
                fn() => $this->managed_employees_exists
            ),
            'delegations' => $this->whenLoaded('employeeDelegations'),
            'active_locations' => LocationResource::collection(
                $this->whenLoaded('activeLocations')
            ),
            'locations' => LocationResource::collection($this->whenLoaded('locations')),
            'active_attendance_record' => new AttendanceResource(
                $this->whenLoaded('activeAttendanceRecord')
            ),
            'shift' => new ShiftResource($this->whenLoaded('shift')),
            'shifts' => ShiftResource::collection($this->whenLoaded('shifts')),
            'tags' => TagResource::collection($this->whenLoaded('tags')),
            'manager' => new self($this->whenLoaded('manager')),
            'upcoming_shifts' => ShiftResource::collection($this->whenLoaded('upcomingShifts')),
            'is_active' => $this->is_active,
        ];
    }
}
