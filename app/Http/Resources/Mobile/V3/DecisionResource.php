<?php

namespace App\Http\Resources\Mobile\V3;

use App\Http\Resources\Mobile\V2\EmployeeResource;
use App\Models\ApprovalRequest;
use App\Models\Decision;
use App\Models\Leave;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Decision */
class DecisionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'layer' => $this->layer,
            'decidable' => $this->whenLoaded(
                'decidable',
                fn() => match ($this->decidable_type) {
                    Leave::class => new LeaveResource($this->decidable),
                    ApprovalRequest::class => new ApprovalRequestResource($this->decidable),
                }
            ),
            'decider' => new EmployeeResource($this->whenLoaded('decider')),
            'created_at' => $this->created_at,
        ];
    }
}
