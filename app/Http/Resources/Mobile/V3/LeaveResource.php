<?php

namespace App\Http\Resources\Mobile\V3;

use App\Http\Resources\DepartmentResource;
use App\Http\Resources\EmployeeResource;
use App\Models\Leave;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Leave */
class LeaveResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'from_date' => $this->from_date->format('Y-m-d H:i:s'),
            'to_date' => $this->to_date->format('Y-m-d H:i:s'),
            'reason' => $this->reason,
            'rejection_reason' => $this->rejection_reason,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'attachment_url' => $this->attachment_url,
            'total_days' => $this->total_days,
            'department' => new DepartmentResource($this->whenLoaded('department')),
            'employee' => new EmployeeResource($this->whenLoaded('employee')),
            'decisions' => DecisionResource::collection($this->whenLoaded('decisions')),
        ];
    }
}
