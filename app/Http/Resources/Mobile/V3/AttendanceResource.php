<?php

namespace App\Http\Resources\Mobile\V3;

use App\Http\Resources\DeviceResource;
use App\Http\Resources\LocationResource;
use App\Http\Resources\ShiftResource;
use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Attendance
 */
class AttendanceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'date' => $this->date->format('Y-m-d'),
            'check_in' => $this->check_in,
            'check_out' => $this->check_out,
            'net_hours' => $this->net_hours,
            'shift_from' => $this->shift_from,
            'shift_to' => $this->shift_to,
            'is_weekend' => $this->is_weekend,
            'status' => $this->status,
            'in_mood' => $this->in_mood,
            'out_mood' => $this->out_mood,
            'is_upcoming_shift' => $this->is_upcoming_shift,
            'flexible_hours' => $this->flexible_hours,
            'check_in_location' => new LocationResource($this->whenLoaded('checkInLocation')),
            'check_out_location' => new LocationResource($this->whenLoaded('checkoutLocation')),
            'check_in_device' => new DeviceResource($this->whenLoaded('checkInDevice')),
            'check_out_device' => new DeviceResource($this->whenLoaded('checkoutDevice')),
            'shift' => new ShiftResource($this->whenLoaded('shift')),
        ];
    }
}
