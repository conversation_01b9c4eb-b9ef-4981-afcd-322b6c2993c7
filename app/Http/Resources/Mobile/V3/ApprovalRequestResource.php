<?php

namespace App\Http\Resources\Mobile\V3;

use App\Http\Resources\DepartmentResource;
use App\Http\Resources\EmployeeResource;
use App\Models\ApprovalRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin ApprovalRequest */
class ApprovalRequestResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'from_datetime' => $this->from_datetime,
            'to_datetime' => $this->to_datetime,
            'type' => $this->type,
            'reason' => $this->reason,
            'rejection_reason' => $this->rejection_reason,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'attendance_type' => $this->attendance_type,

            'department' => new DepartmentResource($this->whenLoaded('department')),
            'employee' => new EmployeeResource($this->whenLoaded('employee')),
            'decisions' => DecisionResource::collection($this->whenLoaded('decisions')),
        ];
    }
}
