<?php

namespace App\Http\Resources\Mobile\Summary;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttendanceSummaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     */
    public function toArray(Request $request): array
    {
        return [
            'working_hours' => $this->net_hours->format('H:i'),
            'working_time_in_sec' => $this->getActualHoursInSecAttribute(),
            'created_at' => $this->created_at,
            'deleted_at' => $this->deleted_at,
            'actual_hours_pct' => $this->actual_hours_pct,
            'is_weekend' => $this->is_weekend,
            'status' => $this->status,
        ];
    }
}
