<?php

namespace App\Http\Resources\Mobile;

use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Team */
class TeamResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'primary_color' => $this->primary_color,
            'white_logo_url' => $this->white_logo_url,
            'colored_logo_url' => $this->colored_logo_url,

            'leave_request' => $this->leave_request,
            'approval_request' => $this->approval_request,
            'permission_request' => $this->permission_request,

            'vacation_weekend' => $this->vacation_weekend,
            'remote_work' => $this->remote_work,
            'free_checkout' => $this->free_checkout,
            'approval_requests_limit' => $this->approval_requests_limit,
            'remote_work_days_monthly_limit' => $this->remote_work_days_monthly_limit,

            'employees_weekly_summary' => $this->employees_weekly_summary,
            'map_report_thresholds' => $this->map_report_thresholds,

            'permission_request_daily_limit_hours' => $this->permission_request_daily_limit_hours,
            'permission_request_monthly_limit_hours' =>
                $this->permission_request_monthly_limit_hours,
            'approval_type' => $this->approval_type,
        ];
    }
}
