<?php

namespace App\Http\Resources\Mobile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ActivityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     */
    public function toArray(Request $request): array
    {
        return [
            'status' => $this->action,
            'working_hours' => $this->employee->activeAttendanceRecord?->net_hours?->format('H:i'),
            'created_at' => $this->created_at,
            'first_checkin' => $this->employee->activeAttendanceRecord?->check_in,
        ];
    }
}
