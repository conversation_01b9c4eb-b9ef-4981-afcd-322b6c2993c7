<?php

namespace App\Http\Resources;

use App\Models\Proof;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Proof */
class ProofResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'team_id' => $this->team_id,
            'employee_id' => $this->employee_id,
            'notification_id' => $this->notification_id,
            'method' => $this->method,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'expire_at' => $this->expire_at,
        ];
    }
}
