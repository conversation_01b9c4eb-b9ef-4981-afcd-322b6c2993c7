<?php

namespace App\Http\Resources;

use App\Enums\RequestType;
use App\Models\ApprovalRequest;
use App\Models\Leave;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Leave|ApprovalRequest */
class RequestableResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'department' => new DepartmentResource($this->whenLoaded('department')),
            'employee' => new EmployeeResource($this->whenLoaded('employee')),
            'from_date' => $this->from_date ?? $this->from_datetime,
            'to_date' => $this->to_date ?? $this->to_datetime,
            'reason' => $this->reason,
            'rejection_reason' => $this->rejection_reason,
            'status' => $this->status,
            'attachment_url' => $this->attachment_url,
            'type' => RequestType::fromModel($this->resource),
            'created_at' => $this->created_at,
        ];
    }
}
