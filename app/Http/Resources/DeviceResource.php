<?php

namespace App\Http\Resources;

use App\Models\Device;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Device */
class DeviceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'location_selection' => $this->location_selection,
            'plain_text_secret_key' => $this->when(
                $this->plainTextSecretKey,
                $this->plainTextSecretKey
            ),
            'username' => $this->username,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            'location' => new LocationResource($this->whenLoaded('location')),
        ];
    }
}
