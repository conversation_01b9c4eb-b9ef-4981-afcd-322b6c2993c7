<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckOtp
{
    public function handle(Request $request, Closure $next)
    {
        if (
            !loginSession()->rawIdentifier() ||
            !loginSession()->employee() ||
            !loginSession()->resolveIdentifier() ||
            !loginSession()->resolveOtp()
        ) {
            return redirect()->route('identifier-form.create');
        }

        return $next($request);
    }
}
