<?php

namespace App\Http\Middleware;

use Closure;
use DateTimeImmutable;
use Exception;
use Illuminate\Http\Request;
use OTPHP\TOTP;
use Psr\Clock\ClockInterface;
use function Sentry\addBreadcrumb;
use function Sentry\captureException;

class VerifyTOTP
{
    const PERIOD = 60 * 8; // 8 minutes
    const LEEWAY = self::PERIOD - 1; // 8 minutes - 1 second
    const DIGITS = 6;

    public function handle(Request $request, Closure $next)
    {
        $secret = config('app.totp_secret');
        $code = $request->header('X-Verification-Code');

        // todo: remove this check after the feature is completed and tested and all the clients are updated
        if (!$secret || !$code) {
            abort_if(app()->environment('develop'), 403, 'Invalid Request');

            return $next($request);
        }

        if (strlen($code) !== self::DIGITS) {
            return $next($request);
        }

        $this->trackInSentry($code);

        $valid = $this->isValid($secret, $code);

        if (!$valid) {
            captureException(new Exception('Invalid TOTP code'));

            // todo: remove this check after the feature is completed and tested and all the clients are updated
            abort_if(app()->environment('develop'), 403, 'Invalid Request');
        }

        return $next($request);
    }

    public function clock(): ClockInterface
    {
        return new class implements ClockInterface {
            public function now(): DateTimeImmutable
            {
                return new DateTimeImmutable();
            }
        };
    }

    public function trackInSentry(string $code): void
    {
        addBreadcrumb(
            category: 'totp',
            message: 'Verifying TOTP code',
            metadata: [
                'timestamp' => $this->clock()->now()->getTimestamp(),
                'code' => $code,
            ]
        );
    }

    public function isValid(string $secret, string $code): bool
    {
        $totp = TOTP::createFromSecret($secret, $this->clock());

        $totp->setPeriod(self::PERIOD);
        $totp->setDigits(self::DIGITS);

        return $totp->verify(otp: $code, leeway: self::LEEWAY);
    }
}
