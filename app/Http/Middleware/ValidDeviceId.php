<?php

namespace App\Http\Middleware;

use App\Support\ApiResponse;
use Closure;
use Illuminate\Http\Request;

class ValidDeviceId
{
    private array $testEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ];

    public function handle(Request $request, Closure $next)
    {
        $deviceIdHeader = $request->header('X-Device-Id');

        $user = $request->user();

        if (!$deviceIdHeader) {
            return new ApiResponse(message: 'INCORRECT_DEVICE_ID', code: 401);
        }

        if (in_array($user->email, $this->testEmails)) {
            return $next($request);
        }

        if (!$user->device_id) {
            $user->update(['device_id' => $deviceIdHeader]);

            if (!$user->first_login_at) {
                $user->update(['first_login_at' => now()]);
            }

            return $next($request);
        }

        if ($deviceIdHeader !== $user->device_id) {
            // todo: it should be http forbidden 403
            return new ApiResponse(message: 'INCORRECT_DEVICE_ID', code: 401);
        }

        return $next($request);
    }
}
