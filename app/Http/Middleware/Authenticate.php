<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;
use function config;

class Authenticate extends Middleware
{
    protected function redirectTo(Request $request)
    {
        if (
            $request->is('horizon/*') ||
            $request->is('nova-api/*', 'nova/*') ||
            $request->is('docs')
        ) {
            return '/nova';
        }

        return config('services.frontend_url');
    }
}
