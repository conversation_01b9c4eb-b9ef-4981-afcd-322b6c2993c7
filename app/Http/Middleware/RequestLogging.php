<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class RequestLogging
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (app()->isLocal() || app()->runningUnitTests()) {
            return $next($request);
        }

        $url = $request->url();
        $queryString = $request->getQueryString();
        $method = $request->method();
        $ip = $request->ip();
        $headers = $this->getHeadersFromRequest();

        $methodUrlString = "$ip $method $url";
        if ($queryString) {
            $methodUrlString .= "?$queryString";
        }

        if (array_key_exists('Authorization', $headers)) {
            $headers['Authorization'] = 'xxxxxxx';
        }

        $headersString = '';
        foreach ($headers as $key => $header) {
            $headersString .= " $key:$header ";
        }

        $logId = Str::random(6);
        Log::info("Incoming request ($logId): $methodUrlString " . $headersString);

        return $next($request);
    }

    private function getHeadersFromRequest()
    {
        $allowedHeaders = [
            'HTTP_ACCEPT_LANGUAGE',
            'HTTP_ACCEPT',
            'HTTP_ACCEPT_ENCODING',
            'HTTP_USER_AGENT',
            'HTTP_HOST',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED_HOST',
            'HTTP_X_FORWARDED_PORT',
            'HTTP_X_REAL_IP',
            'HTTP_EXPOSE',
        ];

        $headers = [];
        foreach ($_SERVER as $key => $value) {
            if (!str_starts_with($key, 'HTTP_') || !in_array($key, $allowedHeaders)) {
                continue;
            }
            $header = str_replace(
                ' ',
                '-',
                ucwords(str_replace('_', ' ', strtolower(substr($key, 5))))
            );
            $headers[$header] = $value;
        }

        return $headers;
    }
}
