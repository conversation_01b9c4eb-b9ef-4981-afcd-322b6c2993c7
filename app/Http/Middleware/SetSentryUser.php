<?php

namespace App\Http\Middleware;

use App\Models\Device;
use App\Models\Employee;
use App\Models\Team;
use Closure;
use Illuminate\Http\Request;
use Sentry\State\Scope;

class SetSentryUser
{
    public function handle(Request $request, Closure $next)
    {
        if (app()->bound('sentry')) {
            app('sentry')->configureScope(function (Scope $scope): void {
                /** @var Device|Employee|Team|null $user */
                [$user, $guard] = $this->user();

                if ($user) {
                    $scope->setUser([
                        'id' => $user->id,
                        'nawart_uuid' => match (true) {
                            $user instanceof Team, $user instanceof Employee => $user->nawart_uuid,
                            default => null,
                        },
                        'email' => match (true) {
                            $user instanceof Employee => $user->email,
                            default => null,
                        },
                        'tenant_id' => match (true) {
                            $user instanceof Team => $user->id,
                            $user instanceof Employee => $user->team->id,
                            default => null,
                        },
                        'guard' => $guard,
                    ]);
                }
            });
        }

        return $next($request);
    }

    public function user(): ?array
    {
        foreach (config('auth.guards') as $guard => $config) {
            // ignore any errors that might occur when calling auth()->user()
            $user = rescue(callback: fn() => auth($guard)->user(), report: false);

            if ($user) {
                return [$user, $guard];
            }
        }

        return null;
    }
}
