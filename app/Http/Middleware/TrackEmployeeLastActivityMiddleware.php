<?php

namespace App\Http\Middleware;

use App\Models\Employee;
use Cache;
use Closure;
use Illuminate\Http\Request;
use function auth;
use function now;

class TrackEmployeeLastActivityMiddleware
{
    const FIVE_MINUTES = 5 * 60;

    public function handle(Request $request, Closure $next)
    {
        Employee::disableAuditing();

        // we want to avoid updating every request
        if (Cache::lock('employee_last_activity_' . auth()->id(), self::FIVE_MINUTES)->get()) {
            auth()
                ->user()
                ->update([
                    'last_activity_at' => now(),
                    'app_version' => $request->header('X-Application-Version'),
                    'device_os' => $request->header('X-Device-OS'),
                    'device_name' => $request->header('X-Device-Name'),
                ]);
        }

        Employee::enableAuditing();

        return $next($request);
    }
}
