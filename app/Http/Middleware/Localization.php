<?php

namespace App\Http\Middleware;

use App\Models\Team;
use Closure;
use Illuminate\Http\Request;
use function app;

class Localization
{
    const ALLOWED_LANGUAGES = ['ar', 'en'];

    const DEFAULT_LANGUAGE = 'ar';

    public function handle(Request $request, Closure $next)
    {
        // external routes should always be in English
        if ($request->is('api/*/external/*') || $request->user() instanceof Team) {
            app()->setLocale('en');

            return $next($request);
        }

        $userLanguage = $request->user()?->preferred_language ?? self::DEFAULT_LANGUAGE;

        $preferredLanguage = $request->header('Accept-Language');

        $locale = in_array($preferredLanguage, self::ALLOWED_LANGUAGES)
            ? $preferredLanguage
            : $userLanguage;

        app()->setLocale($locale);

        return $next($request);
    }
}
