<?php

namespace App\Http\Requests;

use App\Enums\DelegationType;
use App\Enums\DurationStatus;
use App\Models\Delegation;
use App\Models\Employee;
use Carbon\CarbonPeriod;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Validator;

class EmployeeUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'remote_work' => ['required'],

            'random_proof_notification_config' => ['required', 'array'],
            'random_proof_notification_config.enabled' => ['required', 'bool'],
            'random_proof_notification_config.inherited' => ['required', 'bool'],
            'random_proof_notification_config.count' => [
                'present',
                'nullable',
                'int',
                'min:0',
                'max:15',
            ],

            'locations' => ['nullable', 'array'],
            'locations.*.id' => ['required', Rule::existsTenant('locations', 'id')],
            'locations.*.type' => ['required', Rule::in(DurationStatus::values())],
            'locations.*.start_date' =>
                'required_if:locations.*.type,' . DurationStatus::TEMPORARY->value,
            'locations.*.end_date' =>
                'required_if:locations.*.type,' . DurationStatus::TEMPORARY->value,

            'shifts' => ['present', 'array'],
            'shifts.*.id' => ['required', 'integer', Rule::existsTenant('shifts', 'id')],
            'shifts.*.type' => ['required', Rule::in(DurationStatus::values())],
            'shifts.*.start_date' =>
                'required_if:shifts.*.type,' . DurationStatus::TEMPORARY->value,
            'shifts.*.end_date' => 'required_if:shifts.*.type,' . DurationStatus::TEMPORARY->value,

            'remote_work_request_delegated_id' => [
                'nullable',
                Rule::existsTenant('employees', 'id'),
            ],
            'leave_request_delegated_id' => ['nullable', Rule::existsTenant('employees', 'id')],
            'regularization_request_delegated_id' => [
                'nullable',
                Rule::existsTenant('employees', 'id'),
            ],
            'permission_request_delegated_id' => [
                'nullable',
                Rule::existsTenant('employees', 'id'),
            ],
            'periodical_early_late_report_delegated_id' => [
                'nullable',
                Rule::existsTenant('employees', 'id'),
            ],
        ];
    }

    public function delegationData(): array
    {
        return Arr::only($this->all(), DelegationType::requestInputs());
    }

    protected function passedValidation(): void
    {
        /** @var Employee $employee */
        $employee = $this->route('employee');

        $delegationData = $this->delegationData();

        if (in_array($employee->id, $delegationData)) {
            throw ValidationException::withMessages([__('manager can not delegate to himself')]);
        }

        foreach ($delegationData as $key => $delegatedID) {
            $delegationType = DelegationType::requestInputToType($key);

            if (
                Delegation::query()
                    ->where('delegated_id', $delegatedID)
                    ->where('type', $delegationType)
                    ->whereNot('delegatee_id', $this->route('employee')->id)
                    ->exists()
            ) {
                throw ValidationException::withMessages([
                    __(
                        'the delegated employee is already delegated :type delegation by another manager',
                        ['type' => $delegationType->displayName()]
                    ),
                ]);
            }
        }
    }

    public function messages(): array
    {
        return [
            'locations.*.start_date.required_if' => __(
                'start date is required for temporary location'
            ),
            'locations.*.end_date.required_if' => __('end date is required for temporary location'),
            'shifts.*.start_date.required_if' => __('start date is required for temporary shift'),
            'shifts.*.end_date.required_if' => __('end date is required for temporary shift'),
        ];
    }

    public function after(): array
    {
        return [
            function (Validator $validator) {
                $shifts = collect($validator->validated()['shifts']);

                if ($shifts->countBy('type')->get(DurationStatus::PERMANENT->value) > 1) {
                    $validator
                        ->errors()
                        ->add('shifts', __('there should not be more than one permanent shift'));
                }

                $temporaryShifts = $shifts
                    ->groupBy('type')
                    ->get(DurationStatus::TEMPORARY->value, fn() => collect())
                    ->map(
                        fn(array $shift) => [
                            'id' => $shift['id'],
                            'period' => CarbonPeriod::dates(
                                $shift['start_date'],
                                $shift['end_date']
                            ),
                        ]
                    );
                $areShiftsNotOverlapping = $temporaryShifts->every(
                    key: function (array $shift, int $currentShiftKey) use ($temporaryShifts) {
                        foreach ($temporaryShifts as $key => $temporaryShift) {
                            if (
                                $currentShiftKey !== $key &&
                                $shift['period']->overlaps(
                                    $temporaryShift['period']->getStartDate()->subDay(),
                                    $temporaryShift['period']->getEndDate()->addDay()
                                )
                            ) {
                                return false;
                            }
                        }
                        return true;
                    }
                );

                if (!$areShiftsNotOverlapping) {
                    $validator->errors()->add('shifts', __('shifts should not be overlapping'));
                }
            },
        ];
    }
}
