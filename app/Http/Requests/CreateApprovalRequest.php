<?php

namespace App\Http\Requests;

use App\Models\ApprovalRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class CreateApprovalRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'from' => ['required', 'date', 'before:to'],
            'to' => ['required', 'date'],
            'reason' => ['nullable', 'string', 'max:255'],
            'type' => [
                'required',
                'string',
                Rule::in([ApprovalRequest::PERMISSION, ApprovalRequest::REGULARIZATION]),
            ],
        ];
    }

    /**
     * Configure the validator instance. Runs after validation of rules is done.
     */
    public function withValidator(Validator $validator): void
    {
        // ensure that to and from dates are on the same day
        $validator->after(function (Validator $validator) {
            $validated_data = $validator->safe();
            $from_date = Carbon::parse($validated_data['from']);
            $to_date = Carbon::parse($validated_data['to']);
            if ($from_date->toDateString() != $to_date->toDateString()) {
                $validator
                    ->errors()
                    ->add('to', 'The to field must be in the same day as from field.');
            }
        });
    }
}
