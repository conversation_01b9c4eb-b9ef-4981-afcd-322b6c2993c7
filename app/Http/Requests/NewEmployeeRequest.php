<?php

namespace App\Http\Requests;

use App\Enums\EmployeePreferredLanguage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Propaganistas\LaravelPhone\Rules\Phone;

class NewEmployeeRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        if (phone($this->phone, 'SA')->isValid()) {
            $this->merge([
                'phone' => phone($this->phone, 'SA')->formatE164(),
            ]);
        }

        // otherwise, keep the original value, it will fail validation later,
        // or it's not provided, which is fine
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required', 'max:255'],
            'last_name' => ['required', 'max:255'],
            'role_ids' => ['sometimes', 'array'],
            'tags' => ['sometimes', 'array'],
            'tags.*.name' => ['required', 'string', 'distinct'],
            'tags.*.color' => ['required', 'string', 'hex_color'],
            'phone' => [
                'nullable',
                'numeric',
                (new Phone())->country('SA'),
                Rule::uniqueTenant('employees', 'phone'),
            ],
            'role_ids.*' => 'required|exists:roles,uuid',
            'email' => ['required', 'email', Rule::uniqueTenant('employees', 'email')],
            'position' => 'required|max:60',
            'number' => ['required', 'max:30', Rule::uniqueTenant('employees', 'number')],
            'department_id' => ['required', Rule::existsTenant('departments', 'id')],
            'manager_id' => ['nullable', Rule::existsTenant('employees', 'id')],
            'preferred_language' => [
                'sometimes',
                'string',
                new Enum(EmployeePreferredLanguage::class),
            ],
        ];
    }
}
