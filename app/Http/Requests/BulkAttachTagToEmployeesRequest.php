<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BulkAttachTagToEmployeesRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tags' => ['required', 'array'],
            'tags.*.name' => ['required', 'string', 'distinct'],
            'tags.*.color' => ['required', 'string', 'hex_color'],
            'all' => ['required', 'boolean'],
            'employees' => ['array', Rule::requiredIf(!$this->boolean('all'))],
            'distinct',
            Rule::exists('employees', 'id')->where(
                fn(Builder $query) => $query->where('tenant_id', currentTenant()->id)
            ),
        ];
    }
}
