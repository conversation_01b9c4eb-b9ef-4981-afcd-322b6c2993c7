<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WorkdayRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required'],
            'start_time' => ['required', 'date_format:H:i'],
            'end_time' => ['required', 'date_format:H:i'],
            'color' => ['required', 'hex_color'],
            'flexible_time_before' => ['required', 'date_format:H:i'],
            'flexible_time_after' => ['required', 'date_format:H:i'],
            'prevent_checkout_after' => ['required', 'date_format:H:i', 'after:end_time'],
        ];
    }
}
