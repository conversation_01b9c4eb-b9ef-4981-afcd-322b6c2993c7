<?php

namespace App\Http\Requests\Mobile\V2;

use App\Validations\ValidMaxPeriod;
use Carbon\CarbonInterval;
use Illuminate\Foundation\Http\FormRequest;

class ScheduledShiftsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'from' => ['required', 'date', 'after_or_equal:today'],
            'to' => ['required', 'date', 'after:from'],
        ];
    }

    public function after(): array
    {
        return [
            new ValidMaxPeriod(
                maxLimit: CarbonInterval::days(31)->cascade(),
                startDateKey: 'from',
                endDateKey: 'to',
                message: __('date range exceed limit')
            ),
        ];
    }
}
