<?php

namespace App\Http\Requests\Mobile\V2;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BulkApprovalRequestUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'all' => [Rule::requiredIf(!$this->approval_requests), 'boolean'],
            'approval_requests' => [
                Rule::requiredIf(!$this->all),
                'array',
                Rule::existsTenant('approval_requests', 'id'),
            ],
        ];
    }
}
