<?php

namespace App\Http\Requests\Mobile\V2;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BulkLeaveUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'all' => [Rule::requiredIf(!$this->leaves), 'boolean'],
            'leaves' => [
                Rule::requiredIf(!$this->all),
                'array',
                Rule::existsTenant('leaves', 'id'),
            ],
        ];
    }
}
