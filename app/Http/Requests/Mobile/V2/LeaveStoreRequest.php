<?php

namespace App\Http\Requests\Mobile\V2;

use App\Enums\Folder;
use Illuminate\Foundation\Http\FormRequest;
use Storage;

class LeaveStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'from' => ['required', 'date'],
            'to' => ['required', 'date', 'after_or_equal:from'],
            'reason' => ['required', 'string', 'max:255'],
            'attachment' => ['nullable', 'string'],
        ];
    }

    protected function passedValidation(): void
    {
        $attachmentUrl = $this->input('attachment');

        if (!$attachmentUrl) {
            return;
        }

        $temporaryFileName = Folder::LEAVE_ATTACHMENTS->tempPath($attachmentUrl);

        abort_if(
            !Storage::exists($temporaryFileName) && !app()->runningUnitTests(),
            400,
            "File [$attachmentUrl] is not uploaded"
        );

        $permanentFilePath = Folder::LEAVE_ATTACHMENTS->path($attachmentUrl);

        Storage::move($temporaryFileName, $permanentFilePath);
    }
}
