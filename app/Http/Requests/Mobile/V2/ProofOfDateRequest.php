<?php

namespace App\Http\Requests\Mobile\V2;

use App\Enums\ProofStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProofOfDateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'filter.status' => ['nullable', 'array'],
            'filter.status.*' => ['required', Rule::enum(ProofStatus::class)],
            'filter.from' => ['required', 'date', 'date_format:Y-m-d'],
            'filter.to' => ['required', 'date', 'date_format:Y-m-d'],
            'per_page' => ['nullable', 'integer', 'min:1'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }
}
