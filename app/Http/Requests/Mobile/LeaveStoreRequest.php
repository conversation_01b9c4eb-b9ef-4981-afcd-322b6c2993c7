<?php

namespace App\Http\Requests\Mobile;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @deprecated
 */
class LeaveStoreRequest extends FormRequest
{
    public function rules()
    {
        return [
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'reason' => 'required|string|max:255',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'from_date.required' => 'The start date is required.',
            'from_date.date' => 'The start date must be a valid date.',
            'to_date.required' => 'The end date is required.',
            'to_date.date' => 'The end date must be a valid date.',
            'to_date.after_or_equal' => 'The end date must be after or equal to the start date.',
            'reason.required' => 'The reason is required.',
            'reason.string' => 'The reason must be a string.',
            'reason.max' => 'The reason may not be greater than :max characters.',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
}
