<?php

namespace App\Http\Requests;

use App\Enums\LocationSelection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DeviceRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'location_selection' => ['required', Rule::enum(LocationSelection::class)],
            'location_id' => [
                'exclude_if:location_selection,' . LocationSelection::DeviceLocation->value,
                'required',
                Rule::existsTenant('locations', 'id'),
            ],
        ];
    }
}
