<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class LocationStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                Rule::uniqueTenant('locations', 'name')->withoutTrashed(),
            ],
            'lat' => ['required', 'numeric'],
            'lng' => ['required', 'numeric'],
            'radius' => ['required', 'numeric'],
            'timezone' => ['required', 'string'],
            'is_default' => ['required', 'boolean'],
            // TODO: change to required after updating frontend
            'automatic' => ['sometimes', 'boolean'],
            'check_out_radius' => ['sometimes', 'numeric', 'gte:0'],
        ];
    }

    public function after(): array
    {
        return [
            function (Validator $validator) {
                if (!$this->automatic) {
                    return;
                }

                if (!$this->float('check_out_radius')) {
                    $validator
                        ->errors()
                        ->add(
                            'check_out_radius',
                            __(
                                'check out radius should be greater than 0 if automatic checkin is true'
                            )
                        );
                }
            },
        ];
    }
}
