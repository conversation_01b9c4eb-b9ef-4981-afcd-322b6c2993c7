<?php

namespace App\Http\Requests\External;

use App\Enums\EmployeePreferredLanguage;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Propaganistas\LaravelPhone\Rules\Phone;

class StoreEmployeeRequest extends BaseEmployeeRequest
{
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'max:255'],
            'last_name' => ['nullable', 'max:255'],
            'roles' => ['sometimes', 'array'],
            'roles.*' => ['required', Rule::exists('roles', 'name')],
            'department_id' => ['nullable', 'string'],
            'department_name' => ['nullable', 'string'],
            'manager_email' => [
                'sometimes',
                'email',
                'string',
                'max:255',
                Rule::existsTenant('employees', 'email'),
            ],
            'manager_id' => ['sometimes', Rule::existsTenant('employees', 'id')],
            'phone' => [
                'nullable',
                'numeric',
                (new Phone())->country('SA'),
                Rule::uniqueTenant('employees'),
            ],
            'email' => ['required', Rule::uniqueTenant('employees')],
            'position' => ['nullable', 'max:255'],
            'number' => ['required', Rule::uniqueTenant('employees')],
            'preferred_language' => [
                'sometimes',
                'string',
                new Enum(EmployeePreferredLanguage::class),
            ],
            'is_active' => ['sometimes', 'boolean'],
        ];
    }
}
