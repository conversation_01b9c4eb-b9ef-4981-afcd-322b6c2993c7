<?php

namespace App\Http\Requests\External;

use App\Models\Employee;
use App\Services\GetEmployeeFromRequestService;
use App\Validation\ValidateDepartmentNameOrId;
use App\Validation\ValidateManagerNameOrId;
use Illuminate\Foundation\Http\FormRequest;

class BaseEmployeeRequest extends FormRequest
{
    protected ?Employee $employee = null;

    public function employee(): ?Employee
    {
        return $this->employee ?? (new GetEmployeeFromRequestService(required: false))->handle();
    }

    protected function prepareForValidation(): void
    {
        if (phone($this->phone, 'SA')->isValid()) {
            $this->merge([
                'phone' => phone($this->phone, 'SA')->formatE164(),
            ]);
        }

        // otherwise, keep the original value, it will fail validation later,
        // or it's not provided, which is fine
    }

    // added to follow arch test `every request should have rules function`
    public function rules(): array
    {
        return [];
    }

    public function after(): array
    {
        return [new ValidateDepartmentNameOrId(), new ValidateManagerNameOrId()];
    }
}
