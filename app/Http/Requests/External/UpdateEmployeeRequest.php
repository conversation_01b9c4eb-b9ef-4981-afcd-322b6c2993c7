<?php

namespace App\Http\Requests\External;

use App\Enums\EmployeePreferredLanguage;
use Illuminate\Validation\Rule;
use Propaganistas\LaravelPhone\Rules\Phone;

class UpdateEmployeeRequest extends BaseEmployeeRequest
{
    public function rules(): array
    {
        return [
            'first_name' => ['sometimes', 'max:255'],
            'last_name' => ['sometimes', 'max:255'],
            'phone' => [
                'sometimes',
                'numeric',
                (new Phone())->country('SA'),
                Rule::uniqueTenant('employees')->ignore($this->employee()),
            ],
            'email' => [
                'sometimes',
                'email',
                Rule::uniqueTenant('employees', 'email')->ignore($this->employee()),
            ],
            'roles' => ['sometimes', 'array'],
            'roles.*' => ['required', Rule::exists('roles', 'name')],
            'position' => ['sometimes', 'max:255'],
            'number' => ['sometimes', Rule::uniqueTenant('employees')->ignore($this->employee())],
            'department_id' => ['sometimes'],
            'department_name' => ['sometimes', 'string'],
            'manager_id' => ['sometimes'],
            'manager_name' => ['sometimes', 'string'],
            'preferred_language' => [
                'sometimes',
                'string',
                Rule::enum(EmployeePreferredLanguage::class),
            ],
            'is_active' => ['sometimes', 'boolean'],
        ];
    }
}
