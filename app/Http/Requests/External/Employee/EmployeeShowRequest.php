<?php

namespace App\Http\Requests\External\Employee;

use Illuminate\Foundation\Http\FormRequest;

class EmployeeShowRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'id' => ['required_without_all:email,employee_id'],
            'email' => ['required_without_all:id,employee_id', 'email'],
            'employee_id' => ['required_without_all:id,email', 'integer'],
        ];
    }
}
