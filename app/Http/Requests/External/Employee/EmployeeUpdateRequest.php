<?php

namespace App\Http\Requests\External\Employee;

use App\Enums\DurationStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'shift_id' => ['nullable', 'integer', Rule::existsTenant('shifts', 'id')],
            'remote_work' => ['required'],
            'random_notification' => ['required'],
            'locations' => ['nullable', 'array'],
            'random_proof_notification_config' => ['sometimes', 'array'],
            'random_proof_notification_config.enabled' => ['sometimes', 'bool'],
            'random_proof_notification_config.inherited' => ['sometimes', 'bool'],
            'random_proof_notification_config.count' => [
                'sometimes',
                'nullable',
                'int',
                'min:0',
                'max:15',
            ],
            'locations.*.id' => [
                'required',
                Rule::exists('locations', 'id')->where('team_id', auth()->id()),
            ],
            'locations.*.type' => ['required', Rule::in(DurationStatus::values())],
            'locations.*.start_date' =>
                'required_if:locations.*.type,' . DurationStatus::TEMPORARY->value,
            'locations.*.end_date' =>
                'required_if:locations.*.type,' . DurationStatus::TEMPORARY->value,
        ];
    }
}
