<?php

namespace App\Http\Requests\External\Leaves;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateLeaveRequest extends FormRequest
{
    public function rules(): array
    {
        $validationRules = [
            'email' => [Rule::requiredIf(!$this->route('leave')), 'email'],
            'new_from_date' => ['required', 'date:Y-m-d'],
            'new_to_date' => ['required', 'date:Y-m-d', 'after_or_equal:from_date'],
            'reason' => ['nullable', 'string', 'max:255'],
        ];

        if (!$this->route('leave')) {
            $validationRules['old_from_date'] = ['required', 'date:Y-m-d'];
            $validationRules['old_to_date'] = [
                'required',
                'date:Y-m-d',
                'after_or_equal:from_date',
            ];
        }

        return $validationRules;
    }
}
