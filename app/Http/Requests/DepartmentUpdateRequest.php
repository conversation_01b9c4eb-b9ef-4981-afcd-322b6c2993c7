<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DepartmentUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'remote_work' => [
                'required',
                Rule::in(['inherited', 'allowed', 'allowed_with_approval', 'not_allowed']),
            ],
            'random_proof_notification_config' => ['required', 'array'],
            'random_proof_notification_config.enabled' => ['required', 'bool'],
            'random_proof_notification_config.inherited' => ['required', 'bool'],
            'random_proof_notification_config.count' => [
                'present',
                'nullable',
                'int',
                'min:0',
                'max:15',
            ],
        ];
    }
}
