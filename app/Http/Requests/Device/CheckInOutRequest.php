<?php

namespace App\Http\Requests\Device;

use App\Models\Device;
use App\Models\Employee;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class CheckInOutRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'identifier' => ['required', 'string'],
        ];
    }

    public function employee(): Employee
    {
        $employee = Employee::whereAny(['email', 'number'], $this->identifier)->first();

        if (!$employee) {
            throw ValidationException::withMessages([
                'identifier' => __('This employee does not exists'),
            ]);
        }

        return $employee;
    }

    public function device(): Device
    {
        return auth()->user();
    }
}
