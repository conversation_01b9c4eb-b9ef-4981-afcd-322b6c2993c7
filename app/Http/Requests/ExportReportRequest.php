<?php

namespace App\Http\Requests;

use App\Validations\ValidMaxPeriod;
use Carbon\CarbonInterval;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ExportReportRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => ['required', 'in:monthly,daily'],
            'start_date' => ['required', 'date'],
            'end_date' => [
                'required_if:type,monthly',
                'exclude_if:type,daily',
                'date',
                'after:start_date',
            ],
            'departments_ids' => ['nullable', 'array', Rule::existsTenant('departments', 'id')],
            'employees_ids' => ['nullable', 'array', Rule::existsTenant('employees', 'id')],
        ];
    }

    public function after(): array
    {
        return $this->type === 'monthly'
            ? [
                new ValidMaxPeriod(
                    maxLimit: CarbonInterval::month(6),
                    startDateKey: 'start_date',
                    endDateKey: 'end_date',
                    message: __('report date range exceed limit')
                ),
            ]
            : [];
    }
}
