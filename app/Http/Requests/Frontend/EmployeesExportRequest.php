<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeesExportRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'departments_ids' => ['nullable', 'array'],
            'departments_ids.*' => ['required', 'integer', Rule::existsTenant('departments', 'id')],
            'employees_ids' => ['nullable', 'array'],
            'employees_ids.*' => ['required', 'integer', Rule::existsTenant('employees', 'id')],
            'direct_managers' => ['nullable', 'array'],
            'direct_managers.*' => ['required', 'integer', Rule::existsTenant('employees', 'id')],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['required', 'integer', Rule::existsTenant('tags', 'id')],
            'locations' => ['nullable', 'array'],
            'locations.*' => ['required', 'integer', Rule::existsTenant('locations', 'id')],
            'shifts' => ['nullable', 'array'],
            'shifts.*' => ['required', 'integer', Rule::existsTenant('shifts', 'id')],
            'show_inactive_employees' => ['nullable', 'boolean'],
        ];
    }
}
