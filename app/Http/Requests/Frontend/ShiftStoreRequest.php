<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;
use Str;

class ShiftStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                Rule::uniqueTenant('shifts', 'name')->withoutTrashed(),
            ],
            //todo: make it required after the frontend is updated
            'is_default' => ['sometimes', 'boolean'],
            'flexible_hours' => ['required'],
            'force_checkout' => ['required', 'date_format:H:i'],
            'weekdays' => ['required', 'array'],
            'weekdays.*.value' => [
                'required',
                'string',
                Rule::in([
                    'sunday',
                    'monday',
                    'tuesday',
                    'wednesday',
                    'thursday',
                    'friday',
                    'saturday',
                ]),
            ],
            'weekdays.*.enabled' => ['required', 'boolean'],
            'weekdays.*.from' => ['required_if:weekdays.*.value,1', 'date_format:H:i'],
            'weekdays.*.to' => [
                'required_if:weekdays.*.value,1',
                'date_format:H:i',
                'different:weekdays.*.from',
            ],
            'weekdays.*.next_day_checkout' => ['nullable', 'boolean'],
            'weekdays.*.prevent_checkout_after' => [
                'nullable',
                'required_if:weekdays.*.next_day_checkout,1',
                'date_format:H:i',
            ],
        ];
    }

    public function after(): array
    {
        return [
            function (Validator $validator) {
                foreach ($this->input('weekdays') as $weekday) {
                    if (
                        $weekday['enabled'] &&
                        $weekday['from'] > $weekday['to'] &&
                        !$weekday['next_day_checkout']
                    ) {
                        $validator->errors()->add(
                            'weekdays',
                            __('the from field should be before the to field for :day', [
                                'day' => __(Str::title($weekday['value'])),
                            ])
                        );
                    }
                }
            },
            function (Validator $validator) {
                foreach ($this->input('weekdays') as $weekday) {
                    if (
                        $weekday['enabled'] &&
                        isset($weekday['prevent_checkout_after']) &&
                        $weekday['to'] > $weekday['prevent_checkout_after'] &&
                        $weekday['next_day_checkout']
                    ) {
                        $validator->errors()->add(
                            'weekdays',
                            __(
                                'the to after field should be before the prevent checkout field for :day',
                                [
                                    'day' => __(Str::title($weekday['value'])),
                                ]
                            )
                        );
                    }
                }
            },
        ];
    }
}
