<?php

namespace App\Http\Requests\Frontend;

use App\Enums\DurationStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AttachEmployeesToLocationRequest extends FormRequest
{
    public function rules(): array
    {
        $rules = [
            'all' => [Rule::requiredIf(!$this->employees), 'boolean'],
            'employees' => [
                Rule::requiredIf(!$this->all),
                'array',
                Rule::existsTenant('employees', 'id'),
            ],
        ];

        // when attaching employees to a single location
        if ($this->route('location')) {
            return [
                ...$rules,
                'type' => ['required', 'string', Rule::in(DurationStatus::values())],
                ...$this->type === DurationStatus::PERMANENT->value
                    ? []
                    : [
                        'start_date' => ['required', 'date_format:Y-m-d'],
                        'end_date' => ['required', 'date_format:Y-m-d', 'after:start_date'],
                    ],
            ];
        }

        // when attaching employees to multiple locations
        return [
            ...$rules,
            'locations' => [Rule::requiredIf(!$this->route('location'))],
            'locations.*.id' => ['required', 'int', Rule::existsTenant('locations', 'id')],
            'locations.*.start_date' => [
                'required_if:locations.*.type,' . DurationStatus::TEMPORARY->value,
                'date_format:Y-m-d',
            ],
            'locations.*.end_date' => [
                'required_if:locations.*.type,' . DurationStatus::TEMPORARY->value,
                'date_format:Y-m-d',
                'after:locations.*.start_date',
            ],
            'locations.*.type' => ['required', 'string', Rule::in(DurationStatus::values())],
        ];
    }
}
