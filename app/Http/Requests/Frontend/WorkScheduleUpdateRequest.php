<?php

namespace App\Http\Requests\Frontend;

use App\Enums\Days;
use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkScheduleUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        $workSchedule = $this->route('workSchedule');

        return [
            'type' => ['required', Rule::enum(WorkScheduleType::class)],
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::uniqueTenant('work_schedules', 'name')->ignore($workSchedule->id),
            ],
            'work_and_off_days_distribution_type' => [
                'required',
                Rule::enum(WorkAndOffDaysDistributionType::class),
            ],
            'specific_days' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::SpecificDays->value,
                'nullable',
                'array',
                'min:1',
            ],
            'specific_days.*' => ['array', Rule::enum(Days::class)],
            'off_days_after_each_repetition' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'start_date' => ['required', 'date'],
            'workdays' => ['required', 'array', 'min:1'],
            'workdays.*.workday_id' => [
                'required',
                'integer',
                Rule::existsTenant('workdays', 'id'),
            ],
            'workdays.*.work_days_number' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'integer',
                'min:1',
                'max:30',
            ],
            'workdays.*.off_days_number' => [
                'required_if:work_and_off_days_distribution_type,' .
                WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'workdays.*.repetitions_number' => [
                'required_if:type,' . WorkScheduleType::Rotational->value,
                'nullable',
                'integer',
                'min:1',
                'max:52',
            ],
            'assignments' => ['nullable', 'array'],
            'assignments.employees' => ['nullable', 'array'],
            'assignments.employees.*' => ['integer', Rule::existsTenant('employees', 'id')],
            'assignments.departments' => ['nullable', 'array'],
            'assignments.departments.*' => ['integer', Rule::existsTenant('departments', 'id')],
            'assignments.direct_managers' => ['nullable', 'array'],
            'assignments.direct_managers.*' => ['integer', Rule::existsTenant('employees', 'id')],
            'assignments.tags' => ['nullable', 'array'],
            'assignments.tags.*' => ['integer', Rule::existsTenant('tags', 'id')],
            'assignments.locations' => ['nullable', 'array'],
            'assignments.locations.*' => ['integer', Rule::existsTenant('locations', 'id')],
        ];
    }

    public function messages(): array
    {
        return [
            'workdays.required' => 'At least one workday must be selected.',
            'workdays.min' => 'At least one workday must be selected.',
            'specific_days.required_if' =>
                'Specific days must be selected when using specific days distribution.',
            'specific_days.min' => 'At least one day must be selected.',
            'off_days_after_each_repetition.required_if' =>
                'Off days after each repetition is required when using number of days distribution.',
            'workdays.*.work_days_number.required_if' =>
                'Work days number is required when using number of days distribution.',
            'workdays.*.off_days_number.required_if' =>
                'Off days number is required when using number of days distribution.',
            'workdays.*.repetitions_number.required_if' =>
                'Repetitions number is required for rotational schedules.',
        ];
    }

    protected function prepareForValidation(): void
    {
        // Ensure rotational schedules have multiple workdays
        if (
            $this->type === WorkScheduleType::Rotational->value &&
            count($this->workdays ?? []) < 2
        ) {
            $this->merge([
                'workdays' => [], // This will trigger validation error
            ]);
        }
    }
}
