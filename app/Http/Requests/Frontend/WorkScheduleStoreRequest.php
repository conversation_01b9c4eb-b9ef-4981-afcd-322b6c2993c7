<?php

namespace App\Http\Requests\Frontend;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkScheduleStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => ['required', Rule::enum(WorkScheduleType::class)],
            'name' => ['required', 'string', 'max:255'],
            'work_and_off_days_distribution_type' => ['required', Rule::enum(WorkAndOffDaysDistributionType::class)],
            'specific_days' => [
                'required_if:work_and_off_days_distribution_type,' . WorkAndOffDaysDistributionType::SpecificDays->value,
                'nullable',
                'array',
                'min:1',
            ],
            'specific_days.*' => [
                'string',
                'in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
            ],
            'off_days_after_each_repetition' => [
                'required_if:work_and_off_days_distribution_type,' . WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'start_date' => ['required', 'date', 'after_or_equal:today'],
            'workdays' => ['required', 'array', 'min:1'],
            'workdays.*.workday_id' => ['required', 'integer', Rule::existsTenant('workdays', 'id')],
            'workdays.*.work_days_number' => [
                'required_if:work_and_off_days_distribution_type,' . WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'integer',
                'min:1',
                'max:30',
            ],
            'workdays.*.off_days_number' => [
                'required_if:work_and_off_days_distribution_type,' . WorkAndOffDaysDistributionType::NumberOfDays->value,
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'workdays.*.repetitions_number' => [
                'required_if:type,' . WorkScheduleType::Rotational->value,
                'nullable',
                'integer',
                'min:1',
                'max:52',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'workdays.required' => 'At least one workday must be selected.',
            'workdays.min' => 'At least one workday must be selected.',
            'specific_days.required_if' => 'Specific days must be selected when using specific days distribution.',
            'specific_days.min' => 'At least one day must be selected.',
            'off_days_after_each_repetition.required_if' => 'Off days after each repetition is required when using number of days distribution.',
            'workdays.*.work_days_number.required_if' => 'Work days number is required when using number of days distribution.',
            'workdays.*.off_days_number.required_if' => 'Off days number is required when using number of days distribution.',
            'workdays.*.repetitions_number.required_if' => 'Repetitions number is required for rotational schedules.',
        ];
    }

    protected function prepareForValidation(): void
    {
        // Ensure rotational schedules have multiple workdays
        if ($this->type === WorkScheduleType::Rotational->value && count($this->workdays ?? []) < 2) {
            $this->merge([
                'workdays' => [], // This will trigger validation error
            ]);
        }
    }
}
