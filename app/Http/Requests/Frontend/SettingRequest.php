<?php

namespace App\Http\Requests\Frontend;

use App\Enums\ApprovalType;
use App\Enums\CheckoutReminderConfig;
use App\Enums\EarlyLateNestingPolicy;
use App\Enums\EarlyLatePeriodPolicy;
use App\Enums\RandomProofOfAttendanceDeadline;
use App\Enums\SheetMode;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SettingRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        // convert any attribute yes/no to boolean
        $this->collect()
            ->filter(fn($value, $key) => $value === 'yes' || $value === 'no')
            ->each(fn($value, $key) => $this->merge([$key => $value === 'yes']));
    }

    public function rules(): array
    {
        return [
            'employees_weekly_summary' => ['sometimes', 'boolean'],

            'early_late_config' => ['sometimes', 'array'],
            'early_late_config.enabled' => ['sometimes', 'bool'],
            'early_late_config.period_policy' => [
                'sometimes',
                Rule::enum(EarlyLatePeriodPolicy::class),
            ],
            'early_late_config.nesting_policy' => [
                'sometimes',
                Rule::enum(EarlyLateNestingPolicy::class),
            ],
            'early_late_config.sheet_mode' => [
                'sometimes',
                Rule::enum(SheetMode::class)->except(SheetMode::SingleSheet),
            ],

            'early_late_config.excluded_tags' => ['sometimes', 'array'],
            'early_late_config.excluded_tags.*' => [
                'required',
                'integer',
                Rule::existsTenant('tags', 'id'),
            ],

            'remote_work' => [
                'sometimes',
                Rule::in(['allowed', 'allowed_with_approval', 'not_allowed']),
            ],
            'free_checkout' => ['sometimes', Rule::in(['allowed', 'not_allowed'])],
            'approval_requests_limit' => ['sometimes', 'numeric', 'min:0'],

            'vacation_weekend' => ['sometimes', 'boolean'],
            'leave_request' => ['sometimes', 'boolean'],
            'approval_request' => ['sometimes', 'boolean'],

            'permission_request' => ['sometimes', 'boolean'],
            'permission_request_monthly_limit_hours' => [
                'sometimes',
                'integer',
                'min:0',
                'max:' . 31 * 24,
            ],
            'permission_request_daily_limit_hours' => [
                'sometimes',
                'integer',
                'min:0',
                'max:' . 23,
            ],

            'remote_work_days_yearly_limit' => [
                'sometimes',
                'nullable',
                'integer',
                'min:0',
                'max:366',
            ],
            'remote_work_days_monthly_limit' => [
                'sometimes',
                'nullable',
                'integer',
                'min:0',
                'max:31',
            ],
            'remote_work_days_weekly_limit' => [
                'sometimes',
                'nullable',
                'integer',
                'min:0',
                'max:7',
            ],
            'random_proof_notification_config' => ['sometimes', 'array'],
            'random_proof_notification_config.enabled' => ['sometimes', 'bool'],
            'random_proof_notification_config.count' => [
                'sometimes',
                'nullable',
                'int',
                'min:0',
                'max:15',
            ],
            'random_proof_notification_config.deadline' => [
                'sometimes',
                Rule::enum(RandomProofOfAttendanceDeadline::class),
            ],
            'checkout_reminder_config' => ['sometimes', Rule::enum(CheckoutReminderConfig::class)],
            'map_report_thresholds.yellow.min' => ['sometimes', 'int', 'max:96', 'min:1'],
            'map_report_thresholds.yellow.max' => [
                'sometimes',
                'int',
                'max:97',
                'gt:map_report_thresholds.yellow.min',
            ],

            'approval_type' => ['sometimes', Rule::enum(ApprovalType::class)],

            'employee_statement_config' => ['sometimes', 'array'],
            'employee_statement_config.enabled' => ['sometimes', 'bool'],

            'employee_statement_config.prevent_requests_enabled' => ['sometimes', 'bool'],
            'employee_statement_config.days_before_preventing_requests' => [
                'sometimes',
                'int',
                'min:1',
                'max:30',
            ],

            'employee_statement_config.late_checkin_buffer_minutes' => [
                'sometimes',
                'nullable',
                'int',
                'min:0',
                'max:60',
            ],
            'employee_statement_config.early_checkout_buffer_minutes' => [
                'sometimes',
                'nullable',
                'int',
                'min:0',
                'max:60',
            ],
            'employee_statement_config.absent_enabled' => ['sometimes', 'bool'],
        ];
    }
}
