<?php

namespace App\Http\Requests\Frontend;

use App\Enums\DurationStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AttachEmployeesToShiftRequest extends FormRequest
{
    public function rules(): array
    {
        $rules = [
            'all' => [Rule::requiredIf(!$this->employees), 'boolean'],
            'employees' => [
                Rule::requiredIf(!$this->all),
                'array',
                Rule::existsTenant('employees', 'id'),
            ],
            'type' => ['required', 'string', Rule::in(DurationStatus::values())],
        ];

        if ($this->type !== DurationStatus::PERMANENT->value) {
            $rules['start_date'] = ['required', 'date_format:Y-m-d'];
            $rules['end_date'] = ['required', 'date_format:Y-m-d', 'after:start_date'];
        }

        return $rules;
    }
}
