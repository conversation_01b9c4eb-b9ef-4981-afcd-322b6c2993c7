<?php

namespace App\Queries;

use App\Enums\DelegationType;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Collection;

class ManagerEmployeesApprovalRequestQuery
{
    public static function build(Employee $employee): Builder|Relation|null
    {
        $ids = collect();

        $delegatedTypes = $employee
            ->managerDelegations()
            ->pluck('type')
            ->map(fn(DelegationType $type) => $type->delegationTypeToApprovalRequestType());

        // 1. collect the approval requests that this manager is directly managing (excluding delegated approval requests)
        $ids->push(
            ...self::collectApprovalRequestsIdsForDirectlyManagedEmployees(
                employee: $employee,
                excludedTypes: $delegatedTypes
            )
        );

        // 2. collect the approval requests that this manager is managing by department (excluding delegated approval requests)
        $ids->push(
            ...self::collectApprovalRequestsIdsForManagedEmployeesByDepartment(
                employee: $employee,
                excludedTypes: $delegatedTypes
            )
        );

        // 3. collect the approval requests that this manager is managing by delegatee manager
        $ids->push(...self::collectApprovalRequestsIdsForDelegateeManagers(employee: $employee));

        return ApprovalRequest::whereIn('approval_requests.id', $ids->unique());
    }

    public static function collectApprovalRequestsIdsForDirectlyManagedEmployees(
        Employee $employee,
        Collection $includedTypes = null,
        Collection $excludedTypes = null
    ): Collection {
        return $employee
            ->directlyManagedEmployeesApprovalRequests()
            ->belongToActiveEmployee()
            ->inLayerOne()
            ->when($includedTypes, fn($query) => $query->whereIn('type', $includedTypes))
            ->when($excludedTypes, fn($query) => $query->whereNotIn('type', $excludedTypes))
            ->pluck('approval_requests.id');
    }

    public static function collectApprovalRequestsIdsForManagedEmployeesByDepartment(
        Employee $employee,
        Collection $includedTypes = null,
        Collection $excludedTypes = null
    ): Collection {
        return $employee
            ->managedEmployeesByDepartmentApprovalRequests()
            ->belongToActiveEmployee()
            ->when($includedTypes, fn($query) => $query->whereIn('type', $includedTypes))
            ->when($excludedTypes, fn($query) => $query->whereNotIn('type', $excludedTypes))
            ->where(function (Builder $query) {
                $query
                    ->where(function (Builder $query) {
                        $query
                            ->whereHas('employee', fn($query) => $query->managedByDepartment())
                            ->inLayerOne();
                    })
                    ->orWhere(function (Builder $query) {
                        $query
                            ->whereHas('employee', fn($query) => $query->directlyManaged())
                            ->inLayerTwo();
                    });
            })
            ->pluck('approval_requests.id');
    }

    public static function collectApprovalRequestsIdsForDelegateeManagers(
        Employee $employee
    ): Collection {
        $delegatedManagers = $employee->delegateeManagers()->active()->get();

        $delegatedTypes = $employee
            ->employeeDelegations()
            ->whereIn('delegatee_id', $delegatedManagers->pluck('id'))
            ->pluck('type')
            ->map(fn(DelegationType $type) => $type->delegationTypeToApprovalRequestType());

        return $delegatedManagers->flatMap(
            fn(Employee $manager) => [
                ...self::collectApprovalRequestsIdsForDirectlyManagedEmployees(
                    employee: $manager,
                    includedTypes: $delegatedTypes
                ),
                ...self::collectApprovalRequestsIdsForManagedEmployeesByDepartment(
                    employee: $manager,
                    includedTypes: $delegatedTypes
                ),
            ]
        );
    }
}
