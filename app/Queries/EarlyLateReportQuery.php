<?php

namespace App\Queries;

use App\Models\Department;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Scopes\EarlyLateFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EarlyLateReportQuery
{
    public function __construct(private ReportTask $reportTask)
    {
    }

    public function attendanceQuery(): HasMany
    {
        return $this->reportTask->team
            ->attendances()
            ->select([
                'id',
                'team_id',
                'employee_id',
                'shift_from',
                'shift_to',
                'date',
                'shift_id',
                'net_hours',
                'out_type',
                'in_type',
                'flexible_hours',
                'check_in',
                'check_out',
                'status',
                'check_in_location_id',
                'check_out_location_id',
                'check_in_device_id',
                'check_out_device_id',
            ])
            ->tap(new EarlyLateFilters($this->reportTask->data))
            ->orderBy(
                Employee::select('first_name')
                    ->whereColumn('employees.id', 'attendances.employee_id')
                    ->limit(1)
            )
            ->orderBy(
                Department::select('name')
                    ->join('employees', 'departments.id', '=', 'employees.department_id')
                    ->whereColumn('employees.id', 'attendances.employee_id')
                    ->limit(1)
            )
            ->orderBy('date');
    }

    public function employeeQuery(): Builder
    {
        return Employee::whereIn('id', $this->attendanceQuery()->pluck('employee_id'));
    }
}
