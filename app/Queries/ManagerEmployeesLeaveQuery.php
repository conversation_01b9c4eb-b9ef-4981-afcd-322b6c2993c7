<?php

namespace App\Queries;

use App\Models\Employee;
use App\Models\Leave;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Collection;

class ManagerEmployeesLeaveQuery
{
    public static function build(Employee $employee): Builder|Relation
    {
        // manager can't access to pending leave requests when delegating leave requests to another employee
        if ($employee->leaveDelegatedEmployee()->exists()) {
            return Leave::whereIn('id', []);
        }

        $ids = collect();

        // 1. collect the leaves that this manager is directly managing
        $ids->push(...self::collectLeavesIdsForDirectlyManagedEmployees($employee));

        // 2. collect the leaves that this manager is managing by department
        $ids->push(...self::collectLeavesIdsForManagedEmployeesByDepartment($employee));

        // 3. collect the leaves that this manager is managing by delegatee manager
        $ids->push(...self::collectLeavesIdsForDelegateeManagers($employee));

        return Leave::whereIn('id', $ids->unique());
    }

    public static function collectLeavesIdsForDirectlyManagedEmployees(
        Employee $employee
    ): Collection {
        return $employee
            ->directlyManagedEmployeesLeaves()
            ->belongToActiveEmployee()
            ->inLayerOne()
            ->pluck('leaves.id');
    }

    public static function collectLeavesIdsForManagedEmployeesByDepartment(
        Employee $employee
    ): Collection {
        return $employee
            ->managedEmployeesByDepartmentLeaves()
            ->belongToActiveEmployee()
            ->where(function (Builder $query) {
                $query
                    ->where(function (Builder $query) {
                        $query
                            ->whereHas('employee', fn($query) => $query->managedByDepartment())
                            ->inLayerOne();
                    })
                    ->orWhere(function (Builder $query) {
                        $query
                            ->whereHas('employee', fn($query) => $query->directlyManaged())
                            ->inLayerTwo();
                    });
            })
            ->pluck('leaves.id');
    }

    public static function collectLeavesIdsForDelegateeManagers(Employee $employee): Collection
    {
        return $employee
            ->delegateeManagers()
            ->active()
            ->get()
            ->flatMap(
                fn(Employee $manager) => [
                    ...self::collectLeavesIdsForDirectlyManagedEmployees($manager),
                    ...self::collectLeavesIdsForManagedEmployeesByDepartment($manager),
                ]
            );
    }
}
