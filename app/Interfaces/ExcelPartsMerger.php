<?php

namespace App\Interfaces;

use App\Models\ReportTask;
use Illuminate\Support\Collection;
use OpenSpout\Writer\WriterInterface;
use Spatie\SimpleExcel\SimpleExcelWriter;

abstract class ExcelPartsMerger
{
    public function configureOptions(WriterInterface $writer)
    {
    }

    public function beforeMerging(SimpleExcelWriter $writer, ReportTask $reportTask): void
    {
    }

    abstract public function mergePart(
        SimpleExcelWriter $writer,
        Collection $values,
        ReportTask $reportTask
    ): void;
}
