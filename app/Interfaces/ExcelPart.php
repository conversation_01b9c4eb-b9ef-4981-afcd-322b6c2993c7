<?php

namespace App\Interfaces;

use App\Models\ReportTask;
use Illuminate\Support\Collection;

abstract class ExcelPart
{
    public function __construct(protected int|string $offset, protected ?int $limit = null)
    {
    }

    abstract public function fetchPartValues(ReportTask $reportTask): Collection;

    public function offset(): int
    {
        return $this->offset;
    }

    public function limit(): ?int
    {
        return $this->limit;
    }
}
