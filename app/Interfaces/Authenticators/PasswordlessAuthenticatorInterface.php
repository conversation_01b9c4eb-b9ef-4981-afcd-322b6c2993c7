<?php

namespace App\Interfaces\Authenticators;

use App\DTOs\Identifier;
use App\Models\Tenant;

interface PasswordlessAuthenticatorInterface
{
    public function sendOtp(Tenant $tenant, Identifier $identifier): bool; // identifier, e.g. email, mobile etc.

    public function resendOtp(Tenant $tenant, Identifier $identifier): bool;

    public function invalidateOtp(Tenant $tenant, Identifier $identifier): void;
}
