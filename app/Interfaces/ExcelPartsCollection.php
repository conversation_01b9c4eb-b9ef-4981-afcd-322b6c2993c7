<?php

namespace App\Interfaces;

use Illuminate\Support\Collection;

class ExcelPartsCollection
{
    /**
     * @param  Collection<ExcelPart>  $parts
     */
    private Collection $parts;

    public function __construct()
    {
        $this->parts = new Collection();
    }

    public function addPart(ExcelPart $part): void
    {
        $this->parts[] = $part;
    }

    public function parts(): Collection
    {
        return $this->parts;
    }
}
