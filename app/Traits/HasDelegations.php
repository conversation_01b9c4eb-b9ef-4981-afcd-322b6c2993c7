<?php

namespace App\Traits;

use App\Enums\DelegationType;
use App\Models\Delegation;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

trait HasDelegations
{
    public function delegatedEmployee(): HasOneThrough
    {
        return $this->hasOneThrough(
            Employee::class,
            Delegation::class,
            'delegatee_id',
            'id',
            'id',
            'delegated_id'
        );
    }

    public function delegateeEmployee(): HasOneThrough
    {
        return $this->hasOneThrough(
            Employee::class,
            Delegation::class,
            'delegated_id',
            'id',
            'id',
            'delegatee_id'
        );
    }

    public function remoteWorkDelegatedEmployee(): HasOneThrough
    {
        return $this->delegatedEmployee()->where('type', DelegationType::RemoteWorkRequest);
    }

    public function earlyLateDelegatedEmployee(): HasOneThrough
    {
        return $this->delegatedEmployee()->where('type', DelegationType::EarlyLate);
    }

    public function permissionDelegatedEmployee(): HasOneThrough
    {
        return $this->delegatedEmployee()->where('type', DelegationType::PermissionRequest);
    }

    public function leaveDelegatedEmployee(): HasOneThrough
    {
        return $this->delegatedEmployee()->where('type', DelegationType::LeaveRequest);
    }

    public function regularizationDelegatedEmployee(): HasOneThrough
    {
        return $this->delegatedEmployee()->where('type', DelegationType::RegularizationRequest);
    }

    public function remoteWorkDelegateeEmployee(): HasOneThrough
    {
        return $this->delegateeEmployee()->where('type', DelegationType::RemoteWorkRequest);
    }

    public function earlyLateDelegateeEmployee(): HasOneThrough
    {
        return $this->delegateeEmployee()->where('type', DelegationType::EarlyLate);
    }

    public function permissionDelegateeEmployee(): HasOneThrough
    {
        return $this->delegateeEmployee()->where('type', DelegationType::PermissionRequest);
    }

    public function leaveDelegateeEmployee(): HasOneThrough
    {
        return $this->delegateeEmployee()->where('type', DelegationType::LeaveRequest);
    }

    public function regularizationDelegateeEmployee(): HasOneThrough
    {
        return $this->delegateeEmployee()->where('type', DelegationType::RegularizationRequest);
    }

    public function loadDelegated()
    {
        return $this->load([
            'remoteWorkDelegatedEmployee',
            'earlyLateDelegatedEmployee',
            'permissionDelegatedEmployee',
            'leaveDelegatedEmployee',
            'regularizationDelegatedEmployee',
        ]);
    }

    public function loadDelegatee()
    {
        return $this->load([
            'remoteWorkDelegateeEmployee',
            'earlyLateDelegateeEmployee',
            'permissionDelegateeEmployee',
            'leaveDelegateeEmployee',
            'regularizationDelegateeEmployee',
        ]);
    }

    /**
     * Get the delegations this employee is delegating to other employees.
     *
     * @example $manager->managerDelegations; // Delegations created by this manager
     * */
    public function managerDelegations(): HasMany
    {
        return $this->hasMany(Delegation::class, 'delegatee_id');
    }

    /**
     * Get the delegations this employee is receiving from other employees.
     *
     * @example $employee->employeeDelegations; // Delegations received by this employee
     */
    public function employeeDelegations(): HasMany
    {
        return $this->hasMany(Delegation::class, 'delegated_id');
    }

    /**
     * Get the employees this employee is delegating tasks to.
     *
     * @example $manager->delegatedEmployees; // Employees receiving tasks from this manager
     */
    public function delegatedEmployees(): HasManyThrough
    {
        return $this->hasManyThrough(
            Employee::class,
            Delegation::class,
            'delegatee_id',
            'id',
            'id',
            'delegated_id'
        );
    }

    /**
     * Get the managers this employee is delegating tasks to.
     *
     * @example $employee->delegateeManagers; // Managers who are delegating tasks to this employee
     */
    public function delegateeManagers(): HasManyThrough
    {
        return $this->hasManyThrough(
            Employee::class,
            Delegation::class,
            'delegated_id',
            'id',
            'id',
            'delegatee_id'
        );
    }

    public function receiverOfEarlyLateReport(): Employee
    {
        return $this->earlyLateDelegatedEmployee ?? $this;
    }

    public function receiverOfLeaveRequest(): Employee
    {
        return $this->leaveDelegatedEmployee ?? $this;
    }

    public function receiverOfRemoteWorkRequest(): Employee
    {
        return $this->remoteWorkDelegatedEmployee ?? $this;
    }

    public function receiverOfPermissionRequest(): Employee
    {
        return $this->permissionDelegatedEmployee ?? $this;
    }

    public function receiverOfRegularizationRequest(): Employee
    {
        return $this->regularizationDelegatedEmployee ?? $this;
    }
}
