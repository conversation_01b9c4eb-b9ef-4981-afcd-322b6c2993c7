<?php

namespace App\Traits;

use App\Enums\ApprovalType;
use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Enums\RequestType;
use App\Models\Decision;
use App\Models\Employee;
use App\Services\GetRequestApproversService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Validation\UnauthorizedException;
use Str;

trait Decidable
{
    public function decisions(): MorphMany
    {
        return $this->morphMany(Decision::class, 'decidable');
    }

    public function scopeInLayerOne(Builder $query): Builder
    {
        return $query->doesntHave('decisions');
    }

    public function scopeInLayerTwo(Builder $query): Builder
    {
        return $query->whereHas(
            'decisions',
            fn(Builder $query) => $query->where('layer', DecisionLayer::First)
        );
    }

    public function updateOrCreateDecision(
        RequestStatus $status,
        DecisionLayer $decisionLayer,
        Employee $decider
    ): Decision {
        return $this->decisions()->updateOrCreate(
            [
                'layer' => $decisionLayer,
            ],
            [
                'team_id' => $this->team_id,
                'employee_id' => $this->employee_id,
                'decider_id' => $decider->id,
                'status' => $status,
            ]
        );
    }

    public function decisionByLayer(DecisionLayer $decisionLayer): Decision|null
    {
        return $this->decisions()->firstWhere('layer', $decisionLayer);
    }

    public function currentDecisionLayer(): DecisionLayer
    {
        if ($this->team->approval_type === ApprovalType::OneLayer) {
            return DecisionLayer::First;
        }

        $firstLayerDecision = $this->decisionByLayer(DecisionLayer::First);

        if (!$firstLayerDecision) {
            return DecisionLayer::First;
        }

        return DecisionLayer::Second;
    }

    public function isFullyApproved(): bool
    {
        $status = RequestStatus::fromStringOrInstance($this->status);

        // when the model is approved, no need to look for decisions
        if ($status->isApproved()) {
            return true;
        }

        // otherwise, let's look at decisions...
        $firstLayerDecision = $this->decisionByLayer(DecisionLayer::First);

        if ($this->team->approval_type === ApprovalType::OneLayer) {
            return (bool) $firstLayerDecision->status?->isApproved();
        }

        $secondLayerDecision = $this->decisionByLayer(DecisionLayer::Second);

        return $firstLayerDecision?->status->isApproved() &&
            $secondLayerDecision?->status->isApproved();
    }

    public function ensureEmployeeCanDecideOnLayer(Employee $employee, DecisionLayer $layer): void
    {
        $status = RequestStatus::fromStringOrInstance($this->status);

        $modelName = Str::lower(class_basename($this));

        if ($status->isNotPending()) {
            throw new UnauthorizedException(
                "You don't have permission to approve [$modelName]: [$this->id], [$layer->value]. [status is not pending]"
            );
        }

        $approver = (new GetRequestApproversService($this->employee))
            ->approvers()
            ->resolveCurrentApprover(layer: $layer, requestType: RequestType::fromModel($this));

        if ($employee->isNot($approver)) {
            throw new UnauthorizedException(
                "You don't have permission to approve [$modelName]: [$this->id], [$layer->value]. [$employee->name] is not responsible for layer [$layer->name]"
            );
        }
    }
}
