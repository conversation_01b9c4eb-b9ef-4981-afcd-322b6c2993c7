<?php

namespace App\Traits;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

trait PaginateOrGet
{
    public function scopePaginateOrGet(Builder $query): Collection|LengthAwarePaginator
    {
        if (request('all')) {
            return $query->get();
        }

        return $query->paginate(request('per_page'))->withQueryString();
    }
}
