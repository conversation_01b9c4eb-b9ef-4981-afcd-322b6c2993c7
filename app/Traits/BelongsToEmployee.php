<?php

namespace App\Traits;

use App\Models\Employee;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait BelongsToEmployee
{
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function scopeBelongToActiveEmployee(Builder $query): Builder
    {
        return $query->whereHas('employee', fn($query) => $query->active());
    }
}
