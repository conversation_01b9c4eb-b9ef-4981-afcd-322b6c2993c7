<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @codeCoverageIgnore until we are using it
 */
trait HasSamlConfig
{
    public function idpEntityId(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['idp_entity_id'] ?? null);
    }

    public function idpLoginUrl(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['idp_login_url'] ?? null);
    }

    public function idpLogoutUrl(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['idp_logout_url'] ?? null);
    }

    public function nameIdFormat(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->config['name_id_format'] ??
                'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress'
        );
    }

    public function idpX509Cert(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['idp_x509_cert'] ?? null);
    }

    public function singMessage(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->config['sign_message'] ?? true // default true
        );
    }

    public function emailClaim(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->config['email_claim'] ??
                'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'
        );
    }

    public function useNameIdForAuthentication(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->config['use_name_id_for_authentication'] ?? true
        );
    }

    public function firstNameClaim(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->config['first_name_claim'] ??
                'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname'
        );
    }

    public function lastNameClaim(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['last_name_claim'] ?? null);
    }

    public function employeeNumberClaim(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['employee_number_claim'] ?? null);
    }

    public function positionClaim(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['position_claim'] ?? null);
    }

    public function phoneClaim(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['phone_claim'] ?? null);
    }

    public function departmentNameClaim(): Attribute
    {
        return Attribute::make(get: fn() => $this->config['department_name_claim'] ?? null);
    }
}
