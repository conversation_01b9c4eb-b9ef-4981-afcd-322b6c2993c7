<?php

namespace App\Traits;

use App\Models\Team;
use App\Scopes\TenantScope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait BelongsToTenant
{
    protected static function bootBelongsToTenant()
    {
        static::addGlobalScope(new TenantScope());

        static::creating(function ($model) {
            $model->team_id ??= currentTeam()->id;
        });
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function scopeBelongToActiveTeam(Builder $query): Builder
    {
        return $query->whereHas('team', fn($query) => $query->active());
    }
}
