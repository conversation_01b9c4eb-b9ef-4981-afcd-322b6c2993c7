<?php

namespace App\Notifications;

use App\Models\Shift;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class NewPermanentShiftAssignedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly Shift $shift)
    {
    }

    public function via(): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toArray(): array
    {
        return [
            'shift' => $this->shift,
        ];
    }

    public function toOneSignal(): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('New shift assigned'))
            ->setBody(__('permanent shift is scheduled'))
            ->setData('shift_id', $this->shift->id);
    }
}
