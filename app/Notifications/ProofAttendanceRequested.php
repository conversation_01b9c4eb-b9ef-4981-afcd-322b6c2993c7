<?php

namespace App\Notifications;

use App\Enums\ProofMethod;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class ProofAttendanceRequested extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public ProofMethod $method)
    {
    }

    public function via(): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toOneSignal(): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('Verify your Attendance'))
            ->setBody(__('You have been requested to verify your attendance'))
            ->setData('notification_id', $this->id)
            ->setParameter('priority', 10)
            ->setParameter('ios_interruption_level', 'critical');
    }

    public function toArray(): array
    {
        return [
            'type' => 'PROOF_OF_ATTENDANCE',
        ];
    }

    public function shouldSend($notifiable)
    {
        return $notifiable->isCheckedIn();
    }
}
