<?php

namespace App\Notifications;

use App\Models\Employee;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(Employee $employee): MailMessage
    {
        return (new MailMessage())
            ->subject('Welcome - نورتنا')
            ->view('maizzle.emails.welcome', [
                'employee' => $employee,
                'todayInAr' => $employee->shift?->today_in?->locale('ar')->isoFormat('h:mm a'),
                'todayOutAr' => $employee->shift?->today_in?->locale('ar')->isoFormat('h:mm a'),
                'todayInEn' => $employee->shift?->today_in?->locale('en')->format('g:i A'),
                'todayOutEn' => $employee->shift?->today_in?->locale('en')->format('g:i A'),
                'todayHours' => $employee->shift?->durationOfDay()?->totalHours,
            ]);
    }
}
