<?php

namespace App\Notifications;

use App\Enums\IdentifierType;
use App\Models\Employee;
use App\Models\Otp;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class OneTimePasswordNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly Otp $otp,
        private readonly IdentifierType $identifierType
    ) {
    }

    public function via(): string
    {
        return $this->identifierType->channel();
    }

    public function toMail(Employee $notifiable): MailMessage
    {
        return (new MailMessage())
            ->subject('One Time Password - كلمة مرور لمرة واحدة')
            ->view('maizzle.emails.otp', [
                'employee' => $notifiable,
                'otp' => $this->otp,
            ]);
    }

    public function toSms(): array
    {
        return [
            'otp' => $this->otp,
            'message' => __('Your verification code is') . ' ' . $this->otp->value,
        ];
    }
}
