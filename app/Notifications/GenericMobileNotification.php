<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

/**
 * to be used when we want to send a generic mobile notification
 */
class GenericMobileNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public string $subject, public string $body)
    {
    }

    public function via($notifiable): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toOneSignal($notifiable): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject($this->subject)
            ->setBody($this->body);
    }

    public function toArray($notifiable): array
    {
        return [
            'subject' => $this->subject,
            'body' => $this->body,
        ];
    }
}
