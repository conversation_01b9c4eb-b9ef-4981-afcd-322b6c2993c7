<?php

namespace App\Notifications;

use App\Models\Leave;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class LeaveRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public Leave $leave)
    {
    }

    public function via($notifiable): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toArray($notifiable): array
    {
        return [
            'type' => 'LEAVE_REJECTED',
            'payload' => [
                'id' => $this->leave->id,
                'rejection_reason' => $this->leave->rejection_reason,
            ],
        ];
    }

    public function toOneSignal($notifiable): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('Attendance Request Rejected'))
            ->setBody(__('Your attendance request has been rejected by your department manager'));
    }
}
