<?php

namespace App\Notifications;

use App\Models\Shift;
use Carbon\CarbonImmutable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class TemporaryShiftChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct()
    {
    }

    public function via(): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toArray(): array
    {
        return [];
    }

    public function toOneSignal(): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('temporary shift has been changed'))
            ->setBody(__('Temporary shift is rescheduled'));
    }
}
