<?php

namespace App\Notifications;

use App\Http\Resources\Mobile\ApprovalRequestResource;
use App\Models\ApprovalRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class NewApprovalRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(protected ApprovalRequest $approvalRequest)
    {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            'type' => 'NEW_APPROVAL_REQUESTS_PENDING',
            'payload' => new ApprovalRequestResource(
                $this->approvalRequest->loadMissing(['employee.manager', 'department'])
            ),
        ];
    }

    public function toOneSignal($notifiable): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('New Request'))
            ->setBody(__('You have a new Approval Request.'))
            ->setData('notification_id', $this->id);
    }
}
