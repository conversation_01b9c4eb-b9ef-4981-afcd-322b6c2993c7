<?php

namespace App\Notifications;

use App\Http\Resources\Mobile\LeaveResource;
use App\Models\Leave;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class NewLeaveRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public Leave $leave)
    {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            'type' => 'NEW_LEAVE_REQUEST_PENDING',
            'payload' => new LeaveResource(
                $this->leave->loadMissing(['employee.manager', 'department'])
            ),
        ];
    }

    public function toOneSignal($notifiable): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('New Request'))
            ->setBody(__('You have a new Approval Request.'))
            ->setData('notification_id', $this->id);
    }
}
