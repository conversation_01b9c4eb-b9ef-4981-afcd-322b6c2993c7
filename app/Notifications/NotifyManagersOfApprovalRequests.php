<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class NotifyManagersOfApprovalRequests extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $count)
    {
    }

    public function via(): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toArray(): array
    {
        return [
            'type' => 'APPROVAL_REQUESTS_PENDING',
            'payload' => [
                'count' => $this->count,
            ],
        ];
    }

    public function toOneSignal(): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('Your Action is Needed'))
            ->setBody(__('You have :count pending requests', ['count' => $this->count]))
            ->setData('notification_id', $this->id);
    }
}
