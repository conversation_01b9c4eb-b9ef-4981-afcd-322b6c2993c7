<?php

namespace App\Notifications;

use App\Models\Shift;
use Carbon\CarbonImmutable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class NewTemporaryShiftAssignedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly Shift $shift,
        private readonly CarbonImmutable $startDate,
        private readonly CarbonImmutable $endDate
    ) {
    }

    public function via(): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toArray(): array
    {
        return [
            'shift' => $this->shift,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    public function toOneSignal(): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('New shift assigned'))
            ->setBody(
                __('Temporary shift is scheduled from :from to :to', [
                    'from' => $this->startDate->toDateString(),
                    'to' => $this->endDate->toDateString(),
                ])
            )
            ->setData('shift_id', $this->shift->id);
    }
}
