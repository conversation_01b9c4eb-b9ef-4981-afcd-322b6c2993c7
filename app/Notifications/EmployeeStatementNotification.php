<?php

namespace App\Notifications;

use App\Enums\EmployeeStatementType;
use App\Models\Attendance;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class EmployeeStatementNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Attendance $attendance,
        public EmployeeStatementType $employeeStatementType
    ) {
    }

    public function via($notifiable): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    public function toOneSignal($notifiable): OneSignalMessage
    {
        return OneSignalMessage::create()->setSubject($this->employeeStatementType->title());
    }

    public function toArray($notifiable): array
    {
        return [
            'type' => 'EMPLOYEE_STATEMENT',
            'payload' => [
                'subject' => $this->employeeStatementType->title(),
                'type' => $this->employeeStatementType->value,
                'date' => $this->attendance->date,
                'non_completed_hours' => $this->attendance
                    ->noneCompleteWorkHours()
                    ->format('%H:%I'),
            ],
        ];
    }
}
