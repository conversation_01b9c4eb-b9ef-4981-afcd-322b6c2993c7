<?php

namespace App\Notifications;

use App\Http\Resources\Mobile\ApprovalRequestResource;
use App\Models\ApprovalRequest;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Notifications\Notification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

class ApprovalRequestRejectedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public ApprovalRequest $approvalRequest)
    {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     */
    public function via($notifiable): array
    {
        return [OneSignalChannel::class, 'database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            'type' => 'APPROVAL_REQUEST_REJECTED',
            'payload' => new ApprovalRequestResource(
                $this->approvalRequest->loadMissing(['employee.manager', 'department'])
            ),
        ];
    }

    public function toOneSignal($notifiable): OneSignalMessage
    {
        return OneSignalMessage::create()
            ->setSubject(__('Attendance Request Rejected'))
            ->setBody(__('Your attendance request has been rejected by your department manager'));
    }
}
