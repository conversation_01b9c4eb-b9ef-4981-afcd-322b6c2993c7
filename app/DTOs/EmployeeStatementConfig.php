<?php

namespace App\DTOs;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;
use JsonSerializable;

class EmployeeStatementConfig implements CastsAttributes, Arrayable, JsonSerializable
{
    public function __construct(
        public bool $enabled = false,

        public bool $preventRequestsEnabled = false,
        public int $daysBeforePreventingRequests = 3,

        public ?int $lateCheckinBufferMinutes = null,

        public ?int $earlyCheckoutBufferMinutes = null,

        public bool $absentEnabled = false
    ) {
        if ($this->preventRequestsEnabled && $this->daysBeforePreventingRequests < 1) {
            throw new InvalidArgumentException(
                'days_before_preventing_requests should be greater than 0 when prevent_requests_enabled is true'
            );
        }
    }

    public static function fromJson(mixed $value): ?EmployeeStatementConfig
    {
        if (!$value) {
            return null;
        }

        $array = json_decode($value, true);

        return new self(
            enabled: $array['enabled'],

            preventRequestsEnabled: $array['prevent_requests_enabled'],
            daysBeforePreventingRequests: $array['days_before_preventing_requests'],

            lateCheckinBufferMinutes: $array['late_checkin_buffer_minutes'],
            earlyCheckoutBufferMinutes: $array['early_checkout_buffer_minutes'],

            absentEnabled: $array['absent_enabled'] ?? false // remove false in the future
        );
    }

    public function get(Model $model, string $key, mixed $value, array $attributes): ?self
    {
        return self::fromJson($value);
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if ($value instanceof self) {
            $value = $value->toArray();
        }

        $config = isset($attributes[$key])
            ? self::fromJson($attributes[$key])
            : new self(enabled: false);

        return json_encode([
            'enabled' => $value['enabled'] ?? $config->enabled,

            'prevent_requests_enabled' =>
                $value['prevent_requests_enabled'] ?? $config->preventRequestsEnabled,
            'days_before_preventing_requests' =>
                $value['days_before_preventing_requests'] ?? $config->daysBeforePreventingRequests,

            'late_checkin_buffer_minutes' => array_key_exists('late_checkin_buffer_minutes', $value)
                ? $value['late_checkin_buffer_minutes']
                : $config->lateCheckinBufferMinutes,

            'early_checkout_buffer_minutes' => array_key_exists(
                'early_checkout_buffer_minutes',
                $value
            )
                ? $value['early_checkout_buffer_minutes']
                : $config->earlyCheckoutBufferMinutes,

            'absent_enabled' => $value['absent_enabled'] ?? $config->absentEnabled,
        ]);
    }

    public function shouldSendLateCheckinNotification(): bool
    {
        return $this->enabled &&
            !is_null($this->lateCheckinBufferMinutes) &&
            $this->lateCheckinBufferMinutes > 0;
    }

    public function shouldSendEarlyCheckoutNotification(): bool
    {
        return $this->enabled &&
            !is_null($this->earlyCheckoutBufferMinutes) &&
            $this->earlyCheckoutBufferMinutes > 0;
    }

    public function toArray(): array
    {
        return [
            'enabled' => $this->enabled,

            'prevent_requests_enabled' => $this->preventRequestsEnabled,
            'days_before_preventing_requests' => $this->daysBeforePreventingRequests,

            'late_checkin_buffer_minutes' => $this->lateCheckinBufferMinutes,
            'early_checkout_buffer_minutes' => $this->earlyCheckoutBufferMinutes,

            'absent_enabled' => $this->absentEnabled,
        ];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
