<?php

namespace App\DTOs;

use App\Enums\DurationStatus;
use App\Models\Employee;
use App\QueryBuilders\EmployeeQueryBuilder;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

class AttachEmployeesToShiftLocationData
{
    public bool $all = false;
    public array $employeesIds;
    public ?DurationStatus $type = null;
    public ?CarbonPeriod $period = null;

    // locations only
    public ?Collection $locations = null;

    public function __construct(array $data)
    {
        $this->all = $data['all'] ?? false;

        $this->employeesIds = $data['employees'] ?? [];

        $this->type = isset($data['type']) ? DurationStatus::from($data['type']) : null;

        if ($this->type === DurationStatus::TEMPORARY) {
            $this->period = CarbonPeriod::create($data['start_date'], $data['end_date']);
        }

        $this->locations = collect($data['locations'] ?? []);
    }

    public static function fromRequest(): self
    {
        $data = new self(request()->all());

        $employeesQuery = $data->all
            ? // If all, let's try to filter by request's query parameters
            EmployeeQueryBuilder::query()
            : // Otherwise, let's filter by the provided employees ids
            Employee::whereIn('id', $data->employeesIds);

        // Overriding the employeesIds in request with the actual employees from query
        $data->employeesIds = $employeesQuery->pluck('id')->toArray();
        $data->all = false;

        return $data;
    }

    public function toArray(): array
    {
        return [
            'all' => $this->all,
            'employees' => $this->employeesIds,
            'type' => $this->type->value,
            'start_date' => $this->period?->start,
            'end_date' => $this->period?->end,
        ];
    }
}
