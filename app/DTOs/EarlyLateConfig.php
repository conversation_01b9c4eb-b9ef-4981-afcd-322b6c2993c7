<?php

namespace App\DTOs;

use App\Enums\EarlyLateNestingPolicy;
use App\Enums\EarlyLatePeriodPolicy;
use App\Enums\SheetMode;
use App\Models\Tag;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;
use JsonSerializable;

class EarlyLateConfig implements CastsAttributes, Arrayable, JsonSerializable
{
    public function __construct(
        public bool $enabled = false,
        public ?EarlyLatePeriodPolicy $periodPolicy = null,
        public ?EarlyLateNestingPolicy $nestingPolicy = null,
        public ?SheetMode $sheetMode = null,
        public array $excludedTags = []
    ) {
    }

    public static function fromJson(mixed $value): ?EarlyLateConfig
    {
        if (!$value) {
            return null;
        }

        $array = json_decode($value, true);

        return new self(
            enabled: $array['enabled'],
            periodPolicy: EarlyLatePeriodPolicy::from($array['period_policy']),
            nestingPolicy: EarlyLateNestingPolicy::from($array['nesting_policy']),
            sheetMode: SheetMode::from($array['sheet_mode']),
            excludedTags: $array['excluded_tags'] ?? []
        );
    }

    public function get(Model $model, string $key, mixed $value, array $attributes): ?self
    {
        return self::fromJson($value);
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if ($value instanceof self) {
            $value = $value->toArray();
        }

        $config = isset($attributes[$key])
            ? self::fromJson($attributes[$key])
            : new self(
                enabled: false,
                periodPolicy: EarlyLatePeriodPolicy::Monthly,
                nestingPolicy: EarlyLateNestingPolicy::DirectDepartment,
                sheetMode: SheetMode::SingleSheet,
                excludedTags: []
            );

        return json_encode([
            'enabled' => $value['enabled'] ?? $config->enabled,
            'period_policy' => $value['period_policy'] ?? $config->periodPolicy->value,
            'nesting_policy' => $value['nesting_policy'] ?? $config->nestingPolicy->value,
            'sheet_mode' => $value['sheet_mode'] ?? $config->sheetMode->value,
            'excluded_tags' => $value['excluded_tags'] ?? $config->excludedTags,
        ]);
    }

    public function toArrayWithTagsLoaded(): array
    {
        return [...$this->toArray(), 'excluded_tags' => Tag::find($this->excludedTags)];
    }

    public function toArray(): array
    {
        return [
            'enabled' => $this->enabled,
            'period_policy' => $this->periodPolicy->value,
            'nesting_policy' => $this->nestingPolicy->value,
            'sheet_mode' => $this->sheetMode->value,
            'excluded_tags' => $this->excludedTags,
        ];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    public static function makeDefault(): EarlyLateConfig
    {
        return new self(
            enabled: false,
            periodPolicy: EarlyLatePeriodPolicy::Monthly,
            nestingPolicy: EarlyLateNestingPolicy::DirectDepartment,
            sheetMode: SheetMode::SingleSheet,
            excludedTags: []
        );
    }
}
