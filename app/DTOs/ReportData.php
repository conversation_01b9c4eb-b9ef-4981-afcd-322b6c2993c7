<?php

namespace App\DTOs;

use App\Enums\MonthlyDaily;
use App\Enums\SheetMode;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;

class ReportData implements CastsAttributes, Arrayable, \JsonSerializable
{
    public ?CarbonPeriod $period = null;
    public ?MonthlyDaily $type = null;
    public array $employeesIds = [];
    public array $departmentsIds = [];
    public array $tags = [];
    public array $locations = [];
    public array $shifts = [];
    public array $directManagers = [];
    public bool $showInactiveEmployees = false;

    // late-early report
    public ?SheetMode $sheetMode = null;

    // usage status report
    public ?bool $usageStatus = null;

    public function __construct(array $data = null)
    {
        if (!$data) {
            return;
        }

        if (isset($data['start_date'])) {
            $this->period = CarbonPeriod::create(
                $data['start_date'],
                $data['end_date'] ?? $data['start_date']
            );
        }

        $this->type = isset($data['type']) ? MonthlyDaily::from($data['type']) : null;

        $this->employeesIds = $data['employees_ids'] ?? [];
        $this->departmentsIds = $data['departments_ids'] ?? [];
        $this->tags = $data['tags'] ?? [];
        $this->directManagers = $data['direct_managers'] ?? [];
        $this->locations = $data['locations'] ?? [];
        $this->shifts = $data['shifts'] ?? [];
        $this->usageStatus = $data['usage_status'] ?? null;
        $this->showInactiveEmployees = $data['show_inactive_employees'] ?? false;

        $this->sheetMode = isset($data['sheet_mode']) ? SheetMode::from($data['sheet_mode']) : null;
    }

    public function get(Model $model, string $key, mixed $value, array $attributes): ReportData
    {
        return new self(json_decode($value, true));
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if ($value instanceof self) {
            $value = $value->toArray();
        }

        return json_encode([
            ...$value,
            'start_date' => isset($value['start_date'])
                ? Carbon::parse($value['start_date'])->format('Y-m-d H:i:s')
                : null,
            'end_date' => isset($value['end_date'])
                ? Carbon::parse($value['end_date'])->format('Y-m-d H:i:s')
                : null,
        ]);
    }

    public function toArray(): array
    {
        return [
            'start_date' => $this->period?->start->format('Y-m-d H:i:s'),
            'end_date' => $this->period?->end->format('Y-m-d H:i:s'),
            'sheet_mode' => $this->sheetMode?->value,
            'type' => $this->type?->value,
            'employees_ids' => $this->employeesIds,
            'departments_ids' => $this->departmentsIds,
            'tags' => $this->tags,
            'locations' => $this->locations,
            'shifts' => $this->shifts,
            'show_inactive_employees' => $this->showInactiveEmployees,
            'usage_status' => $this->usageStatus,
            'direct_managers' => $this->directManagers,
        ];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
