<?php

namespace App\DTOs;

use App\Models\Employee;
use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;

class ApprovalLayer implements JsonSerializable, Arrayable
{
    public function __construct(
        public ?Employee $leaveApprover = null,
        public ?Employee $permissionApprover = null,
        public ?Employee $regularizationApprover = null,
        public ?Employee $remoteWorkApprover = null
    ) {
    }

    public function toArray(): array
    {
        return [
            'leave_approver' => $this->leaveApprover,
            'permission_approver' => $this->permissionApprover,
            'regularization_approver' => $this->regularizationApprover,
            'remote_work_approver' => $this->remoteWorkApprover,
        ];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
