<?php

namespace App\DTOs;

use App\Enums\IdentifierType;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;
use InvalidArgumentException;
use Propaganistas\LaravelPhone\PhoneNumber;

class Identifier implements Arrayable
{
    protected string $value;
    protected IdentifierType $type;

    private array $testIdentifiers = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '+966501234567',
    ];

    public function __construct(string|Phonenumber $value)
    {
        if ($value instanceof PhoneNumber) {
            $this->value = $value->formatE164();
            $this->type = IdentifierType::Phone;
            return;
        }

        $isSaudiPhone = phone($value, 'SA')->isValid();

        if ($isSaudiPhone) {
            $this->value = phone($value, 'SA')->formatE164();
            $this->type = IdentifierType::Phone;
            return;
        }

        $isEmail = self::hasDomain($value);

        if ($isEmail) {
            $this->value = strtolower($value);
            $this->type = IdentifierType::Email;
            return;
        }

        throw new InvalidArgumentException("Invalid identifier [$value]");
    }

    public function value()
    {
        return $this->isEmail() ? strtolower($this->value) : $this->value;
    }

    public function type()
    {
        return $this->type;
    }

    public function isEmail(): bool
    {
        return $this->type->isEmail();
    }

    public function isPhone(): bool
    {
        return $this->type->isPhone();
    }

    public function isTestIdentifier(): bool
    {
        return in_array($this->value, $this->testIdentifiers);
    }

    public function domain(): ?string
    {
        return $this->isEmail() ? self::extractDomain($this->value) : null;
    }

    public function __toString(): string
    {
        return $this->value();
    }

    public static function extractDomain(string $email): string
    {
        return Arr::last(explode('@', $email));
    }

    public static function hasDomain(?string $email): bool
    {
        return $email && str_contains($email, '@');
    }

    public function masked(): string
    {
        if ($this->type === IdentifierType::Email) {
            [$name, $domain] = explode('@', $this->value);
            $maskedName =
                strlen($name) <= 3
                    ? str_repeat('*', strlen($name))
                    : substr($name, 0, 2) . str_repeat('*', strlen($name) - 2);

            return "$maskedName@$domain";
        }

        return substr($this->value, 0, 7) . '****';
    }

    public function toArray(): array
    {
        return [
            'value' => $this->value,
            'type' => $this->type->value,
        ];
    }
}
