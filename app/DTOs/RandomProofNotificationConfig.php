<?php

namespace App\DTOs;

use App\Enums\RandomProofOfAttendanceDeadline;
use App\Models\Team;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;
use JsonSerializable;

class RandomProofNotificationConfig implements CastsAttributes, Arrayable, JsonSerializable
{
    public function __construct(
        public bool $enabled = false,
        public bool $inherited = false,
        // null means random
        public ?int $count = null,
        public ?RandomProofOfAttendanceDeadline $deadline = null
    ) {
    }

    public static function fromJson(mixed $value): ?RandomProofNotificationConfig
    {
        if (!$value) {
            return null;
        }

        $array = json_decode($value, true);

        return new self(
            enabled: $array['enabled'],
            inherited: $array['inherited'],
            count: $array['count'],
            deadline: $array['deadline'] ?? false
                ? RandomProofOfAttendanceDeadline::from($array['deadline'])
                : null
        );
    }

    public function get(Model $model, string $key, mixed $value, array $attributes): ?self
    {
        return self::fromJson($value);
    }

    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if ($value instanceof self) {
            $value = $value->toArray();
        }

        $config = isset($attributes[$key]) ? self::fromJson($attributes[$key]) : new self();

        $this->validate($model, $value, $config);

        return json_encode([
            'enabled' => $value['enabled'] ?? $config->enabled,
            'inherited' => $value['inherited'] ?? $config->inherited,
            'count' => $value['count'], // count can be null
            'deadline' => $value['deadline'] ?? $config->deadline,
        ]);
    }

    public static function makeDefault(): RandomProofNotificationConfig
    {
        return new RandomProofNotificationConfig(
            enabled: true,
            inherited: false,
            count: 5,
            deadline: RandomProofOfAttendanceDeadline::thirty
        );
    }

    public function toArray(): array
    {
        return [
            'enabled' => $this->enabled,
            'inherited' => $this->inherited,
            'count' => $this->count,
            'deadline' => $this->deadline?->value,
        ];
    }

    // for backward compatibility, especially for external API, until we migrate to v2
    public function oldRandomNotificationValue(): string
    {
        if ($this->inherited) {
            return 'inherited';
        }

        if ($this->enabled) {
            return 'enabled';
        }

        return 'disabled';
    }

    // for backward compatibility, especially for external API, until we migrate to v2
    public static function oldValueToNewConfig(
        string $value,
        self $randomProofNotificationConfig
    ): self {
        if ($value === 'inherited') {
            $randomProofNotificationConfig->inherited = true;

            return $randomProofNotificationConfig;
        }

        if ($value === 'enabled') {
            $randomProofNotificationConfig->enabled = true;

            return $randomProofNotificationConfig;
        }

        if ($value === 'disabled') {
            $randomProofNotificationConfig->enabled = false;

            return $randomProofNotificationConfig;
        }

        return $randomProofNotificationConfig;
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    public function validate(
        Model $model,
        mixed $value,
        ?RandomProofNotificationConfig $config
    ): void {
        if ($model instanceof Team && ($value['inherited'] ?? $config->inherited)) {
            throw new InvalidArgumentException(
                'Team random proof notification config cannot be inherited'
            );
        }

        if (!($model instanceof Team) && !is_null($value['deadline'] ?? $config->deadline)) {
            throw new InvalidArgumentException(
                'Only team random proof notification config can have a deadline'
            );
        }
    }
}
