<?php

namespace App\DTOs\Stats;

use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;
use ReflectionClass;
use Str;

abstract class BaseStats implements Arrayable, JsonSerializable
{
    /**
     * Increments the properties of the current instance by the corresponding properties of another instance.
     *
     * @example
     * $stats1 = new EarlyLateStats(total: 10, onTime: 5);
     * $stats2 = new EarlyLateStats(total: 5, onTime: 3);
     *
     * // Increment stats1 by stats2
     * $stats1->increment($stats2);
     *
     * // $stats1 now has total = 15 and onTime = 8
     */
    public function increment(self $stats): static
    {
        foreach ($this->properties() as $property) {
            $name = $property->getName();

            $this->$name += $stats->$name;
        }

        return $this;
    }

    public function toArray(): array
    {
        return collect($this->properties())
            ->mapWithKeys(function ($property) {
                $name = $property->getName();

                return [
                    Str::snake($name) => $this->$name,
                ];
            })
            ->toArray();
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    public function properties(): array
    {
        return (new ReflectionClass($this))->getProperties();
    }

    /**
     * @return array{key: string, value: float}
     */
    public function max(): array
    {
        $properties = collect($this->properties())->filter(
            fn($property) => $property->getName() !== 'total'
        );

        $key = collect($properties)->reduce(function ($carry, $property) {
            $name = $property->getName();

            return $this->{$name} > $this->{$carry} ? $name : $carry;
        }, $properties->first()->getName());

        return [
            'key' => $key,
            'value' => $this->{$key},
        ];
    }
}
