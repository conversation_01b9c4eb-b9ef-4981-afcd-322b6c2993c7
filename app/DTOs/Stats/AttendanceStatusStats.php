<?php

namespace App\DTOs\Stats;

use App\Calculations\AttendanceStatusCalculator;

class AttendanceStatusStats extends BaseStats
{
    public function __construct(
        // these properties can represent count like 100 attendance records, or percentage like 50% of total attendance records
        public float $total = 0,
        public float $present = 0,
        public float $absent = 0,
        public float $yet = 0,
        public float $weekend = 0,
        public float $holiday = 0,
        public float $leave = 0
    ) {
    }

    public function percentages(): self
    {
        return (new AttendanceStatusCalculator())->percentages($this);
    }

    public function presentIncludingNonWorkDays(): float|int
    {
        return $this->present + $this->weekend + $this->holiday;
    }
}
