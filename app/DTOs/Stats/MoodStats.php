<?php

namespace App\DTOs\Stats;

class MoodStats extends BaseStats
{
    public function __construct(
        public float $total = 0,
        public float $veryHappy = 0,
        public float $happy = 0,
        public float $neutral = 0,
        public float $sad = 0,
        public float $verySad = 0
    ) {
    }

    public function percentages(): self
    {
        return new MoodStats(
            total: 100,
            veryHappy: percentage($this->veryHappy, $this->total),
            happy: percentage($this->happy, $this->total),
            neutral: percentage($this->neutral, $this->total),
            sad: percentage($this->sad, $this->total),
            verySad: percentage($this->verySad, $this->total)
        );
    }

    /**
     * @return array{key: string, value: float, emoji: string, title: string, description: string}
     */
    public function formatForEarlyLateMail(): array
    {
        $max = $this->max();

        $title = match ($max['key']) {
            'veryHappy' => __('very-happy'),
            'happy' => __('happy'),
            'neutral' => __('neutral'),
            'sad' => __('sad'),
            'verySad' => __('very-sad'),
        };

        $description = __(':percentage% of team feels :mood checking in', [
            'percentage' => $max['value'],
            'mood' => $title,
        ]);

        $emoji = match ($max['key']) {
            'veryHappy' => asset('moods/very-happy.svg'),
            'happy' => asset('moods/happy.svg'),
            'neutral' => asset('moods/neutral.svg'),
            'sad' => asset('moods/sad.svg'),
            'verySad' => asset('moods/very-sad.svg'),
        };

        return [
            'value' => $max['value'],
            'emoji' => $emoji,
            'title' => $title,
            'description' => $description,
        ];
    }
}
