<?php

namespace App\DTOs\Stats;

use App\Calculations\EarlyLateCalculator;

class EarlyLateStats extends BaseStats
{
    public function __construct(
        public float $total = 0,
        public float $onTime = 0,

        public float $earlyIn = 0,
        public float $lateIn = 0,

        public float $earlyOut = 0,
        public float $lateOut = 0
    ) {
    }

    public function percentages(): self
    {
        return (new EarlyLateCalculator())->percentages($this);
    }
}
