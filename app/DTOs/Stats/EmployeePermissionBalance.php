<?php

namespace App\DTOs\Stats;

use Carbon\CarbonInterval;
use Illuminate\Contracts\Support\Arrayable;

class EmployeePermissionBalance extends BaseStats
{
    public function __construct(
        public ?CarbonInterval $monthlyBalance,
        public ?CarbonInterval $dailyBalance,
        public ?CarbonInterval $currentBalance
    ) {
    }

    function toArray(): array
    {
        return [
            'monthly_balance' => $this->monthlyBalance?->format('%H:%I:%S'),
            'daily_balance' => $this->dailyBalance?->format('%H:%I:%S'),
            'current_balance' => $this->currentBalance?->format('%H:%I:%S'),
        ];
    }
}
