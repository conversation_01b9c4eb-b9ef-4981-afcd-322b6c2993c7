<?php

namespace App\DTOs;

use App\Enums\DecisionLayer;
use App\Enums\RequestType;
use App\Models\Employee;
use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;

class RequestApprovers implements JsonSerializable, Arrayable
{
    public function __construct(
        public ApprovalLayer $firstLayer,
        public ?ApprovalLayer $secondLayer = new ApprovalLayer()
    ) {
    }

    public function toArray(): array
    {
        return [
            'first_layer' => $this->firstLayer,
            'second_layer' => $this->secondLayer,
        ];
    }

    public function resolveCurrentApprover(
        DecisionLayer $layer,
        RequestType $requestType
    ): ?Employee {
        $decisionLayer = match ($layer) {
            DecisionLayer::First => $this->firstLayer,
            DecisionLayer::Second => $this->secondLayer,
        };

        return match ($requestType) {
            RequestType::Leave => $decisionLayer->leaveApprover,
            RequestType::Permission => $decisionLayer->permissionApprover,
            RequestType::Regularization => $decisionLayer->regularizationApprover,
            RequestType::RemoteWork => $decisionLayer->remoteWorkApprover,
        };
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }
}
