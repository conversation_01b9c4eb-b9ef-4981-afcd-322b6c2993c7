<?php

namespace App\DTOs;

use App\DTOs\Stats\BaseStats;
use App\Enums\EarlyLatePeriodPolicy;
use App\Models\Employee;
use App\Models\ReportTask;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

class EarlyLateMailData extends BaseStats
{
    public function __construct(
        public Employee $manager,
        public array $departmentsIDs,
        public CarbonPeriod $period,
        public EarlyLatePeriodPolicy $periodType,
        public ?Collection $reportTasksIds = null
    ) {
        $this->reportTasksIds = $reportTasksIds ?? collect();
    }

    public function reportTasks(): Collection
    {
        return ReportTask::findMany($this->reportTasksIds);
    }
}
