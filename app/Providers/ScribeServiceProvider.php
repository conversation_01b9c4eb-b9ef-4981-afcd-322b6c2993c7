<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>ckles\Scribe\Scribe;

/**
 * @codeCoverageIgnore
 */
class ScribeServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (!class_exists(Scribe::class)) {
            return;
        }

        Scribe::bootstrap(function () {
            app()->setLocale('en');
        });
    }
}
