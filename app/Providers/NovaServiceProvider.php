<?php

namespace App\Providers;

use App\Models\Admin;
use App\Nova\ApprovalRequest;
use App\Nova\Dashboards\Main;
use App\Nova\Department;
use App\Nova\Employee;
use App\Nova\Leave;
use App\Nova\Location;
use App\Nova\Proof;
use App\Nova\Report;
use App\Nova\Shift;
use App\Nova\Team;
use App\Nova\WebhookJobResource;
use App\Nova\WebhookResource;
use Illuminate\Support\Facades\Gate;
use Laravel\Nova\Menu\MenuItem;
use Laravel\Nova\Menu\MenuSection;
use Laravel\Nova\Nova;
use Laravel\Nova\NovaApplicationServiceProvider;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        // force the locale to be English
        Nova::booted(fn() => app()->setLocale('en'));

        Nova::mainMenu(function () {
            return [
                MenuSection::dashboard(Main::class)->icon('chart-bar'),

                MenuSection::make('Teams', [
                    MenuItem::resource(Team::class),
                    MenuItem::resource(Department::class),
                    MenuItem::resource(Employee::class),
                ]),

                MenuSection::make('Webhooks', [
                    MenuItem::resource(WebhookJobResource::class),
                    MenuItem::resource(WebhookResource::class),
                ]),

                MenuSection::make('Requests', [
                    MenuItem::resource(ApprovalRequest::class),
                    MenuItem::resource(Leave::class),
                    MenuItem::resource(Proof::class),
                ])
                    ->icon('template')
                    ->collapsable(),

                MenuSection::make('Config', [
                    MenuItem::resource(Location::class),
                    MenuItem::resource(Shift::class),
                    MenuItem::resource(Report::class),
                ])
                    ->icon('template')
                    ->collapsable(),

                MenuSection::make('Administration', [MenuItem::resource(\App\Nova\Admin::class)])
                    ->icon('user-circle')
                    ->collapsable(),
            ];
        });
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()->withAuthenticationRoutes()->withPasswordResetRoutes()->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', fn($admin) => $admin instanceof Admin);
    }

    /**
     * Get the dashboards that should be listed in the Nova sidebar.
     *
     * @return array
     */
    protected function dashboards()
    {
        return [new \App\Nova\Dashboards\Main()];
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
