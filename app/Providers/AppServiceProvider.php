<?php

namespace App\Providers;

use DB;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Unique;
use function currentTenant;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //        Passport::ignoreRoutes();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Rule::macro('uniqueTenant', function (string $table, string $column = 'NULL'): Unique {
            return Rule::unique($table, $column)->where('tenant_id', currentTenant()?->id);
        });

        Rule::macro('existsTenant', function (string $table, string $column = 'NULL'): Exists {
            return Rule::exists($table, $column)->where('tenant_id', currentTenant()?->id);
        });

        DB::prohibitDestructiveCommands($this->app->isProduction());
    }
}
