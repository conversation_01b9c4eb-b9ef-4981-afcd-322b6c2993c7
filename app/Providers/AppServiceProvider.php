<?php

namespace App\Providers;

use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Unique;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (config('app.env') != 'local') {
            URL::forceScheme('https');
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Rule::macro('uniqueTenant', function (string $table, string $column = 'NULL'): Unique {
            return Rule::unique($table, $column)->where('team_id', currentTeam()?->id);
        });

        Rule::macro('existsTenant', function (string $table, string $column = 'NULL'): Exists {
            return Rule::exists($table, $column)->where('team_id', currentTeam()?->id);
        });

        // todo: we will enable it in tests later
        if ($this->app->isLocal() /*|| $this->app->runningUnitTests()*/) {
            Model::shouldBeStrict();
        }

        DB::prohibitDestructiveCommands($this->app->isProduction());
    }
}
