<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('web')->group(base_path('routes/web.php'));

            Route::prefix('api')
                ->middleware('api')
                ->group(function () {
                    // mobile v2
                    Route::prefix('v2/mobile')->group(base_path('routes/v2/mobile.php'));

                    // external v1
                    Route::prefix('v1/external')
                        ->middleware([
                            'auth:external',
                            'localization',
                            'set-sentry-user',
                            'active-tenant',
                        ])
                        ->group(base_path('routes/v1/external.php'));

                    // device v1
                    Route::prefix('v1/device')
                        ->middleware([
                            'auth:device',
                            'localization',
                            'set-sentry-user',
                            'active-tenant',
                        ])
                        ->group(base_path('routes/v1/device.php'));

                    // frontend v1
                    Route::prefix('v1/frontend')
                        ->middleware(['auth:jwt', 'localization', 'set-sentry-user'])
                        ->group(base_path('routes/v1/frontend.php'));

                    // common v1
                    Route::prefix('v1')
                        ->middleware(['auth:jwt', 'localization', 'set-sentry-user'])
                        ->group(base_path('routes/v1/common.php'));
                });
        });
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting()
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
