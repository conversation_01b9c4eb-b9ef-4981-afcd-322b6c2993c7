<?php

namespace App\Providers;

use App\Events\EmployeeCheckedIn;
use App\Events\EmployeeCheckedOut;
use App\Listeners\CreateProofRecord;
use App\Listeners\ScheduleCheckoutReminder;
use App\Listeners\ScheduleRandomNotifications;
use App\Listeners\UpdateCheckInRecord;
use App\Listeners\UpdateCheckOutRecord;
use App\Listeners\WebhookSubscriber;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Notifications\Events\NotificationSent;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [SendEmailVerificationNotification::class],
        EmployeeCheckedIn::class => [
            UpdateCheckInRecord::class,
            ScheduleRandomNotifications::class,
            ScheduleCheckoutReminder::class,
        ],
        EmployeeCheckedOut::class => [UpdateCheckOutRecord::class],
        NotificationSent::class => [CreateProofRecord::class],
    ];

    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [WebhookSubscriber::class];

    protected $observers = [];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
