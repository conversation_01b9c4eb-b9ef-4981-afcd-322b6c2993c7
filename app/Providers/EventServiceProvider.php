<?php

namespace App\Providers;

use App\Listeners\SetCustomAttributesOnAccessToken;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Software;
use App\Models\Subscription;
use App\Models\SubscriptionItem;
use App\Models\Tag;
use App\Models\Tenant;
use App\Observers\DepartmentObserver;
use App\Observers\EmployeeObserver;
use App\Observers\SoftwareObserver;
use App\Observers\SubscriptionItemObserver;
use App\Observers\SubscriptionObserver;
use App\Observers\TagObserver;
use App\Observers\TenantObserver;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Laravel\Passport\Events\AccessTokenCreated;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        AccessTokenCreated::class => [SetCustomAttributesOnAccessToken::class],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        Tenant::observe(TenantObserver::class);
        Employee::observe(EmployeeObserver::class);
        Department::observe(DepartmentObserver::class);
        Subscription::observe(SubscriptionObserver::class);
        SubscriptionItem::observe(SubscriptionItemObserver::class);
        Software::observe(SoftwareObserver::class);
        Tag::observe(TagObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
