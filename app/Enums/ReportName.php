<?php

namespace App\Enums;

enum ReportName: string
{
    case EarlyLate = 'early_late';
    case Mood = 'mood';
    case Permission = 'permission';
    case PresentAbsent = 'present_absent';
    case ProofStatus = 'proof_status';
    case UsageRate = 'usage_rate';
    case Leave = 'leave';

    case Employees = 'employees';

    public function path(string $fileName, string $extension): string
    {
        return "reports/$this->value/$fileName.$extension";
    }

    public function fileName(string $suffix = null): string
    {
        return $suffix ? "{$this->value}_$suffix" : $this->value;
    }

    public function displayName(): string
    {
        return match ($this) {
            self::EarlyLate => __('Early/Late Report Of Attendances'),
            self::Mood => __('Mood report'),
            self::Permission => __('Permission status'),
            self::PresentAbsent => __('Employee present/absent status'),
            self::ProofStatus => __('Proofs status'),
            self::UsageRate => __('App usage rate'),
            self::Leave => __('Leave'),
            self::Employees => __('Employees'),
        };
    }
}
