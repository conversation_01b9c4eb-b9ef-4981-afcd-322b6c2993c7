<?php

namespace App\Enums;

enum EmployeeStatementType: string
{
    case LateCheckin = 'late_checkin';
    case EarlyCheckout = 'early_checkout';
    case LateCheckinAndEarlyCheckout = 'late_checkin_and_early_checkout';
    case Absent = 'absent';

    public function title(): string
    {
        return match ($this) {
            self::LateCheckin => __('Late Checkin Statement'),
            self::EarlyCheckout => __('Early Checkout Statement'),
            self::LateCheckinAndEarlyCheckout => __('Late Checkin & Early Checkout Statement'),
            self::Absent => __('Absent Statement'),
        };
    }
}
