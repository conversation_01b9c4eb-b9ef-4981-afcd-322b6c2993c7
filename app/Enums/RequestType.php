<?php

namespace App\Enums;

use App\Models\ApprovalRequest;
use App\Models\Leave;
use Exception;

enum RequestType: string
{
    case Leave = 'leave';
    case Permission = 'permission';
    case Regularization = 'regularization';
    case RemoteWork = 'remote_work';

    public static function fromApprovalRequestType(string $approvalRequestType): RequestType
    {
        return match ($approvalRequestType) {
            ApprovalRequest::PERMISSION => RequestType::Permission,
            ApprovalRequest::REGULARIZATION => RequestType::Regularization,
            ApprovalRequest::REMOTE_WORK => RequestType::RemoteWork,
            default => throw new Exception('Invalid approval request type'),
        };
    }

    public static function fromModel(Leave|ApprovalRequest $requestable): RequestType
    {
        return match (true) {
            $requestable instanceof Leave => RequestType::Leave,
            $requestable instanceof ApprovalRequest => self::fromApprovalRequestType(
                $requestable->type
            ),
            default => throw new Exception('Invalid requestable type'),
        };
    }
}
