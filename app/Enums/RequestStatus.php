<?php

namespace App\Enums;

enum RequestStatus: string
{
    case Pending = 'PENDING';
    case Approved = 'APPROVED';
    case Rejected = 'REJECTED';

    // this is for pending in approval requests
    case DeprecatedPending = 'pending';

    public function displayName(): string
    {
        return match ($this) {
            self::Pending, self::DeprecatedPending => __('Pending'),
            self::Approved => __('Approved'),
            self::Rejected => __('Rejected'),
        };
    }

    public function isApproved(): bool
    {
        return $this === self::Approved;
    }

    public function isNotPending(): bool
    {
        return $this !== self::Pending;
    }

    // we will not need this when we remove DeprecatedPending
    public static function fromStringOrInstance(self|string $status): RequestStatus
    {
        if ($status instanceof self) {
            return $status;
        }

        return match ($status) {
            self::Pending->value, self::DeprecatedPending->value => self::Pending,
            self::Approved->value => self::Approved,
            self::Rejected->value => self::Rejected,
        };
    }
}
