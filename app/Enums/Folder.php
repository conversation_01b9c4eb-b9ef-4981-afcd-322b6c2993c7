<?php

namespace App\Enums;

use Storage;

enum Folder: string
{
    case LOGOS = 'logos';
    case LEAVE_ATTACHMENTS = 'leave_attachments';

    public function tempPath(string $fileName): string
    {
        return "temp/$this->value/$fileName";
    }

    public function path(string $fileName): string
    {
        return "$this->value/$fileName";
    }

    public function temporaryUrl(string|null $fileName): ?string
    {
        return $fileName
            ? Storage::temporaryUrl(
                path: $this->path($fileName),
                expiration: now()->addDays(5),
                options: [
                    'ResponseContentDisposition' => 'inline',
                    'ResponseContentType' => $this->contentType($fileName),
                ]
            )
            : null;
    }

    public function contentType(string $fileName): string
    {
        return match (pathinfo($fileName, PATHINFO_EXTENSION)) {
            'pdf' => 'application/pdf',
            'png' => 'image/png',
            'jpg', 'jpeg' => 'image/jpeg',
            default => 'application/octet-stream',
        };
    }
}
