<?php

namespace App\Enums;

use App\Models\ApprovalRequest;

enum DelegationType: string
{
    case RemoteWorkRequest = 'REMOTE_WORK_REQUEST';
    case LeaveRequest = 'LEAVE_REQUEST';
    case RegularizationRequest = 'REGULARIZATION_REQUEST';
    case PermissionRequest = 'PERMISSION_REQUEST';
    case EarlyLate = 'PERIODICAL_EARLY_LATE_REPORT';

    public static function approvalRequestTypeToDelegationType(string $type): DelegationType
    {
        return match ($type) {
            ApprovalRequest::PERMISSION => DelegationType::PermissionRequest,
            ApprovalRequest::REMOTE_WORK => DelegationType::RemoteWorkRequest,
            ApprovalRequest::REGULARIZATION => DelegationType::RegularizationRequest,
        };
    }

    /**
     * map delegation type to approval request type, return null if delegation type is not an approval request type
     */
    public function delegationTypeToApprovalRequestType(): ?string
    {
        return match ($this) {
            DelegationType::PermissionRequest => ApprovalRequest::PERMISSION,
            DelegationType::RemoteWorkRequest => ApprovalRequest::REMOTE_WORK,
            DelegationType::RegularizationRequest => ApprovalRequest::REGULARIZATION,
            DelegationType::LeaveRequest, DelegationType::EarlyLate => null,
        };
    }

    public static function requestInputToType(string $input): DelegationType
    {
        return match ($input) {
            'remote_work_request_delegated_id' => DelegationType::RemoteWorkRequest,
            'leave_request_delegated_id' => DelegationType::LeaveRequest,
            'regularization_request_delegated_id' => DelegationType::RegularizationRequest,
            'permission_request_delegated_id' => DelegationType::PermissionRequest,
            'periodical_early_late_report_delegated_id' => DelegationType::EarlyLate,
        };
    }

    public static function requestInputs(): array
    {
        return [
            'remote_work_request_delegated_id',
            'leave_request_delegated_id',
            'regularization_request_delegated_id',
            'permission_request_delegated_id',
            'periodical_early_late_report_delegated_id',
        ];
    }

    public function displayName(): string
    {
        return match ($this) {
            DelegationType::RemoteWorkRequest => __('Remote Work'),
            DelegationType::LeaveRequest => __('Leave Request'),
            DelegationType::RegularizationRequest => __('Regularization Request'),
            DelegationType::PermissionRequest => __('Permission Request'),
            DelegationType::EarlyLate => __('Early Late'),
        };
    }
}
