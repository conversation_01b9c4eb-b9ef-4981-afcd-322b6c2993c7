<?php

use App\Models\Device;
use App\Models\Employee;
use App\Models\Team;

if (!function_exists('currentTeam')) {
    function currentTeam(): Team|null
    {
        /** @var Employee|Team|Device $user */
        $user = auth()->user();

        return match (true) {
            $user instanceof Team => $user,
            $user instanceof Employee, $user instanceof Device => $user->team,
            default => null,
        };
    }
}

if (!function_exists('percentage')) {
    function percentage(int $value, int $total, int $precision = 0, bool $flip = false): float
    {
        $result = $total === 0 ? 0 : ($value / $total) * 100;

        return round($flip ? 100 - $result : $result, $precision);
    }
}

if (!function_exists('divide')) {
    function divide(int $value, int $dividedBy, int $min = 0): float
    {
        $result = $dividedBy === 0 ? 0 : $value / $dividedBy;

        return max($result, $min);
    }
}
