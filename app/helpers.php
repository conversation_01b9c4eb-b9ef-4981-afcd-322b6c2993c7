<?php

use App\Models\Tenant;
use App\Support\LoginSession;

if (!function_exists('currentTenant')) {
    function currentTenant(): Tenant|null
    {
        if (auth()->user() instanceof Tenant) {
            return auth()->user();
        }

        return auth()->user()?->tenant;
    }
}

if (!function_exists('loginSession')) {
    function loginSession(): LoginSession
    {
        return new LoginSession();
    }
}
