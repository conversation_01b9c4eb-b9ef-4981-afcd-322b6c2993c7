<?php

namespace App\Policies;

use App\Enums\DelegationType;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Http\Request;

class ApprovalRequestPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(Employee $employee, Request $approvalRequest)
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(Employee $employee, ApprovalRequest $approvalRequest)
    {
        return $employee->id == $approvalRequest->employee_id ||
            $approvalRequest->employee->manager->id == $employee->id;
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(Employee $employee)
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(Employee $employee, ApprovalRequest $approvalRequest)
    {
        return false;
    }

    /**
     * Determine whether the user can approve an apprval request or not the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function approve(Employee $employee, ApprovalRequest $approvalRequest)
    {
        $employeeManagerID = $approvalRequest->employee->manager->id;
        $managerApprovalDelegations = $employee
            ->managerDelegations()
            ->where(
                'type',
                DelegationType::approvalRequestTypeToDelegationType($approvalRequest->type)
            );
        $employeeApprovalDelegations = $employee
            ->employeeDelegations()
            ->where(
                'type',
                DelegationType::approvalRequestTypeToDelegationType($approvalRequest->type)
            );
        return ($employeeManagerID === $employee->id &&
            $managerApprovalDelegations->doesntExist()) ||
            $employeeManagerID === $employeeApprovalDelegations->first()?->delegatee_id;
    }

    public function reject(Employee $employee, ApprovalRequest $approvalRequest)
    {
        $employeeManagerID = $approvalRequest->employee->manager->id;
        $managerApprovalDelegations = $employee
            ->managerDelegations()
            ->where(
                'type',
                DelegationType::approvalRequestTypeToDelegationType($approvalRequest->type)
            );
        $employeeApprovalDelegations = $employee
            ->employeeDelegations()
            ->where(
                'type',
                DelegationType::approvalRequestTypeToDelegationType($approvalRequest->type)
            );
        return ($employeeManagerID === $employee->id &&
            $managerApprovalDelegations->doesntExist()) ||
            $employeeManagerID === $employeeApprovalDelegations->first()?->delegatee_id;
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(Employee $employee, ApprovalRequest $approvalRequest)
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(Employee $employee, ApprovalRequest $approvalRequest)
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(Employee $employee, ApprovalRequest $approvalRequest)
    {
        return false;
    }
}
