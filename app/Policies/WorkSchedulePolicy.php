<?php

namespace App\Policies;

use App\Models\Employee;
use App\Models\WorkSchedule;
use Illuminate\Auth\Access\HandlesAuthorization;

class WorkSchedulePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(Employee $employee): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(Employee $employee, WorkSchedule $workSchedule): bool
    {
        return $workSchedule->team_id === $employee->team_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(Employee $employee): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(Employee $employee, WorkSchedule $workSchedule): bool
    {
        return $workSchedule->team_id === $employee->team_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(Employee $employee, WorkSchedule $workSchedule): bool
    {
        return $workSchedule->team_id === $employee->team_id;
    }
}
