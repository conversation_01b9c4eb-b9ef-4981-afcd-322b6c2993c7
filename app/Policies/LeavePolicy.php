<?php

namespace App\Policies;

use App\Enums\DelegationType;
use App\Models\Employee;
use App\Models\Leave;
use Illuminate\Auth\Access\HandlesAuthorization;

class LeavePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(Employee $employee)
    {
        //
    }

    public function approve(Employee $employee, Leave $leaveRequest)
    {
        return ($leaveRequest->employee->manager->id === $employee->id &&
            $employee
                ->managerDelegations()
                ->where('type', DelegationType::LeaveRequest)
                ->doesntExist()) ||
            $leaveRequest->employee->manager->id ===
                $employee
                    ->employeeDelegations()
                    ->where('type', DelegationType::LeaveRequest)
                    ->first()?->delegatee_id;
    }

    public function reject(Employee $employee, Leave $leaveRequest)
    {
        return ($leaveRequest->employee->manager->id === $employee->id &&
            $employee
                ->managerDelegations()
                ->where('type', DelegationType::LeaveRequest)
                ->doesntExist()) ||
            $leaveRequest->employee->manager->id ===
                $employee
                    ->employeeDelegations()
                    ->where('type', DelegationType::LeaveRequest)
                    ->first()?->delegatee_id;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(Employee $employee, Leave $leave)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(Employee $employee)
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(Employee $employee, Leave $leave)
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(Employee $employee, Leave $leave)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(Employee $employee, Leave $leave)
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(Employee $employee, Leave $leave)
    {
        //
    }
}
