<?php

namespace App\Calculations;

use App\Models\Attendance;
use Carbon\CarbonInterval;
use Illuminate\Support\Collection;

class TotalCommittedHoursCalculator
{
    public function calculate(Attendance|Collection $attendances): CarbonInterval
    {
        return Collection::wrap($attendances)->reduce(function (
            CarbonInterval $acc,
            Attendance $attendance
        ) {
            return $acc->add(CarbonInterval::seconds($attendance->committed_hours));
        }, CarbonInterval::seconds(0));
    }
}
