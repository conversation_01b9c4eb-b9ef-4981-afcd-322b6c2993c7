<?php

namespace App\Calculations;

use App\DTOs\Stats\EarlyLateStats;
use App\Models\Attendance;
use Carbon\CarbonInterval;
use Illuminate\Support\Collection;

class EarlyLateCalculator
{
    public function calculateStats(Collection $attendances): EarlyLateStats
    {
        return $attendances->reduce(function (EarlyLateStats $acc, Attendance $attendance) {
            $isEarlyIn = $this->calculateEarlyIn($attendance)->hasPositiveValues();
            $isLateIn = $this->calculateLateIn($attendance)->hasPositiveValues();
            $isEarlyOut = $this->calculateEarlyOut($attendance)->hasPositiveValues();
            $isLateOut = $this->calculateLateOut($attendance)->hasPositiveValues();

            $isOnTime = !$isEarlyIn && !$isLateIn && !$isEarlyOut && !$isLateOut;

            return new EarlyLateStats(
                total: $acc->total + 1,
                onTime: $acc->onTime + ($isOnTime ? 1 : 0),
                earlyIn: $acc->earlyIn + ($isEarlyIn ? 1 : 0),
                lateIn: $acc->lateIn + ($isLateIn ? 1 : 0),
                earlyOut: ($isEarlyOut ? 1 : 0) + $acc->earlyOut,
                lateOut: $acc->lateOut + ($isLateOut ? 1 : 0)
            );
        }, new EarlyLateStats());
    }

    public function percentages(EarlyLateStats $stats): EarlyLateStats
    {
        return new EarlyLateStats(
            total: 100,
            onTime: percentage($stats->onTime, $stats->total),
            earlyIn: percentage($stats->earlyIn, $stats->total),
            lateIn: percentage($stats->lateIn, $stats->total),
            earlyOut: percentage($stats->earlyOut, $stats->total),
            lateOut: percentage($stats->lateOut, $stats->total)
        );
    }

    public function calculateStatsByDays(Collection $attendances): Collection
    {
        $attendances = $attendances->groupBy(fn($attendance) => $attendance->date->format('l'));

        return collect([
            'sunday' => $this->calculateStats($attendances['Sunday'] ?? collect()),
            'monday' => $this->calculateStats($attendances['Monday'] ?? collect()),
            'tuesday' => $this->calculateStats($attendances['Tuesday'] ?? collect()),
            'wednesday' => $this->calculateStats($attendances['Wednesday'] ?? collect()),
            'thursday' => $this->calculateStats($attendances['Thursday'] ?? collect()),
            'friday' => $this->calculateStats($attendances['Friday'] ?? collect()),
            'saturday' => $this->calculateStats($attendances['Saturday'] ?? collect()),
        ]);
    }

    public function calculateEarlyIn(Attendance|Collection $attendances): CarbonInterval
    {
        return Collection::wrap($attendances)->reduce(function (
            CarbonInterval $acc,
            Attendance $attendance
        ) {
            $value = null;

            $flexibleHoursShiftStart = $attendance->shift_from->subMinutes(
                $attendance->flexible_hours
            );

            if (
                $attendance->in_type &&
                $attendance->check_in?->lessThan($flexibleHoursShiftStart)
            ) {
                $diff = $attendance->check_in->diffAsCarbonInterval($flexibleHoursShiftStart);

                // difference less than 60 second is not considered early
                $value = $diff->greaterThan(CarbonInterval::seconds(59)) ? $diff : $acc;
            }

            return $value ? $acc->add($value) : $acc;
        }, CarbonInterval::minutes(0));
    }

    public function calculateLateIn(Attendance|Collection $attendances): CarbonInterval
    {
        return Collection::wrap($attendances)->reduce(function (
            CarbonInterval $acc,
            Attendance $attendance
        ) {
            $value = null;

            $flexibleShiftStart = $attendance->shift_from->addMinutes($attendance->flexible_hours);

            if ($attendance->in_type && $attendance->check_in?->greaterThan($flexibleShiftStart)) {
                $diff = $flexibleShiftStart->diffAsCarbonInterval($attendance->check_in);

                // difference less than 60 second is not considered late
                $value = $diff->greaterThan(CarbonInterval::seconds(59)) ? $diff : $acc;
            }

            return $value ? $acc->add($value) : $acc;
        }, CarbonInterval::minutes(0));
    }

    public function calculateEarlyOut(Attendance|Collection $attendances): CarbonInterval
    {
        return Collection::wrap($attendances)->reduce(function (
            CarbonInterval $acc,
            Attendance $attendance
        ) {
            $value = null;

            $flexibleShiftEnd = $attendance->shift_to->subMinutes($attendance->flexible_hours);

            if ($attendance->out_type && $attendance->check_out?->lessThan($flexibleShiftEnd)) {
                $diff = $attendance->check_out->diffAsCarbonInterval($flexibleShiftEnd);

                // difference less than 60 second is not considered early
                $value = $diff->greaterThan(CarbonInterval::seconds(59)) ? $diff : $acc;
            }

            return $value ? $acc->add($value) : $acc;
        }, CarbonInterval::minutes(0));
    }

    public function calculateLateOut(Attendance|Collection $attendances): CarbonInterval
    {
        return Collection::wrap($attendances)->reduce(function (
            CarbonInterval $acc,
            Attendance $attendance
        ) {
            $value = null;

            $flexibleShiftEnd = $attendance->shift_to->addMinutes($attendance->flexible_hours);

            if ($attendance->out_type && $attendance->check_out?->greaterThan($flexibleShiftEnd)) {
                $diff = $flexibleShiftEnd->diffAsCarbonInterval($attendance->check_out);

                // difference less than 60 second is not considered late
                $value = $diff->greaterThan(CarbonInterval::seconds(59)) ? $diff : $acc;
            }

            return $value ? $acc->add($value) : $acc;
        }, CarbonInterval::minutes(0));
    }
}
