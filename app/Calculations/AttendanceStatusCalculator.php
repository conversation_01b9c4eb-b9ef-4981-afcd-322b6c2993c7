<?php

namespace App\Calculations;

use App\DTOs\Stats\AttendanceStatusStats;
use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use Illuminate\Support\Collection;

class AttendanceStatusCalculator
{
    /**
     * @param  Collection<Attendance>  $attendances
     */
    public function calculateStats(Collection $attendances): AttendanceStatusStats
    {
        return new AttendanceStatusStats(
            total: $attendances->count(),
            present: $attendances->where('status', AttendanceStatus::PRESENT->value)->count(),
            absent: $attendances->where('status', AttendanceStatus::ABSENT->value)->count(),
            yet: $attendances->where('status', AttendanceStatus::YET->value)->count(),
            weekend: $attendances->where('status', AttendanceStatus::WEEKEND->value)->count(),
            holiday: $attendances->where('status', AttendanceStatus::HOLIDAY->value)->count(),
            leave: $attendances->where('status', AttendanceStatus::LEAVE->value)->count()
        );
    }

    public function percentages(AttendanceStatusStats $stats): AttendanceStatusStats
    {
        return new AttendanceStatusStats(
            total: 100,
            present: percentage($stats->present, $stats->total),
            absent: percentage($stats->absent, $stats->total),
            yet: percentage($stats->yet, $stats->total),
            weekend: percentage($stats->weekend, $stats->total),
            holiday: percentage($stats->holiday, $stats->total),
            leave: percentage($stats->leave, $stats->total)
        );
    }

    public function calculateByDays(Collection $attendances): Collection
    {
        $attendances = $attendances->groupBy(fn($attendance) => $attendance->date->format('l'));

        return collect([
            'sunday' => $this->calculateStats($attendances['Sunday'] ?? collect()),
            'monday' => $this->calculateStats($attendances['Monday'] ?? collect()),
            'tuesday' => $this->calculateStats($attendances['Tuesday'] ?? collect()),
            'wednesday' => $this->calculateStats($attendances['Wednesday'] ?? collect()),
            'thursday' => $this->calculateStats($attendances['Thursday'] ?? collect()),
            'saturday' => $this->calculateStats($attendances['Saturday'] ?? collect()),
            'friday' => $this->calculateStats($attendances['Friday'] ?? collect()),
        ]);
    }
}
