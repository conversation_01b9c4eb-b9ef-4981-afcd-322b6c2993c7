<?php

namespace App\Calculations;

use App\DTOs\EarlyLateMailData;
use App\DTOs\Stats\AttendanceStatusStats;
use App\DTOs\Stats\EarlyLateStats;
use App\DTOs\Stats\EmployeesStats;
use App\DTOs\Stats\MoodStats;
use App\Enums\AttendanceMood;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Collection;

class EarlyLatePeriodicalMailCalculations
{
    private MoodStats $inMoodStats;

    private MoodStats $outMoodStats;

    /**@var AttendanceStatusStats[] */
    private array $attendanceStatsByDays;

    /**@var EarlyLateStats[] */
    private array $earlyLateStatsByDays;

    private Collection $calculatedAttendanceIds;

    private MostLeastCommittedAttendanceCalculator $mostLeastCommitted;

    private Collection $employeesIds;

    protected Team $team;

    public function __construct(protected EarlyLateMailData $earlyLateMailData)
    {
        $this->calculatedAttendanceIds = collect();

        $this->employeesIds = collect();

        $this->attendanceStatsByDays = [
            'sunday' => new AttendanceStatusStats(),
            'monday' => new AttendanceStatusStats(),
            'tuesday' => new AttendanceStatusStats(),
            'wednesday' => new AttendanceStatusStats(),
            'thursday' => new AttendanceStatusStats(),
            'friday' => new AttendanceStatusStats(),
            'saturday' => new AttendanceStatusStats(),
        ];

        $this->earlyLateStatsByDays = [
            'sunday' => new EarlyLateStats(),
            'monday' => new EarlyLateStats(),
            'tuesday' => new EarlyLateStats(),
            'wednesday' => new EarlyLateStats(),
            'thursday' => new EarlyLateStats(),
            'friday' => new EarlyLateStats(),
            'saturday' => new EarlyLateStats(),
        ];

        $this->mostLeastCommitted = new MostLeastCommittedAttendanceCalculator();

        $this->inMoodStats = new MoodStats();
        $this->outMoodStats = new MoodStats();

        $this->team = $this->earlyLateMailData->manager->team;
    }

    private function processDepartments(): void
    {
        $this->team
            ->departments()
            ->whereIn('id', $this->earlyLateMailData->departmentsIDs)
            ->each(function (Department $department) {
                $attendances = $this->team
                    ->attendances()
                    ->withWhereHas(
                        'employee',
                        fn($query) => $query
                            ->with('tags')
                            ->excludeTags($this->team->early_late_config->excludedTags)
                            ->where('department_id', $department->id)
                    )
                    ->whereBetween('date', [
                        $this->earlyLateMailData->period->start,
                        $this->earlyLateMailData->period->end,
                    ])
                    ->orderBy('created_at')
                    ->get()
                    ->map(function (Attendance $attendance) use ($department) {
                        $attendance->employee->setRelation('department', $department);

                        return $attendance;
                    });

                $this->addToCalculations($attendances);
            });
    }

    private function processDirectlyManagedEmployees(): void
    {
        $attendances = $this->team
            ->attendances()
            ->withWhereHas(
                'employee',
                fn($query) => $query
                    ->with(['tags', 'department'])
                    ->excludeTags($this->team->early_late_config->excludedTags)
                    ->whereIn(
                        'id',
                        $this->earlyLateMailData->manager
                            ->directlyManagedEmployees()
                            ->pluck('id')
                            ->toArray()
                    )
            )
            ->whereBetween('date', [
                $this->earlyLateMailData->period->start,
                $this->earlyLateMailData->period->end,
            ])
            ->orderBy('created_at')
            ->get();

        $this->addToCalculations($attendances);
    }

    private function addToCalculations(Collection $attendances): void
    {
        // make sure to not calculate the same attendance twice
        $attendances = $attendances->filter(
            fn($att) => $this->calculatedAttendanceIds->doesntContain($att->id)
        );

        if ($attendances->isEmpty()) {
            return;
        }

        $this->employeesIds = $this->employeesIds
            ->merge($attendances->pluck('employee_id')->unique())
            ->unique();

        (new AttendanceStatusCalculator())
            ->calculateByDays($attendances)
            ->each(fn($stats, $day) => $this->attendanceStatsByDays[$day]->increment($stats));

        (new EarlyLateCalculator())
            ->calculateStatsByDays($attendances)
            ->each(fn($stats, $day) => $this->earlyLateStatsByDays[$day]->increment($stats));

        $this->calculateMoods($attendances);

        $this->calculatedAttendanceIds = $this->calculatedAttendanceIds->merge(
            $attendances->pluck('id')
        );
    }

    private function calculateMoods(Collection $attendances): void
    {
        $inMoodStats = (new MoodCalculator())->calculate($attendances, AttendanceMood::InMood);

        $this->inMoodStats->increment($inMoodStats);

        $outMoodStats = (new MoodCalculator())->calculate($attendances, AttendanceMood::OutMood);

        $this->outMoodStats->increment($outMoodStats);
    }

    private function processEmployeeStatsAndMostLeastCommitted(): EmployeesStats
    {
        $employeeQuery = $this->team
            ->employees()
            ->whereIn('id', $this->employeesIds)
            ->active()
            ->with([
                'approvalRequests' => fn($query) => $query
                    ->permission()
                    ->date($this->earlyLateMailData->period),
            ])
            ->withWhereHas(
                'attendances',
                fn($query) => $query->whereBetween('date', [
                    $this->earlyLateMailData->period->start,
                    $this->earlyLateMailData->period->end,
                ])
            );

        $employeeStats = new EmployeesStats(total: $employeeQuery->count());

        $employeeQuery->each(function (Employee $employee) use ($employeeStats) {
            $hasAdditionalHours = (new AdditionalHoursCalculator())
                ->calculate($employee->attendances)
                ->hasPositiveValues();

            $hasPermissionRequests = $employee->approvalRequests->isNotEmpty();

            $employeeStats->increment(
                new EmployeesStats(
                    employeesHasAdditionalHoursCount: $hasAdditionalHours ? 1 : 0,
                    employeesHasPermissionRequestsCount: $hasPermissionRequests ? 1 : 0
                )
            );

            $this->mostLeastCommitted->nominate($employee);
        });

        return $employeeStats;
    }

    public function calculate(): array
    {
        $this->processDepartments();

        $this->processDirectlyManagedEmployees();

        /** @var AttendanceStatusStats $attendanceSum */
        $attendanceSum = collect($this->attendanceStatsByDays)->reduce(
            fn(AttendanceStatusStats $acc, AttendanceStatusStats $stats) => $acc->increment($stats),
            new AttendanceStatusStats()
        );

        /** @var EarlyLateStats $earlyLateSum */
        $earlyLateSum = collect($this->earlyLateStatsByDays)->reduce(
            fn(EarlyLateStats $acc, EarlyLateStats $stats) => $acc->increment($stats),
            new EarlyLateStats()
        );

        return [
            'attendancesStats' => [
                'sunday' => $this->attendanceStatsByDays['sunday']->percentages(),
                'monday' => $this->attendanceStatsByDays['monday']->percentages(),
                'tuesday' => $this->attendanceStatsByDays['tuesday']->percentages(),
                'wednesday' => $this->attendanceStatsByDays['wednesday']->percentages(),
                'thursday' => $this->attendanceStatsByDays['thursday']->percentages(),
                'friday' => $this->attendanceStatsByDays['friday']->percentages(),
                'saturday' => $this->attendanceStatsByDays['saturday']->percentages(),
                'sum' => $attendanceSum->percentages(),
            ],
            'earlyLateStats' => [
                'sunday' => $this->earlyLateStatsByDays['sunday']->percentages(),
                'monday' => $this->earlyLateStatsByDays['monday']->percentages(),
                'tuesday' => $this->earlyLateStatsByDays['tuesday']->percentages(),
                'wednesday' => $this->earlyLateStatsByDays['wednesday']->percentages(),
                'thursday' => $this->earlyLateStatsByDays['thursday']->percentages(),
                'friday' => $this->earlyLateStatsByDays['friday']->percentages(),
                'saturday' => $this->earlyLateStatsByDays['saturday']->percentages(),
                'sum' => $earlyLateSum->percentages(),
            ],

            'employeesStats' => $this->processEmployeeStatsAndMostLeastCommitted(),
            'mostLeastCommitted' => $this->mostLeastCommitted,
            'employeesCount' => $this->employeesIds->count(),
            'inMood' => $this->inMoodStats->percentages()->formatForEarlyLateMail(),
            'outMood' => $this->outMoodStats->percentages()->formatForEarlyLateMail(),
        ];
    }
}
