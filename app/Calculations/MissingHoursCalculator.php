<?php

namespace App\Calculations;

use App\Models\Attendance;
use Carbon\CarbonInterval;
use Illuminate\Support\Collection;

class MissingHoursCalculator
{
    public function calculate(Attendance|Collection $attendances): CarbonInterval
    {
        return Collection::wrap($attendances)->reduce(function (
            CarbonInterval $acc,
            Attendance $attendance
        ) {
            $netHours = $attendance->net_hours_as_carbon_interval;

            $missingHours = $attendance->shift_duration->greaterThan($netHours)
                ? $attendance->shift_duration->subtract($netHours)
                : CarbonInterval::seconds(0);

            return $acc->add($missingHours);
        }, CarbonInterval::seconds(0));
    }
}
