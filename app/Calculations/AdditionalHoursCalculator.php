<?php

namespace App\Calculations;

use App\Models\Attendance;
use Carbon\CarbonInterval;
use Illuminate\Support\Collection;

class AdditionalHoursCalculator
{
    public function calculate(Attendance|Collection $attendances): CarbonInterval
    {
        return Collection::wrap($attendances)->reduce(function (
            CarbonInterval $acc,
            Attendance $attendance
        ) {
            $netHours = $attendance->net_hours_as_carbon_interval;

            $additionalHours = $attendance->shift_duration->lessThan($netHours)
                ? $netHours->subtract($attendance->shift_duration)
                : CarbonInterval::seconds(0);

            return $acc->add($additionalHours);
        }, CarbonInterval::seconds(0));
    }
}
