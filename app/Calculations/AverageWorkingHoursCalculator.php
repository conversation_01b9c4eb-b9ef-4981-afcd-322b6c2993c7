<?php

namespace App\Calculations;

use App\DTOs\WeekDays;
use App\Models\Attendance;
use Illuminate\Database\Eloquent\Builder;

class AverageWorkingHoursCalculator
{
    public function calculateByArrayOfHours(array $hours): float|int
    {
        if (empty($hours)) {
            return 0;
        }

        return array_sum($hours) / count($hours);
    }

    public function calculateStatsByDays(Builder $query): WeekDays
    {
        $hoursForEachDay = new WeekDays(
            sunday: [],
            monday: [],
            tuesday: [],
            wednesday: [],
            thursday: [],
            friday: [],
            saturday: []
        );

        $query->each(function (Attendance $attendance) use (&$hoursForEachDay) {
            $day = strtolower($attendance->date->format('l'));

            $hoursForEachDay->$day[] = $attendance->net_hours_as_carbon_interval->totalHours;
        }, 500);

        return new WeekDays(
            sunday: $this->calculateByArrayOfHours($hoursForEachDay->sunday),
            monday: $this->calculateByArrayOfHours($hoursForEachDay->monday),
            tuesday: $this->calculateByArrayOfHours($hoursForEachDay->tuesday),
            wednesday: $this->calculateByArrayOfHours($hoursForEachDay->wednesday),
            thursday: $this->calculateByArrayOfHours($hoursForEachDay->thursday),
            friday: $this->calculateByArrayOfHours($hoursForEachDay->friday),
            saturday: $this->calculateByArrayOfHours($hoursForEachDay->saturday)
        );
    }
}
