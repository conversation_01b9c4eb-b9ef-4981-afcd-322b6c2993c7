<?php

namespace App\Calculations;

use App\DTOs\Stats\MoodStats;
use App\Enums\AttendanceMood;
use App\Enums\Mood;
use App\Models\Attendance;
use Illuminate\Support\Collection;

class MoodCalculator
{
    public function calculate(
        Collection $attendances,
        AttendanceMood $attendanceMoodType
    ): MoodStats {
        return new MoodStats(
            total: $attendances->count(),
            veryHappy: $this->countByMood($attendances, Mood::VeryHappy, $attendanceMoodType),
            happy: $this->countByMood($attendances, Mood::Happy, $attendanceMoodType),
            neutral: $this->countByMood($attendances, Mood::Neutral, $attendanceMoodType),
            sad: $this->countByMood($attendances, Mood::Sad, $attendanceMoodType),
            verySad: $this->countByMood($attendances, Mood::VerySad, $attendanceMoodType)
        );
    }

    protected function countByMood(
        Collection $attendances,
        Mood $mood,
        AttendanceMood $attendanceMoodType
    ): int {
        return $attendances
            ->filter(fn(Attendance $att) => $att->{$attendanceMoodType->value} === $mood->value)
            ->count();
    }
}
