<?php

namespace App\Calculations;

use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder;

class TodayAttendanceCalculator
{
    public function calculate(Builder|Relation $employeesQuery): array
    {
        return [
            'total' => $employeesQuery->count(),
            'present' => $employeesQuery
                ->clone()
                ->whereHas('activeAttendanceRecord', fn($query) => $query->present())
                ->count(),
            'absent' => $employeesQuery
                ->clone()
                ->whereHas('activeAttendanceRecord', fn($query) => $query->absent())
                ->count(),
            'leave' => $employeesQuery
                ->clone()
                ->whereHas('activeAttendanceRecord', fn($query) => $query->leave())
                ->count(),
            'yet' => $employeesQuery
                ->clone()
                ->whereHas('activeAttendanceRecord', fn($query) => $query->yet())
                ->count(),
            'upcoming_shift' => $employeesQuery
                ->clone()
                ->whereHas(
                    'activeAttendanceRecord',
                    fn($query) => $query->yet()->where('shift_from', '>', now())
                )
                ->count(),
        ];
    }
}
