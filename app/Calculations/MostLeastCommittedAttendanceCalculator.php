<?php

namespace App\Calculations;

use App\DTOs\Stats\MostLeastCommittedStats;
use App\Enums\AttendanceStatus;
use App\Models\Employee;
use Illuminate\Support\Collection;

class MostLeastCommittedAttendanceCalculator
{
    public function __construct(
        private ?Collection $mostCommittedList = null,
        private ?Collection $leastCommittedList = null,
        private ?int $count = 5
    ) {
        $this->mostCommittedList = $this->mostCommittedList ?? collect();
        $this->leastCommittedList = $this->leastCommittedList ?? collect();
    }

    public function mostCommittedList(): Collection
    {
        return $this->mostCommittedList;
    }

    public function leastCommittedList(): Collection
    {
        return $this->leastCommittedList;
    }

    public function nominate(Employee $employee): void
    {
        if ($this->nominateToMostCommitted($employee)) {
            return;
        }

        $this->nominateToLeastCommitted($employee);
    }

    public function nominateToMostCommitted(Employee $employee): bool
    {
        $stats = $this->calculateStats($employee);

        if (!$stats) {
            return false;
        }

        $this->mostCommittedList = $this->mostCommittedList
            ->push($stats)
            // most fulfilled count will be first
            ->sortBy(fn(MostLeastCommittedStats $stats) => $stats->missingHours->totalSeconds)
            ->take($this->count)
            ->values();

        return $this->mostCommittedList->contains(fn($stats) => $stats->employee->is($employee));
    }

    public function nominateToLeastCommitted(Employee $employee): bool
    {
        $stats = $this->calculateStats($employee);

        if (!$stats) {
            return false;
        }

        $this->leastCommittedList = $this->leastCommittedList
            ->push($stats)
            // least fulfilled count will be first
            ->sortByDesc(fn(MostLeastCommittedStats $stats) => $stats->missingHours->totalSeconds)
            ->take($this->count)
            ->values();

        return $this->leastCommittedList->contains(fn($stats) => $stats->employee->is($employee));
    }

    public function calculateStats(Employee $employee): ?MostLeastCommittedStats
    {
        $attendanceToConsider = $employee->attendances->whereIn('status', [
            AttendanceStatus::PRESENT->value,
            AttendanceStatus::ABSENT->value,
        ]);

        if ($attendanceToConsider->isEmpty()) {
            return null;
        }

        $missingHours = (new MissingHoursCalculator())->calculate($attendanceToConsider);

        $totalHours = (new TotalCommittedHoursCalculator())->calculate($attendanceToConsider);

        $percentage = percentage(
            $missingHours->totalSeconds,
            $totalHours->totalSeconds,
            // flip the percentage to show the fulfilled percentage
            flip: true
        );

        return new MostLeastCommittedStats(
            employee: $employee,
            missingHours: $missingHours,
            attendanceRatePercentage: $percentage
        );
    }
}
