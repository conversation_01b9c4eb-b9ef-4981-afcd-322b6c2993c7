<?php

namespace App\Calculations;

use App\DTOs\Stats\EmployeeBalanceStats;
use App\DTOs\Stats\EmployeePermissionBalance;
use App\DTOs\Stats\EmployeeRemoteWorkBalance;
use App\Enums\RemoteWorkPolicy;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;

readonly class EmployeeBalanceCalculator
{
    public function __construct(private Employee $employee, private CarbonPeriod $period)
    {
    }

    public function calculate(): EmployeeBalanceStats
    {
        return new EmployeeBalanceStats(
            remoteWork: $this->remainingRemoteWorkDays(),
            permission: $this->remainingPermissionRequests(),
            regularization: $this->remainingRegularizationRequests(),
            leave: null // no limit currently
        );
    }

    public function remainingRemoteWorkDays(): ?EmployeeRemoteWorkBalance
    {
        if ($this->employee->team->remote_work === RemoteWorkPolicy::ALLOWED->value) {
            return new EmployeeRemoteWorkBalance(null, null, null, null);
        } elseif ($this->employee->team->remote_work === RemoteWorkPolicy::NOT_ALLOWED->value) {
            return null;
        }

        $yearlyRemoteWorkRequests = $this->employee
            ->approvalRequests()
            ->selectRaw('DATE(from_datetime) as date')
            ->withCasts(['date' => 'date'])
            ->remoteWork()
            ->approved()
            ->date(today()->startOfYear(), today()->endOfYear())
            ->groupBy('date')
            ->pluck('date');

        $monthlyRemoteWorkRequests = $yearlyRemoteWorkRequests->filter(
            fn(Carbon $date) => $date->isCurrentMonth()
        );

        $weeklyRemoteWork = $yearlyRemoteWorkRequests->filter(
            fn(Carbon $date) => $date->isLocalCurrentWeek()
        );

        $yearlyRemainingBalance = $this->employee->team->remote_work_days_yearly_limit
            ? $this->employee->team->remote_work_days_yearly_limit -
                $yearlyRemoteWorkRequests->count()
            : null;

        $monthlyRemainingBalance = $this->employee->team->remote_work_days_monthly_limit
            ? $this->employee->team->remote_work_days_monthly_limit -
                $monthlyRemoteWorkRequests->count()
            : null;

        $weeklyRemainingBalance = $this->employee->team->remote_work_days_weekly_limit
            ? $this->employee->team->remote_work_days_weekly_limit - $weeklyRemoteWork->count()
            : null;

        return new EmployeeRemoteWorkBalance(
            $yearlyRemainingBalance,
            $monthlyRemainingBalance,
            $weeklyRemainingBalance,
            currentBalance: collect([
                $yearlyRemainingBalance,
                $monthlyRemainingBalance,
                $weeklyRemainingBalance,
            ])->min()
        );
    }

    public function remainingPermissionRequests(): ?EmployeePermissionBalance
    {
        if (!$this->employee->team->permission_request) {
            return null;
        }

        $monthlyPermissionRequests = $this->employee
            ->approvalRequests()
            ->permission()
            ->whereIn('status', [ApprovalRequest::PENDING, ApprovalRequest::APPROVED])
            ->date(CarbonImmutable::today()->startOfMonth(), CarbonImmutable::today()->endOfMonth())
            ->get();

        $monthlyPermissionsDuration = CarbonInterval::second(
            $monthlyPermissionRequests
                ->map(function (ApprovalRequest $permissionRequest) {
                    return $permissionRequest->duration->totalSeconds;
                })
                ->sum()
        );

        $todayPermissionsDuration = CarbonInterval::second(
            $monthlyPermissionRequests
                ->filter(function (ApprovalRequest $permissionRequest) {
                    return $permissionRequest->from_datetime->isCurrentDay();
                })
                ->map(function (ApprovalRequest $permissionRequest) {
                    return $permissionRequest->duration->totalSeconds;
                })
                ->sum()
        );

        $monthlyLimitDuration = $this->employee->team->permission_request_monthly_limit_hours
            ? CarbonInterval::hour($this->employee->team->permission_request_monthly_limit_hours)
            : null;

        $monthlyBalance = $monthlyLimitDuration?->sub($monthlyPermissionsDuration);

        $dailyLimitDuration = $this->employee->team->permission_request_daily_limit_hours
            ? CarbonInterval::hour($this->employee->team->permission_request_daily_limit_hours)
            : null;

        $dailyBalance = $dailyLimitDuration?->sub($todayPermissionsDuration);

        $minInSeconds = collect([$monthlyBalance, $dailyBalance])->min(
            fn(?CarbonInterval $interval) => is_null($interval) ? null : $interval->totalSeconds
        );

        $balance = is_null($minInSeconds) ? null : CarbonInterval::second($minInSeconds);

        return new EmployeePermissionBalance(
            $monthlyBalance?->cascade(),
            $dailyBalance?->cascade(),
            $balance?->cascade()
        );
    }

    public function remainingRegularizationRequests(): int
    {
        return $this->remainingRequests(ApprovalRequest::REGULARIZATION);
    }

    public function remainingRequests(string $type): int
    {
        $requestsCount = $this->employee
            ->approvalRequests()
            ->where('type', $type)
            ->pendingOrApproved()
            ->date($this->period)
            ->count();

        return $this->employee->team->approval_requests_limit - $requestsCount;
    }

    public static function remoteWorkBalance(
        Employee $employee,
        CarbonPeriod $period
    ): EmployeeRemoteWorkBalance|null {
        return (new static($employee, $period))->remainingRemoteWorkDays();
    }
}
