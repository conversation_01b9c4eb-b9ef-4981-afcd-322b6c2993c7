<?php

namespace App\Exports;

use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Tag;
use App\Support\ExcelStyles;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\LazyCollection;
use Illuminate\Support\Str;
use OpenSpout\Common\Entity\Style\Color;
use Spatie\SimpleExcel\SimpleExcelWriter;

class EmployeesExport
{
    private string $tempPath;

    /**
     * @param LazyCollection<Employee> $employees
     */
    public function __construct(private readonly LazyCollection $employees)
    {
        $this->tempPath = 'temp/' . Str::uuid() . '.xlsx';
    }

    public function handle(string $filePath): bool
    {
        Storage::disk('local')->put($this->tempPath, contents: '');

        $this->createExcel();
        return $this->savePermanently($filePath);
    }

    private function createExcel(): void
    {
        SimpleExcelWriter::create(Storage::disk('local')->path($this->tempPath))
            ->setHeaderStyle((new ExcelStyles())->headerStyle(Color::BLACK))
            ->addHeader([
                "Name\nالموظف",
                "Employee ID\nالرقم الوظيفي",
                "Email\nالبريد الإلكتروني",
                "Tag\nالتصنيف",
                "Position\nالمسمى الوظيفي",
                "Department\nالإدارة",
                "Direct manager\nالمدير المباشر",
                "Shift\nالفترة",
                "Location\nالموقع",
            ])
            ->addRows($this->addEmployees())
            ->close();
    }

    public function addEmployees(): array
    {
        return $this->employees
            ->map(
                fn(Employee $employee) => [
                    $employee->name,
                    $employee->number,
                    $employee->email,
                    $employee->tags->map(fn(Tag $tag) => $tag->name)->join(', '),
                    $employee->position,
                    $employee->department->name,
                    $employee->manager?->name,
                    $employee->shifts
                        ?->map(
                            fn(Shift $shift) => $shift->pivot->permanent
                                ? $shift->name
                                : $shift->name .
                                    '(from ' .
                                    Carbon::parse($shift->pivot->start_at)->toDateString() .
                                    ' to ' .
                                    Carbon::parse($shift->pivot->end_at)->toDateString() .
                                    ')'
                        )
                        ->join(', '),
                    $employee->locations
                        ?->map(fn(Location $location) => $location->name)
                        ->join(', '),
                ]
            )
            ->toArray();
    }

    private function savePermanently(string $filePath): bool
    {
        Storage::disk('minio')->put(
            path: $filePath,
            contents: Storage::disk('local')->get($this->tempPath)
        );

        return Storage::disk('local')->delete($this->tempPath);
    }
}
