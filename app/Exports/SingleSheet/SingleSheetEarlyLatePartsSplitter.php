<?php

namespace App\Exports\SingleSheet;

use App\Interfaces\ExcelPartsCollection;
use App\Interfaces\ExcelPartsSplitter;
use App\Models\ReportTask;
use Carbon\Carbon;

class SingleSheetEarlyLatePartsSplitter implements ExcelPartsSplitter
{
    public function splitToParts(ReportTask $reportTask): ExcelPartsCollection
    {
        $parts = new ExcelPartsCollection();

        collect($reportTask->data->period)->each(function (Carbon $date) use ($reportTask, $parts) {
            $parts->addPart(new SingleSheetEarlyLatePart(offset: $date->format('Y-m-d')));
        });

        return $parts;
    }
}
