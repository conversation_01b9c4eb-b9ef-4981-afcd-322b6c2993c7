<?php

namespace App\Exports\SingleSheet;

use App\Calculations\AdditionalHoursCalculator;
use App\Interfaces\ExcelPart;
use App\Interfaces\ExcelPartCreator;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\ReportTask;
use App\Support\IntervalFormat;
use Carbon\CarbonPeriod;
use Str;

class SingleSheetEarlyLatePartCreator implements ExcelPartCreator
{
    public function createPart(ReportTask $reportTask, ExcelPart $part): mixed
    {
        return $part
            ->fetchPartValues($reportTask)
            ->map(fn(Attendance $att) => $this->buildCells($att));
    }

    private function buildCells(Attendance $attendance): array
    {
        return [
            $attendance->date->toDateString(),
            $attendance->employee->number,
            $attendance->employee->name,
            $attendance->employee->tags->pluck('name')->implode(', '),
            $attendance->employee->department?->rootAncestor?->name,
            $attendance->employee->department?->name,
            $attendance->shift?->name,
            IntervalFormat::toHoursMinutes($attendance->shift_duration),
            $attendance->check_in?->toTimeString(),
            $attendance->check_out?->toTimeString(),
            $attendance->net_hours->toTimeString(),
            IntervalFormat::toHoursMinutes($attendance->actualDurationWithinShift()),
            IntervalFormat::toHoursMinutes(
                ApprovalRequest::calculatePermissionsDuration(
                    employee: $attendance->employee,
                    period: CarbonPeriod::create($attendance->shift_from, $attendance->shift_to)
                )
            ),
            IntervalFormat::toHoursMinutes($attendance->noneCompleteWorkHours()),
            IntervalFormat::toHoursMinutes(
                (new AdditionalHoursCalculator())->calculate($attendance)
            ),
            $attendance->early_in,
            $attendance->late_in,
            $attendance->early_out,
            $attendance->late_out,
            $attendance->checkInDevice?->name ?? config('app.default_device_name'),
            $attendance->checkInLocation?->name,
            $attendance->checkoutDevice?->name ?? config('app.default_device_name'),
            $attendance->checkoutLocation?->name,
            Str::of(str_replace('_', ' ', $attendance->singleSheetComputedStatus()))->title(),
            $attendance->singleSheetBackgroundColor(), // to be removed after merging all pages
        ];
    }
}
