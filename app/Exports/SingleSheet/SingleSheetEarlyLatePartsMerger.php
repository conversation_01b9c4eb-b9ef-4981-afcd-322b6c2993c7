<?php

namespace App\Exports\SingleSheet;

use App\Interfaces\ExcelPartsMerger;
use App\Models\ReportTask;
use App\Support\ExcelStyles;
use Illuminate\Support\Collection;
use OpenSpout\Common\Entity\Cell;
use OpenSpout\Common\Entity\Row;
use OpenSpout\Writer\WriterInterface;
use Spatie\SimpleExcel\SimpleExcelWriter;

class SingleSheetEarlyLatePartsMerger extends ExcelPartsMerger
{
    public function configureOptions(WriterInterface $writer): void
    {
        $options = $writer->getOptions();

        $options->setColumnWidth(15, 1); // Date
        $options->setColumnWidth(15, 2); // Employee ID
        $options->setColumnWidth(30, 3); // Name
        $options->setColumnWidth(30, 4); // Tag
        $options->setColumnWidth(30, 5); // Parent Department
        $options->setColumnWidth(30, 6); // Department
        $options->setColumnWidth(20, 7); // Shift
        $options->setColumnWidth(15, 8); // Shift Hours
        $options->setColumnWidth(15, 9); // First In
        $options->setColumnWidth(15, 10); // Last Out
        $options->setColumnWidth(20, 11); // Full working hours
        $options->setColumnWidth(25, 12); // In-Shift Hours
        $options->setColumnWidth(15, 13); // Permission
        $options->setColumnWidth(25, 14); // Non-Complete Working Hours
        $options->setColumnWidth(25, 15); // Additional Working Hours
        $options->setColumnWidth(10, 16); // Early
        $options->setColumnWidth(10, 17); // Late
        $options->setColumnWidth(10, 18); // Early
        $options->setColumnWidth(15, 19); // Late
        $options->setColumnWidth(20, 20); // Device Type
        $options->setColumnWidth(20, 21); // Check in
        $options->setColumnWidth(20, 22); // Device Type
        $options->setColumnWidth(20, 23); // Check out
        $options->setColumnWidth(10, 24); // Status

        $options->mergeCells(15, 6, 16, 6); // for Check in - upper header
        $options->mergeCells(17, 6, 18, 6); // for Check out - upper header
        $options->mergeCells(19, 6, 22, 6); // for Location - upper header
    }

    public function beforeMerging(SimpleExcelWriter $writer, ReportTask $reportTask): void
    {
        $this->addMetaHeader($writer, $reportTask);

        $this->addTableHeader($writer);
    }

    public function mergePart(
        SimpleExcelWriter $writer,
        Collection $values,
        ReportTask $reportTask
    ): void {
        $values->each(function (array $cells) use ($writer) {
            $blackBorderStyle = ExcelStyles::blackBorderStyle();

            $bg = array_pop($cells); // last cell is background color

            if ($bg) {
                $blackBorderStyle->setBackgroundColor($bg);
            }

            $newRow = Row::fromValues($cells, $blackBorderStyle)->setHeight(25);

            $writer->addRow($newRow);
        });
    }

    public function addMetaHeader(SimpleExcelWriter $writer, ReportTask $reportTask): void
    {
        $headerStyle = ExcelStyles::headerStyle();
        $blackBorderStyle = ExcelStyles::blackBorderStyle();

        $printDate = (new Row([
            Cell::fromValue('Print Date', $headerStyle),
            Cell::fromValue(today()->toDateString(), $blackBorderStyle),
            Cell::fromValue(''),
            Cell::fromValue("Period\nالفترة", $headerStyle),
        ]))->setHeight(30);

        $from = (new Row([
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(
                $reportTask->data->period->getStartDate()->toDateString(),
                $blackBorderStyle
            ),
        ]))->setHeight(25);

        $to = (new Row([
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(
                $reportTask->data->period->getEndDate()->toDateString(),
                $blackBorderStyle
            ),
        ]))->setHeight(25);

        $writer->addRows([Row::fromValues(), Row::fromValues(), $printDate, $from, $to]);
    }

    public function addTableHeader(SimpleExcelWriter $writer): void
    {
        // upper table header
        $headerStyle = ExcelStyles::headerStyle();

        $row = (new Row([
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue("Check in\nالدخول", $headerStyle),
            Cell::fromValue('', $headerStyle),
            Cell::fromValue("Check out\nالخروج", $headerStyle),
            Cell::fromValue('', $headerStyle),
            Cell::fromValue("Location\nالموقع", $headerStyle),
            Cell::fromValue('', $headerStyle),
            Cell::fromValue('', $headerStyle),
            Cell::fromValue('', $headerStyle),
        ]))->setHeight(40);

        $writer->addRow($row);

        $row = (new Row([
            Cell::fromValue("Date\nالتاريخ", $headerStyle),
            Cell::fromValue("Employee ID\nرقم الموظف", $headerStyle),
            Cell::fromValue("Name\nالموظف", $headerStyle),
            Cell::fromValue("Tag\nالتصنيف", $headerStyle),
            Cell::fromValue("Parent Department\nالإدارة الأم", $headerStyle),
            Cell::fromValue("Department\nالإدارة", $headerStyle),
            Cell::fromValue("Shift\nالفترة", $headerStyle),
            Cell::fromValue("Shift Hours\nساعات الفترة", $headerStyle),
            Cell::fromValue("First In\nأول دخول", $headerStyle),
            Cell::fromValue("Last Out\nآخر خروج", $headerStyle),
            Cell::fromValue("Full working hours\nساعات العمل الكاملة", $headerStyle),
            Cell::fromValue("In-Shift Hours\nساعات العمل أثناء الفترة", $headerStyle),
            Cell::fromValue("Permission\nالاستئذان", $headerStyle),
            Cell::fromValue("Non-Complete Working Hours\nساعات العمل غير المكتملة", $headerStyle),
            Cell::fromValue("Additional Working Hours\nساعات العمل الإضافية", $headerStyle),
            Cell::fromValue("Early\nمبكر", $headerStyle),
            Cell::fromValue("Late\nمتأخر", $headerStyle),
            Cell::fromValue("Early\nمبكر", $headerStyle),
            Cell::fromValue("Late\nمتأخر", $headerStyle),
            Cell::fromValue("Device Type\nنوع الجهاز", $headerStyle),
            Cell::fromValue("Check in\nالدخول", $headerStyle),
            Cell::fromValue("Device Type\nنوع الجهاز", $headerStyle),
            Cell::fromValue("Check out\nالخروج", $headerStyle),
            Cell::fromValue("Status\nالحالة", $headerStyle),
        ]))->setHeight(40);

        $writer->addRow($row);
    }
}
