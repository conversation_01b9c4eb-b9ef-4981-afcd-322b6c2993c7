<?php

namespace App\Exports\SingleSheet;

use App\Interfaces\ExcelPart;
use App\Models\ReportTask;
use App\Queries\EarlyLateReportQuery;
use Illuminate\Support\Collection;

class SingleSheetEarlyLatePart extends ExcelPart
{
    public function fetchPartValues(ReportTask $reportTask): Collection
    {
        return (new EarlyLateReportQuery($reportTask))
            ->attendanceQuery()
            ->with([
                'shift',
                'checkInLocation',
                'checkoutLocation',
                'checkInDevice',
                'checkoutDevice',
                'employee' => fn($q) => $q->with([
                    'tags',
                    'shifts',
                    'department.rootAncestor',
                    'approvalRequests' => fn($q) => $q
                        ->approved()
                        ->regularization()
                        ->date($reportTask->data->period),
                ]),
            ])
            ->date($this->offset)
            ->get();
    }
}
