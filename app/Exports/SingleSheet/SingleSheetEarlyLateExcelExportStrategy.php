<?php

namespace App\Exports\SingleSheet;

use App\Interfaces\ExcelExportStrategy;
use App\Interfaces\ExcelPartCreator;
use App\Interfaces\ExcelPartsMerger;
use App\Interfaces\ExcelPartsSplitter;

class SingleSheetEarlyLateExcelExportStrategy implements ExcelExportStrategy
{
    public function creator(): ExcelPartCreator
    {
        return new SingleSheetEarlyLatePartCreator();
    }

    public function merger(): ExcelPartsMerger
    {
        return new SingleSheetEarlyLatePartsMerger();
    }

    public function splitter(): ExcelPartsSplitter
    {
        return new SingleSheetEarlyLatePartsSplitter();
    }
}
