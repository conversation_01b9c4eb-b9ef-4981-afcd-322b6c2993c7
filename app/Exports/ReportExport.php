<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;

class ReportExport implements FromView, WithEvents
{
    use Exportable;
    use RegistersEventListeners;

    public function __construct(public string $view, public array $data)
    {
    }

    public function view(): View
    {
        return view($this->view, $this->data);
    }

    public static function afterSheet(AfterSheet $event): void
    {
        // add padding cells
        $worksheet = $event->sheet->getDelegate();
        $worksheet->insertNewRowBefore(1, 2);
        $worksheet->insertNewRowBefore(6, 2);

        $worksheet->insertNewColumnBefore('A', 2);
        $worksheet->insertNewColumnBefore('B', 2);
    }
}
