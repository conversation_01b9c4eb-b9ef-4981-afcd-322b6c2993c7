<?php

namespace App\Exports\MultiSheet;

use App\Interfaces\ExcelPartsMerger;
use App\Models\ReportTask;
use App\Support\ExcelStyles;
use Illuminate\Support\Collection;
use OpenSpout\Common\Entity\Cell;
use OpenSpout\Common\Entity\Row;
use OpenSpout\Writer\WriterInterface;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Str;

class MultiSheetEarlyLatePartsMerger extends ExcelPartsMerger
{
    public function configureOptions(WriterInterface $writer): void
    {
        $index = $writer->getCurrentSheet()->getIndex();

        $options = $writer->getOptions();

        $options->setColumnWidth(25, 2);
        $options->setColumnWidth(15, 3);
        $options->setColumnWidth(15, 4);
        $options->setColumnWidth(15, 5);
        $options->setColumnWidth(20, 6);
        $options->setColumnWidth(20, 7);
        $options->setColumnWidth(20, 8);
        $options->setColumnWidth(20, 9);
        $options->setColumnWidth(20, 11);
        $options->setColumnWidth(20, 12);
        $options->setColumnWidth(20, 13);
        $options->setColumnWidth(20, 14);
        $options->setColumnWidth(20, 15);

        $options->mergeCells(1, 6, 5, 6, $index);
        $options->mergeCells(2, 8, 4, 8, $index);
        $options->mergeCells(2, 9, 4, 9, $index);
        $options->mergeCells(2, 10, 4, 10, $index);

        $options->mergeCells(6, 8, 7, 8, $index);
        $options->mergeCells(6, 9, 7, 9, $index);

        $options->mergeCells(1, 12, 2, 12, $index);
        $options->mergeCells(1, 13, 2, 13, $index);
        $options->mergeCells(1, 14, 2, 14, $index);
        $options->mergeCells(1, 15, 2, 15, $index);
        $options->mergeCells(1, 16, 2, 16, $index);
        $options->mergeCells(1, 17, 2, 17, $index);
        $options->mergeCells(1, 18, 2, 18, $index);

        $options->mergeCells(5, 12, 6, 12, $index);
        $options->mergeCells(5, 13, 6, 13, $index);
        $options->mergeCells(5, 14, 6, 14, $index);
        $options->mergeCells(5, 15, 6, 15, $index);
        $options->mergeCells(5, 16, 6, 16, $index);

        $options->mergeCells(5, 20, 6, 20, $index);
        $options->mergeCells(7, 20, 8, 20, $index);
        $options->mergeCells(10, 20, 13, 20, $index);
    }

    public function mergePart(
        SimpleExcelWriter $writer,
        Collection $values,
        ReportTask $reportTask
    ): void {
        $values->each(function ($sheetData) use ($values, $reportTask, $writer) {
            $employee = $sheetData['employee'];

            $summary = $sheetData['summary'];
            $attendanceRows = $sheetData['attendance_rows'];

            $writer->nameCurrentSheet(
                Str::limit(
                    $employee['name'] . '-' . ($employee['number'] ?? $employee['email']),
                    25
                )
            );

            $writer->addRows([[], [], [], []]);

            $this->addTitle($writer, $reportTask);

            $writer->addRow([]);

            $this->addEmployeeInfo($writer, $employee);

            $writer->addRow([]);

            $this->addSums($writer, $summary);

            $writer->addRow([]);

            $this->addTableHeader($writer);

            $this->addAttendanceTable($writer, $attendanceRows);

            $writer->addNewSheetAndMakeItCurrent();

            $this->configureOptions($writer->getWriter());
        });
    }

    public function addTitle(SimpleExcelWriter $writer, ReportTask $reportTask): void
    {
        $titleCell = Cell::fromValue(
            'Early/Late Report Of Attendances',
            ExcelStyles::center()->setFontSize(22)->setFontBold()->setFontColor('148576')
        );

        $dateCell = Cell::fromValue(
            $reportTask->end_date
                ? "From {$reportTask->start_date->toDateString()} \n To {$reportTask->end_date->toDateString()}"
                : $reportTask->start_date->toDateString(),
            ExcelStyles::center()
        );

        $title = (new Row([
            Cell::fromValue(''),
            $titleCell,
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            Cell::fromValue(''),
            $dateCell,
        ]))->setHeight(50);

        $writer->addRow($title);
    }

    public function addEmployeeInfo(SimpleExcelWriter $writer, array $employee): void
    {
        $labelStyle = ExcelStyles::center()
            ->setFontSize(12)
            ->setFontBold()
            ->setFontColor('FFFFFF')
            ->setBackgroundColor('148576');

        $cellStyle = ExcelStyles::center()->setBorder(ExcelStyles::border('D9D9D9'));

        // Employee Name row
        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue('Employee Name', $labelStyle),
                Cell::fromValue($employee['name'], $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue('Department', $labelStyle),
                Cell::fromValue($employee['department_name'], $cellStyle),
            ]))->setHeight(15)
        );

        // Employee ID row
        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue('Employee ID', $labelStyle),
                Cell::fromValue($employee['number'], $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue('Manager', $labelStyle),
                Cell::fromValue($employee['manager_name'], $cellStyle),
            ]))->setHeight(15)
        );

        // Email row
        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue('Email', $labelStyle),
                Cell::fromValue($employee['email'], $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue('', $cellStyle),
            ]))->setHeight(15)
        );
    }

    public function addSums(SimpleExcelWriter $writer, array $summary): void
    {
        $defaultLabelStyle = ExcelStyles::label()
            ->setFontColor('FFFFFF')
            ->setBackgroundColor('148576');

        $cellStyle = ExcelStyles::center()->setBorder(ExcelStyles::border('D9D9D9'));

        // Total hours row
        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue("Total Hours\nإجمالي الساعات", $defaultLabelStyle),
                Cell::fromValue(''),
                Cell::fromValue($summary['totalActualHours'] ?? '00:00', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue(
                    "Official Holiday\nعطلة رسمية",
                    ExcelStyles::label()->setBackgroundColor(
                        ExcelStyles::ATTENDANCE_STATUS_COLOR['OFFICIAL_HOLIDAY']
                    )
                ),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue($summary['officialHoliday'] ?? '0', $cellStyle),
            ]))->setHeight(40)
        );

        // Additional Hours row
        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue(
                    "Total Additional Hours\nإجمالي الساعات الإضافية",
                    $defaultLabelStyle
                ),
                Cell::fromValue(''),
                Cell::fromValue($summary['totalAdditionalHours'] ?? '00:00', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue(
                    "Absent\nغائب",
                    ExcelStyles::label()->setBackgroundColor(
                        ExcelStyles::ATTENDANCE_STATUS_COLOR['ABSENT']
                    )
                ),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue($summary['absentCount'] ?? '0', $cellStyle),
            ]))->setHeight(40)
        );

        // Additional Hours Day by Day row
        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue(
                    "Total Additional Hours (Day-By-Day)\nإجمالي الساعات الإضافية (يوم بيوم)",
                    $defaultLabelStyle
                ),
                Cell::fromValue(''),
                Cell::fromValue($summary['totalAdditionalHoursDayByDay'] ?? '00:00', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue(
                    "Forget to check-out\nنسيان تسجيل الخروج",
                    ExcelStyles::label()->setBackgroundColor(
                        ExcelStyles::ATTENDANCE_STATUS_COLOR['FORGET_CHECKOUT']
                    )
                ),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue($summary['forgotToCheckoutCount'] ?? '0', $cellStyle),
            ]))->setHeight(40)
        );

        // Late Check-in row
        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue(
                    "non-complete Work Hours\nساعات العمل غير المكتملة",
                    $defaultLabelStyle
                ),
                Cell::fromValue(''),
                Cell::fromValue($summary['totalLateIn'] ?? '00:00', $cellStyle),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue(
                    "Leave\nإجازة",
                    ExcelStyles::label()->setBackgroundColor(
                        ExcelStyles::ATTENDANCE_STATUS_COLOR['LEAVE']
                    )
                ),
                Cell::fromValue('', $cellStyle),
                Cell::fromValue($summary['leaveCount'] ?? '0', $cellStyle),
            ]))->setHeight(40)
        );

        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue(
                    "Total Late Check-in\nإجمالي تسجيل الدخول المتأخر",
                    $defaultLabelStyle
                ),
                Cell::fromValue(''),
                Cell::fromValue($summary['totalLateIn'] ?? '00:00', $cellStyle),
            ]))->setHeight(40)
        );

        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue(
                    "Total Early Check-out\nإجمالي تسجيل الخروج المبكر",
                    $defaultLabelStyle
                ),
                Cell::fromValue(''),
                Cell::fromValue($summary['totalEarlyOut'] ?? '00:00', $cellStyle),
            ]))->setHeight(40)
        );

        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue("Total Remote days\nأجمالي أيام العمل عن بعد", $defaultLabelStyle),
                Cell::fromValue(''),
                Cell::fromValue($summary['remoteWorkCount'] ?? '00:00', $cellStyle),
            ]))->setHeight(40)
        );
    }

    private function addTableHeader(SimpleExcelWriter $writer): void
    {
        $headerStyle = ExcelStyles::label()
            ->setFontSize(10)
            ->setFontColor('FFFFFF')
            ->setBackgroundColor('148576');

        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue("Date\nالتاريخ", $headerStyle),
                Cell::fromValue("First In\nأول دخول", $headerStyle),
                Cell::fromValue("Last Out\nآخر خروج", $headerStyle),
                Cell::fromValue("Total Hours\nإجمالي الساعات", $headerStyle),
                Cell::fromValue("Check-in\nالدخول", $headerStyle),
                Cell::fromValue(''),
                Cell::fromValue("Check-out\nالخروج", $headerStyle),
                Cell::fromValue(''),
                Cell::fromValue("Shift\nالفترة", $headerStyle),
                Cell::fromValue("Location\nالموقع", $headerStyle),
                Cell::fromValue(''),
                Cell::fromValue(''),
                Cell::fromValue(''),
            ]))->setHeight(35)
        );

        $subHeaderStyle = ExcelStyles::center()->setFontSize(10);

        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue(''),
                Cell::fromValue(''),
                Cell::fromValue(''),
                Cell::fromValue(''),
                Cell::fromValue("Early\nمبكر", $subHeaderStyle),
                Cell::fromValue("Late\nمتأخر", $subHeaderStyle),
                Cell::fromValue("Early\nمبكر", $subHeaderStyle),
                Cell::fromValue("Late\nمتأخر", $subHeaderStyle),
                Cell::fromValue(''),
                Cell::fromValue("Device Type\nنوع الجهاز", $subHeaderStyle),
                Cell::fromValue("Check in\nالدخول", $subHeaderStyle),
                Cell::fromValue("Device Type\nنوع الجهاز", $subHeaderStyle),
                Cell::fromValue("Check out\nالخروج", $subHeaderStyle),
            ]))->setHeight(35)
        );
    }

    private function addAttendanceTable(SimpleExcelWriter $writer, array $attendances): void
    {
        foreach ($attendances as $attendance) {
            /** @see MultiSheetEarlyLatePartCreator::buildCells() */
            $bg = array_pop($attendance); // last cell is background color
            $status = array_pop($attendance); // before last cell is status

            $style = ExcelStyles::center()->setBorder(ExcelStyles::border('D9D9D9'));

            if ($bg) {
                $style->setBackgroundColor($bg);
            }

            if ($status === 'PRESENT' || $status === 'FORGET_CHECKOUT') {
                $attendance = [
                    Cell::fromValue(''), // empty cell at the start
                    ...array_map(fn($cell) => Cell::fromValue($cell, $style), $attendance),
                ];

                $newRow = (new Row($attendance))->setHeight(25);

                $writer->addRow($newRow);
            }

            $displayStatus = match ($status) {
                'LEAVE' => 'Leave',
                'ABSENT' => 'Absent',
                'OFFICIAL_HOLIDAY' => 'Official Holiday',
                'WEEKEND' => 'weekend',
                'NOT_YET' => 'Yet to check in',
                default => '',
            };

            $newRow = (new Row([
                Cell::fromValue(''),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
                Cell::fromValue($displayStatus, $style->setFontSize(10)),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
                Cell::fromValue('', $style),
            ]))->setHeight(25);

            $writer->addRow($newRow);
        }
    }
}
