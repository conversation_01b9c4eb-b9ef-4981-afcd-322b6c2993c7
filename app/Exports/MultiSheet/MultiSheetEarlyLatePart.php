<?php

namespace App\Exports\MultiSheet;

use App\Interfaces\ExcelPart;
use App\Models\ReportTask;
use App\Queries\EarlyLateReportQuery;
use Illuminate\Support\Collection;

class MultiSheetEarlyLatePart extends ExcelPart
{
    public function fetchPartValues(ReportTask $reportTask): Collection
    {
        return (new EarlyLateReportQuery($reportTask))
            ->employeeQuery()
            ->withWhereHas(
                'attendances',
                fn($q) => $q
                    ->with([
                        'shift',
                        'checkInLocation',
                        'checkoutLocation',
                        'checkInDevice',
                        'checkoutDevice',
                    ])
                    ->date($reportTask->data->period)
            )
            ->with(['department', 'manager'])
            ->limit($this->limit)
            ->offset($this->offset)
            ->orderBy('id')
            ->get();
    }
}
