<?php

namespace App\Exports\MultiSheet;

use App\Interfaces\ExcelExportStrategy;
use App\Interfaces\ExcelPartCreator;
use App\Interfaces\ExcelPartsMerger;
use App\Interfaces\ExcelPartsSplitter;

class MultiSheetEarlyLateExcelStrategy implements ExcelExportStrategy
{
    public function creator(): ExcelPartCreator
    {
        return new MultiSheetEarlyLatePartCreator();
    }

    public function merger(): ExcelPartsMerger
    {
        return new MultiSheetEarlyLatePartsMerger();
    }

    public function splitter(): ExcelPartsSplitter
    {
        return new MultiSheetEarlyLatePartsSplitter();
    }
}
