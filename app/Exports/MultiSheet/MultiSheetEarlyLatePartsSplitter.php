<?php

namespace App\Exports\MultiSheet;

use App\Interfaces\ExcelPartsCollection;
use App\Interfaces\ExcelPartsSplitter;
use App\Models\ReportTask;
use App\Queries\EarlyLateReportQuery;
use Illuminate\Support\Collection;

class MultiSheetEarlyLatePartsSplitter implements ExcelPartsSplitter
{
    const EMPLOYEES_LIMIT_PER_JOB = 10;

    public function splitToParts(ReportTask $reportTask): ExcelPartsCollection
    {
        $parts = new ExcelPartsCollection();

        $totalEmployees = (new EarlyLateReportQuery($reportTask))->employeeQuery()->count();

        $totalPages = max(ceil($totalEmployees / self::EMPLOYEES_LIMIT_PER_JOB), 1);

        Collection::range(0, $totalPages - 1)->each(
            fn(int $page) => $parts->addPart(
                new MultiSheetEarlyLatePart(
                    offset: $page * self::EMPLOYEES_LIMIT_PER_JOB,
                    limit: self::EMPLOYEES_LIMIT_PER_JOB
                )
            )
        );

        return $parts;
    }
}
