<?php

namespace App\Exports\SingleSummarySheet;

use App\Interfaces\ExcelExportStrategy;
use App\Interfaces\ExcelPartCreator;
use App\Interfaces\ExcelPartsMerger;
use App\Interfaces\ExcelPartsSplitter;

class SingleSummarySheetEarlyLateExcelExportStrategy implements ExcelExportStrategy
{
    public function splitter(): ExcelPartsSplitter
    {
        return new SingleSummarySheetEarlyLatePartsSplitter();
    }

    public function creator(): ExcelPartCreator
    {
        return new SingleSummarySheetEarlyLatePartCreator();
    }

    public function merger(): ExcelPartsMerger
    {
        return new SingleSummarySheetEarlyLatePartsMerger();
    }
}
