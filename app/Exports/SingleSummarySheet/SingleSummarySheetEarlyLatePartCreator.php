<?php

namespace App\Exports\SingleSummarySheet;

use App\Enums\AttendanceStatus;
use App\Interfaces\ExcelPart;
use App\Interfaces\ExcelPartCreator;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Support\IntervalFormat;
use Carbon\CarbonInterval;
use Illuminate\Support\Collection;

class SingleSummarySheetEarlyLatePartCreator implements ExcelPartCreator
{
    public function createPart(ReportTask $reportTask, ExcelPart $part): Collection
    {
        return $part
            ->fetchPartValues($reportTask)
            ->map(fn(Employee $employee) => $this->buildEmployeeData($employee, $reportTask));
    }

    private function buildEmployeeData(Employee $employee, ReportTask $reportTask): array
    {
        $sums = $employee->attendances->reduce(
            fn(array $sums, Attendance $attendance) => [
                'lateIn' => $sums['lateIn']->add(
                    CarbonInterval::createFromFormat('- H:i', $attendance->late_in)
                ),
                'earlyOut' => $sums['earlyOut']->add(
                    CarbonInterval::createFromFormat('- H:i', $attendance->early_out)
                ),
                'totalCommittedHours' => $sums['totalCommittedHours']->addSeconds(
                    $attendance->committed_hours
                ),
                'totalActualHours' => $sums['totalActualHours']->addSeconds(
                    $attendance->actual_hours_in_sec
                ),
            ],
            [
                'lateIn' => CarbonInterval::hour(0),
                'earlyOut' => CarbonInterval::hour(0),
                'totalCommittedHours' => CarbonInterval::hour(0),
                'totalActualHours' => CarbonInterval::hour(0),
            ]
        );

        $nonCompleteWorkingHours = $sums['totalCommittedHours']->greaterThan(
            $sums['totalActualHours']
        )
            ? $sums['totalCommittedHours']->subtract($sums['totalActualHours'])
            : CarbonInterval::hour(0);

        $absentCount = $employee->attendances
            ->where('status', AttendanceStatus::ABSENT->value)
            ->count();

        return [
            'employeeID' => $employee->number,
            'name' => $employee->name,
            'department' => $employee->department->name,
            'absent' => $absentCount > 1 ? "$absentCount days" : "$absentCount day",
            'leaves' =>
                $employee->leaves_count > 1
                    ? "$employee->leaves_count days"
                    : "$employee->leaves_count day",
            'permission' => ApprovalRequest::calculatePermissionsDuration(
                employee: $employee,
                period: $reportTask->data->period
            )
                ->cascade()
                ->locale('en')
                ->forHumans(['options' => 0]),
            'regularizationRequests' => $employee->approval_requests_count,
            'forgotToCheckout' => $employee->attendances
                ->where('status', Attendance::PRESENT)
                ->whereNull('out_type')
                ->count(),
            'lateIn' => IntervalFormat::toHoursMinutes($sums['lateIn']->cascade()),
            'earlyOut' => IntervalFormat::toHoursMinutes($sums['earlyOut']->cascade()),
            'totalActualHours' => IntervalFormat::toHoursMinutes(
                $sums['totalActualHours']->cascade()
            ),
            'nonCompleteWorkingHours' => IntervalFormat::toHoursMinutes(
                $nonCompleteWorkingHours->cascade()
            ),
        ];
    }
}
