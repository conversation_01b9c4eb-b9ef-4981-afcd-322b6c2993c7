<?php

namespace App\Exports\SingleSummarySheet;

use App\Interfaces\ExcelPartsMerger;
use App\Models\ReportTask;
use App\Support\ExcelStyles;
use Illuminate\Support\Collection;
use OpenSpout\Common\Entity\Cell;
use OpenSpout\Common\Entity\Row;
use OpenSpout\Writer\WriterInterface;
use Spatie\SimpleExcel\SimpleExcelWriter;

class SingleSummarySheetEarlyLatePartsMerger extends ExcelPartsMerger
{
    public function configureOptions(WriterInterface $writer): void
    {
        $options = $writer->getOptions();

        $options->setColumnWidth(15, 1);
        $options->setColumnWidth(25, 2);
        $options->setColumnWidth(30, 3);
        $options->setColumnWidth(15, 4);
        $options->setColumnWidth(15, 5);
        $options->setColumnWidth(20, 6);
        $options->setColumnWidth(20, 7);
        $options->setColumnWidth(20, 8);
        $options->setColumnWidth(20, 9);
        $options->setColumnWidth(20, 10);
        $options->setColumnWidth(20, 11);
        $options->setColumnWidth(25, 12);
        $options->setColumnWidth(25, 13);

        $options->mergeCells(5, 4, 6, 4); // period from to
        $options->mergeCells(1, 6, 5, 6); // title
    }

    public function beforeMerging(SimpleExcelWriter $writer, ReportTask $reportTask): void
    {
        $writer->addRows([[], []]);

        $this->addMetaHeader($writer, $reportTask);

        $writer->addRow([]);

        $this->addTitle($writer);

        $writer->addRow([]);

        $this->addTableHeader($writer);
    }

    public function mergePart(
        SimpleExcelWriter $writer,
        Collection $values,
        ReportTask $reportTask
    ): void {
        $style = ExcelStyles::center()->setBorder(ExcelStyles::border());

        $values->each(function (array $row) use ($writer, $style) {
            $cells = collect($row)->map(fn($cell) => Cell::fromValue($cell, $style))->toArray();

            $writer->addRow((new Row([Cell::fromValue(''), ...$cells]))->setHeight(20));
        });
    }

    public function addMetaHeader(SimpleExcelWriter $writer, ReportTask $reportTask): void
    {
        $headerStyle = ExcelStyles::headerStyle();
        $blackBorderStyle = ExcelStyles::blackBorderStyle();

        $dates = $reportTask->end_date
            ? "From {$reportTask->start_date->toDateString()} \n To {$reportTask->end_date->toDateString()}"
            : $reportTask->start_date->toDateString();

        $printDate = (new Row([
            Cell::fromValue(''),
            Cell::fromValue('Print Date', $headerStyle),
            Cell::fromValue(today()->toDateString(), $blackBorderStyle),
            Cell::fromValue(''),
            Cell::fromValue("Period\nالفترة", $headerStyle),
            Cell::fromValue($dates, $blackBorderStyle),
            Cell::fromValue('', $blackBorderStyle),
        ]))->setHeight(30);

        $writer->addRow($printDate);
    }

    public function addTitle(SimpleExcelWriter $writer): void
    {
        $style = ExcelStyles::center()
            ->setFontSize(14)
            ->setFontBold()
            ->setBackgroundColor('D3D3D3');

        $titleCell = Cell::fromValue('ATTENDANCE OVERVIEW REPORT', $style);

        $title = (new Row([
            Cell::fromValue(''),
            $titleCell,
            Cell::fromValue('', $style),
            Cell::fromValue('', $style),
            Cell::fromValue('', $style),
            Cell::fromValue('', $style),
        ]))->setHeight(50);

        $writer->addRow($title);
    }

    private function addTableHeader(SimpleExcelWriter $writer): void
    {
        $headerStyle = ExcelStyles::label()
            ->setFontSize(10)
            ->setBackgroundColor('D3D3D3')
            ->setBorder(ExcelStyles::border());

        $writer->addRow(
            (new Row([
                Cell::fromValue(''),
                Cell::fromValue("ID\nرقم الموظف", $headerStyle),
                Cell::fromValue("Name\nالموظف", $headerStyle),
                Cell::fromValue("Department\nالقسم", $headerStyle),
                Cell::fromValue("Absent\nالغياب", $headerStyle),
                Cell::fromValue("Leaves\nالإجازات", $headerStyle),
                Cell::fromValue("Permission\nالاستئذان", $headerStyle),
                Cell::fromValue("Regularization Requests\nطلبات تنظيم الحضور", $headerStyle),
                Cell::fromValue("Forget to check-out\nنسيان تسجيل الخروج", $headerStyle),
                Cell::fromValue("Late Sign In\nتسجيل دخول متأخر", $headerStyle),
                Cell::fromValue("Early Sign out\nتسجيل خروج متأخر", $headerStyle),
                Cell::fromValue("Full working hours\nساعات العمل الكاملة", $headerStyle),
                Cell::fromValue(
                    "Non-Complete Working Hours\nساعات العمل غير المكتملة",
                    $headerStyle
                ),
            ]))->setHeight(40)
        );
    }
}
