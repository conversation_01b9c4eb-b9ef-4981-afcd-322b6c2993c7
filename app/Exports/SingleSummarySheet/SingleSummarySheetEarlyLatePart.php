<?php

namespace App\Exports\SingleSummarySheet;

use App\Interfaces\ExcelPart;
use App\Models\ReportTask;
use App\Queries\EarlyLateReportQuery;
use Illuminate\Support\Collection;

class SingleSummarySheetEarlyLatePart extends ExcelPart
{
    public function fetchPartValues(ReportTask $reportTask): Collection
    {
        return (new EarlyLateReportQuery($reportTask))
            ->employeeQuery()
            ->with([
                'tags',
                'shifts',
                'department.rootAncestor',
                'attendances' => fn($q) => $q->date($reportTask->data->period),
            ])
            ->withCount([
                'leaves' => fn($q) => $q->approved()->date($reportTask->data->period),
                'approvalRequests' => fn($q) => $q
                    ->approved()
                    ->regularization()
                    ->date($reportTask->data->period),
            ])
            ->excludeTags($reportTask->team->early_late_config->excludedTags)
            ->limit($this->limit)
            ->offset($this->offset)
            ->get();
    }
}
