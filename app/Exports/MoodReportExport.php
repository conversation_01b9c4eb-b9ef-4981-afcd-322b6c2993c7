<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class MoodReportExport implements WithMultipleSheets
{
    use Exportable;

    public function __construct(protected array $data)
    {
    }

    public function sheets(): array
    {
        return [
            new Sheets\MoodPerShiftSheet($this->data['mood_per_shift']),
            new Sheets\MoodPerDepartmentSheet($this->data['mood_per_department']),
        ];
    }
}
