<?php

namespace App\Exports\Sheets;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class MoodPerShiftSheet implements
    FromCollection,
    ShouldAutoSize,
    WithHeadings,
    WithMapping,
    WithTitle
{
    private Collection $data;

    public function __construct(Collection $data)
    {
        $this->data = $data;
    }

    public function collection(): Collection
    {
        return $this->data;
    }

    public function title(): string
    {
        return 'mood per shift';
    }

    public function headings(): array
    {
        return ['Shift', 'Check-in average mood', 'Check-out average mood'];
    }

    public function map($row): array
    {
        return [$row['shift_name'], $row['in_mood'], $row['out_mood']];
    }
}
