<?php

namespace App\Exports\Sheets;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;

class MoodPerDepartmentSheet implements
    FromCollection,
    ShouldAutoSize,
    WithHeadings,
    WithMapping,
    WithTitle
{
    private Collection $data;

    public function __construct(Collection $data)
    {
        $this->data = $data;
    }

    public function collection(): Collection
    {
        return $this->data;
    }

    public function title(): string
    {
        return 'mood per department';
    }

    public function headings(): array
    {
        return ['Department', 'Check-in average mood', 'Check-out average mood'];
    }

    public function map($row): array
    {
        return [$row['department_name'], $row['in_mood'], $row['out_mood']];
    }
}
