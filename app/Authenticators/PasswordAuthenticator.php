<?php

namespace App\Authenticators;

use App\DTOs\Identifier;
use App\Interfaces\Authenticators\BaseAuthenticatorInterface;
use App\Interfaces\Authenticators\PasswordAuthenticatorInterface;
use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;

/**
 * @codeCoverageIgnore until we are using it
 */
class PasswordAuthenticator implements BaseAuthenticatorInterface, PasswordAuthenticatorInterface
{
    private $errors = [];

    public function configure(TenantAuthenticatorConfig $config): void
    {
    }

    public function authenticate(
        Identifier $identifier,
        Tenant $tenant,
        ?string $password = null
    ): bool {
        return false; // not supported

        $credentials = [
            'email' => $identifier,
            'password' => $password,
            'tenant_id' => $tenant->id,
        ];

        if (!auth()->attempt($credentials)) {
            $this->errors[] = trans('auth.failed');

            return false;
        }

        return true;
    }

    public function errors(): array
    {
        return $this->errors;
    }
}
