<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class ShiftDeleted
{
    use SerializesModels;

    public $shift;

    public $name = 'shift_deleted';

    public $team_id;

    public $payload = [];

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($shift)
    {
        $this->shift = $shift;
        $this->team_id = $shift->team_id;
        $this->payload = $shift->only($shift->webhookPayload);
    }
}
