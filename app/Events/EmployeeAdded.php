<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class EmployeeAdded
{
    use SerializesModels;

    public $employee;

    public $name = 'employee_added';

    public $team_id;

    public $payload = [];

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($employee)
    {
        $this->employee = $employee;
        $this->team_id = $employee->team_id;
        $this->payload = $employee->only($employee->webhookPayload);
    }
}
