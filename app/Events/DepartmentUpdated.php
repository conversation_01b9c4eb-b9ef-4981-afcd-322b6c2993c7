<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class DepartmentUpdated
{
    use SerializesModels;

    public $department;

    public $name = 'department_updated';

    public $team_id;

    public $payload = [];

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($department)
    {
        $this->department = $department;
        $this->team_id = $department->team_id;
        $this->payload = $department->only($department->webhookPayload);
    }
}
