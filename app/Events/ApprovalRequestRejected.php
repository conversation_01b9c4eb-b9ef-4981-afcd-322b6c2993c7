<?php

namespace App\Events;

use App\Enums\RequestStatus;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Str;

class ApprovalRequestRejected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public $approvalRequest;

    public $name = 'approvalRequest_rejected';

    public $id;

    public $team_id;

    public $payload = [];

    public function __construct($approvalRequest)
    {
        $this->approvalRequest = $approvalRequest;
        $this->id = $approvalRequest->id;
        $this->team_id = $approvalRequest->team_id;
        $this->payload = [
            ...$approvalRequest->only($approvalRequest->webhookPayload),
            // todo: we dont need these when we migration database data for example from `pending` to `PENDING`
            'status' => RequestStatus::fromStringOrInstance($approvalRequest->status)->value,
            'type' => Str::upper($approvalRequest->type),
            'email' => $approvalRequest->employee->email,
        ];
    }
}
