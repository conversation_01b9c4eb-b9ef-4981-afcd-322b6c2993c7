<?php

namespace App\Events;

use App\Models\Activity;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EmployeeCheckedIn
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Activity $activity;

    public $name = 'employee_checkin';

    public $team_id;

    public $payload = [];

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Activity $activity)
    {
        $this->activity = $activity;
        $this->team_id = $activity->team_id;
        $this->payload = $activity->only($activity->webhookPayload) + [
            'email' => $activity->employee->email,
        ];
    }
}
