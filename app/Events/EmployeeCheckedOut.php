<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EmployeeCheckedOut
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $activity;

    public $name = 'employee_checkout';

    public $team_id;

    public $payload = [];

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($activity)
    {
        $this->activity = $activity;
        $this->team_id = $activity->team_id;
        $this->payload = $activity->only($activity->webhookPayload) + [
            'email' => $activity->employee->email,
        ];
    }
}
