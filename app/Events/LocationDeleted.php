<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;

class LocationDeleted
{
    use SerializesModels;

    public $location;

    public $name = 'location_deleted';

    public $team_id;

    public $payload = [];

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($location)
    {
        $this->location = $location;
        $this->team_id = $location->team_id;
        $this->payload = $location->only($location->webhookPayload);
    }
}
