<?php

namespace App\Listeners;

use App\Enums\CheckoutReminderConfig;
use App\Events\EmployeeCheckedIn;
use App\Notifications\CheckoutReminderNotification;
use Carbon\CarbonInterval;
use Illuminate\Contracts\Queue\ShouldQueue;

class ScheduleCheckoutReminder implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(EmployeeCheckedIn $event): void
    {
        $employee = $event->activity->employee;

        $duration = $employee->shift?->durationOfDay();

        if (!$duration) {
            return;
        }

        $delay = match ($employee->team->checkout_reminder_config) {
            // the employee would be notified 15 minutes before end of today required work hours ends after checkin.
            CheckoutReminderConfig::ByCheckin => $duration->subMinutes(15),
            // the employee would be notified 15 minutes before flexible shift ends
            // or end of today required work hours after checkin, whichever comes first.
            CheckoutReminderConfig::ByFlexibleHoursEnd => now()->diff(
                min(
                    $employee->activeAttendanceRecord->flexible_shift_end,
                    now()->add($duration)
                )->subMinutes(15)
            ),
            // the notification has been scheduled in PrepareEmployeeAttendanceRecord job
            CheckoutReminderConfig::ByShiftEnd => null,
        };

        if (!$delay || $delay->lessThan(CarbonInterval::hours(0))) {
            return;
        }

        $employee->notify((new CheckoutReminderNotification())->delay(now()->add($delay)));
    }
}
