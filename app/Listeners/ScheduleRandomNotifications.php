<?php

namespace App\Listeners;

use App\DTOs\RandomProofNotificationConfig;
use App\Enums\ProofMethod;
use App\Events\EmployeeCheckedIn;
use App\Models\Attendance;
use App\Models\Employee;
use App\Notifications\ProofAttendanceRequested;
use Carbon\CarbonImmutable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;

class ScheduleRandomNotifications implements ShouldQueue
{
    const BUFFER_MINUTES = 30;

    public function __construct(
        protected ?Employee $employee = null,
        protected ?Attendance $attendance = null,
        protected ?RandomProofNotificationConfig $randomProofNotificationConfig = null
    ) {
    }

    /**
     * Handle the event.
     */
    public function handle(EmployeeCheckedIn $event): void
    {
        $this->employee = $event->activity->employee;

        $this->attendance = $this->employee->firstOrCreateActiveAttendanceRecord();

        $this->randomProofNotificationConfig = $this->proofNotificationConfig();

        if (
            !$this->attendance ||
            $this->attendance->random_notifications ||
            !$this->randomProofNotificationConfig->enabled
        ) {
            return;
        }

        $this->notificationsDates()->each(
            fn($date) => $this->employee->notify(
                (new ProofAttendanceRequested(ProofMethod::Random))->delay($date)
            )
        );

        $this->attendance->update(['random_notifications' => true]);
    }

    public function proofNotificationConfig(): RandomProofNotificationConfig
    {
        $randomProofNotificationConfig = $this->employee->random_proof_notification_config;

        $department = $this->employee->department;

        if ($randomProofNotificationConfig->inherited && $department->exists) {
            $randomProofNotificationConfig = $department->random_proof_notification_config;
        }

        if ($randomProofNotificationConfig->inherited) {
            $randomProofNotificationConfig =
                $this->employee->team->random_proof_notification_config;
        }

        return $randomProofNotificationConfig;
    }

    public function notificationsDates(): Collection
    {
        $shiftStart = CarbonImmutable::parse($this->attendance->shift_from)->addMinutes(
            random_int(15, self::BUFFER_MINUTES) // Add a buffer to avoid sending notifications immediately
        );

        $shiftEnd = CarbonImmutable::parse($this->attendance->shift_to);
        $shiftDuration = $shiftStart->diffInMinutes($shiftEnd);

        $notificationCount = $this->randomProofNotificationConfig->count;

        if ($notificationCount <= 0 || $shiftDuration <= 0) {
            return collect();
        }

        // Divide the shift duration evenly into notification slots
        // to ensure notifications are spread out within the shift duration
        $interval = (int) floor($shiftDuration / $notificationCount);

        return Collection::times($notificationCount, function ($index) use (
            $shiftStart,
            $interval,
            $shiftEnd
        ) {
            $slotStart = $shiftStart->addMinutes(($index - 1) * $interval);
            $slotEnd = $slotStart->addMinutes($interval);

            // Ensure slotEnd does not exceed shiftEnd
            if ($slotEnd->greaterThan($shiftEnd)) {
                $slotEnd = $shiftEnd;
            }

            // Avoid invalid min/max range
            $slotDuration = $slotEnd->diffInMinutes($slotStart);

            if ($slotDuration <= 0) {
                return $slotStart; // Use the start of the slot if randomization is not possible
            }

            return $slotStart->addMinutes(random_int(0, $slotDuration - 1));
        })
            // Filter out dates that are before the checkin date, so if employee checks in late, they won't receive notifications for the past
            ->filter(fn(CarbonImmutable $date) => $date->greaterThan($this->attendance->check_in));
    }
}
