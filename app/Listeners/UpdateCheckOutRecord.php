<?php

namespace App\Listeners;

use App\Events\EmployeeCheckedOut;
use Carbon\Carbon;

class UpdateCheckOutRecord
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(EmployeeCheckedOut $event)
    {
        $attendanceRecord = $event->activity->employee->activeAttendanceRecord;

        // allow next day checkout
        $diff = $attendanceRecord->check_in->diffInSeconds($event->activity->created_at);

        $attendanceRecord->update([
            'check_out' => $event->activity->created_at,
            'out_type' => $event->activity->action,
            'net_hours' => Carbon::today()->addSeconds($diff),
            'on_duty' => false,
            'check_out_location_id' => $event->activity->location_id,
            'check_out_device_id' => $event->activity->device_id,
        ]);
    }
}
