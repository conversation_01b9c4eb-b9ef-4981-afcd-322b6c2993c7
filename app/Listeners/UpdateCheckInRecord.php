<?php

namespace App\Listeners;

use App\Events\EmployeeCheckedIn;
use App\Models\Attendance;

class UpdateCheckInRecord
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(EmployeeCheckedIn $event)
    {
        $attendanceRecord = $event->activity->employee->activeAttendanceRecord;

        if ($attendanceRecord->in_type === null) {
            $attendanceRecord->update([
                'check_in' => $event->activity->created_at,
                'in_type' => $event->activity->action,
                'status' => Attendance::PRESENT,
                'on_duty' => true,
                'check_in_location_id' => $event->activity->location_id,
                'check_in_device_id' => $event->activity->device_id,
            ]);
        } else {
            $attendanceRecord->update(['on_duty' => true]);
        }
    }
}
