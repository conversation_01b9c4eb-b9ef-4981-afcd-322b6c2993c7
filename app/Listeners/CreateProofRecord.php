<?php

namespace App\Listeners;

use App\Enums\ProofStatus;
use App\Models\Employee;
use App\Models\Proof;
use App\Notifications\ProofAttendanceRequested;
use Illuminate\Notifications\Events\NotificationSent;

class CreateProofRecord
{
    public function handle(NotificationSent $event): void
    {
        if (
            $event->notification instanceof ProofAttendanceRequested &&
            $event->channel === 'NotificationChannels\OneSignal\OneSignalChannel'
        ) {
            /** @var Employee $employee */
            $employee = $event->notifiable;

            Proof::create([
                'team_id' => $employee->team_id,
                'employee_id' => $employee->id,
                'notification_id' => $event->notification->id,
                'method' => $event->notification->method,
                'status' => ProofStatus::Sent,
                'expire_at' => now()->addMinutes(
                    (int) $employee->team->random_proof_notification_config->deadline->value
                ),
            ]);
        }
    }
}
