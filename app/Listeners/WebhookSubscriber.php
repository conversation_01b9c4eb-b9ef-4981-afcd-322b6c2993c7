<?php

namespace App\Listeners;

use App\Models\Webhook;
use App\Models\WebhookJob;
use App\Scopes\TenantScope;
use Illuminate\Contracts\Queue\ShouldQueue;

class WebhookSubscriber
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $event_name = $event->name;
        $team_id = $event->team_id;

        $webhooks = Webhook::withoutGlobalScope(TenantScope::class)
            ->where('team_id', $team_id)
            ->where('event_name', $event_name)
            ->where('active', 1)
            ->get();

        if (count($webhooks)) {
            foreach ($webhooks as $webhook) {
                WebhookJob::query()->create([
                    'webhook_id' => $webhook->id,
                    'event_payload' => $event->payload,
                ]);
            }
        }
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param  \Illuminate\Events\Dispatcher  $events
     * @return array
     */
    public function subscribe($events)
    {
        return [
            \App\Events\DepartmentAdded::class => 'handle',
            \App\Events\DepartmentDeleted::class => 'handle',
            \App\Events\DepartmentUpdated::class => 'handle',
            \App\Events\EmployeeAdded::class => 'handle',
            \App\Events\EmployeeCheckedIn::class => 'handle',
            \App\Events\EmployeeCheckedOut::class => 'handle',
            \App\Events\EmployeeDeleted::class => 'handle',
            \App\Events\EmployeeUpdated::class => 'handle',
            \App\Events\LocationAdded::class => 'handle',
            \App\Events\LocationDeleted::class => 'handle',
            \App\Events\LocationUpdated::class => 'handle',
            \App\Events\ShiftAdded::class => 'handle',
            \App\Events\ShiftDeleted::class => 'handle',
            \App\Events\ShiftUpdated::class => 'handle',
            \App\Events\ApprovalRequestApproved::class => 'handle',
            \App\Events\ApprovalRequestCreated::class => 'handle',
            \App\Events\ApprovalRequestRejected::class => 'handle',
        ];
    }
}
