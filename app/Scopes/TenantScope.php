<?php

namespace App\Scopes;

use App\Models\Employee;
use App\Models\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class TenantScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $user = auth()->user();

        $teamColumn = $model->qualifyColumn('team_id');

        $builder
            ->when($user instanceof Team, fn($q) => $q->where($teamColumn, $user->id))
            ->when($user instanceof Employee, fn($q) => $q->where($teamColumn, $user->team_id));
    }
}
