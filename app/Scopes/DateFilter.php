<?php

namespace App\Scopes;

use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;

class DateFilter
{
    public function __construct(
        private string|Carbon|CarbonImmutable|CarbonPeriod $from,
        private string|Carbon|CarbonImmutable|null $to = null,
        private string $fromColumn = 'created_at',
        private ?string $toColumn = null,
        // - If true, only matches records fully within the date range; otherwise, matches any overlap.
        private bool $exactMatch = false
    ) {
    }

    public function __invoke(Builder $query): Builder
    {
        if ($this->from instanceof CarbonPeriod) {
            $fromDate = $this->from->start->startOfDay()->format('Y-m-d H:i:s');

            $toDate = $this->from->end->endOfDay()->format('Y-m-d H:i:s');
        } else {
            $fromDate = Carbon::parse($this->from)
                ->startOfDay()
                ->format('Y-m-d H:i:s');

            $toDate = Carbon::parse($this->to ?? $this->from)
                ->endOfDay()
                ->format('Y-m-d H:i:s');
        }

        if (!$this->toColumn) {
            // Single-column exact range check if no `toColumn` provided
            return $query->whereBetween($this->fromColumn, [$fromDate, $toDate]);
        }

        if ($this->exactMatch) {
            // Exact Match: Only matches records fully within range
            // Example: $fromDate = 2024-11-01, $toDate = 2024-11-10
            // Matches: 2024-11-03 to 2024-11-09 (fully contained)
            // Non-match: 2024-10-30 to 2024-11-05 (starts before)
            return $query
                ->where($this->fromColumn, '>=', $fromDate)
                ->where($this->toColumn, '<=', $toDate);
        }

        // Overlap Match (default): Matches records with any overlap
        // Example: $fromDate = 2024-11-01, $toDate = 2024-11-10
        // Matches: 2024-10-30 to 2024-11-05 (overlaps start)
        // Matches: 2024-11-03 to 2024-11-09 (fully within)
        return $query
            ->where($this->fromColumn, '<=', $toDate)
            ->where($this->toColumn, '>=', $fromDate);
    }

    public static function createdAt(
        string|Carbon|CarbonImmutable|CarbonPeriod $from,
        string|Carbon|CarbonImmutable|CarbonPeriod $to = null,
        bool $exactMatch = false
    ): static {
        return new static(
            from: $from,
            to: $to,
            fromColumn: 'created_at',
            toColumn: null,
            exactMatch: $exactMatch
        );
    }
}
