<?php

namespace App\Scopes;

use App\DTOs\ReportData;
use App\Models\Attendance;
use Illuminate\Database\Eloquent\Builder;
use InvalidArgumentException;

readonly class EarlyLateFilters
{
    public function __construct(protected ReportData $reportData)
    {
    }

    public function __invoke(Builder $query): void
    {
        if (!$query->getModel() instanceof Attendance) {
            throw new InvalidArgumentException(
                'This scope can only be applied to ' . Attendance::class
            );
        }

        $departments = $this->reportData->departmentsIds;
        $employees = $this->reportData->employeesIds;

        $tags = $this->reportData->tags;
        $locations = $this->reportData->locations;
        $shifts = $this->reportData->shifts;

        $query
            ->whereHas(
                'employee',
                fn($query) => $query
                    ->when($shifts, fn($query) => $query->filterByShifts($shifts))
                    ->when($employees, fn($query) => $query->whereIn('id', $employees))
                    ->when($departments, fn($query) => $query->filterByDepartments($departments))
                    ->when($tags, fn($query) => $query->filterByTags($tags))
                    ->when($locations, fn($query) => $query->filterByLocations($locations))
                    ->when(
                        $this->reportData->showInactiveEmployees,
                        fn($query) => $query,
                        fn($query) => $query->active()
                    )
            )
            ->tap(new DateFilter(from: $this->reportData->period, fromColumn: 'date'));
    }
}
