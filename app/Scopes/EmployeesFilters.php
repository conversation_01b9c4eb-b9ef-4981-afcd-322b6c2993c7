<?php

namespace App\Scopes;

use App\DTOs\ReportData;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Builder;
use InvalidArgumentException;

readonly class EmployeesFilters
{
    public function __construct(protected ReportData $reportData)
    {
    }

    public function __invoke(Builder $query): void
    {
        if (!$query->getModel() instanceof Employee) {
            throw new InvalidArgumentException(
                'This scope can only be applied to ' . Employee::class
            );
        }

        $departments = $this->reportData->departmentsIds;
        $employees = $this->reportData->employeesIds;
        $directManagers = $this->reportData->directManagers;
        $tags = $this->reportData->tags;
        $locations = $this->reportData->locations;
        $shifts = $this->reportData->shifts;
        $showInactiveEmployees = $this->reportData->showInactiveEmployees;

        $query
            ->with(['shifts', 'department', 'manager', 'tags', 'locations'])
            ->when($shifts, fn($query) => $query->filterByShifts($shifts))
            ->when($departments, fn($query) => $query->filterByDepartments($departments))
            ->when($employees, fn($query) => $query->whereIn('id', $employees))
            ->when($directManagers, fn($query) => $query->whereIn('manager_id', $directManagers))
            ->when($tags, fn($query) => $query->filterByTags($tags))
            ->when($locations, fn($query) => $query->filterByLocations($locations))
            ->when($showInactiveEmployees, fn($query) => $query, fn($query) => $query->active());
    }
}
