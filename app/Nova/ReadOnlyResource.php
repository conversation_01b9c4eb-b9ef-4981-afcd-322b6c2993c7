<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\DestructiveAction;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

abstract class ReadOnlyResource extends Resource
{
    public function authorizeToView(Request $request)
    {
        return true;
    }

    public function authorizedToView(Request $request)
    {
        return true;
    }

    public static function authorizedToViewAny(Request $request)
    {
        return true;
    }

    public static function authorizedToCreate(Request $request)
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }

    public function authorizedToRestore(Request $request)
    {
        return false;
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function authorizedToAdd(Request $request, $model)
    {
        return false;
    }

    public function authorizedToAttach(Request $request, $model)
    {
        return false;
    }

    public function authorizedToAttachAny(Request $request, $model)
    {
        return false;
    }

    public function authorizedToRunAction(NovaRequest $request, Action $action)
    {
        return !app()->isProduction();
    }

    public function authorizedToRunDestructiveAction(
        NovaRequest $request,
        DestructiveAction $action
    ) {
        return !app()->isProduction();
    }
}
