<?php

namespace App\Nova;

use App\Models\WebhookJob;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\KeyValue;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Text;

class WebhookJobResource extends ReadOnlyResource
{
    public static string $model = WebhookJob::class;

    public static $title = 'id';

    public static $group = 'Webhooks';

    public static $search = ['id'];

    public static function label(): string
    {
        return 'Webhook Jobs';
    }

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Number::make('Tries')->sortable(),

            Text::make('Status')->sortable(),

            KeyValue::make('event_payload'),

            HasMany::make('Logs', 'logs', WebhookJobLogResource::class),

            BelongsTo::make('Webhook', 'webhook', WebhookResource::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
