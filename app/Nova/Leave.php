<?php

namespace App\Nova;

use App\Models\Leave as LeaveModel;
use App\Nova\Actions\SendLeaveRejectedNotification;
use App\Nova\Actions\SendNewLeaveRequestNotification;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;

class Leave extends ReadOnlyResource
{
    public static $model = LeaveModel::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'reason',
        'from_date',
        'to_date',
        'department.name',
        'employee.first_name',
        'employee.last_name',
        'employee.email',
        'employee.mobile',
        'team.name',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Date::make('From Date')->sortable()->rules('required', 'date'),

            Date::make('To Date')->sortable()->rules('required', 'date'),

            Text::make('Reason')->sortable()->rules('required'),

            BelongsTo::make('Department', 'department', Department::class),

            BelongsTo::make('Employee', 'employee', Employee::class),

            BelongsTo::make('Team', 'team'),

            DateTime::make('Created At'),

            DateTime::make('Updated At'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [new SendLeaveRejectedNotification(), new SendNewLeaveRequestNotification()];
    }
}
