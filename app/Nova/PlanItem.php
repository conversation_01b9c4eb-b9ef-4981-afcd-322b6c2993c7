<?php

namespace App\Nova;

use App\Models\PlanItem as PlanItemModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class PlanItem extends Resource
{
    public static $model = PlanItemModel::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'software_id', 'software_package_id', 'plan_id'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Text::make('Name')->rules('required'),

            BelongsTo::make('Plan', 'plan', Plan::class),

            BelongsTo::make('Software', 'software', Software::class),

            BelongsTo::make('SoftwarePackage', 'softwarePackage', SoftwarePackage::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
