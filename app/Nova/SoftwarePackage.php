<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class SoftwarePackage extends ReadOnlyResource
{
    public static $model = \App\Models\SoftwarePackage::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'code', 'software_id'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Text::make('Name')->rules('required'),

            Text::make('Code')->rules('required'),

            BelongsTo::make('Software', 'software', Software::class),

            HasMany::make('softwareFeatures', 'softwareFeatures'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
