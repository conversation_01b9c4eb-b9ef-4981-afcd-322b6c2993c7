<?php

namespace App\Nova\Actions;

use App\Notifications\NewTemporaryShiftAssignedNotification;
use Carbon\CarbonImmutable;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Http\Requests\NovaRequest;

class SendNewTemporaryShiftAssignedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function fields(NovaRequest $request)
    {
        return [
            Date::make('Start Date', 'start_date')
                ->rules('required')
                ->help('The start date of the temporary shift'),

            Date::make('End Date', 'end_date')
                ->rules('required')
                ->help('The end date of the temporary shift'),
        ];
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        $startDate = CarbonImmutable::parse($fields->start_date);
        $endDate = CarbonImmutable::parse($fields->end_date);

        foreach ($models as $employee) {
            $employee->notify(
                new NewTemporaryShiftAssignedNotification(
                    $employee->shift ?? $employee->team->firstOrCreateDefaultShift(),
                    $startDate,
                    $endDate
                )
            );
        }

        return Action::message('Temporary shift assignment notification has been sent!');
    }
}
