<?php

namespace App\Nova\Actions;

use App\Notifications\TokenExpirationNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendTokenExpirationNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $employee) {
            $employee->notify(new TokenExpirationNotification());
        }

        return Action::message('Token expiration notification has been sent!');
    }
}
