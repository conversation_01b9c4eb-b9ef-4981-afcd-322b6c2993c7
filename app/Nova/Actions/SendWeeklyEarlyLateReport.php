<?php

namespace App\Nova\Actions;

use App\Console\Commands\EmailEarlyLateReportWeeklyCommand;
use App\Services\PeriodicalEarlyLateReportService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\DestructiveAction;
use Laravel\Nova\Fields\ActionFields;

class SendWeeklyEarlyLateReport extends DestructiveAction
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, $models)
    {
        try {
            (new EmailEarlyLateReportWeeklyCommand())->handle(
                new PeriodicalEarlyLateReportService()
            );

            return Action::message('Weekly early/late report has been sent successfully!');
        } catch (\Exception $e) {
            return Action::danger('Failed to send weekly early/late report: ' . $e->getMessage());
        }
    }
}
