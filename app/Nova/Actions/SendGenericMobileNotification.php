<?php

namespace App\Nova\Actions;

use App\Notifications\GenericMobileNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class SendGenericMobileNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function fields(NovaRequest $request)
    {
        return [
            Text::make('Subject', 'subject')->rules('required', 'string', 'max:255'),

            Textarea::make('Message', 'message')->rules('required', 'string')->rows(3),
        ];
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->notify(new GenericMobileNotification($fields->subject, $fields->message));
        }

        return Action::message('Generic notification has been sent!');
    }
}
