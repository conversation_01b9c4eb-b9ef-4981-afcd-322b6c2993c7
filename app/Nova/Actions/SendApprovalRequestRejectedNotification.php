<?php

namespace App\Nova\Actions;

use App\Notifications\ApprovalRequestRejectedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendApprovalRequestRejectedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->employee?->notify(new ApprovalRequestRejectedNotification($model));
        }

        return Action::message('Approval request rejected notification has been sent!');
    }
}
