<?php

namespace App\Nova\Actions;

use App\Console\Commands\EmailEarlyLateReportMonthlyCommand;
use App\Services\PeriodicalEarlyLateReportService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\DestructiveAction;
use Laravel\Nova\Fields\ActionFields;

class SendMonthlyEarlyLateReport extends DestructiveAction
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, $models)
    {
        try {
            (new EmailEarlyLateReportMonthlyCommand())->handle(
                new PeriodicalEarlyLateReportService()
            );

            return Action::message('Monthly early/late report has been sent successfully!');
        } catch (\Exception $e) {
            return Action::danger('Failed to send monthly early/late report: ' . $e->getMessage());
        }
    }
}
