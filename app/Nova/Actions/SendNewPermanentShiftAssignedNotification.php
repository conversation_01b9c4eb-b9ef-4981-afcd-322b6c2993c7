<?php

namespace App\Nova\Actions;

use App\Notifications\NewPermanentShiftAssignedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendNewPermanentShiftAssignedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $shift) {
            // Notify the employee assigned to the shift
            $shift->employee?->notify(new NewPermanentShiftAssignedNotification($shift));
        }

        return Action::message('Permanent shift assignment notification has been sent!');
    }
}
