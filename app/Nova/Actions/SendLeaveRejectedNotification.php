<?php

namespace App\Nova\Actions;

use App\Notifications\LeaveRejectedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendLeaveRejectedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $leave) {
            $leave->employee?->notify(new LeaveRejectedNotification($leave));
        }

        return Action::message('Leave rejected notification has been sent!');
    }
}
