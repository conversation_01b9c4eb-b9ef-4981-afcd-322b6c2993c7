<?php

namespace App\Nova\Actions;

use App\Notifications\NewLeaveRequestNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendNewLeaveRequestNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $leave) {
            // Notify the department manager
            $leave->employee->department->manager?->notify(new NewLeaveRequestNotification($leave));
        }

        return Action::message('New leave request notification has been sent!');
    }
}
