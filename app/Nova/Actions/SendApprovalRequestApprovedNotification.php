<?php

namespace App\Nova\Actions;

use App\Notifications\ApprovalRequestApprovedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendApprovalRequestApprovedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->employee?->notify(new ApprovalRequestApprovedNotification($model));
        }

        return Action::message('Approval request approved notification has been sent!');
    }
}
