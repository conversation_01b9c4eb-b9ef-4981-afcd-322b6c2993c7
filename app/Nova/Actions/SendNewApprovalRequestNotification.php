<?php

namespace App\Nova\Actions;

use App\Notifications\NewApprovalRequestNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendNewApprovalRequestNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $approvalRequest) {
            // Notify the department manager
            $approvalRequest->department->manager?->notify(
                new NewApprovalRequestNotification($approvalRequest)
            );
        }

        return Action::message('New approval request notification has been sent!');
    }
}
