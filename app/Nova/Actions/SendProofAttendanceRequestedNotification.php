<?php

namespace App\Nova\Actions;

use App\Enums\ProofMethod;
use App\Notifications\ProofAttendanceRequested;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class SendProofAttendanceRequestedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Proof Method', 'method')
                ->options([
                    ProofMethod::Manual->value => 'manual',
                    ProofMethod::Random->value => 'random',
                ])
                ->rules('required')
                ->help('The method of proof required'),
        ];
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        $proofMethod = ProofMethod::from($fields->method);

        foreach ($models as $employee) {
            $employee->notify(new ProofAttendanceRequested($proofMethod));
        }

        return Action::message('Proof attendance request has been sent!');
    }
}
