<?php

namespace App\Nova\Actions;

use App\Notifications\TemporaryShiftChangedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;

class SendTemporaryShiftChangedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $employee) {
            $employee->notify(new TemporaryShiftChangedNotification());
        }

        return Action::message('Temporary shift change notification has been sent!');
    }
}
