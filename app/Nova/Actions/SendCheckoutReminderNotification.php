<?php

namespace App\Nova\Actions;

use App\Notifications\CheckoutReminderNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;

class SendCheckoutReminderNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->notify(new CheckoutReminderNotification());
        }

        return Action::message('Check-out reminder notification has been sent!');
    }
}
