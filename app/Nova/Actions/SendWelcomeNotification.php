<?php

namespace App\Nova\Actions;

use App\Notifications\WelcomeNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendWelcomeNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $employee) {
            $employee->notify(new WelcomeNotification());
        }

        return Action::message('Welcome notification has been sent!');
    }
}
