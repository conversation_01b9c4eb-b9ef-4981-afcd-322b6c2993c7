<?php

namespace App\Nova\Actions;

use App\Notifications\PermanentShiftAssignedChangedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendPermanentShiftAssignedChangedNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $employee) {
            $employee->notify(
                new PermanentShiftAssignedChangedNotification(
                    $employee->shift ?? $employee->team->firstOrCreateDefaultShift()
                )
            );
        }

        return Action::message('Shift change notification has been sent to assigned employees!');
    }
}
