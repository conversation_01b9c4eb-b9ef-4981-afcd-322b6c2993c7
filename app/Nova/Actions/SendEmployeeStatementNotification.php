<?php

namespace App\Nova\Actions;

use App\Enums\EmployeeStatementType;
use App\Notifications\EmployeeStatementNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class SendEmployeeStatementNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function fields(NovaRequest $request)
    {
        return [
            Select::make('Statement Type', 'statement_type')
                ->options([
                    EmployeeStatementType::EarlyCheckout
                        ->value => EmployeeStatementType::EarlyCheckout->title(),
                    EmployeeStatementType::LateCheckin
                        ->value => EmployeeStatementType::LateCheckin->title(),
                    EmployeeStatementType::Absent->value => EmployeeStatementType::Absent->title(),
                ])
                ->required(),
        ];
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        $statementType = EmployeeStatementType::from($fields->statement_type);

        foreach ($models as $attendance) {
            $attendance->employee?->notify(
                new EmployeeStatementNotification($attendance, $statementType)
            );
        }

        return Action::message('Employee statement notification has been sent!');
    }
}
