<?php

namespace App\Nova\Actions;

use App\Notifications\CheckinReminderNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendCheckinReminderNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $model->notify(new CheckinReminderNotification());
        }

        return Action::message('Check-in reminder notification has been sent!');
    }
}
