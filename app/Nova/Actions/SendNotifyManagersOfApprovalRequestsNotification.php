<?php

namespace App\Nova\Actions;

use App\Notifications\NotifyManagersOfApprovalRequests;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class SendNotifyManagersOfApprovalRequestsNotification extends Action
{
    use InteractsWithQueue, Queueable;

    public function fields(NovaRequest $request)
    {
        return [
            Number::make('Count', 'count')
                ->rules('required', 'integer', 'min:1')
                ->help('The number of approval requests to notify about'),
        ];
    }

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $employee) {
            $employee->notify(new NotifyManagersOfApprovalRequests($fields->count));
        }

        return Action::message('Managers have been notified about approval requests!');
    }
}
