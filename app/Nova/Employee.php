<?php

namespace App\Nova;

use App\Models\Employee as EmployeeModel;
use App\Nova\Actions\SendCheckinReminderNotification;
use App\Nova\Actions\SendCheckoutReminderNotification;
use App\Nova\Actions\SendGenericMobileNotification;
use App\Nova\Actions\SendNewPermanentShiftAssignedNotification;
use App\Nova\Actions\SendNewTemporaryShiftAssignedNotification;
use App\Nova\Actions\SendNotifyManagersOfApprovalRequestsNotification;
use App\Nova\Actions\SendPermanentShiftAssignedChangedNotification;
use App\Nova\Actions\SendProofAttendanceRequestedNotification;
use App\Nova\Actions\SendTemporaryShiftChangedNotification;
use App\Nova\Actions\SendTokenExpirationNotification;
use App\Nova\Actions\SendWelcomeNotification;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;

class Employee extends ReadOnlyResource
{
    public static $model = EmployeeModel::class;

    public static $title = 'first_name';

    public static $search = [
        'id',
        'email',
        'mobile',
        'position',
        'device_id',
        'team.name',
        'first_name',
        'last_name',
        'number',
        'remote_work',
        'preferred_language',
        'nawart_uuid',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Email')->sortable()->rules('nullable', 'email', 'max:254'),

            Number::make('Shift Id')->sortable()->rules('nullable', 'integer'),

            Text::make('First Name')->sortable()->rules('required'),

            Text::make('Last Name')->sortable()->rules('nullable'),

            Text::make('Mobile')->sortable()->rules('nullable'),

            Text::make('Position')->sortable()->rules('nullable'),

            Text::make('Remote Work')->sortable()->rules('required'),

            Text::make('Preferred Language')->sortable()->rules('required'),

            Text::make('Device Id')->sortable()->rules('nullable'),

            Text::make('Random Notification')->sortable()->rules('required'),

            Text::make('Number')->sortable()->rules('nullable'),

            Text::make('Nawart Uuid')->sortable()->rules('required'),

            Date::make('Last Activity At')->sortable()->rules('nullable', 'date'),

            Date::make('First Login At')->sortable()->rules('nullable', 'date'),

            Number::make('Manager Id')->sortable()->rules('nullable', 'integer'),

            HasOne::make('ActiveAttendanceRecord', 'activeAttendanceRecord', Attendance::class),

            HasMany::make('Activities', 'activities'),

            HasMany::make('Approval Requests', 'approvalRequests'),

            HasMany::make('Attendances', 'attendances'),

            BelongsTo::make('Department', 'department'),

            HasMany::make('Leaves', 'leaves', Leave::class),

            HasMany::make('Managed Departments', 'managedDepartments', Department::class),

            HasMany::make('Managed Employees', 'managedEmployees', Employee::class),

            HasMany::make(
                'Managed Employees Approval Requests',
                'employeesApprovalRequests',
                ApprovalRequest::class
            ),

            HasMany::make('Managed Employees Leaves', 'managedEmployeesLeaves', Leave::class),

            BelongsTo::make('Current Shift', 'shift', Shift::class),
            BelongsToMany::make('Shifts', 'shifts'),

            HasMany::make('Proofs', 'proofs'),

            BelongsTo::make('Team', 'team'),

            DateTime::make('Created At'),

            DateTime::make('Updated At'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [
            new SendCheckinReminderNotification(),
            new SendCheckoutReminderNotification(),
            new SendGenericMobileNotification(),
            new SendNewTemporaryShiftAssignedNotification(),
            new SendNewPermanentShiftAssignedNotification(),
            new SendNotifyManagersOfApprovalRequestsNotification(),
            new SendPermanentShiftAssignedChangedNotification(),
            new SendTemporaryShiftChangedNotification(),
            new SendProofAttendanceRequestedNotification(),
            new SendTokenExpirationNotification(),
            new SendWelcomeNotification(),
        ];
    }
}
