<?php

namespace App\Nova;

use App\Models\Device as DeviceModel;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;

class Device extends Resource
{
    public static $model = DeviceModel::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'team_id', 'username'];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name', 'name')->sortable()->rules('required'),

            Text::make('Username', 'username')->sortable()->rules('required'),

            Number::make('Lat', 'lat')->sortable()->rules('nullable', 'numeric'),

            Number::make('Lng', 'lng')->sortable()->rules('nullable', 'numeric'),

            BelongsTo::make('Location', 'location', Location::class),

            BelongsTo::make('Team', 'team', Team::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
