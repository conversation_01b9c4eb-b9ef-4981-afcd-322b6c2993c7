<?php

namespace App\Nova;

use App\Models\Webhook;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;

class WebhookResource extends ReadOnlyResource
{
    public static string $model = Webhook::class;

    public static $title = 'name';

    public static $group = 'Webhooks';

    public static $search = ['id', 'name', 'event_name', 'Url'];

    public static function label(): string
    {
        return 'Webhooks';
    }

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Text::make('Name'),

            Text::make('Url'),

            Text::make('Event Name'),

            Boolean::make('active'),

            BelongsTo::make('Team', 'team', Team::class),

            HasMany::make('Webhook Jobs', 'webhookJobs', WebhookJobResource::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
