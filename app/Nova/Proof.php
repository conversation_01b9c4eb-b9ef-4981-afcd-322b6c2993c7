<?php

namespace App\Nova;

use App\Models\Proof as ProofModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;

class Proof extends ReadOnlyResource
{
    public static $model = ProofModel::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'notification_id',
        'method',
        'status',
        'location.name',
        'employee.first_name',
        'employee.last_name',
        'employee.email',
        'employee.mobile',
        'team.name',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Notification Id')->sortable()->rules('required'),

            Text::make('Method')->sortable()->rules('required'),

            Text::make('Status')->sortable()->rules('required'),

            Number::make('Lat')->sortable()->rules('nullable', 'numeric'),

            Number::make('Lng')->sortable()->rules('nullable', 'numeric'),

            Date::make('Responded At')->sortable()->rules('nullable', 'date'),

            Date::make('Expire At')->sortable()->rules('nullable', 'date'),

            Number::make('Score')->sortable()->rules('required', 'integer'),

            BelongsTo::make('Employee', 'employee', Employee::class),

            BelongsTo::make('Location', 'location'),

            BelongsTo::make('Team', 'team'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
