<?php

namespace App\Nova;

use Illuminate\Http\Request;
use Imumz\Nova4FieldMap\Nova4FieldMap;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\URL;

class Activity extends ReadOnlyResource
{
    public static $model = \App\Models\Activity::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'action',
        'employee.first_name',
        'employee.last_name',
        'employee.email',
        'employee.mobile',
        'team.name',
    ];

    /**
     * The number of resources to show per page via relationships.
     *
     * @var int
     */
    public static $perPageViaRelationship = 15;

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Action')->sortable()->rules('required'),

            Number::make('Lat')->sortable()->rules('required', 'numeric'),

            Number::make('Lng')->sortable()->rules('required', 'numeric'),

            BelongsTo::make('Employee', 'employee', Employee::class),

            BelongsTo::make('Location', 'location'),

            BelongsTo::make('Team', 'team'),

            DateTime::make('Created At'),

            Nova4FieldMap::make('Map')
                ->type('LatLon')
                ->point($this->lat, $this->lng),

            URL::make(
                'google map',
                fn() => "https://maps.google.com/?q=$this->lat,$this->lng"
            )->displayUsing(fn() => "https://maps.google.com/?q=$this->lat,$this->lng"),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
