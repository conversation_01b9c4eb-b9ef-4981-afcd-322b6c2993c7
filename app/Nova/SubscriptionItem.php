<?php

namespace App\Nova;

use App\Models\SubscriptionItem as SubscriptionItemModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;

class SubscriptionItem extends Resource
{
    public static $model = SubscriptionItemModel::class;

    public static $title = 'id';

    public static $search = ['id', 'subscription_id', 'software_id', 'software_package_id'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            BelongsTo::make('Software', 'software', Software::class),

            BelongsTo::make(
                'SoftwarePackage',
                'softwarePackage',
                SoftwarePackage::class
            )->dependsOn(['software'], function (BelongsTo $field, Request $request, $formData) {
                if (!$formData->software) {
                    $field->hide();
                }
                $field->relatableQueryUsing(function (Request $request, $query) use ($formData) {
                    return $query->where('software_id', $formData->software);
                });
            }),

            BelongsTo::make('Subscription', 'subscription', Subscription::class),

            BelongsTo::make('Tenant', 'tenant', Tenant::class)->exceptOnForms(),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
