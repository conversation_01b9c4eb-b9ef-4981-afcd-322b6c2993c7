<?php

namespace App\Nova;

use App\Models\Department as DepartmentModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class Department extends ReadOnlyResource
{
    public static $model = DepartmentModel::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'nawart_uuid', 'team.name', 'parent.name'];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->sortable()->rules('required'),

            Text::make('Remote Work')->sortable()->rules('required'),

            Text::make('Random Notification')->sortable()->rules('required'),

            Text::make('Nawart Uuid')->sortable()->rules('required'),

            HasMany::make('Employees', 'employees', Employee::class),

            BelongsTo::make('Manager', 'manager', Employee::class),

            BelongsTo::make('Team', 'team'),

            HasMany::make('Children', 'children', Department::class),

            BelongsTo::make('Parent', 'parent', Department::class),

            DateTime::make('Created At'),

            DateTime::make('Updated At'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
