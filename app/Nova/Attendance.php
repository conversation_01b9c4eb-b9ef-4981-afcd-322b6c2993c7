<?php

namespace App\Nova;

use App\Models\Attendance as AttendanceModel;
use App\Nova\Actions\SendEmployeeStatementNotification;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;

class Attendance extends ReadOnlyResource
{
    public static $model = AttendanceModel::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'status',
        'date',
        'employee.department.name',
        'employee.first_name',
        'employee.last_name',
        'employee.email',
        'employee.mobile',
        'team.name',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),
            Date::make('Date'),

            Number::make('In Mood'),

            Number::make('Out Mood'),

            Text::make('Status'),

            DateTime::make('Shift From'),

            DateTime::make('Shift To'),

            DateTime::make('Check In'),

            DateTime::make('Check Out'),

            DateTime::make('Net Hours'),

            Text::make('In Type'),

            Text::make('Out Type'),

            Text::make('Force Checkout Time'),

            DateTime::make('Active Until'),

            Number::make('Flexible Hours'),

            BelongsTo::make('CheckInLocation', 'checkInLocation', Location::class),

            BelongsTo::make('CheckoutLocation', 'checkoutLocation', Location::class),

            BelongsTo::make('Employee', 'employee', Employee::class),

            BelongsTo::make('Shift', 'shift'),

            BelongsTo::make('Team', 'team'),

            DateTime::make('Created At'),

            DateTime::make('Updated At'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [new SendEmployeeStatementNotification()];
    }
}
