<?php

namespace App\Nova;

use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class Shift extends ReadOnlyResource
{
    public static $model = \App\Models\Shift::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'team.name'];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->sortable()->rules('required'),

            Date::make('Force Checkout')->sortable()->rules('required', 'date'),

            Text::make('Timezone')->sortable()->rules('required'),

            Text::make('Day - Sunday - From', 'working_hours->weekdays->sunday->from')
                ->sortable()
                ->rules('required'),
            Text::make('Day - Sunday - To', 'working_hours->weekdays->sunday->to')
                ->sortable()
                ->rules('required'),

            Text::make('Day - Monday - From', 'working_hours->weekdays->monday->from')
                ->sortable()
                ->rules('required'),
            Text::make('Day - Monday - To', 'working_hours->weekdays->monday->to')
                ->sortable()
                ->rules('required'),

            Text::make('Day - Tuesday - From', 'working_hours->weekdays->tuesday->from')
                ->sortable()
                ->rules('required'),
            Text::make('Day - Tuesday - To', 'working_hours->weekdays->tuesday->to')
                ->sortable()
                ->rules('required'),

            Text::make('Day - Wednesday - From', 'working_hours->weekdays->wednesday->from')
                ->sortable()
                ->rules('required'),
            Text::make('Day - Wednesday - To', 'working_hours->weekdays->wednesday->to')
                ->sortable()
                ->rules('required'),

            Text::make('Day - Thursday - From', 'working_hours->weekdays->thursday->from')
                ->sortable()
                ->rules('required'),
            Text::make('Day - Thursday - To', 'working_hours->weekdays->thursday->to')
                ->sortable()
                ->rules('required'),

            Text::make('Day - Friday - From', 'working_hours->weekdays->friday->from')
                ->sortable()
                ->rules('required'),
            Text::make('Day - Friday - To', 'working_hours->weekdays->friday->to')
                ->sortable()
                ->rules('required'),

            Text::make('Day - Saturday - From', 'working_hours->weekdays->saturday->from')
                ->sortable()
                ->rules('required'),
            Text::make('Day - Saturday - To', 'working_hours->weekdays->saturday->to')
                ->sortable()
                ->rules('required'),

            BelongsToMany::make('employees'),
            BelongsTo::make('Team', 'team', Team::class),
        ];
    }
}
