<?php

namespace App\Nova;

use App\Models\Report as ReportModel;
use App\Nova\Actions\SendMonthlyEarlyLateReport;
use App\Nova\Actions\SendWeeklyEarlyLateReport;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class Report extends ReadOnlyResource
{
    public static $model = ReportModel::class;

    public function title()
    {
        return $this->name->value;
    }

    public static $search = ['id'];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->sortable()->rules('required'),

            HasMany::make('Report Tasks', 'reportTasks', ReportTask::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [
            (new SendMonthlyEarlyLateReport())
                ->onlyOnIndex()
                ->confirmButtonText('Send Report')
                ->cancelButtonText('Cancel')
                ->confirmText('Are you sure you want to send the monthly early/late report?')
                ->standalone(),
            (new SendWeeklyEarlyLateReport())
                ->onlyOnIndex()
                ->confirmButtonText('Send Report')
                ->cancelButtonText('Cancel')
                ->confirmText('Are you sure you want to send the weekly early/late report?')
                ->standalone(),
        ];
    }
}
