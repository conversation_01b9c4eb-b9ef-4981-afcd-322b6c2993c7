<?php

namespace App\Nova;

use App\Models\ReportTask as ReportTaskModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\Date;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;

class ReportTask extends Resource
{
    public static $model = ReportTaskModel::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'file_name',
        'team_id',
        'created_by_id',
        'report_id',
        'team.name',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('File Name', 'file_name')->sortable()->rules('required'),

            Text::make('File Path', 'file_path')->sortable()->rules('nullable'),

            Text::make('Error Message', 'error_message')->sortable()->rules('nullable'),

            Date::make('Start Date', 'start_date')->sortable()->rules('nullable', 'date'),

            Date::make('End Date', 'end_date')->sortable()->rules('nullable', 'date'),

            Date::make('Completed At', 'completed_at')->sortable()->rules('nullable', 'date'),

            BelongsTo::make('CreatedBy', 'createdBy', Employee::class),

            BelongsTo::make('Report', 'report'),

            BelongsTo::make('Team', 'team', Team::class),
        ];
    }
}
