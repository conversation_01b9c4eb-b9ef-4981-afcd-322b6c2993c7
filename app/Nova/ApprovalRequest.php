<?php

namespace App\Nova;

use App\Models\ApprovalRequest as ApprovalRequestModel;
use App\Nova\Actions\SendApprovalRequestApprovedNotification;
use App\Nova\Actions\SendApprovalRequestRejectedNotification;
use App\Nova\Actions\SendNewApprovalRequestNotification;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;

class ApprovalRequest extends ReadOnlyResource
{
    public static $model = ApprovalRequestModel::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'type',
        'status',
        'department.name',
        'employee.first_name',
        'employee.last_name',
        'employee.email',
        'employee.mobile',
        'team.name',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Date::make('From Datetime')->sortable()->rules('required', 'date'),

            Date::make('To Datetime')->sortable()->rules('required', 'date'),

            Text::make('Type')->sortable()->rules('required'),

            Text::make('Reason')->sortable()->rules('nullable'),

            Text::make('Status')->sortable()->rules('required'),

            Text::make('Attendance Type')->sortable()->rules('nullable'),

            Text::make('Old From Datetime')->sortable()->rules('nullable'),

            Text::make('Old To Datetime')->sortable()->rules('nullable'),

            BelongsTo::make('Department', 'department'),

            BelongsTo::make('Employee', 'employee', Employee::class),

            BelongsTo::make('Team', 'team'),

            DateTime::make('Created At'),

            DateTime::make('Updated At'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [
            new SendApprovalRequestApprovedNotification(),
            new SendApprovalRequestRejectedNotification(),
            new SendNewApprovalRequestNotification(),
        ];
    }
}
