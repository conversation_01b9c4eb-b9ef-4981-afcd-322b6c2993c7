<?php

namespace App\Nova;

use dacoto\DomainValidator\Validator\Domain;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Color;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\HasOne;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Tenant extends Resource
{
    public static $model = \App\Models\Tenant::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'domain', 'is_active'];

    public static $relatableSearchResults = 25;

    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('name'),

            Text::make('domain')
                ->hideWhenUpdating()
                ->rules([new Domain()]),

            Color::make('color'),

            Boolean::make('is_active'),

            Boolean::make('allow_sms'),

            HasMany::make('employees'),

            HasMany::make('departments'),

            HasOne::make(
                'Authenticator Config',
                'authenticatorConfig',
                TenantAuthenticatorConfig::class
            ),

            HasMany::make('Subscriptions'),

            HasMany::make('SubscriptionItems'),

            HasOne::make('Active Subscription', 'activeSubscription', Subscription::class),
        ];
    }

    public function cards(NovaRequest $request)
    {
        return [];
    }

    public function filters(NovaRequest $request)
    {
        return [];
    }

    public function lenses(NovaRequest $request)
    {
        return [];
    }

    public function actions(NovaRequest $request)
    {
        return [];
    }
}
