<?php

namespace App\Nova;

use Imumz\Nova4FieldMap\Nova4FieldMap;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class Location extends ReadOnlyResource
{
    public static $model = \App\Models\Location::class;

    public static $title = 'name';

    public static $search = ['id', 'name', 'lat', 'lng', 'team.name'];

    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('name'),

            Number::make('Lat')->sortable()->rules('required', 'numeric'),

            Number::make('Lng')->sortable()->rules('required', 'numeric'),

            Number::make('radius')->rules('required', 'numeric'),

            Boolean::make('is_default'),

            BelongsTo::make('Team', 'team'),

            Text::make('timezone'),

            DateTime::make('Created At'),

            DateTime::make('Updated At'),

            Nova4FieldMap::make('Map')
                ->type('LatLon')
                ->point($this->lat, $this->lng),
        ];
    }
}
