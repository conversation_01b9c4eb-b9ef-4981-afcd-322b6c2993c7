<?php

namespace App\Nova;

use App\Models\WebhookJobLog;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;

class WebhookJobLogResource extends ReadOnlyResource
{
    public static string $model = WebhookJobLog::class;

    public static $title = 'id';

    public static $search = ['id', 'url', 'Http Code'];

    public function fields(Request $request): array
    {
        return [
            ID::make(),

            Text::make('Url'),

            Number::make('Http Code'),

            KeyValue::make('response'),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
