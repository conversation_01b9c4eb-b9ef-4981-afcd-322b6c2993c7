<?php

namespace App\Sync;

use App\DTOs\EarlyLateConfig;
use App\DTOs\EmployeeStatementConfig;
use App\DTOs\RandomProofNotificationConfig;
use App\Enums\ApprovalType;
use App\Models\Team;

class UpdateOrCreateTenant
{
    public function handle(array $data): void
    {
        $team = Team::findByNawartUuid($data['id']);

        if ($team) {
            $team->update($this->formatStartData($data));
            return;
        }

        $team = Team::create([
            ...$this->formatStartData($data),
            'remote_work' => 'not_allowed',
            'free_checkout' => 'not_allowed',
            'map_report_thresholds' => ['yellow' => ['min' => 60, 'max' => 80]],
            'early_late_config' => EarlyLateConfig::makeDefault(),
            'random_proof_notification_config' => RandomProofNotificationConfig::makeDefault(),
            'employee_statement_config' => new EmployeeStatementConfig(),
            'approval_type' => ApprovalType::OneLayer,
        ]);

        $team->firstOrCreateDefaultShift();
    }

    public function formatStartData(array $data): array
    {
        return [
            'nawart_uuid' => $data['id'],
            'name' => $data['name'],
            // for some reason, the is_active field is not always present in the data
            'active' => $data['is_active'] ?? false,
            'primary_color' => $data['color'] ?: '#ddd',
            'colored_logo_url' => $data['colored_logo_url'] ?? null,
            'white_logo_url' => $data['white_logo_url'] ?? null,
        ];
    }
}
