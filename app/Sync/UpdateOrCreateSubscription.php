<?php

namespace App\Sync;

use App\Models\Subscription;
use App\Models\Team;

class UpdateOrCreateSubscription
{
    public function handle(array $data): void
    {
        $team = Team::findByNawartUuid($data['tenant_id']);

        $existingSubscription = Subscription::firstWhere('team_id', $team->id);

        if ($existingSubscription?->id === $data['id']) {
            info('Subscription already exists, no need to create it.');

            // in the future, we may need to update the subscription.
            return;
        }

        if ($existingSubscription && $existingSubscription->id !== $data['id']) {
            info(
                'Subscription already exists, but the ID is different, deleting the existing subscription.'
            );

            $existingSubscription->subscriptionItem?->delete();
            $existingSubscription->delete();
        }

        info(
            "Creating new subscription by ID [{$data['id']}] for tenant: [$team->name - $team->id]."
        );

        // no need to use nawart_uuid like others, since here the id is the same as the nawart_uuid in Nawart Start
        // forceCreate is used here because we want to sync the id from Nawart Start
        Subscription::forceCreate([
            'id' => $data['id'],
            'team_id' => $team->id,
        ]);

        info('New subscription created');
    }
}
