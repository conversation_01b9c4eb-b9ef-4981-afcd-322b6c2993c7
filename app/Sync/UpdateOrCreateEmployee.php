<?php

namespace App\Sync;

use App\DTOs\RandomProofNotificationConfig;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Tag;
use App\Models\Team;
use App\Services\OnboardEmployee;
use Arr;
use Exception;

class UpdateOrCreateEmployee
{
    public function handle(array $startData): void
    {
        $team = Team::findByNawartUuid($startData['tenant_id']);

        if (!$team) {
            throw new Exception(
                "Tenant Id {$startData['tenant_id']} Could not be found, it exists in `Start` but not in the local database."
            );
        }

        $formattedData = $this->prepareData($startData);

        $employee = $this->createOrUpdateEmployee($formattedData, $team);

        $this->syncTags($formattedData['tags'], $employee);

        if (!$employee->wasRecentlyCreated) {
            info("Employee [$employee->id] has been updated.");
            return;
        }

        // for inactive employees or teams and teams without subscription,
        // we just save the employee without any further action
        if (!$employee->is_active || !$employee->team->active_and_has_subscription) {
            info(
                match (true) {
                    !$employee->is_active
                        => "Employee [$employee->id] is inactive, skipping further actions.",
                    !$employee->team->active_and_has_subscription
                        => "Team [$employee->team_id] is inactive or has no subscription, skipping further actions.",
                }
            );
            return;
        }

        $this->afterCreate($employee);
    }

    protected function prepareData(array $data): array
    {
        return [
            'nawart_uuid' => $data['id'],
            'department_id' => isset($data['department_id'])
                ? Department::findByNawartUuid($data['department_id'])?->id
                : null,
            'manager_id' => isset($data['manager_id'])
                ? Employee::findByNawartUuid($data['manager_id'])?->id
                : null,
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'position' => $data['position'],
            'number' => $data['number'],
            'mobile' => $data['phone'] ?? null,
            'is_active' => $data['is_active'],
            'preferred_language' => $data['preferred_language'],
            'roles' => Arr::pluck($data['roles'] ?? [], 'name'),
            'tags' => $data['tags'],
        ];
    }

    protected function createOrUpdateEmployee(array $data, Team $team): Employee
    {
        $employee = Employee::findByNawartUuid($data['nawart_uuid']);

        $data = Arr::except($data, ['tags']);

        if ($employee) {
            $employee->update($data);

            return $employee;
        }

        return Employee::create([
            ...$data,
            'is_ready' => false,
            'team_id' => $team->id,
            'random_proof_notification_config' => new RandomProofNotificationConfig(
                enabled: $team->random_proof_notification_config->enabled,
                inherited: true,
                count: $team->random_proof_notification_config->count
            ),
        ]);
    }

    public function syncTags($tags, Employee $employee): void
    {
        $tagsIds = collect($tags)
            ->map(
                fn($tag) => Tag::updateOrCreate(
                    ['name' => $tag['name'], 'team_id' => $employee->team_id],
                    ['color' => $tag['color']]
                )
            )
            ->pluck('id');

        $employee->tags()->sync($tagsIds);
    }

    public function afterCreate(Employee $employee): void
    {
        // todo: this is a special case for Tawuniya, remove later
        if ($employee->team->is(Team::findByNawartUuid('9d6e6c00-f99b-40c1-90f9-1823b02b5f58'))) {
            return;
        }

        info(
            "assigning shifts and locations and sending welcome email for employee: [$employee->id]..."
        );

        (new OnboardEmployee())->handle($employee, sendWelcomeNotification: true);

        info("Employee [$employee->id] is created and onboarded.");
    }
}
