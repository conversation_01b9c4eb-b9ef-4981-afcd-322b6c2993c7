<?php

namespace App\Sync;

use App\Models\SoftwarePackage;
use Arr;

class DeleteSoftware
{
    public function handle(array $data): void
    {
        SoftwarePackage::findMany(Arr::pluck($data['software_packages'], 'id'))->each(function (
            SoftwarePackage $softwarePackage
        ) {
            $softwarePackage->subscriptionItems->each->delete();
            $softwarePackage->softwareFeatures->each->delete();
            $softwarePackage->delete();
        });
    }
}
