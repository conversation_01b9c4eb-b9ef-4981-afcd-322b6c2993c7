<?php

namespace App\Sync;

use App\Models\SubscriptionItem;

class DeleteSubscriptionItem
{
    public function handle(array $data): void
    {
        info("Deleting subscription item ID: [{$data['id']}]");

        $item = SubscriptionItem::find($data['id']);

        if ($item) {
            $item->delete();
        } else {
            info('Subscription item does not exist, skipping deletion.');
        }
    }
}
