<?php

namespace App\Sync;

use App\Models\Tag;
use App\Models\Team;

class UpdateOrCreateTag
{
    public function handle(array $data): void
    {
        $team = Team::findByNawartUuid($data['tenant_id']);

        Tag::updateOrCreate(
            ['name' => $data['name'], 'team_id' => $team->id],
            ['color' => $data['color']]
        );

        info("Tag [$data[name]] has been updated.");
    }
}
