<?php

namespace App\Sync;

use App\Models\Tag;
use App\Models\Team;
use Exception;

class DeleteTag
{
    public function handle(array $data): void
    {
        info("Deleting tag ID: [{$data['id']}]");

        $team = Team::findByNawartUuid($data['tenant_id']);

        if (!$team) {
            throw new Exception(
                "Tenant Id {$data['tenant_id']} Could not be found, it exists in `Start` but not in the local database."
            );
        }

        $tag = Tag::firstWhere([
            'name' => $data['name'],
            'team_id' => $team->id,
        ]);

        if ($tag) {
            $tag->delete();
            // employees will be detached by cascade delete
        } else {
            info('Tag does not exist, skipping deletion.');
        }
    }
}
