<?php

namespace App\Sync;

use App\Enums\SoftwareCode;
use App\Models\SoftwarePackage;
use App\Models\Subscription;
use App\Models\SubscriptionItem;
use App\Models\Team;
use Exception;

class UpdateOrCreateSubscriptionItem
{
    public function handle(array $data): void
    {
        $team = Team::findByNawartUuid($data['tenant_id']);

        $this->ensureSubscriptionExists($team);

        $isAttendanceSubscriptionItem =
            $data['software']['code'] === SoftwareCode::Attendance->value;

        if (!$isAttendanceSubscriptionItem) {
            info('Software code is not Attendance, skipping creating subscription items.');

            return;
        }

        $this->processSubscriptionItem($data, $team);
    }

    public function processSubscriptionItem(array $data, Team $team): void
    {
        info("Creating subscription item ID: [{$data['id']}]");

        $softwarePackage = $this->getSoftwarePackage($data['software_package']['code']);

        $existingSubscriptionItem = SubscriptionItem::firstWhere('id', $data['id']);

        if ($existingSubscriptionItem?->id === $data['id']) {
            info('Subscription item already exists, updating it.');

            $existingSubscriptionItem->update([
                'software_package_id' => $softwarePackage->id,
            ]);

            return;
        }

        if ($existingSubscriptionItem && $existingSubscriptionItem->id !== $data['id']) {
            info(
                'Subscription item already exists, but the ID is different, deleting the existing subscription item.'
            );

            $existingSubscriptionItem->delete();
        }

        // forceCreate is used here because we want to sync the id from Nawart Start
        SubscriptionItem::forceCreate([
            'id' => $data['id'],
            'subscription_id' => $data['subscription_id'],
            'team_id' => $team->id,
            'software_package_id' => $data['software_package']['id'],
        ]);

        info('Subscription item created');
    }

    public function getSoftwarePackage(string $code): SoftwarePackage
    {
        $softwarePackage = SoftwarePackage::firstWhere('code', $code);

        if (!$softwarePackage) {
            throw new Exception(
                "Software package with code $code not found, you should sync `software` from Nawart Start"
            );
        }

        return $softwarePackage;
    }

    public function ensureSubscriptionExists(Team $team): void
    {
        if (!Subscription::firstWhere('team_id', $team->id)) {
            throw new Exception(
                "Subscription not found for team: [$team->name - $team->id], you should sync `subscription` from Nawart Start"
            );
        }
    }
}
