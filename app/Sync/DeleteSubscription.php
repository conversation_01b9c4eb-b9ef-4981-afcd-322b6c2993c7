<?php

namespace App\Sync;

use App\Models\Subscription;

class DeleteSubscription
{
    public function handle(array $data): void
    {
        info("Deleting subscription ID: [{$data['id']}]");

        $subscription = Subscription::find($data['id']);

        if ($subscription) {
            $subscription->subscriptionItem->delete();
            $subscription->delete();
        } else {
            info('Subscription does not exist, skipping deletion.');
        }
    }
}
