<?php

namespace App\Sync;

use App\DTOs\RandomProofNotificationConfig;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use Exception;

class UpdateOrCreateDepartment
{
    public function handle(array $data): void
    {
        $team = Team::findByNawartUuid($data['tenant_id']);

        if (!$team) {
            throw new Exception('Tenant with Id ' . $data['tenant_id'] . ' Could not be found');
        }

        $manager = isset($data['manager_id'])
            ? Employee::findByNawartUuid($data['manager_id'])
            : null;

        $parentDepartment = isset($data['parent_id'])
            ? Department::findByNawartUuid($data['parent_id'])
            : null;

        $department = Department::findByNawartUuid($data['id']);

        $formattedStartData = [
            'name' => $data['name'],
            'team_id' => $team->id,
            'manager_id' => $manager?->id,
            'parent_id' => $parentDepartment?->id,
        ];

        if ($department) {
            $department->update($formattedStartData);

            return;
        }

        Department::create([
            ...$formattedStartData,
            'nawart_uuid' => $data['id'],
            'random_proof_notification_config' => new RandomProofNotificationConfig(
                enabled: $team->random_proof_notification_config->enabled,
                inherited: true,
                count: $team->random_proof_notification_config->count
            ),
        ]);
    }
}
