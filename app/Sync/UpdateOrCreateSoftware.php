<?php

namespace App\Sync;

use App\Enums\SoftwareCode;
use App\Models\SoftwareFeature;
use App\Models\SoftwarePackage;

class UpdateOrCreateSoftware
{
    public function handle(array $data): void
    {
        if ($data['code'] !== SoftwareCode::Attendance->value) {
            info('Skipping software sync because the code is not attendance');
            return;
        }

        info("Syncing software... [{$data['code']}]");

        foreach ($data['software_packages'] as $rawSoftwarePackage) {
            // upsert to force filling the uuid
            SoftwarePackage::upsert(
                values: [
                    'id' => $rawSoftwarePackage['id'],
                    'code' => $rawSoftwarePackage['code'],
                    'is_active' => $rawSoftwarePackage['is_active'],
                    'name' => $rawSoftwarePackage['name'],
                ],
                uniqueBy: ['id', 'code'],
                update: ['is_active', 'name']
            );

            foreach ($rawSoftwarePackage['software_features'] as $softwareFeature) {
                // upsert to force filling the uuid
                SoftwareFeature::upsert(
                    values: [
                        'id' => $softwareFeature['id'],
                        'code' => $softwareFeature['code'],
                        'is_active' => $softwareFeature['is_active'],
                        'software_package_id' => $rawSoftwarePackage['id'],
                    ],
                    uniqueBy: ['id', 'code'],
                    update: ['is_active', 'software_package_id']
                );
            }
        }
    }
}
