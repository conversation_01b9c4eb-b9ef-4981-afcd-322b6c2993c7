<?php

namespace App\Sync;

use App\Models\ApprovalRequest;
use App\Models\Department;
use App\Models\Leave;

class DeleteDepartment
{
    public function handle(array $data): void
    {
        $department = Department::findByNawartUuid($data['id']);

        ApprovalRequest::query()
            ->where('team_id', $department->team_id)
            ->where('department_id', $department->id)
            ->forceDelete();

        Leave::query()
            ->where('team_id', $department->team_id)
            ->where('department_id', $department->id)
            ->forceDelete();

        // it's not safe to delete the children since they may have employees and employees have relations...
        // so we just remove the parent_id from the children
        // plus, we don't care about the descendants since they are the responsibility of the direct parent not this department
        $department->children->each(fn(Department $child) => $child->update(['parent_id' => null]));

        $department->forceDelete();
    }
}
