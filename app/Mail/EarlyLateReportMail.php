<?php

namespace App\Mail;

use App\Calculations\EarlyLatePeriodicalMailCalculations;
use App\DTOs\EarlyLateMailData;
use App\Models\ReportTask;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class EarlyLateReportMail extends Mailable implements ShouldQueue
{
    use Queueable;

    public function __construct(protected EarlyLateMailData $earlyLateMailData)
    {
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(subject: 'Early Late Report');
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $data = (new EarlyLatePeriodicalMailCalculations($this->earlyLateMailData))->calculate();

        return new Content(
            view: 'maizzle.emails.early-late-report',
            with: [
                'receiver' => $this->earlyLateMailData->manager->receiverOfEarlyLateReport(),
                'periodType' => $this->earlyLateMailData->periodType,
                'from' => $this->earlyLateMailData->period->start->format('Y-m-d'),
                'to' => $this->earlyLateMailData->period->end->format('Y-m-d'),
                ...$data,
            ]
        );
    }

    public function attachments(): array
    {
        return $this->earlyLateMailData
            ->reportTasks()
            ->map(fn(ReportTask $reportTask) => Attachment::fromStorage($reportTask->file_path))
            ->toArray();
    }
}
