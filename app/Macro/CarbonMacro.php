<?php

namespace App\Macro;

use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;

class CarbonMacro
{
    static function defineMacro(): self
    {
        $carbonMacro = new self();
        $carbonMacro->defineCarbonMacro();
        return $carbonMacro;
    }

    /** @noinspection PhpUndefinedMethodInspection */
    private function defineCarbonMacro(): void
    {
        Carbon::macro('localStartOfWeek', static function (): CarbonImmutable|Carbon {
            return self::this()->isSunday()
                ? self::this()
                : self::this()->previous(CarbonInterface::SUNDAY);
        });

        Carbon::macro('localEndOfWeek', static function (): CarbonImmutable|Carbon {
            return self::this()->isSaturday()
                ? self::this()
                : self::this()->next(CarbonInterface::SATURDAY);
        });

        Carbon::macro('isLocalCurrentWeek', static function (): bool {
            return self::this()->betweenIncluded(
                today()->localStartOfWeek(),
                today()->localEndOfWeek()
            );
        });

        CarbonImmutable::macro('localStartOfWeek', static function (): CarbonImmutable|Carbon {
            return self::this()->isSunday()
                ? self::this()
                : self::this()->previous(CarbonInterface::SUNDAY);
        });

        CarbonImmutable::macro('localEndOfWeek', static function (): CarbonImmutable|Carbon {
            return self::this()->isSaturday()
                ? self::this()
                : self::this()->next(CarbonInterface::SATURDAY);
        });

        CarbonImmutable::macro('isLocalCurrentWeek', static function (): bool {
            return self::this()->betweenIncluded(
                today()->localStartOfWeek(),
                today()->localEndOfWeek()
            );
        });

        CarbonPeriod::macro('localWeek', static function (): CarbonPeriod {
            return CarbonPeriod::create(today()->localStartOfWeek(), today()->localEndOfWeek());
        });
    }
}
