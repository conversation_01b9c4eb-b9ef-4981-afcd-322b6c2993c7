<?php

namespace App\Jobs;

use App\Exports\ReportExport;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Scopes\DateFilter;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\Collection;

class GeneratePresentAbsentExcelJob extends BaseGenerateExcelJob
{
    public function __construct(Employee $employee, ReportTask $reportTask)
    {
        parent::__construct($employee, $reportTask);
    }

    public function generateExcel(string $path): bool|PendingDispatch
    {
        return (new ReportExport('report.excel.status', [
            'employees' => $this->getEmployees(),
            'days' => $this->generateDaysOf($this->reportTask->data->period),
            'reportData' => $this->reportTask->data,
        ]))->store(filePath: $path, disk: 'minio');
    }

    public function getEmployees(): Collection
    {
        $data = $this->reportTask->data;

        return $this->employee->team
            ->employees()
            ->when(
                $data->departmentsIds,
                fn($query) => $query->whereIn('department_id', $data->departmentsIds)
            )
            ->when($data->employeesIds, fn($query) => $query->whereIn('id', $data->employeesIds))
            ->with([
                'attendances' => fn($query) => $query
                    ->tap(new DateFilter(from: $data->period, fromColumn: 'date'))
                    ->where('status', '!=', Attendance::YET),
                'department',
            ])
            ->get();
    }

    protected function generateDaysOf(CarbonPeriod $period): array
    {
        $days = [];
        $start = $period->start;
        $endDate = $period->end;

        for ($i = $start->day; $i <= $endDate->day; $i++) {
            $day = Carbon::createFromDate($start->year, $start->month, $i);

            $days[$i] = [
                'month' => $day->shortEnglishMonth,
                'dayOfWeek' => $day->shortEnglishDayOfWeek,
                'day' => $day->format('d'),
            ];
        }

        return $days;
    }
}
