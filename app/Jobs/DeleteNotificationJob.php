<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\DB;

class DeleteNotificationJob implements ShouldQueue
{
    use Queueable;

    public function __construct(private string $notificationClass, private int $payloadId)
    {
    }

    public function handle(): void
    {
        // we need retries for deadlock issues
        DB::transaction(function () {
            DatabaseNotification::query()
                ->where('type', $this->notificationClass)
                ->where('data->payload->id', $this->payloadId)
                ->delete();
        }, 5);
    }
}
