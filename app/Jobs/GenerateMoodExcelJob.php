<?php

namespace App\Jobs;

use App\Exports\MoodReportExport;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Scopes\DateFilter;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\Collection;

class GenerateMoodExcelJob extends BaseGenerateExcelJob
{
    public function __construct(Employee $employee, ReportTask $reportTask)
    {
        parent::__construct($employee, $reportTask);
    }

    public function generateExcel(string $path): bool|PendingDispatch
    {
        $attendances = $this->getAttendances();

        return (new MoodReportExport([
            'mood_per_department' => $attendances->groupBy('employee.department_id')->map(
                fn($attendance) => [
                    'department_name' => $attendance->first()->employee->department?->name,
                    'in_mood' => $attendance->avg('in_mood'),
                    'out_mood' => $attendance->avg('out_mood'),
                ]
            ),
            'mood_per_shift' => $attendances->groupBy('shift_id')->map(
                fn($attendance) => [
                    'shift_name' => $attendance->first()->shift?->name,
                    'in_mood' => $attendance->avg('in_mood'),
                    'out_mood' => $attendance->avg('out_mood'),
                ]
            ),
        ]))->store(filePath: $path, disk: 'minio');
    }

    public function getAttendances(): Collection
    {
        return $this->employee->team
            ->attendances()
            ->select('id', 'team_id', 'employee_id', 'in_mood', 'out_mood', 'shift_id', 'date')
            ->with(
                'employee:id,department_id,first_name,last_name',
                'employee.department:id,name',
                'shift'
            )
            ->where('status', '!=', Attendance::YET)
            ->tap(new DateFilter(from: $this->reportTask->data->period, fromColumn: 'date'))
            ->get();
    }
}
