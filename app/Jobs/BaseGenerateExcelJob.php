<?php

namespace App\Jobs;

use App\Models\Employee;
use App\Models\ReportTask;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Str;
use Throwable;

abstract class BaseGenerateExcelJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Employee $employee, public ReportTask $reportTask)
    {
        $this->onQueue('long-running-queue');
        $this->onConnection('redis-long-running');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $path = $this->reportTask->report->name->path(Str::uuid(), 'xlsx');

        $stored = $this->generateExcel($path);

        if (!$stored) {
            $this->fail('Failed to store the file');
            return;
        }

        $this->reportTask->markAsCompleted($path);
    }

    /** @codeCoverageIgnore handled by <PERSON>vel */
    public function failed(?Throwable $exception): void
    {
        $this->reportTask->markAsFailed($exception->getMessage());

        report($exception);
    }

    abstract public function generateExcel(string $path): bool|PendingDispatch;
}
