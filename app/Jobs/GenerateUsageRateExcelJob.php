<?php

namespace App\Jobs;

use App\Exports\ReportExport;
use App\Models\Employee;
use App\Models\ReportTask;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\Collection;

class GenerateUsageRateExcelJob extends BaseGenerateExcelJob
{
    public function __construct(Employee $employee, ReportTask $reportTask)
    {
        parent::__construct($employee, $reportTask);
    }

    public function generateExcel(string $path): bool|PendingDispatch
    {
        return (new ReportExport('report.excel.usage_rate', [
            'employees' => $this->getEmployees(),
        ]))->store(filePath: $path, disk: 'minio');
    }

    public function getEmployees(): Collection
    {
        $data = $this->reportTask->data;

        return $this->employee->team
            ->employees()
            ->with(['tags', 'department', 'shifts', 'locations'])
            ->when($data->employeesIds, fn($query) => $query->whereIn('id', $data->employeesIds))
            ->when(
                $data->departmentsIds,
                fn($query) => $query->whereIn('department_id', $data->departmentsIds)
            )
            ->when($data->shifts, fn($query) => $query->filterByShifts($data->shifts))
            ->when($data->locations, fn($query) => $query->filterByLocations($data->locations))
            ->when($data->tags, fn($query) => $query->filterByTags($data->tags))
            ->when(
                is_bool($data->usageStatus),
                fn($query) => $data->usageStatus
                    ? $query->whereNotNull('last_activity_at')
                    : $query->whereNull('last_activity_at')
            )
            ->get();
    }
}
