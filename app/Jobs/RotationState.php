<?php

namespace App\Jobs;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Models\WorkSchedule;
use Illuminate\Support\Collection;

class RotationState
{
    private int $workdayIndex = 0;
    private int $workDaysCount = 0;
    private int $offDaysCount = 0;
    private int $repetitionsCount = 0;
    private bool $isInWorkPeriod = true;
    private int $offDaysAfterRepetitionCount = 0;
    private bool $isInOffDaysAfterRepetition = false;

    public function __construct(
        private readonly Collection $workdays
    ) {}

    public function getCurrentWorkday()
    {
        return $this->workdays[$this->workdayIndex];
    }

    public function advanceDay(WorkSchedule $workSchedule): void
    {
        // Handle off days after repetition first
        if ($this->isInOffDaysAfterRepetition) {
            $this->offDaysAfterRepetitionCount++;
            if ($this->offDaysAfterRepetitionCount >= $workSchedule->off_days_after_each_repetition) {
                $this->isInOffDaysAfterRepetition = false;
                $this->offDaysAfterRepetitionCount = 0;
            }
            return;
        }

        // Only handle rotation logic for number of days distribution
        if ($workSchedule->work_and_off_days_distribution_type !== WorkAndOffDaysDistributionType::NumberOfDays) {
            return;
        }

        $currentWorkday = $this->getCurrentWorkday();

        if ($this->isInWorkPeriod) {
            $this->workDaysCount++;
            if ($this->workDaysCount >= $currentWorkday->pivot->work_days_number) {
                $this->isInWorkPeriod = false;
                $this->workDaysCount = 0;
            }
        } else {
            $this->offDaysCount++;
            if ($this->offDaysCount >= $currentWorkday->pivot->off_days_number) {
                $this->isInWorkPeriod = true;
                $this->offDaysCount = 0;
                $this->repetitionsCount++;

                // Check if we need to move to next workday
                if ($this->repetitionsCount >= $currentWorkday->pivot->repetitions_number) {
                    $this->moveToNextWorkday($workSchedule);
                }
            }
        }
    }

    private function moveToNextWorkday(WorkSchedule $workSchedule): void
    {
        $this->repetitionsCount = 0;
        $this->workdayIndex = ($this->workdayIndex + 1) % $this->workdays->count();

        // Start off days after repetition if configured
        if ($workSchedule->off_days_after_each_repetition > 0) {
            $this->isInOffDaysAfterRepetition = true;
            $this->offDaysAfterRepetitionCount = 0;
        }
    }
}
