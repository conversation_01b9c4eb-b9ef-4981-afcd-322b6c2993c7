<?php

namespace App\Jobs;

use App\Exports\ReportExport;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Scopes\DateFilter;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\Collection;

class GenerateProofStatusExcelJob extends BaseGenerateExcelJob
{
    public function __construct(Employee $employee, ReportTask $reportTask)
    {
        parent::__construct($employee, $reportTask);
    }

    public function generateExcel(string $path): bool|PendingDispatch
    {
        return (new ReportExport('report.excel.proof-status', [
            'proofs' => $this->getProofs(),
        ]))->store(filePath: $path, disk: 'minio');
    }

    public function getProofs(): Collection
    {
        $data = $this->reportTask->data;

        return $this->employee->team
            ->proofs()
            ->with('employee')
            ->when(
                $data->employeesIds || $data->departmentsIds,
                fn($query) => $query->whereHas(
                    'employee',
                    fn($query) => $query
                        ->when(
                            $data->employeesIds,
                            fn($query) => $query->whereIn('id', $data->employeesIds)
                        )
                        ->when(
                            $data->departmentsIds,
                            fn($query) => $query->whereIn('department_id', $data->departmentsIds)
                        )
                )
            )
            ->tap(DateFilter::createdAt($data->period))
            ->latest()
            ->get();
    }
}
