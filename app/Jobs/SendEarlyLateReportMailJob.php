<?php

namespace App\Jobs;

use App\DTOs\EarlyLateMailData;
use App\Mail\EarlyLateReportMail;
use Mail;
use Sassnowski\Venture\WorkflowableJob;
use <PERSON>ssnowski\Venture\WorkflowStep;

class SendEarlyLateReportMailJob implements WorkflowableJob
{
    use WorkflowStep;

    public function __construct(protected EarlyLateMailData $earlyLateMailData)
    {
    }

    public function handle(): void
    {
        $receiver = $this->earlyLateMailData->manager
            ->loadMissing('earlyLateDelegatedEmployee')
            ->receiverOfEarlyLateReport();

        Mail::to($receiver)->send(new EarlyLateReportMail($this->earlyLateMailData));
    }
}
