<?php

namespace App\Jobs;

use App\Interfaces\ExcelPartsMerger;
use App\Models\ReportTask;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON><PERSON><PERSON>\Venture\WorkflowableJob;
use <PERSON><PERSON><PERSON><PERSON>\Venture\WorkflowStep;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Str;
use Throwable;

class MergeExcelPartsJob implements WorkflowableJob
{
    use WorkflowStep;

    public function __construct(
        private ReportTask $reportTask,
        private ExcelPartsMerger $excelPartsMerger
    ) {
    }

    public function handle(): void
    {
        retry(
            times: 3,
            callback: function () {
                $tempFilePath = 'temp/' . Str::uuid() . '.xlsx';

                $fullFileTempPath = $this->createTempFile($tempFilePath);

                $writer = SimpleExcelWriter::create(file: $fullFileTempPath);

                $this->excelPartsMerger->configureOptions($writer->getWriter());

                $this->excelPartsMerger->beforeMerging($writer, $this->reportTask);

                $this->processParts($writer);

                $writer->close(); // manually save the file to store it

                $finalFilePath = $this->storeFinalFile($tempFilePath);

                Storage::disk('local')->delete($tempFilePath);

                $this->reportTask->markAsCompleted($finalFilePath);
            }
        );
    }

    protected function processParts(SimpleExcelWriter $writer): void
    {
        $prefix = config('database.redis.options.prefix');

        $sortedKeys = collect(Redis::keys($this->reportTask->partNameKeysPattern()))
            ->map(fn(string $key) => Str::replace($prefix, '', $key))
            ->sortBy(fn(string $key) => explode('_', $key)[2]);

        // Get all values in a single pipeline operation
        $values = Redis::pipeline(function ($pipe) use ($sortedKeys) {
            foreach ($sortedKeys as $key) {
                $pipe->get($key);
            }
        });

        // Process each value and delete the key
        foreach ($sortedKeys as $index => $key) {
            $formattedValue = collect(json_decode($values[$index], true));

            $this->excelPartsMerger->mergePart($writer, $formattedValue, $this->reportTask);

            Redis::del($key);
        }
    }

    protected function storeFinalFile(string $finalFileTempPath): string
    {
        $permanentPath = $this->reportTask->report->name->path(Str::uuid(), 'xlsx');

        Storage::disk('minio')->put(
            path: $permanentPath,
            contents: Storage::disk('local')->get($finalFileTempPath)
        );

        return $permanentPath;
    }

    protected function createTempFile(string $tempFilePath): string
    {
        Storage::disk('local')->put($tempFilePath, contents: '');

        return Storage::disk('local')->path($tempFilePath);
    }

    /** @codeCoverageIgnore handled by Laravel */
    public function failed(?Throwable $exception): void
    {
        $this->reportTask->markAsFailed($exception->getMessage());

        report($exception);
    }
}
