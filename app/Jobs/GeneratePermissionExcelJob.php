<?php

namespace App\Jobs;

use App\Exports\ReportExport;
use App\Models\Employee;
use App\Models\ReportTask;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\Collection;

class GeneratePermissionExcelJob extends BaseGenerateExcelJob
{
    public function __construct(Employee $employee, ReportTask $reportTask)
    {
        parent::__construct($employee, $reportTask);
    }

    public function generateExcel(string $path): bool|PendingDispatch
    {
        return (new ReportExport('report.excel.permission', [
            'permissions' => $this->getPermissions(),
        ]))->store(filePath: $path, disk: 'minio');
    }

    public function getPermissions(): Collection
    {
        $data = $this->reportTask->data;

        return $this->employee->team
            ->approvalRequests()
            ->permission()
            ->with('employee')
            ->when(
                $data->employeesIds || $data->departmentsIds,
                fn($query) => $query->whereHas(
                    'employee',
                    fn($query) => $query
                        ->when(
                            $data->employeesIds,
                            fn($query) => $query->whereIn('id', $data->employeesIds)
                        )
                        ->when(
                            $data->departmentsIds,
                            fn($query) => $query->whereIn('department_id', $data->departmentsIds)
                        )
                )
            )
            ->date($data->period)
            ->latest()
            ->get();
    }
}
