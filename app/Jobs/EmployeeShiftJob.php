<?php

namespace App\Jobs;

use App\DTOs\AttachEmployeesToShiftLocationData;
use App\Enums\DurationStatus;
use App\Models\Employee;
use App\Models\Shift;
use App\Models\Team;
use App\Notifications\NewPermanentShiftAssignedNotification;
use App\Notifications\NewTemporaryShiftAssignedNotification;
use App\Notifications\PermanentShiftAssignedChangedNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class EmployeeShiftJob implements ShouldQueue
{
    use Queueable;

    protected Team $team;
    protected ?Notification $notification;

    public function __construct(
        protected Shift $shift,
        protected AttachEmployeesToShiftLocationData $data,
        protected ?Employee $manager = null
    ) {
        $this->team = $this->shift->team;
        $this->notification = null;
    }

    public function handle(): void
    {
        $this->employeesQuery()
            ->with(['shifts', 'shift'])
            ->each(function (Employee $employee) {
                $employee->shifts()->sync($this->formatShifts($employee));
                $employee->notify($this->notification);
            });
    }

    public function employeesQuery(): QueryBuilder|Relation
    {
        $query = $this->manager
            ? $this->manager->managedEmployees()->active()
            : $this->team->employees();

        return $query->whereIn('id', $this->data->employeesIds);
    }

    protected function formatShifts(Employee $employee): array
    {
        $isPermanent = $this->data->type === DurationStatus::PERMANENT;
        $hasExistingPermanentShift = $employee->shifts
            ->where('pivot.permanent', true)
            ->isNotEmpty();

        $permanentShift = [
            'shift_id' => $isPermanent ? $this->shift->id : $employee->shift?->id,
            'permanent' => true,
            'start_at' => null,
            'end_at' => null,
        ];

        $temporaryShifts = $employee->shifts->whereNotNull('pivot.start_at')->map(
            fn(Shift $shift) => [
                'shift_id' => $shift->id,
                'permanent' => false,
                'start_at' => $shift->pivot->start_at,
                'end_at' => $shift->pivot->end_at,
            ]
        );

        if (!$isPermanent) {
            $temporaryShifts->push([
                'shift_id' => $this->shift->id,
                'permanent' => false,
                'start_at' => $this->data->period->getStartDate(),
                'end_at' => $this->data->period->getendDate(),
            ]);
        }

        $this->notification = $isPermanent
            ? ($hasExistingPermanentShift
                ? new PermanentShiftAssignedChangedNotification($this->shift)
                : new NewPermanentShiftAssignedNotification($this->shift))
            : new NewTemporaryShiftAssignedNotification(
                $this->shift,
                $this->data->period->getStartDate()->toImmutable(),
                $this->data->period->getendDate()->toImmutable()
            );

        if ($permanentShift['shift_id']) {
            return [$permanentShift, ...$temporaryShifts];
        }

        return $temporaryShifts->toArray();
    }

    public function hasInvalidEmployees(): ?Collection
    {
        if (!$this->data->period) {
            return null;
        }

        $invalidEmployees = $this->team
            ->employees()
            ->whereIn('id', $this->data->employeesIds)
            ->whereHas(
                'shifts',
                fn($query) => $query
                    ->where('start_at', '<', $this->data->period->end)
                    ->where('end_at', '>', $this->data->period->start)
            )
            ->get();

        if ($invalidEmployees->isEmpty()) {
            return null;
        }

        return $invalidEmployees;
    }

    public function queueIfNeededAndReturnMessage(): string
    {
        $isEmployeesCountTooHigh = $this->employeesQuery()->count() > 100;

        if ($isEmployeesCountTooHigh) {
            dispatch($this);
        } else {
            dispatch_sync($this);
        }

        return $isEmployeesCountTooHigh
            ? __('The shift was added to employees successfully, it may take a while')
            : __('The shift was added to employees successfully.');
    }
}
