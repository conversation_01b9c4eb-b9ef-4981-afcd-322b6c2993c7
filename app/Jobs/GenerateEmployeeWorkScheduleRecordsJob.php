<?php

namespace App\Jobs;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkdayType;
use App\Enums\WorkScheduleType;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleRecord;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Throwable;

class GenerateEmployeeWorkScheduleRecordsJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly WorkSchedule $workSchedule,
        private readonly Employee $employee
    ) {
        $this->onQueue('work-schedule-records');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Load workdays with pivot data
        $this->workSchedule->load('workdays');

        // Calculate start date (never in the past)
        $startDate = Carbon::parse($this->workSchedule->start_date)->max(Carbon::today());

        // Generate records for the next 6 months
        $endDate = $startDate->copy()->addMonths(6);

        // Delete existing future records for this employee and work schedule
        WorkScheduleRecord::where('work_schedule_id', $this->workSchedule->id)
            ->where('employee_id', $this->employee->id)
            ->where('date', '>=', $startDate->toDateString())
            ->delete();

        // Generate records based on schedule type
        if ($this->workSchedule->type === WorkScheduleType::Fixed) {
            $this->generateFixedScheduleRecords($startDate, $endDate);
        } else {
            $this->generateRotationalScheduleRecords($startDate, $endDate);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        report($exception);
    }

    private function generateFixedScheduleRecords(Carbon $startDate, Carbon $endDate): void
    {
        $workday = $this->workSchedule->workdays->first();
        if (!$workday) {
            return;
        }

        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            if ($this->shouldCreateRecordForDate($currentDate)) {
                $this->createRecord($currentDate, $workday);
            }
            $currentDate->addDay();
        }
    }

    private function generateRotationalScheduleRecords(Carbon $startDate, Carbon $endDate): void
    {
        $workdays = $this->workSchedule->workdays;
        if ($workdays->isEmpty()) {
            return;
        }

        $currentDate = $startDate->copy();
        $workdayIndex = 0;
        $currentWorkday = $workdays[$workdayIndex];

        // Counters for current workday cycle
        $workDaysCount = 0;
        $offDaysCount = 0;
        $repetitionsCount = 0;
        $isInWorkPeriod = true;

        while ($currentDate->lte($endDate)) {
            if ($this->shouldCreateRecordForDate($currentDate)) {
                $this->createRecord($currentDate, $currentWorkday);
            }
            $currentDate->addDay();

            // Handle rotational logic
            if (
                $this->workSchedule->work_and_off_days_distribution_type ===
                WorkAndOffDaysDistributionType::NumberOfDays
            ) {
                if ($isInWorkPeriod) {
                    $workDaysCount++;
                    if ($workDaysCount >= $currentWorkday->pivot->work_days_number) {
                        $isInWorkPeriod = false;
                        $workDaysCount = 0;
                    }
                    continue;
                }

                $offDaysCount++;
                if ($offDaysCount >= $currentWorkday->pivot->off_days_number) {
                    $isInWorkPeriod = true;
                    $offDaysCount = 0;
                    $repetitionsCount++;

                    // Check if we need to move to next workday
                    if ($repetitionsCount >= $currentWorkday->pivot->repetitions_number) {
                        $repetitionsCount = 0;
                        $workdayIndex = ($workdayIndex + 1) % $workdays->count();
                        $currentWorkday = $workdays[$workdayIndex];

                        // Add off days after repetition if configured
                        if ($this->workSchedule->off_days_after_each_repetition > 0) {
                            for (
                                $i = 0;
                                $i < $this->workSchedule->off_days_after_each_repetition;
                                $i++
                            ) {
                                if ($currentDate->lte($endDate)) {
                                    if ($this->shouldCreateRecordForDate($currentDate)) {
                                        $this->createRecord($currentDate, $currentWorkday);
                                    }
                                    $currentDate->addDay();
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private function createRecord(Carbon $date, $workday): void
    {
        $workdayType = $this->determineWorkdayType($date);

        WorkScheduleRecord::create([
            'team_id' => $this->workSchedule->team_id,
            'work_schedule_id' => $this->workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday->id,
            'date' => $date->toDateString(),
            'workday_type' => $workdayType,
        ]);
    }

    private function determineWorkdayType(Carbon $date): WorkdayType
    {
        // Check for holidays first
        $isHoliday = Holiday::query()
            ->where('team_id', $this->employee->team_id)
            ->whereDate('start_date', '<=', $date)
            ->whereDate('end_date', '>=', $date)
            ->exists();

        if ($isHoliday) {
            return WorkdayType::Holiday;
        }

        // Check for approved leave
        $onLeave = $this->employee->leaves()->date($date)->approved()->exists();

        if ($onLeave) {
            return WorkdayType::Leave;
        }

        // Check if it's a weekend based on work schedule distribution
        if ($this->isWeekendDay($date)) {
            return WorkdayType::Weekend;
        }

        return WorkdayType::Weekday;
    }

    private function isWeekendDay(Carbon $date): bool
    {
        if (
            $this->workSchedule->work_and_off_days_distribution_type ===
            WorkAndOffDaysDistributionType::SpecificDays
        ) {
            // If using specific days, unselected days are weekends
            return !$this->workSchedule->isDateInSpecificDays($date);
        }

        // For number of days distribution, off days are weekends
        // This is handled in the rotational logic above
        return false;
    }

    private function shouldCreateRecordForDate(Carbon $date): bool
    {
        if (
            $this->workSchedule->work_and_off_days_distribution_type ===
            WorkAndOffDaysDistributionType::SpecificDays
        ) {
            // For specific days, only create records for selected days
            return $this->workSchedule->isDateInSpecificDays($date);
        }

        // For number of days distribution, create records for all days
        return true;
    }
}
