<?php

namespace App\Jobs;

use App\Exports\ReportExport;
use App\Models\Employee;
use App\Models\ReportTask;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\LazyCollection;

class GenerateLeaveExcelJob extends BaseGenerateExcelJob
{
    public function __construct(Employee $employee, ReportTask $reportTask)
    {
        parent::__construct($employee, $reportTask);
    }

    public function generateExcel(string $path): bool|PendingDispatch
    {
        return (new ReportExport('report.excel.leaves', [
            'leaves' => $this->leaves(),
            'period' => $this->reportTask->data->period,
        ]))->store(filePath: $path, disk: 'minio');
    }

    public function leaves(): LazyCollection
    {
        $data = $this->reportTask->data;

        return $this->employee->team
            ->leaves()
            ->with(['employee' => ['department', 'tags']])
            ->when(
                $data->employeesIds,
                fn($query) => $query->whereIn('employee_id', $data->employeesIds)
            )
            ->when(
                $data->departmentsIds,
                fn($query) => $query->whereIn('department_id', $data->departmentsIds)
            )
            ->date($data->period)
            ->cursor();
    }
}
