<?php

namespace App\Jobs;

use App\Models\WorkSchedule;
use App\Services\ResolveWorkScheduleEmployeesService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class GenerateWorkScheduleRecordsJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     */
    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private readonly WorkSchedule $workSchedule
    ) {
        $this->onQueue('work-schedule-records');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Load assignments
        $this->workSchedule->load('assignments');

        // Resolve all affected employees
        $employees = ResolveWorkScheduleEmployeesService::handle($this->workSchedule->assignments);

        // Dispatch individual jobs for each employee
        foreach ($employees as $employee) {
            GenerateEmployeeWorkScheduleRecordsJob::dispatch($this->workSchedule, $employee);
        }
    }
}
