<?php

namespace App\Jobs;

use App\Interfaces\ExcelPart;
use App\Interfaces\ExcelPartCreator;
use App\Models\ReportTask;
use Illuminate\Support\Facades\Redis;
use <PERSON><PERSON><PERSON><PERSON>\Venture\WorkflowableJob;
use <PERSON><PERSON><PERSON><PERSON>\Venture\WorkflowStep;
use Throwable;

class CreateExcelPartJob implements WorkflowableJob
{
    use WorkflowStep;

    const TWO_HOURS = 60 * 60 * 2;

    public function __construct(
        protected int $index,
        protected ExcelPart $part,
        protected ExcelPartCreator $excelPartCreator,
        protected ReportTask $reportTask
    ) {
    }

    public function handle(): void
    {
        retry(
            times: 3,
            callback: function () {
                Redis::set(
                    key: "{$this->reportTask->team_id}_{$this->reportTask->id}_$this->index",
                    value: json_encode(
                        $this->excelPartCreator->createPart($this->reportTask, $this->part)
                    ),
                    expireResolution: 'EX',
                    expireTTL: self::TWO_HOURS
                );
            },
            sleepMilliseconds: 500
        );
    }

    /** @codeCoverageIgnore handled by <PERSON><PERSON> */
    public function failed(?Throwable $exception): void
    {
        $this->reportTask->markAsFailed($exception->getMessage());

        report($exception);
    }
}
