<?php

namespace App\Jobs;

use App\DTOs\AttachEmployeesToShiftLocationData;
use App\Enums\DurationStatus;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use App\Support\FormatLocationsForEmployees;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Spatie\QueryBuilder\QueryBuilder;

class EmployeeLocationJob implements ShouldQueue
{
    use Queueable;

    protected Team $team;

    public function __construct(
        protected AttachEmployeesToShiftLocationData $data,
        protected ?Location $location = null,
        protected ?Employee $manager = null
    ) {
        $this->team = $this->location?->team ?? $this->manager?->team;
    }

    public function handle(): void
    {
        $this->employeesQuery()->each(function (Employee $employee) {
            $locations = $this->formatLocations($employee);

            // for some reason, sync doesn't work here and it's buggy
            $employee->locations()->detach();

            $employee->locations()->attach($locations);
        });
    }

    protected function formatLocations(Employee $employee): Collection
    {
        $locations = Collection::wrap($this->data->locations);

        if ($this->location) {
            $locations->push([
                'id' => $this->location->id,
                'type' => $this->data->type?->value,
                'start_date' => $this->data->period?->start,
                'end_date' => $this->data->period?->end,
            ]);
        }

        $formattedCurrentLocations = $employee->locations->map(
            fn(Location $location) => [
                'id' => $location->id,
                'type' => $location->pivot->permanent
                    ? DurationStatus::PERMANENT->value
                    : DurationStatus::TEMPORARY->value,
                'start_date' => $location->pivot->start_date,
                'end_date' => $location->pivot->end_date,
            ]
        );

        return FormatLocationsForEmployees::format(
            locations: collect([...$formattedCurrentLocations, ...$locations])
        );
    }

    public function employeesQuery(): QueryBuilder|Relation
    {
        $query = $this->manager
            ? $this->manager->managedEmployees()->active()
            : $this->team->employees();

        return $query->with('locations')->whereIn('id', $this->data->employeesIds);
    }

    public function queueIfNeededAndReturnMessage(): string
    {
        $isEmployeesCountTooHigh = $this->employeesQuery()->count() > 100;

        if ($isEmployeesCountTooHigh) {
            dispatch($this);
        } else {
            dispatch_sync($this);
        }

        return $isEmployeesCountTooHigh
            ? __('The location was added to employees successfully, it may take a while')
            : __('The location was added to employees successfully.');
    }
}
