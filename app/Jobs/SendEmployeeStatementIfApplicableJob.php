<?php

namespace App\Jobs;

use App\Enums\EmployeeStatementType;
use App\Models\Attendance;
use App\Models\EmployeeStatement;
use App\Notifications\EmployeeStatementNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendEmployeeStatementIfApplicableJob implements ShouldQueue
{
    use Queueable;

    public function __construct(protected Attendance $attendance)
    {
    }

    public function handle(): void
    {
        $employeeStatementType = $this->employeeStatementTypeOfAttendance();

        // if employee has fulfilled the attendance record, no need to send statement
        if (!$employeeStatementType) {
            return;
        }

        EmployeeStatement::updateOrCreate(
            ['attendance_id' => $this->attendance->id],
            [
                'team_id' => $this->attendance->team_id,
                'employee_id' => $this->attendance->employee_id,
                'type' => $employeeStatementType,
            ]
        );

        $this->attendance->employee->notify(
            new EmployeeStatementNotification(
                attendance: $this->attendance,
                employeeStatementType: $employeeStatementType
            )
        );
    }

    public function employeeStatementTypeOfAttendance(): ?EmployeeStatementType
    {
        $employeeStatementConfig = $this->attendance->team->employee_statement_config;

        if (!$employeeStatementConfig->enabled) {
            return null;
        }

        if ($this->attendance->considered_as_absent) {
            return $employeeStatementConfig->absentEnabled ? EmployeeStatementType::Absent : null;
        }

        $allowedMinutes =
            ($this->attendance->flexible_hours ?: // when there is flexible hours, use it and ignore the buffer in config
                $employeeStatementConfig->lateCheckinBufferMinutes) ?? 0;

        $didCheckedInTooLate = $this->attendance->check_in?->greaterThan(
            $this->attendance->shift_from->addMinutes($allowedMinutes)
        );

        $didCheckedOutTooEarly = $this->attendance->check_out?->lessThan(
            $this->attendance->shift_to->subMinutes($allowedMinutes)
        );

        if ($didCheckedInTooLate && $didCheckedOutTooEarly) {
            return EmployeeStatementType::LateCheckinAndEarlyCheckout;
        }

        if ($didCheckedInTooLate) {
            return EmployeeStatementType::LateCheckin;
        }

        if ($didCheckedOutTooEarly) {
            return EmployeeStatementType::EarlyCheckout;
        }

        return null;
    }
}
