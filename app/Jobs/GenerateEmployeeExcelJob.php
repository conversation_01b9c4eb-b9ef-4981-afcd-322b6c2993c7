<?php

namespace App\Jobs;

use App\Exports\EmployeesExport;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Scopes\EmployeesFilters;
use Illuminate\Foundation\Bus\PendingDispatch;
use Illuminate\Support\LazyCollection;

class GenerateEmployeeExcelJob extends BaseGenerateExcelJob
{
    public function __construct(Employee $employee, ReportTask $reportTask)
    {
        parent::__construct($employee, $reportTask);
    }

    public function generateExcel(string $path): bool|PendingDispatch
    {
        return (new EmployeesExport($this->employees()))->handle($path);
    }

    public function employees(): LazyCollection
    {
        return $this->employee->team
            ->employees()
            ->tap(new EmployeesFilters($this->reportTask->data))
            ->lazy();
    }
}
