<?php

namespace App\Observers;

use App\Enums\SyncName;
use App\Messages\SyncMessage;
use App\Models\Tenant;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class TenantObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Tenant "created" event.
     */
    public function created(Tenant $tenant): void
    {
        (new SyncMessage($this->format($tenant), SyncName::TenantCreate))->send();

        $tenant->refreshLogos();
    }

    /**
     * Handle the Tenant "updated" event.
     */
    public function updated(Tenant $tenant): void
    {
        (new SyncMessage($this->format($tenant), SyncName::TenantUpdate))->send();
    }

    public function format(Tenant $tenant): array
    {
        $tenant->refresh();

        return [
            'id' => $tenant->id,
            'domain' => $tenant->domain,
            'allow_sms' => $tenant->allow_sms,
            'name' => $tenant->name,
            'color' => $tenant->color,
            'is_active' => $tenant->is_active,
            'colored_logo_url' => $tenant->colored_logo_url,
            'white_logo_url' => $tenant->white_logo_url,
        ];
    }
}
