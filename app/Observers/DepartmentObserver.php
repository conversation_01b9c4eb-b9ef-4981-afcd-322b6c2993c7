<?php

namespace App\Observers;

use App\Enums\SyncName;
use App\Messages\SyncMessage;
use App\Models\Department;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class DepartmentObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Department "created" event.
     */
    public function created(Department $department): void
    {
        (new SyncMessage($this->format($department), SyncName::DepartmentCreate))->send();
    }

    /**
     * Handle the Department "updated" event.
     */
    public function updated(Department $department): void
    {
        (new SyncMessage($this->format($department), SyncName::DepartmentUpdate))->send();
    }

    /**
     * Handle the Department "deleted" event.
     */
    public function deleted(Department $department): void
    {
        (new SyncMessage($this->format($department), SyncName::DepartmentDelete))->send();
    }

    public function format(Department $department): array
    {
        return $department->refresh()->toArray();
    }
}
