<?php

namespace App\Observers;

use App\Enums\SyncName;
use App\Messages\SyncMessage;
use App\Models\Employee;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class EmployeeObserver implements ShouldHandleEventsAfterCommit
{
    /**
     * Handle the Employee "created" event.
     */
    public function created(Employee $employee): void
    {
        (new SyncMessage($this->format($employee), SyncName::EmployeeCreate))->send();
    }

    /**
     * Handle the Employee "updated" event.
     */
    public function updated(Employee $employee): void
    {
        (new SyncMessage($this->format($employee), SyncName::EmployeeUpdate))->send();
    }

    public function format(Employee $employee): array
    {
        return $employee
            ->refresh()
            ->load(['tags', 'roles'])
            ->toArray();
    }
}
