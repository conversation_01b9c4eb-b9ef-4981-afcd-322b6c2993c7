<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class WebhookJob extends Model implements AuditableContract
{
    use Auditable;
    use HasFactory;

    const PENDING = 'PENDING';

    const SUCCESS = 'SUCCESS';

    const FAILED = 'FAILED';

    protected $guarded = ['id'];

    protected $casts = ['event_payload' => 'array'];

    public function scopeCanBeProcessed($query)
    {
        return $this->where('tries', '<', 5)->whereIn('status', [self::PENDING, self::FAILED]);
    }

    public function webhook()
    {
        return $this->belongsTo(Webhook::class);
    }

    public function logs()
    {
        return $this->hasMany(WebhookJobLog::class);
    }
}
