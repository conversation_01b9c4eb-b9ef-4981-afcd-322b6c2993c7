<?php

namespace App\Models;

use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Enums\ReportTaskCreatedByType;
use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Exports\MultiSheet\MultiSheetEarlyLateExcelStrategy;
use App\Exports\SingleSheet\SingleSheetEarlyLateExcelExportStrategy;
use App\Exports\SingleSummarySheet\SingleSummarySheetEarlyLateExcelExportStrategy;
use App\Interfaces\ExcelExportStrategy;
use App\Jobs\GenerateEmployeeExcelJob;
use App\Jobs\GenerateLeaveExcelJob;
use App\Jobs\GenerateMoodExcelJob;
use App\Jobs\GeneratePermissionExcelJob;
use App\Jobs\GeneratePresentAbsentExcelJob;
use App\Jobs\GenerateProofStatusExcelJob;
use App\Jobs\GenerateUsageRateExcelJob;
use App\Traits\BelongsToTenant;
use App\Workflows\GenerateEarlyLateReportWorkflow;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use RuntimeException;
use Storage;

class ReportTask extends Model implements AuditableContract
{
    use BelongsToTenant;
    use Auditable;
    use HasFactory;

    protected $fillable = [
        'status',
        'file_name',
        'file_path',
        'team_id',
        'created_by_id',
        'report_id',
        'error_message',
        'start_date',
        'end_date',
        'completed_at',
        'data',
        'created_by_type',
    ];

    protected $casts = [
        'status' => ReportTaskStatus::class,
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'completed_at' => 'datetime',
        'data' => ReportData::class,
        'created_by_type' => ReportTaskCreatedByType::class,
    ];

    public function scopeCreatedByEmployee(Builder $query): Builder
    {
        return $query->where('created_by_type', ReportTaskCreatedByType::EMPLOYEE);
    }

    protected function filePathUrl(): Attribute
    {
        return Attribute::get(
            fn() => $this->available()
                ? Storage::temporaryUrl($this->file_path, now()->addDay(), [
                    'ResponseContentDisposition' => "attachment; filename=$this->file_name.xlsx",
                ])
                : null
        );
    }

    public static function createFromData(
        ReportData $reportData,
        ReportName $reportName,
        Team $team,
        Employee $createdBy = null
    ) {
        return ReportTask::create([
            'file_name' => $reportName->fileName(
                $reportData->period
                    ? Carbon::parse($reportData->period->start)->format('Y-m-d')
                    : null
            ),
            'status' => ReportTaskStatus::Pending,
            'start_date' => $reportData->period?->start,
            'end_date' => $reportData->period?->end,
            'data' => $reportData,
            'report_id' => Report::byName($reportName)->id,
            'team_id' => $team->id,
            'created_by_id' => $createdBy?->id,
            'created_by_type' => $createdBy
                ? ReportTaskCreatedByType::EMPLOYEE
                : ReportTaskCreatedByType::SYSTEM,
        ]);
    }

    public function partNameKeysPattern(): string
    {
        return "{$this->team_id}_{$this->id}_*";
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function report(): BelongsTo
    {
        return $this->belongsTo(Report::class);
    }

    public function available(): bool
    {
        return $this->status === ReportTaskStatus::Success && $this->file_path;
    }

    public function markAsCompleted(string $path): void
    {
        $this->update([
            'status' => ReportTaskStatus::Success,
            'file_path' => $path,
            'completed_at' => now(),
        ]);
    }

    public function markAsFailed(?string $errorMessage = null): void
    {
        $this->update([
            'status' => ReportTaskStatus::Failed,
            'error_message' => $errorMessage,
        ]);
    }

    public function scopeSearch(Builder $query, string $search): void
    {
        $query->where(
            fn(Builder $query) => $query
                ->where('file_name', 'like', "%$search%")
                ->orWhereHas('report', fn($query) => $query->where('name', 'like', "%$search%"))
                ->orWhereHas('createdBy', fn($query) => $query->search($search))
        );
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', ReportTaskStatus::Pending);
    }

    public function regenerate(): void
    {
        $employee = $this->createdBy;

        $this->update(['status' => ReportTaskStatus::Pending]);

        match ($this->report->name) {
            ReportName::EarlyLate => GenerateEarlyLateReportWorkflow::start($this),
            ReportName::Mood => GenerateMoodExcelJob::dispatch($employee, $this),
            ReportName::Permission => GeneratePermissionExcelJob::dispatch($employee, $this),
            ReportName::PresentAbsent => GeneratePresentAbsentExcelJob::dispatch($employee, $this),
            ReportName::ProofStatus => GenerateProofStatusExcelJob::dispatch($employee, $this),
            ReportName::UsageRate => GenerateUsageRateExcelJob::dispatch($employee, $this),
            ReportName::Leave => GenerateLeaveExcelJob::dispatch($employee, $this),
            ReportName::Employees => GenerateEmployeeExcelJob::dispatch($employee, $this),
        };
    }

    public function getExcelExportStrategy(): ExcelExportStrategy
    {
        return match ($this->report->name) {
            ReportName::EarlyLate => match ($this->data->sheetMode) {
                SheetMode::SingleSheet => new SingleSheetEarlyLateExcelExportStrategy(),
                SheetMode::SingleSummarySheet
                    => new SingleSummarySheetEarlyLateExcelExportStrategy(),
                SheetMode::MultiSheet => new MultiSheetEarlyLateExcelStrategy(),
            },
            default => throw new RuntimeException('Invalid report name'),
        };
    }
}
