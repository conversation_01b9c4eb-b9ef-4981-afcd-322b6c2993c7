<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Subscription extends Model implements AuditableContract
{
    use Auditable;
    use HasFactory;
    use HasUuids;
    use BelongsToTenant;

    // We want to disable generate uuid because we sync uuid from Nawart Start
    public function newUniqueId(): null
    {
        return null;
    }

    public $incrementing = false;

    protected $fillable = ['id', 'team_id'];

    public function subscriptionItem(): HasOne
    {
        // this is intended to be a hasOne relationship (since a subscription to attendance will only have a single subscription item)
        return $this->hasOne(SubscriptionItem::class);
    }
}
