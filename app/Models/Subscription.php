<?php

namespace App\Models;

use App\Enums\SoftwareCode;
use App\Enums\SoftwarePackageCode;
use DB;
use Exception;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use function now;

class Subscription extends Model implements AuditableContract
{
    use Auditable, HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'total_employees',
        'start_at',
        'end_at',
        'tenant_id',
        'plan_id',
        'price',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'total_employees' => 'integer',
        'start_at' => 'date',
        'end_at' => 'date',
        'price' => 'float',
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function subscriptionItems()
    {
        return $this->hasMany(SubscriptionItem::class);
    }

    /**
     *
     * @param  Tenant  $tenant
     * @param  array{
     *     software_code: SoftwareCode,
     *     software_package_code: SoftwarePackageCode
     * }  $softwareData
     * @return Subscription
     */
    public static function createSubscription(Tenant $tenant, array $softwareData): Subscription
    {
        return DB::transaction(function () use ($softwareData, $tenant) {
            $subscription = Subscription::create([
                'total_employees' => 1000, // currently not enforced
                'start_at' => now(),
                'end_at' => now()->addYear(),
                'tenant_id' => $tenant->id,
                'price' => 0, // not implemented
            ]);

            foreach ($softwareData as $rawSoftware) {
                $softwareModel = Software::firstWhere('code', $rawSoftware['software_code']);

                if (!$softwareModel) {
                    throw new Exception(
                        'Software not found for code: ' . $rawSoftware['software_code']
                    );
                }

                $softwarePackageModel = $softwareModel
                    ->softwarePackages()
                    ->firstWhere('code', $rawSoftware['software_package_code']);

                $subscription->subscriptionItems()->create([
                    'software_id' => $softwareModel->id,
                    'software_package_id' => $softwarePackageModel->id,
                    'tenant_id' => $tenant->id,
                ]);
            }

            return $subscription;
        });
    }
}
