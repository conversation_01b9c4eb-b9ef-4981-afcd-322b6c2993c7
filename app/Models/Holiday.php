<?php

namespace App\Models;

use App\Scopes\DateFilter;
use App\Traits\BelongsToTenant;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Holiday extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;

    protected $fillable = ['name', 'team_id', 'start_date', 'end_date'];

    public function scopeDate(
        Builder $query,
        string|Carbon|CarbonImmutable|CarbonPeriod $from,
        string|Carbon|CarbonImmutable|null $to = null
    ): Builder {
        return $query->tap(
            new DateFilter(from: $from, to: $to, fromColumn: 'start_date', toColumn: 'end_date')
        );
    }
}
