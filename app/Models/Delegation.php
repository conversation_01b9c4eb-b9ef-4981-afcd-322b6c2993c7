<?php

namespace App\Models;

use App\Enums\DelegationType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Delegation extends Model implements AuditableContract
{
    use Auditable;
    use HasFactory;

    protected $fillable = ['type', 'delegated_id', 'delegatee_id'];

    protected $casts = ['type' => DelegationType::class];

    /**
     * manager who will delegate a task to employee
     */
    public function delegatee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'delegatee_id');
    }

    /**
     * employee who will be delegated a task from employee
     */
    public function delegated(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'delegated_id');
    }
}
