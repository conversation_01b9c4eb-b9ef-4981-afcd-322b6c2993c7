<?php

namespace App\Models;

use App\Enums\ProofMethod;
use App\Enums\ProofStatus;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Proof extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'employee_id',
        'notification_id',
        'method',
        'status',
        'location_id',
        'lat',
        'lng',
        'responded_at',
        'expire_at',
        'score',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'employee_id' => 'integer',
        'location_id' => 'integer',
        'responded_at' => 'timestamp',
        'expire_at' => 'datetime',
        'status' => ProofStatus::class,
        'method' => ProofMethod::class,
    ];

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function department(): HasOneThrough
    {
        return $this->hasOneThrough(
            Department::class,
            Employee::class,
            'id',
            'id',
            'employee_id',
            'department_id'
        );
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }
}
