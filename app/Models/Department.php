<?php

namespace App\Models;

use App\DTOs\RandomProofNotificationConfig;
use App\Traits\BelongsToTenant;
use App\Traits\HasNawartUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;

class Department extends Model implements AuditableContract
{
    use HasRecursiveRelationships;
    use Auditable;
    use BelongsToTenant;
    use HasFactory;
    use HasNawartUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'name',
        'parent_id',
        'remote_work',
        'manager_id',
        'nawart_uuid',
        'random_proof_notification_config',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'parent_id' => 'integer',
        'manager_id' => 'integer',
        'random_proof_notification_config' => RandomProofNotificationConfig::class,
    ];

    public $webhookPayload = ['id', 'name', 'parent_id'];

    public function parent()
    {
        return $this->belongsTo(self::class);
    }

    public function children()
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function manager()
    {
        return $this->belongsTo(Employee::class);
    }

    public function employees()
    {
        return $this->hasMany(Employee::class);
    }

    public function scopeHasActiveManager(Builder $query): Builder
    {
        return $query->whereHas('manager', fn(Builder $query) => $query->active());
    }

    public function setInitialRandomNotificationConfig(): void
    {
        if ($this->random_proof_notification_config) {
            return;
        }

        $this->update([
            'random_proof_notification_config' => new RandomProofNotificationConfig(
                enabled: $this->team->random_proof_notification_config->enabled,
                inherited: true,
                count: $this->team->random_proof_notification_config->count
            ),
        ]);
    }
}
