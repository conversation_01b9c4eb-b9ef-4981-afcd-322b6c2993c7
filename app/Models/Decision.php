<?php

namespace App\Models;

use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Notifications\NewApprovalRequestNotification;
use App\Notifications\NewLeaveRequestNotification;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Decision extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;

    protected $casts = [
        'status' => RequestStatus::class,
        'layer' => DecisionLayer::class,
    ];

    protected $fillable = [
        'status',
        'layer',
        'decidable_id',
        'decidable_type',
        'team_id',
        'decider_id',
        'employee_id',
    ];

    public function decidable(): MorphTo
    {
        return $this->morphTo();
    }

    public function decider(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'decider_id');
    }

    public function sendNotificationToLayerTwoManagerIfApplicable(): void
    {
        if (!$this->shouldSendLayerTwoNotification()) {
            return;
        }

        $notification = match (true) {
            $this->decidable instanceof Leave => new NewLeaveRequestNotification($this->decidable),
            $this->decidable instanceof ApprovalRequest => new NewApprovalRequestNotification(
                $this->decidable
            ),
        };

        $this->decidable->employee->departmentManager?->notify($notification);
    }

    public function shouldSendLayerTwoNotification(): bool
    {
        // To send, we need to make sure
        // 1. The decision is for the second layer
        // 2. The team has two layers of approval settings
        // 3. The employee has a department manager
        // 4. The employee's department manager is different from the employee's direct manager
        return $this->layer === DecisionLayer::Second &&
            $this->team->approval_type->isTwoLayers() &&
            $this->decidable->employee->departmentManager &&
            $this->decidable->employee->departmentManager->isNot(
                $this->decidable->employee->directManager
            );
    }
}
