<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Arr;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Shift extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'name',
        'working_hours',
        'force_checkout',
        'start_at',
        'end_at',
        'total_commited_houres',
        'is_default',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'working_hours' => 'array',
        'force_checkout' => 'datetime',
        'start_at' => 'datetime',
        'end_at' => 'datetime',
        'total_commited_houres' => 'integer',
        'is_default' => 'boolean',
    ];

    public $webhookPayload = ['id', 'name', 'working_hours'];

    protected function todayIn(): Attribute
    {
        return Attribute::get(function () {
            $today = $this->getDayShift()['from'] ?? null;

            return $today ? Carbon::parse($today) : null;
        });
    }

    protected function todayOut(): Attribute
    {
        return Attribute::get(function () {
            $today = $this->getDayShift()['to'] ?? null;

            return $today ? Carbon::parse($today) : null;
        });
    }

    public function employees(): BelongsToMany
    {
        return $this->belongsToMany(Employee::class);
    }

    public function scopeFilterByEmployees(
        Builder $query,
        string|array|null $employees = null
    ): Builder {
        return $query->when(
            $employees,
            fn(Builder $query) => $query->whereHas(
                'employees',
                fn(Builder $query) => $query->whereIn('employees.id', Arr::wrap($employees))
            )
        );
    }

    public function getDayShift($date = null)
    {
        if ($date === null) {
            $date = now();
        }
        $day = strtolower($date->format('l'));
        $day_shift = $this->working_hours['weekdays'][$day];

        if ($day_shift) {
            return $day_shift;
        } else {
            return null;
        }
    }

    public function durationOfDay(Carbon $date = null): ?CarbonInterval
    {
        $date ??= now();

        $todayShiftConfig = $this->getDayShift($date);

        if (!$todayShiftConfig) {
            return null;
        }

        $shiftFrom = Carbon::parse($todayShiftConfig['from']);
        $shiftTo = Carbon::parse($todayShiftConfig['to']);

        return CarbonInterval::seconds($shiftFrom->diffInSeconds($shiftTo));
    }

    public static function defaultWorkingHours(): array
    {
        return [
            'weekdays' => collect([
                'sunday',
                'monday',
                'tuesday',
                'wednesday',
                'thursday',
                'friday',
                'saturday',
            ])
                ->mapWithKeys(
                    fn($day) => $day !== 'friday' && $day !== 'saturday'
                        ? [
                            $day => [
                                'from' => '08:00',
                                'to' => '16:00',
                            ],
                        ]
                        : [$day => false]
                )
                ->toArray(),
            'flexible_hours' => '0',
        ];
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }
}
