<?php

namespace App\Models;

use App\Enums\ReportName;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Report extends Model
{
    protected $casts = [
        'name' => ReportName::class,
    ];

    protected $fillable = ['name'];

    public function reportTasks(): HasMany
    {
        return $this->hasMany(ReportTask::class);
    }

    public static function byName(ReportName $name): self
    {
        return self::firstWhere('name', $name);
    }
}
