<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use App\Traits\PaginateOrGet;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Tag extends Model implements AuditableContract
{
    use Auditable;
    use PaginateOrGet;
    use HasFactory;
    use BelongsToTenant;

    protected $fillable = ['name', 'color', 'team_id'];

    public function taggable(): MorphTo
    {
        return $this->morphTo();
    }
}
