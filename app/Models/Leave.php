<?php

namespace App\Models;

use App\Enums\Folder;
use App\Enums\RequestStatus;
use App\Scopes\DateFilter;
use App\Traits\BelongsToEmployee;
use App\Traits\BelongsToTenant;
use App\Traits\Decidable;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Leave extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;
    use SoftDeletes;
    use BelongsToEmployee;
    use Decidable;

    protected $fillable = [
        'team_id',
        'employee_id',
        'department_id',
        'from_date',
        'to_date',
        'reason',
        'status',
        'synced_to_attendance_at',
        'attachment',
        'rejection_reason',
    ];

    protected $with = ['employee'];

    public $webhookPayload = [
        'team_id',
        'employee_id',
        'department_id',
        'from_date',
        'to_date',
        'reason',
        'status',
    ];

    protected $casts = [
        'from_date' => 'date',
        'to_date' => 'date',
        'status' => RequestStatus::class,
    ];

    protected function attachmentUrl(): Attribute
    {
        return Attribute::get(fn() => Folder::LEAVE_ATTACHMENTS->temporaryUrl($this->attachment));
    }

    public function totalDays(): Attribute
    {
        return Attribute::get(fn() => $this->from_date->diffInDays($this->to_date) + 1);
    }

    public function scopeDate(
        Builder $query,
        string|Carbon|CarbonImmutable|CarbonPeriod $from,
        string|Carbon|CarbonImmutable|null $to = null
    ): Builder {
        return $query->tap(
            new DateFilter(from: $from, to: $to, fromColumn: 'from_date', toColumn: 'to_date')
        );
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class, 'employee_id', 'employee_id')
            ->where('date', '>=', $this->from_date->toDateString())
            ->where('date', '<=', $this->to_date->toDateString())
            ->latest();
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', RequestStatus::Pending);
    }

    public function scopeNotPending(Builder $query): Builder
    {
        return $query->where('status', '!=', RequestStatus::Pending);
    }

    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', RequestStatus::Approved);
    }
}
