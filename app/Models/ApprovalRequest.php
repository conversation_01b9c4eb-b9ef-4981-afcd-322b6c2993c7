<?php

namespace App\Models;

use App\Enums\RequestStatus;
use App\Events\ApprovalRequestCreated;
use App\Notifications\NewApprovalRequestNotification;
use App\Scopes\DateFilter;
use App\Traits\BelongsToEmployee;
use App\Traits\BelongsToTenant;
use App\Traits\Decidable;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class ApprovalRequest extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use BelongsToEmployee;
    use HasFactory;
    use SoftDeletes;
    use Decidable;

    // todo: change it in database to be PENDING
    const PENDING = 'pending';

    const APPROVED = 'APPROVED';

    const REJECTED = 'REJECTED';

    // todo: change it in database to be REGULARIZATION
    const REGULARIZATION = 'Regularization';

    // todo: change it in database to be PERMISSION
    const PERMISSION = 'Permission';

    const REMOTE_WORK = 'REMOTE_WORK';

    const CHECK_IN = 'CHECK_IN';

    const CHECK_OUT = 'CHECK_OUT';

    const CHECK_IN_OUT = 'CHECK_IN_OUT';

    protected $fillable = [
        'team_id',
        'employee_id',
        'department_id',
        'from_datetime',
        'to_datetime',
        'type',
        'reason',
        'status',
        'attendance_type',
        'rejection_reason',
    ];

    protected $with = ['employee'];

    public $webhookPayload = [
        'employee_id',
        'department_id',
        'from_datetime',
        'to_datetime',
        'type',
        'reason',
        'status',
        'attendance_type',
        'rejection_reason',
    ];

    protected $casts = [
        'from_datetime' => 'datetime',
        'to_datetime' => 'datetime',
    ];

    protected static function booted(): void
    {
        static::created(function (ApprovalRequest $approvalRequest) {
            $approvalRequest->employee->manager?->notify(
                new NewApprovalRequestNotification($approvalRequest)
            );

            event(new ApprovalRequestCreated($approvalRequest));
        });
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', self::PENDING);
    }

    public function scopePendingOrApproved(Builder $query): Builder
    {
        return $query->whereIn('status', [self::PENDING, RequestStatus::Approved->value]);
    }

    public function scopeNotPending(Builder $query): Builder
    {
        return $query->where('status', '!=', self::PENDING);
    }

    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', RequestStatus::Approved);
    }

    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('status', RequestStatus::Rejected);
    }

    public function scopeRegularization(Builder $query): Builder
    {
        return $query->where('type', self::REGULARIZATION);
    }

    public function scopePermission(Builder $query): Builder
    {
        return $query->where('type', self::PERMISSION);
    }

    public function scopeRemoteWork(Builder $query): Builder
    {
        return $query->where('type', self::REMOTE_WORK);
    }

    public function scopeDate(
        Builder $query,
        string|Carbon|CarbonImmutable|CarbonPeriod $from,
        string|Carbon|CarbonImmutable|null $to = null
    ): Builder {
        return $query->tap(
            new DateFilter(
                from: $from,
                to: $to,
                fromColumn: 'from_datetime',
                toColumn: 'to_datetime',
                exactMatch: true
            )
        );
    }

    protected function duration(): Attribute
    {
        return Attribute::get(
            fn(mixed $value, array $attributes): CarbonInterval => Carbon::parse(
                $attributes['from_datetime']
            )->diffAsCarbonInterval($attributes['to_datetime'])
        );
    }

    public static function calculatePermissionsDuration(
        Employee $employee,
        CarbonPeriod $period
    ): CarbonInterval {
        return self::query()
            ->where('employee_id', $employee->id)
            ->permission()
            ->approved()
            ->date($period)
            ->get()
            ->reduce(
                fn(CarbonInterval $interval, ApprovalRequest $approvalRequest) => $interval->add(
                    $approvalRequest->from_datetime->diffAsCarbonInterval(
                        $approvalRequest->to_datetime
                    )
                ),
                CarbonInterval::seconds(0)
            );
    }
}
