<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use App\Traits\PaginateOrGet;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Location extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;
    use PaginateOrGet;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'name',
        'lat',
        'lng',
        'radius',
        'timezone',
        'is_default',
        'automatic',
        'check_out_radius',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'lat' => 'double',
        'lng' => 'double',
        'radius' => 'double',
        'is_default' => 'boolean',
        'automatic' => 'boolean',
        'check_out_radius' => 'double',
    ];

    public $webhookPayload = ['id', 'name', 'lat', 'lng', 'radius'];

    public function checkInAttendances(): HasMany
    {
        return $this->hasMany(Attendance::class, 'check_in_location_id');
    }

    public function checkOutAttendances(): HasMany
    {
        return $this->hasMany(Attendance::class, 'check_out_location_id');
    }

    public function activities(): HasMany
    {
        return $this->hasMany(Activity::class);
    }

    public function devices(): HasMany
    {
        return $this->hasMany(Device::class);
    }

    // Converts numeric degrees to radians
    //TODO: move the helper function
    private function toRad($value)
    {
        return ($value * pi()) / 180;
    }

    private function calcDistance($lat1, $lng1, $lat2, $lng2)
    {
        $R = 6371; // radius if the earth in km
        $dLat = $this->toRad($lat2 - $lat1);
        $dLng = $this->toRad($lng2 - $lng1);
        $lat1 = $this->toRad($lat1);
        $lat2 = $this->toRad($lat2);

        $a =
            sin($dLat / 2) * sin($dLat / 2) +
            sin($dLng / 2) * sin($dLng / 2) * cos($lat1) * cos($lat2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $d = $R * $c;

        // returns the result in meters
        return $d * 1000;
    }

    public function isWithinLocation($lat, $lng, int $margin): bool
    {
        $fullRadius = $this->radius + $margin;

        $distance = $this->calcDistance($lat, $lng, $this->lat, $this->lng);

        return $distance < $fullRadius;
    }

    public function employees(): MorphToMany
    {
        return $this->morphedByMany(Employee::class, 'locationable')
            ->withPivot('id', 'permanent', 'start_date', 'end_date')
            ->withTimestamps();
    }

    public function scopeActiveLocations(Builder $query): Builder
    {
        return $query->where(function ($query) {
            $query->where('locationables.permanent', true)->orWhere(function ($q) {
                $q->whereDate('locationables.start_date', '<=', now()->format('Y-m-d'))->whereDate(
                    'locationables.end_date',
                    '>=',
                    now()->format('Y-m-d')
                );
            });
        });
    }

    public function scopeAutomatic(Builder $query): Builder
    {
        return $query->where('automatic', true);
    }

    public static function setAsDefaultLocation(Location $location): void
    {
        $defaultLocation = Location::firstWhere('is_default', true);

        if ($defaultLocation && $defaultLocation->isNot($location)) {
            $defaultLocation->update(['is_default' => false]);
        }

        $location->update(['is_default' => true]);
    }

    public static function clearCurrentDefaultLocation(): void
    {
        Location::firstWhere('is_default', true)?->update(['is_default' => false]);
    }
}
