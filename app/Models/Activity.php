<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use App\Traits\PaginateOrGet;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Activity extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;
    use PaginateOrGet;
    use MassPrunable;

    const CHECK_IN = 'CHECK_IN';

    const AUTOMATIC_CHECK_IN = 'AUTOMATIC_CHECK_IN';

    const CHECK_OUT = 'CHECK_OUT';

    const AUTOMATIC_CHECK_OUT = 'AUTOMATIC_CHECK_OUT';

    const AUTO_LOCATION_CHECK_IN = 'AUTO_LOCATION_CHECK_IN';

    const AUTO_LOCATION_CHECK_OUT = 'AUTO_LOCATION_CHECK_OUT';

    const FORCE_CHECK_OUT = 'FORCE_CHECK_OUT';

    const REMOTE_CHECK_IN = 'REMOTE_CHECK_IN';

    const REMOTE_CHECK_IN_APPROVED = 'REMOTE_CHECK_IN_APPROVED';

    const REMOTE_CHECK_IN_REJECTED = 'REMOTE_CHECK_IN_REJECTED';

    const REMOTE_CHECK_OUT = 'REMOTE_CHECK_OUT';

    const REMOTE_CHECK_OUT_APPROVED = 'REMOTE_CHECK_OUT_APPROVED';

    const REMOTE_CHECK_OUT_REJECTED = 'REMOTE_CHECK_OUT_REJECTED';

    const FREE_CHECK_OUT = 'FREE_CHECK_OUT';

    const REGULATED_CHECK_IN = 'REGULATED_CHECK_IN';

    const REGULATED_CHECK_OUT = 'REGULATED_CHECK_OUT';

    const OUT_OF_ZONE_CHECK_IN_LOG = 'OUT_OF_ZONE_CHECK_IN_LOG';

    const OUT_OF_ZONE_AUTOMATIC_CHECK_IN_LOG = 'OUT_OF_ZONE_AUTOMATIC_CHECK_IN_LOG';

    const OUT_OF_ZONE_CHECK_OUT_LOG = 'OUT_OF_ZONE_CHECK_OUT_LOG';

    const OUT_OF_ZONE_AUTOMATIC_CHECK_OUT_LOG = 'OUT_OF_ZONE_AUTOMATIC_CHECK_OUT_LOG';

    const PROOF_OF_ATTENDANCE = 'PROOF_OF_ATTENDANCE';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'employee_id',
        'device_id',
        'action',
        'lat',
        'lng',
        'payload',
        'created_at',
        'updated_at',
        'location_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'employee_id' => 'integer',
        'payload' => 'array',
    ];

    public $webhookPayload = ['id', 'action', 'lat', 'lng', 'created_at'];

    public function prunable(): Builder
    {
        return static::where('created_at', '<=', now()->subYear());
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', now());
    }

    public function getStatusAttribute()
    {
        $checkins = [
            self::CHECK_IN,
            self::REMOTE_CHECK_IN,
            self::REMOTE_CHECK_IN_APPROVED,
            self::REMOTE_CHECK_IN_REJECTED,
            self::AUTO_LOCATION_CHECK_IN,
        ];

        if (in_array($this->action, $checkins)) {
            return 'Check In';
        }

        return 'Check Out';
    }

    public static function checkInOutActivities()
    {
        return [
            Activity::CHECK_IN,
            Activity::CHECK_OUT,
            Activity::AUTO_LOCATION_CHECK_IN,
            Activity::AUTO_LOCATION_CHECK_OUT,
            Activity::FORCE_CHECK_OUT,
            Activity::REMOTE_CHECK_IN,
            Activity::REMOTE_CHECK_IN_APPROVED,
            Activity::REMOTE_CHECK_IN_REJECTED,
            Activity::REMOTE_CHECK_OUT,
            Activity::REMOTE_CHECK_OUT_APPROVED,
            Activity::REMOTE_CHECK_OUT_REJECTED,
            Activity::FREE_CHECK_OUT,
            Activity::REGULATED_CHECK_IN,
            Activity::REGULATED_CHECK_OUT,
        ];
    }
}
