<?php

namespace App\Models;

use App\Enums\SoftwareFeatureCode;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SoftwareFeature extends Model
{
    use HasFactory;
    use HasUuids;

    public $incrementing = false;

    protected $fillable = ['code', 'software_package_id', 'is_active'];

    protected $casts = [
        'code' => SoftwareFeatureCode::class,
        'is_active' => 'boolean',
    ];

    // We want to disable generate uuid because we sync uuid from Nawart Start
    public function newUniqueId(): null
    {
        return null;
    }

    public function softwarePackage(): BelongsTo
    {
        return $this->belongsTo(SoftwarePackage::class);
    }
}
