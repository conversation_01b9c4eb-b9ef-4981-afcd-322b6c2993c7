<?php

namespace App\Models;

use App\Enums\SoftwarePackageCode;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SoftwarePackage extends Model
{
    use HasFactory;
    use HasUuids;

    // We want to disable generate uuid because we sync uuid from Nawart Start
    public function newUniqueId(): null
    {
        return null;
    }

    public $incrementing = false;

    protected $fillable = ['name', 'code', 'is_active'];

    protected $casts = [
        'code' => SoftwarePackageCode::class,
        'is_active' => 'boolean',
    ];

    public function softwareFeatures(): HasMany
    {
        return $this->hasMany(SoftwareFeature::class);
    }

    public function subscriptionItems(): HasMany
    {
        return $this->hasMany(SubscriptionItem::class);
    }

    public static function findByCode(SoftwarePackageCode $code): self
    {
        return self::firstWhere('code', $code);
    }
}
