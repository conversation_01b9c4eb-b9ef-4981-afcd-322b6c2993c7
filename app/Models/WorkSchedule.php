<?php

namespace App\Models;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class WorkSchedule extends Model
{
    protected $casts = [
        'type' => WorkScheduleType::class,
        'work_and_off_days_distribution_type' => WorkAndOffDaysDistributionType::class,
    ];

    public function workdays(): BelongsToMany
    {
        return $this->belongsToMany(
            Workday::class,
            'workday_work_schedule',
            'work_schedule_id',
            'workday_id'
        );
    }
}
