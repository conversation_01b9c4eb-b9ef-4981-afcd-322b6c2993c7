<?php

namespace App\Models;

use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use App\Traits\BelongsToTenant;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class WorkSchedule extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;

    protected $fillable = [
        'team_id',
        'type',
        'name',
        'work_and_off_days_distribution_type',
        'specific_days',
        'off_days_after_each_repetition',
        'start_date',
    ];

    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'type' => WorkScheduleType::class,
        'work_and_off_days_distribution_type' => WorkAndOffDaysDistributionType::class,
        'specific_days' => 'array',
        'off_days_after_each_repetition' => 'integer',
        'start_date' => 'date',
    ];

    public $webhookPayload = ['id', 'name', 'type'];

    public function workdays(): BelongsToMany
    {
        return $this->belongsToMany(
            Workday::class,
            'workday_work_schedule',
            'work_schedule_id',
            'workday_id'
        )
            ->using(WorkdayWorkSchedule::class)
            ->withPivot(
                'id',
                'work_schedule_id',
                'workday_id',
                'work_days_number',
                'off_days_number',
                'repetitions_number'
            )
            ->withTimestamps();
    }

    public function isDateInSpecificDays(Carbon $date): bool
    {
        return in_array(strtolower($date->format('l')), $this->specific_days ?? []);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(WorkScheduleAssignment::class);
    }

    public function records(): HasMany
    {
        return $this->hasMany(WorkScheduleRecord::class);
    }

    public function scopeSearch(Builder $query, string $search = null): Builder
    {
        if (blank($search)) {
            return $query;
        }

        return $query->where('name', 'like', "%$search%");
    }
}
