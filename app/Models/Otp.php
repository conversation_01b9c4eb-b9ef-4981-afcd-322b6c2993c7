<?php

namespace App\Models;

use App\DTOs\Identifier;
use App\Enums\IdentifierType;
use App\Notifications\OneTimePasswordNotification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;
use OwenIt\Auditing\Contracts\Auditable;

class Otp extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use HasFactory;

    protected $fillable = [
        'value',
        'identifier',
        'identifier_type',
        'tenant_id',
        'employee_id',
        'tries',
        'expires_at',
        'used_at',
        'sent_at',
    ];

    protected $casts = [
        'value' => 'encrypted',
        'identifier_type' => IdentifierType::class,
        'expires_at' => 'datetime',
        'sent_at' => 'datetime',
        'used_at' => 'datetime',
    ];

    const TIMEOUT = 60 * 5; // valid for 5 minutes

    const ALLOWED_NUMBER_OF_TRIES = 10;

    public function scopeNotUsed(Builder $query): Builder
    {
        return $query->whereNull('used_at');
    }

    /**
     * This is intentionally a private method to prevent outside sending of OTPs
     */
    protected static function generate(Employee $employee, Identifier $identifier): Otp
    {
        $code = app()->isLocal() ? '1234' : str_pad(random_int(0, 9999), 4, '0', STR_PAD_LEFT);

        $otp = Otp::create([
            'value' => $code,
            'identifier' => $identifier->value(),
            'identifier_type' => $identifier->type(),
            'tenant_id' => $employee->tenant_id,
            'employee_id' => $employee->id,
            'expires_at' => now()->addSeconds(self::TIMEOUT),
        ]);

        // @codeCoverageIgnoreStart
        if (app()->isLocal()) {
            Log::info('One Time Password for "' . $identifier . '": ' . $otp->value);
        }
        // @codeCoverageIgnoreEnd

        return $otp;
    }

    public static function getOrGenerate(Employee $employee, Identifier $identifier): Otp
    {
        $otp = self::get(employee: $employee, identifier: $identifier);

        if ($otp && !$otp->isExpired()) {
            return $otp;
        }

        return self::generate(employee: $employee, identifier: $identifier);
    }

    public static function get(Employee $employee, Identifier $identifier): ?Otp
    {
        return Otp::identifier($identifier)
            ->latest()
            ->notUsed()
            ->firstWhere('employee_id', $employee->id);
    }

    public static function invalidate(Tenant $tenant, Identifier $identifier): ?bool
    {
        return $tenant
            ->otps()
            ->identifier($identifier)
            ->latest()
            ->first()
            ?->update(['used_at' => now()]);
    }

    public function validate(string $code): bool
    {
        if (!$this->isExpired() && $code === $this->value) {
            return true;
        }

        $this->update(['tries' => $this->tries + 1]);

        return false;
    }

    public function isExpired(): bool
    {
        return $this->expires_at->isPast() || $this->tries >= self::ALLOWED_NUMBER_OF_TRIES;
    }

    public function canSend(): bool
    {
        // if OTP is not sent yet, it's okay to send
        if (!$this->sent_at) {
            return true;
        }

        // if OTP is expired, it's okay to send
        if ($this->isExpired()) {
            return true;
        }

        // if OTP is sent more than 2 minutes ago, it's okay to send again
        if ($this->sent_at && $this->sent_at->diffInMinutes(now()) >= 2) {
            return true;
        }

        return false;
    }

    public function send(Identifier $identifier): void
    {
        if ($this->canSend()) {
            $this->employee->notify(
                new OneTimePasswordNotification(otp: $this, identifierType: $identifier->type())
            );

            $this->update(['sent_at' => now()]);
        }
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function scopeIdentifier(Builder $query, Identifier $identifier): Builder
    {
        return $query
            ->where('identifier', $identifier->value())
            ->where('identifier_type', $identifier->type());
    }
}
