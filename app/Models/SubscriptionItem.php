<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class SubscriptionItem extends Model implements AuditableContract
{
    use Auditable;
    use HasFactory;
    use HasUuids;
    use BelongsToTenant;

    // We want to disable generate uuid because we sync uuid from Nawart Start
    public function newUniqueId(): null
    {
        return null;
    }

    public $incrementing = false;

    protected $fillable = ['team_id', 'subscription_id', 'software_package_id'];

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function softwarePackage(): BelongsTo
    {
        return $this->belongsTo(SoftwarePackage::class);
    }
}
