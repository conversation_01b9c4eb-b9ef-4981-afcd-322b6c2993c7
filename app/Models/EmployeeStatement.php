<?php

namespace App\Models;

use App\Enums\EmployeeStatementType;
use App\Traits\BelongsToEmployee;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class EmployeeStatement extends Model implements AuditableContract
{
    use Auditable;
    use HasFactory;
    use BelongsToTenant;
    use BelongsToEmployee;

    protected $fillable = ['type', 'attendance_id', 'employee_id', 'team_id'];

    protected $casts = [
        'type' => EmployeeStatementType::class,
    ];

    public function attendance(): BelongsTo
    {
        return $this->belongsTo(Attendance::class);
    }

    public function requestable(): MorphTo
    {
        return $this->morphTo();
    }
}
