<?php

namespace App\Models;

use App\Enums\LocationSelection;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Device extends Authenticatable implements AuditableContract
{
    use SoftDeletes;
    use HasFactory;
    use Auditable;
    use BelongsToTenant;
    use HasApiTokens;

    protected $fillable = [
        'name',
        'username',
        'location_id',
        'team_id',
        'lat',
        'lng',
        'location_selection',
        'secret_key',
    ];

    protected $casts = [
        'location_selection' => LocationSelection::class,
        'secret_key' => 'hashed',
    ];

    public ?string $plainTextSecretKey = null;

    protected static function booted(): void
    {
        static::creating(static function (self $model): void {
            $model->username = Str::ulid();

            $secretKey = Str::password(60, symbols: false);

            $model->secret_key = $secretKey;
            $model->plainTextSecretKey = $secretKey;
        });
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function latLng(): array
    {
        return match ($this->location_selection) {
            LocationSelection::ExistingLocation => [
                'lat' => $this->location->lat,
                'lng' => $this->location->lng,
            ],
            LocationSelection::DeviceLocation => [
                'lat' => $this->lat,
                'lng' => $this->lng,
            ],
        };
    }

    public function scopeSearch(Builder $query, string $search): void
    {
        $query->where(
            fn(Builder $query) => $query
                ->where('name', 'like', '%' . $search . '%')
                ->orWhere('username', 'like', '%' . $search . '%')
                ->orWhereHas(
                    'location',
                    fn($query) => $query->where('name', 'like', '%' . $search . '%')
                )
        );
    }
}
