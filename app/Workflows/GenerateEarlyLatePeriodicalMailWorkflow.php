<?php

namespace App\Workflows;

use App\DTOs\EarlyLateMailData;
use App\DTOs\ReportData;
use App\Enums\ReportName;
use App\Jobs\SendEarlyLateReportMailJob;
use App\Models\Department;
use App\Models\ReportTask;
use Illuminate\Support\Collection;
use <PERSON><PERSON><PERSON><PERSON>\Venture\AbstractWorkflow;
use <PERSON>ss<PERSON><PERSON>\Venture\Graph\GroupDependency;
use <PERSON>ssnowski\Venture\WorkflowDefinition;

class GenerateEarlyLatePeriodicalMailWorkflow extends AbstractWorkflow
{
    private array $defaultReportData;

    public function __construct(protected EarlyLateMailData $earlyLateMailData)
    {
        $this->defaultReportData = [
            'start_date' => $this->earlyLateMailData->period->start,
            'end_date' => $this->earlyLateMailData->period->end,
            'sheet_mode' =>
                $this->earlyLateMailData->manager->team->early_late_config->sheetMode->value,
            'type' => 'monthly',
        ];
    }

    public function earlyLateMailData(): EarlyLateMailData
    {
        return $this->earlyLateMailData;
    }

    public function definition(): WorkflowDefinition
    {
        $excelFilesWorkflows = collect([
            ...$this->excelJobsOfDepartmentsWorkflows(),
            $this->excelJobOfDirectEmployeeWorkflow(),
        ])->filter();

        return $this->define('Generate Early Late Report Periodical Mail')
            ->each(
                collection: $excelFilesWorkflows,
                factory: fn($workflow) => $workflow,
                id: 'generate-excel-files'
            )
            ->addJob(new SendEarlyLateReportMailJob($this->earlyLateMailData), [
                GroupDependency::forGroup('generate-excel-files'),
            ]);
    }

    public function excelJobsOfDepartmentsWorkflows(): Collection
    {
        return $this->earlyLateMailData->manager->team
            ->departments()
            ->whereIn('id', $this->earlyLateMailData->departmentsIDs)
            ->chunkMap(function (Department $department) {
                $reportTask = ReportTask::createFromData(
                    reportData: new ReportData([
                        'departments_ids' => [$department->id],
                        ...$this->defaultReportData,
                    ]),
                    reportName: ReportName::EarlyLate,
                    team: $this->earlyLateMailData->manager->team
                );

                $this->earlyLateMailData->reportTasksIds->push($reportTask->id);

                return new GenerateEarlyLateReportWorkflow($reportTask);
            });
    }

    public function excelJobOfDirectEmployeeWorkflow(): GenerateEarlyLateReportWorkflow|null
    {
        $employeeIds = $this->earlyLateMailData->manager
            ->directlyManagedEmployees()
            ->pluck('id')
            ->toArray();

        if (empty($employeeIds)) {
            return null;
        }

        $reportTask = ReportTask::createFromData(
            reportData: new ReportData([
                'employees_ids' => $employeeIds,
                ...$this->defaultReportData,
            ]),
            reportName: ReportName::EarlyLate,
            team: $this->earlyLateMailData->manager->team
        );

        $this->earlyLateMailData->reportTasksIds->push($reportTask->id);

        return new GenerateEarlyLateReportWorkflow($reportTask);
    }
}
