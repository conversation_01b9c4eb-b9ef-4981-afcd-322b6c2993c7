<?php

namespace App\Workflows;

use App\Interfaces\ExcelPart;
use App\Jobs\CreateExcelPartJob;
use App\Jobs\MergeExcelPartsJob;
use App\Models\ReportTask;
use <PERSON><PERSON><PERSON><PERSON>\Venture\AbstractWorkflow;
use <PERSON><PERSON><PERSON><PERSON>\Venture\Graph\GroupDependency;
use <PERSON><PERSON><PERSON><PERSON>\Venture\WorkflowDefinition;

class GenerateEarlyLateReportWorkflow extends AbstractWorkflow
{
    public function __construct(private ReportTask $reportTask)
    {
    }

    public function definition(): WorkflowDefinition
    {
        $excelExportStrategy = $this->reportTask->getExcelExportStrategy();

        $excelPartCreator = $excelExportStrategy->creator();

        $partsJobs = $excelExportStrategy
            ->splitter()
            ->splitToParts($this->reportTask)
            ->parts()
            ->map(
                fn(ExcelPart $part, int $index) => new CreateExcelPartJob(
                    index: $index,
                    part: $part,
                    excelPartCreator: $excelPartCreator,
                    reportTask: $this->reportTask
                )
            );

        return $this->define(
            'Generate Early Late Report - ' . $this->reportTask->data->sheetMode->value
        )
            // add parts jobs
            ->each(collection: $partsJobs, factory: fn($job) => $job, id: 'parts')
            // then add merge job
            ->addJob(
                new MergeExcelPartsJob(
                    reportTask: $this->reportTask,
                    excelPartsMerger: $excelExportStrategy->merger()
                ),
                [GroupDependency::forGroup('parts')]
            );
    }
}
