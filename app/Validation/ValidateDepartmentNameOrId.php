<?php

namespace App\Validation;

use Illuminate\Contracts\Validation\Validator;
use function currentTenant;
use function request;

class ValidateDepartmentNameOrId
{
    public function __invoke(Validator $validator)
    {
        $id = request('department_id');
        $name = request('department_name');

        if (!$id && !$name) {
            return;
        }

        $department = $name
            ? currentTenant()->departments()->firstWhere('name', $name)
            : currentTenant()->departments()->firstWhere('id', $id);

        // if department does not exist and name is provided, create a new department
        if (!$department && $name) {
            $department = currentTenant()
                ->departments()
                ->create(['name' => $name]);
        }

        if ($department) {
            request()->merge(['department_id' => $department->id]);
            return;
        }

        $validator->errors()->add('department_id', 'department does not exist');
    }
}
