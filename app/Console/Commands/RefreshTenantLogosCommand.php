<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use Illuminate\Console\Command;

class RefreshTenantLogosCommand extends Command
{
    protected $signature = 'refresh:tenant-logos';

    public function handle(): void
    {
        Tenant::each(function (Tenant $tenant) {
            $this->info("Refreshing tenant logos for $tenant->name");

            // this will sync Nawart modules with refreshed logos urls
            $tenant->refreshLogos();
        });
    }
}
