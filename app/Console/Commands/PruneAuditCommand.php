<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Models\Audit;

class PruneAuditCommand extends Command
{
    protected $signature = 'audit:prune';

    protected $description = 'Command description';

    public function handle(): void
    {
        DB::transaction(function () {
            $this->info('Pruning audits...');

            $records = Audit::where('created_at', '<', now()->subMonths(3))->delete();

            $this->info("$records records pruned.");
        }, 5);
    }
}
