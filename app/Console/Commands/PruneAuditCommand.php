<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use OwenIt\Auditing\Models\Audit;

class PruneAuditCommand extends Command
{
    protected $signature = 'audit:prune';

    protected $description = 'delete old audit records';

    public function handle(): void
    {
        $this->info('Pruning audits...');

        $records = Audit::where('created_at', '<', now()->subMonths(3))->delete();

        $this->info("$records records pruned.");
    }
}
