<?php

namespace App\Console\Commands;

use App\Enums\DurationStatus;
use App\Models\Employee;
use App\Models\Location;
use App\Support\FormatLocationsForEmployees;
use Illuminate\Console\Command;

class FixEmployeesDuplicatedLocationsCommand extends Command
{
    protected $signature = 'fix:employees-duplicated-locations';

    protected $description = 'Command description';

    public function handle(): void
    {
        Employee::withWhereHas('locations')->each(function (Employee $employee) {
            $formattedCurrentLocations = $employee->locations->map(
                fn(Location $location) => [
                    'id' => $location->id,
                    'type' =>
                        $location->pivot->permanent ||
                        !$location->pivot->start_date ||
                        !$location->pivot->end_date
                            ? DurationStatus::PERMANENT->value
                            : DurationStatus::TEMPORARY->value,
                    'start_date' => $location->pivot->start_date,
                    'end_date' => $location->pivot->end_date,
                ]
            );

            $employee
                ->locations()
                ->sync(FormatLocationsForEmployees::format(locations: $formattedCurrentLocations));
        });
    }
}
