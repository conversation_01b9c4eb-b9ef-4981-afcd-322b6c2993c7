<?php

namespace App\Console\Commands;

use App\Services\PeriodicalEarlyLateReportService;
use Carbon\CarbonPeriod;
use Illuminate\Console\Command;

class EmailEarlyLateReportMonthlyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:email-early-late-report-monthly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a monthly early/late report about previous month to managers of departments where option "periodPolicy" is set to monthly or weekly_monthly in config of tenant';

    /**
     * Execute the console command.
     */
    public function handle(PeriodicalEarlyLateReportService $periodicalEarlyLateReportService): void
    {
        $periodicalEarlyLateReportService->sendMonthlyReport(
            period: CarbonPeriod::create(
                now()->subDay()->startOfMonth(),
                now()->subDay()->endOfMonth()
            )
        );
    }
}
