<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Storage;

class ClearTempFilesCommand extends Command
{
    protected $signature = 'clear:temp-files';

    public function handle(): void
    {
        $files = collect(Storage::files('temp'))
            ->filter(fn($file) => Carbon::parse(Storage::lastModified($file))->diffInHours() >= 24)
            ->toArray();

        $this->info('Clearing temporary files... ' . count($files) . ' files found');

        Storage::delete($files);

        $this->info('Temporary files are cleared');
    }
}
