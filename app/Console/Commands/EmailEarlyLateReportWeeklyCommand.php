<?php

namespace App\Console\Commands;

use App\Services\PeriodicalEarlyLateReportService;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Illuminate\Console\Command;

class EmailEarlyLateReportWeeklyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:email-early-late-report-weekly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a weekly early/late report about previous week to managers of departments where option "periodPolicy" is set to weekly or weekly_monthly in config of tenant';

    /**
     * Execute the console command.
     */
    public function handle(PeriodicalEarlyLateReportService $periodicalEarlyLateReportService): void
    {
        $periodicalEarlyLateReportService->sendWeeklyReport(
            period: CarbonPeriod::create(
                now()
                    ->subDay()
                    ->startOfWeek(CarbonInterface::SUNDAY),
                now()
                    ->subDay()
                    ->endOfWeek(CarbonInterface::SATURDAY)
            )
        );
    }
}
