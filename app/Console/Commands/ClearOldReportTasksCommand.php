<?php

namespace App\Console\Commands;

use App\Models\ReportTask;
use Illuminate\Console\Command;
use Storage;

class ClearOldReportTasksCommand extends Command
{
    protected $signature = 'clear:old-reports';

    public function handle(): void
    {
        $count = 0;

        ReportTask::query()
            ->where('created_at', '<', now()->subDays(15))
            ->each(function (ReportTask $reportTask) use (&$count) {
                $count++;

                $this->info("deleting report task id: [$reportTask->id]");

                if ($reportTask->file_path) {
                    Storage::delete($reportTask->file_path);
                }

                $reportTask->delete();
            });

        $this->info("deleted [$count] report tasks");
    }
}
