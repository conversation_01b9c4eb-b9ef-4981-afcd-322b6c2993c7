<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Team;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Illuminate\Console\Command;

class FixMissingAttendanceRecordsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-missing-attendance-records';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This is a temporary command for fixing missing days of the imported data from old Smart Attendance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $teamId = $this->ask('what is the team id?');
        $startDate = $this->ask('what is the start date?'); // 2022-9-25
        $endDate = $this->ask('what is the end date?'); // 2024-3-14

        if (!Carbon::parse($startDate)->isValid() || !Carbon::parse($endDate)->isValid()) {
            throw new \Exception('Date is not valid');
        }

        if (Carbon::parse($startDate)->gte(Carbon::parse($endDate))) {
            throw new \Exception('start date is greater than end date');
        }

        $team = Team::findOrFail($teamId);

        $this->table(
            [],
            [
                ['Team id', $team->id],
                ['Team name', $team->name],
                ['Start date', $startDate],
                ['End date', $endDate],
            ]
        );

        if (!$this->confirm('are you sure? this is very dangerous')) {
            return 0;
        }

        $employees = Employee::where('team_id', $teamId)
            ->whereHas('attendances', function ($q) use ($startDate, $endDate) {
                $q->whereDate('date', '>=', $startDate)->whereDate('date', '<=', $endDate);
            })
            ->get();

        $defaultShift = $team->shifts()->first();
        $totalAddedRows = [
            Attendance::ABSENT => 0,
            Attendance::WEEKEND => 0,
            'Affected Employees' => 0,
        ];
        foreach ($employees as $employee) {
            $isAffected = false;
            $employeeStartDate = $employee->attendances()->min('date');
            // if the start date of the employee is before the start
            if (Carbon::parse($employeeStartDate)->isBefore($startDate)) {
                $employeeStartDate = $startDate;
            }
            $period = CarbonPeriod::create($employeeStartDate, CarbonInterval::day(), $endDate);
            foreach ($period as $date) {
                if ($employee->attendances()->whereDate('date', $date)->doesntExist()) {
                    $isAffected = true;
                    $employeeID = $employee->id;
                    $shift = $employee->shift ?? $defaultShift;

                    // if date is weekday
                    if ($this->isWeekDay($date) && $shift->getDayShift($date)) {
                        $dayShift = $shift->getDayShift($date);
                        $shiftFrom = Carbon::parse($dayShift['from']);
                        $shiftTo = Carbon::parse($dayShift['to']);

                        // create absent attendance record
                        $this->info(
                            "creating absent attendance record for employee $employeeID in date $date"
                        );

                        Attendance::create([
                            'employee_id' => $employeeID,
                            'team_id' => $employee->team_id,
                            'date' => $date,
                            'status' => Attendance::ABSENT,
                            'shift_id' => $shift->id,
                            'shift_from' => $shiftFrom,
                            'shift_to' => $shiftTo,
                            'is_weekend' => false,
                            'is_holiday' => false,
                            'net_hours' => Carbon::parse('00:00'),
                            'active_until' => $shiftTo,
                            'force_checkout_time' => $shiftTo,
                            'flexible_hours' => $shift->working_hours['flexible_hours'],
                            'created_at' => $date,
                            'updated_at' => $date,
                        ]);

                        $totalAddedRows[Attendance::ABSENT]++;
                    } else {
                        // else date is weekend, create weekend attendance
                        $this->info(
                            "creating weekend attendance record for employee $employeeID in date $date"
                        );

                        Attendance::create([
                            'employee_id' => $employeeID,
                            'team_id' => $employee->team_id,
                            'date' => $date,
                            'status' => Attendance::WEEKEND,
                            'shift_id' => $shift->id,
                            'shift_from' => $date,
                            'shift_to' => $date,
                            'is_weekend' => true,
                            'is_holiday' => false,
                            'net_hours' => Carbon::parse('00:00'),
                            'active_until' => $date->addHours(23)->addMinutes(59),
                            'force_checkout_time' => $date,
                            'flexible_hours' => $shift->working_hours['flexible_hours'],
                            'created_at' => $date,
                            'updated_at' => $date,
                        ]);

                        $totalAddedRows[Attendance::WEEKEND]++;
                    }
                }
            }
            if ($isAffected) {
                $totalAddedRows['Affected Employees']++;
            }
        }

        $this->info(collect($totalAddedRows));
    }

    private function isWeekDay(Carbon $date): bool
    {
        return !($date->isFriday() || $date->isSaturday());
    }
}
