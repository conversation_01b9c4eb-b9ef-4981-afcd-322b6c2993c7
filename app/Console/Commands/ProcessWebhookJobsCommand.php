<?php

namespace App\Console\Commands;

use App\Models\WebhookJob;
use Illuminate\Console\Command;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

class ProcessWebhookJobsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhook:proccess';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process webhook jobs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        WebhookJob::canBeProcessed()
            ->with('webhook')
            ->chunk(500, function ($webhookJobs) {
                foreach ($webhookJobs as $job) {
                    $status = WebhookJob::FAILED;
                    $httpCode = null;

                    try {
                        if ($job->webhook->secret) {
                            $response = Http::withToken($job->webhook->secret)
                                ->post($job->webhook->url, (array) $job->event_payload)
                                ->throw();
                        } else {
                            $response = Http::post(
                                $job->webhook->url,
                                (array) $job->event_payload
                            )->throw();
                        }

                        $responsePayload = $response->json();
                        $httpCode = $response->status();
                        $status = WebhookJob::SUCCESS;
                    } catch (RequestException $e) {
                        report($e);
                        $responsePayload = $e->response;
                        $httpCode = $e->response?->status();
                    } catch (\Exception $e) {
                        report($e);
                        $responsePayload = $e->getMessage();
                    }

                    $job->status = $status;
                    $job->tries = $job->tries + 1;
                    $job->save();

                    $job->logs()->create([
                        'url' => $job->webhook->url,
                        'response' => $responsePayload,
                        'http_code' => $httpCode,
                    ]);
                }
            });

        return Command::SUCCESS;
    }
}
