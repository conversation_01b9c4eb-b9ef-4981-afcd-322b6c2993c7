<?php

namespace App\Console\Commands;

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use Illuminate\Console\Command;

class RegisterAbsenceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:register-absence';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Attendance::query()
            ->where('status', AttendanceStatus::YET)
            ->where('shift_to', '<', now())
            // chunking and updating individually to enable logging
            ->each(function (Attendance $attendance) {
                $this->info("Registering absence for attendance ID: $attendance->id");

                $attendance->update(['status' => AttendanceStatus::ABSENT]);
            });

        $this->info('Register absence has been executed.');

        return 0;
    }
}
