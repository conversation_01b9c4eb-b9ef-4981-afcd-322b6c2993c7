<?php

namespace App\Console\Commands;

use App\Models\Team;
use App\Notifications\TokenExpirationNotification;
use Illuminate\Console\Command;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Facades\Notification;

class TokenExpirationReminderCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:token-expiration-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To remind developers of the tenants to regenerate tokens before they expire.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Team::query()
            ->where('active', true) // check for `active` column only without subscriptions
            ->whereHas('tokens', function (Builder $query) {
                $query
                    ->where('expires_at', '>', now()) // not expired yet
                    ->where('expires_at', '<=', now()->addWeeks(2)); // will expire within two weeks
            })
            ->each(function (Team $team) {
                $developers = $team
                    ->employees()
                    ->active()
                    ->whereJsonContains('roles', 'start-developer')
                    ->take(2)
                    ->get();

                if ($developers->isEmpty()) {
                    // send the notification to start-admin if no developer in the team
                    $admin = $team
                        ->employees()
                        ->active()
                        ->whereJsonContains('roles', 'start-admin')
                        ->first();

                    $admin?->notify(new TokenExpirationNotification());
                    return;
                }

                Notification::send($developers, new TokenExpirationNotification());
            });
    }
}
