<?php

namespace App\Console\Commands;

use App\Enums\ProofStatus;
use App\Models\Proof;
use Illuminate\Console\Command;

class UpdateExpiredProofsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:expired-proofs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Proof::query()
            ->where('status', ProofStatus::Sent)
            ->where('expire_at', '<', now())
            ->update([
                'status' => ProofStatus::Missed,
            ]);

        $this->info('Expired proofs has been updated.');

        return 0;
    }
}
