<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use App\Notifications\CheckinReminderNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CheckinReminderCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:checkin-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $time = now()->format('Y-m-d H:i:00');

        Attendance::query()
            ->belongToActiveTeam()
            ->where('shift_from', '=', $time)
            ->where('status', Attendance::YET)
            ->where('status', '!=', 'WEEKEND')
            ->where('date', '>=', Carbon::today())
            ->withWhereHas('employee', function ($q) {
                $q->active()->whereDoesntHave('notifications', function ($q) {
                    $q->where('type', CheckinReminderNotification::class)->where(
                        'created_at',
                        '>=',
                        Carbon::today()
                    );
                });
            })
            ->chunk(1000, function ($attendances) {
                $this->withProgressBar($attendances, function ($attendance) {
                    $attendance->employee->notify(new CheckinReminderNotification());
                });
            });

        return 0;
    }
}
