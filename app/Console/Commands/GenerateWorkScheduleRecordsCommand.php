<?php

namespace App\Console\Commands;

use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\WorkSchedule;
use Illuminate\Console\Command;

class GenerateWorkScheduleRecordsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'work-schedule:generate-records 
                            {--schedule-id= : Generate records for a specific work schedule ID}
                            {--force : Force regeneration even if records exist}';

    /**
     * The console command description.
     */
    protected $description = 'Generate work schedule records for the next 6 months for all active work schedules';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting work schedule records generation...');

        $query = WorkSchedule::with('assignments');

        if ($scheduleId = $this->option('schedule-id')) {
            $query->where('id', $scheduleId);
        }

        $workSchedules = $query->get();

        if ($workSchedules->isEmpty()) {
            $this->warn('No work schedules found.');
            return self::SUCCESS;
        }

        $this->info("Found {$workSchedules->count()} work schedule(s) to process.");

        $bar = $this->output->createProgressBar($workSchedules->count());
        $bar->start();

        foreach ($workSchedules as $workSchedule) {
            // Only process schedules that have assignments
            if ($workSchedule->assignments->isNotEmpty()) {
                GenerateWorkScheduleRecordsJob::dispatch($workSchedule);
                $this->line(" - Dispatched job for: {$workSchedule->name}");
            } else {
                $this->line(" - Skipped (no assignments): {$workSchedule->name}");
            }
            
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Work schedule records generation jobs dispatched successfully!');

        return self::SUCCESS;
    }
}
