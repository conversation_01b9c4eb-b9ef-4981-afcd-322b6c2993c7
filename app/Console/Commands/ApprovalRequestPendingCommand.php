<?php

namespace App\Console\Commands;

use App\Enums\DelegationType;
use App\Models\Delegation;
use App\Models\Employee;
use App\Notifications\NotifyManagersOfApprovalRequests;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ApprovalRequestPendingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:notify-managers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'notify managers of pending approval requests';

    public function handle(): void
    {
        Employee::belongToActiveTeam()
            ->active()
            ->whereRelation('team', 'approval_request', true) // todo: write test for this
            ->whereHas('employeesApprovalRequests', fn($query) => $query->pending())
            ->withCount([
                'employeesApprovalRequests as remote_work_count' => fn($query) => $query
                    ->pending()
                    ->remoteWork(),
                'employeesApprovalRequests as permission_count' => fn($query) => $query
                    ->pending()
                    ->permission(),
                'employeesApprovalRequests as regularization_count' => fn($query) => $query
                    ->pending()
                    ->regularization(),
            ])
            ->with('managerDelegations')
            ->each(function (Employee $manager) {
                $notificationsCounts = $manager
                    ->managerDelegations()
                    ->with('delegated')
                    ->get()
                    ->groupBy('delegated_id')
                    ->map(
                        fn(Collection $delegations) => [
                            'notifiable' => $delegations->first()->delegated,
                            'count' => $delegations
                                ->map(
                                    fn(Delegation $delegation) => match ($delegation->type) {
                                        DelegationType::RemoteWorkRequest
                                            => $manager->remote_work_count,
                                        DelegationType::PermissionRequest
                                            => $manager->permission_count,
                                        DelegationType::RegularizationRequest
                                            => $manager->regularization_count,
                                        default => 0,
                                    }
                                )
                                ->sum(),
                        ]
                    );

                $notificationsCounts[$manager->id] = [
                    'notifiable' => $manager,
                    'count' =>
                        array_sum([
                            $manager->remote_work_count,
                            $manager->permission_count,
                            $manager->regularization_count,
                        ]) - $notificationsCounts->sum('count'),
                ];

                $notificationsCounts = $notificationsCounts->filter(
                    fn($notificationCount) => $notificationCount['count'] !== 0
                );

                // FIXME: delegated employee shouldn't receive 2 notifications when delegated by multiple manager
                foreach ($notificationsCounts as $notificationCount) {
                    $notificationCount['notifiable']->notify(
                        new NotifyManagersOfApprovalRequests($notificationCount['count'])
                    );
                }
            });
    }
}
