<?php

namespace App\Console\Commands;

use App\Models\ReportTask;
use Illuminate\Console\Command;

class MarkStaleReportTasksAsFailedCommand extends Command
{
    protected $signature = 'report-tasks:mark-stale-as-failed';

    protected $description = 'Command description';

    public function handle(): void
    {
        ReportTask::query()
            ->where('created_at', '<', now()->subHours(2))
            ->pending()
            ->each(function (ReportTask $reportTask) {
                $reportTask->markAsFailed('failed because it was not completed in 2 hour');
            });
    }
}
