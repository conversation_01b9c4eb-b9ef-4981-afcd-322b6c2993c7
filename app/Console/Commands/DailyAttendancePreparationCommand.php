<?php

namespace App\Console\Commands;

use App\Jobs\PrepareEmployeeAttendanceRecord;
use App\Models\Employee;
use Illuminate\Console\Command;

class DailyAttendancePreparationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:prepare';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'prepare attendance records for all employees';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Employee::query()
            ->belongToActiveTeam()
            ->active()
            ->withWhereHas('shift')
            ->each(fn($employee) => PrepareEmployeeAttendanceRecord::dispatch($employee));
    }
}
