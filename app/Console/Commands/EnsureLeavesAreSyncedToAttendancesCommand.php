<?php

namespace App\Console\Commands;

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Leave;
use App\Services\SyncLeaveAttendanceService;
use Illuminate\Console\Command;
use LogicException;
use Sentry;

class EnsureLeavesAreSyncedToAttendancesCommand extends Command
{
    protected $signature = 'leaves:sync-to-attendance';

    protected $description = 'Command description';

    public function handle(): void
    {
        $count = 0;

        Leave::query()
            ->approved()
            ->whereNull('synced_to_attendance_at')
            ->each(function (Leave $leave) use (&$count) {
                $attendances = Attendance::ofLeave($leave)
                    ->whereIn('status', AttendanceStatus::statusesShouldBeLeaveByTeam($leave->team))
                    ->get();

                if ($attendances->isEmpty()) {
                    Sentry::captureException(
                        new LogicException(
                            "synced_to_attendance_at is null for leave ID: $leave->id"
                        )
                    );

                    $this->info("Leave ID: $leave->id is already synced to attendances.");

                    $leave->update(['synced_to_attendance_at' => now()]);

                    return;
                }

                $count++;

                $ids = $attendances->pluck('id')->join(', ');

                // todo: temporary until leaves are stable
                Sentry::captureException(
                    new LogicException(
                        "Leave ID: $leave->id has is not synced to attendances. [$ids]"
                    )
                );

                $this->warn("Leave ID: $leave->id has is not synced to attendances. [$ids]");

                $this->info('Syncing leave to attendances...');

                (new SyncLeaveAttendanceService())->setAttendanceToLeave($leave);

                $this->info('Leave has been synced to attendances.');
            });

        $this->info("Synced $count leaves to attendances.");
    }
}
