<?php

namespace App\Console\Commands;

use App\Models\Activity;
use App\Models\Attendance;
use App\Scopes\TenantScope;
use Carbon\Carbon;
use Illuminate\Console\Command;

/**
 * to be used when needed
 */
class CalcNetHoursCommand extends Command
{
    protected $signature = 'attendance:calc-net-hours {teamId}';

    protected $description = 'Command description';

    public function handle()
    {
        $attendances = Attendance::withoutGlobalScope(TenantScope::class)
            ->where('out_type', Activity::CHECK_OUT)
            ->where('net_hours', '00:00:00')
            ->where('team_id', $this->argument('teamId'))
            ->get();

        if (count($attendances)) {
            foreach ($attendances as $attendance) {
                $check_in = Carbon::parse($attendance->check_in);
                $diff = $check_in->diffAsCarbonInterval($attendance->check_out);
                $attendance->update([
                    'net_hours' => $diff->format('%H:%i:%s'),
                ]);
            }
        }

        $this->info('Net hours has been calculated. for ' . count($attendances));

        return 0;
    }
}
