<?php

namespace App\Console\Commands;

use App\Mail\WeeklySummary;
use App\Models\Employee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SendWeeklySummaryCommand extends Command
{
    protected $signature = 'attendance:weekly-summary';

    public function handle(): void
    {
        Employee::query()
            ->belongToActiveTeam()
            ->active()
            ->whereRelation('team', 'employees_weekly_summary', true)
            ->has('shift')
            ->each(function (Employee $employee) {
                Mail::to($employee->email)
                    ->locale($employee->preferred_language)
                    ->send(new WeeklySummary($employee));
            });
    }
}
