<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Notifications\DatabaseNotification;

class PruneOldNotificationsCommand extends Command
{
    protected $signature = 'old-notifications:prune';

    protected $description = 'Command description';

    public function handle(): void
    {
        $this->info('Pruning old records...');

        $records = DatabaseNotification::where('created_at', '<', now()->subMonths(3))->delete();

        $this->info("$records records pruned.");
    }
}
