<?php

namespace App\Support;

use OpenSpout\Common\Entity\Style\Border;
use OpenSpout\Common\Entity\Style\BorderPart;
use OpenSpout\Common\Entity\Style\CellAlignment;
use OpenSpout\Common\Entity\Style\Color;
use OpenSpout\Common\Entity\Style\Style;

class ExcelStyles
{
    const ATTENDANCE_STATUS_COLOR = [
        'ABSENT' => 'D9D9D9',
        'LEAVE' => '8BBEDD',
        'FORGET_CHECKOUT' => 'F9A755',
        'OFFICIAL_HOLIDAY' => 'C6E0B4',
        'WEEKEND' => 'FDE047',
        'NOT_YET' => 'FFFFFF',
    ];

    public static function border(string $color = Color::BLACK): Border
    {
        return new Border(
            new BorderPart(Border::TOP, $color, Border::WIDTH_THIN, Border::STYLE_SOLID),
            new BorderPart(Border::BOTTOM, $color, Border::WIDTH_THIN, Border::STYLE_SOLID),
            new BorderPart(Border::LEFT, $color, Border::WIDTH_THIN, Border::STYLE_SOLID),
            new BorderPart(Border::RIGHT, $color, Border::WIDTH_THIN, Border::STYLE_SOLID)
        );
    }

    public static function headerStyle(string $backgroundColor = '00B062'): Style
    {
        return self::center()
            ->setFontSize(10)
            ->setFontBold()
            ->setBackgroundColor($backgroundColor)
            ->setBorder(self::border());
    }

    public static function blackBorderStyle(): Style
    {
        return self::center()->setFontSize(12)->setBorder(self::border());
    }

    public static function center(): Style
    {
        return (new Style())
            ->setCellAlignment(CellAlignment::CENTER)
            ->setCellVerticalAlignment(CellAlignment::CENTER);
    }

    public static function label(): Style
    {
        return ExcelStyles::center()->setFontSize(10)->setFontBold();
    }
}
