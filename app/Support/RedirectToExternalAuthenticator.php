<?php

namespace App\Support;

use App\Interfaces\Authenticators\RedirectAuthenticatorInterface;
use Exception;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

class RedirectToExternalAuthenticator
{
    public static function handle(): Response
    {
        $tenant = loginSession()->resolveTenant();

        if ($tenant->hasInternalAuthenticator()) {
            throw new Exception(
                'Internal authenticator should not be handled by RedirectToExternalAuthenticator'
            );
        }

        $authenticator = $tenant->authenticator();

        // since we may not have an employee,
        // and we don't control the identifier in external authenticators
        // we will use the raw identifier
        $identifier = loginSession()->rawIdentifier();

        if ($authenticator instanceof RedirectAuthenticatorInterface) {
            $authenticator->configure($tenant->authenticatorConfig);
            $authenticator->authenticate($identifier, $tenant);

            return Inertia::location($authenticator->getRedirectTo());
        }

        // if the authenticator is not configured correctly
        // we will fall back to the OTP authenticator
        return RedirectToOtpAuthenticator::handle(force: true);
    }
}
