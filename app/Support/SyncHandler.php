<?php

namespace App\Support;

use App\Sync\DeleteDepartment;
use App\Sync\DeleteSoftware;
use App\Sync\DeleteSubscription;
use App\Sync\DeleteSubscriptionItem;
use App\Sync\DeleteTag;
use App\Sync\UpdateOrCreateDepartment;
use App\Sync\UpdateOrCreateEmployee;
use App\Sync\UpdateOrCreateSoftware;
use App\Sync\UpdateOrCreateSubscription;
use App\Sync\UpdateOrCreateSubscriptionItem;
use App\Sync\UpdateOrCreateTag;
use App\Sync\UpdateOrCreateTenant;
use DB;
use Exception;
use Illuminate\Support\Collection;
use Prwnr\Streamer\Contracts\MessageReceiver;
use Prwnr\Streamer\EventDispatcher\ReceivedMessage;
use Str;

class SyncHandler implements MessageReceiver
{
    public static array $syncJobMapper = [
        // Employee
        'tenant.employee.create' => UpdateOrCreateEmployee::class,
        'tenant.employee.update' => UpdateOrCreateEmployee::class,

        // Department
        'tenant.department.create' => UpdateOrCreateDepartment::class,
        'tenant.department.update' => UpdateOrCreateDepartment::class,
        'tenant.department.delete' => DeleteDepartment::class,

        // Tenant
        'tenant.create' => UpdateOrCreateTenant::class,
        'tenant.update' => UpdateOrCreateTenant::class,

        // Subscription
        'tenant.subscription.create' => UpdateOrCreateSubscription::class,
        'tenant.subscription.update' => UpdateOrCreateSubscription::class,
        'tenant.subscription.delete' => DeleteSubscription::class,

        // Subscription Item
        'tenant.subscription-item.create' => UpdateOrCreateSubscriptionItem::class,
        'tenant.subscription-item.update' => UpdateOrCreateSubscriptionItem::class,
        'tenant.subscription-item.delete' => DeleteSubscriptionItem::class,

        // Software
        'tenant.software.create' => UpdateOrCreateSoftware::class,
        'tenant.software.update' => UpdateOrCreateSoftware::class,
        'tenant.software.delete' => DeleteSoftware::class,

        // Tag
        'tenant.tag.create' => UpdateOrCreateTag::class,
        'tenant.tag.update' => UpdateOrCreateTag::class,
        'tenant.tag.delete' => DeleteTag::class,
    ];

    public function handle(ReceivedMessage $message): void
    {
        info('=================');

        $data = $message->getData();
        $eventName = $message->getEventName();

        info('Id: ' . $data['id']);
        info('Event Name: ' . $eventName);
        info('Event id: ' . $message->getId());
        info('Time: ' . now()->toDateTimeString());

        $syncJob = self::$syncJobMapper[$eventName] ?? null;

        if (!$syncJob) {
            throw new Exception("could not find job with the routing [$eventName] key to sync it.");
        }

        info('Syncing...');
        DB::transaction(fn() => (new $syncJob())->handle($data), 3);
        info('Synced');

        info('=================');
    }

    public static function events(): Collection
    {
        return collect(array_keys(self::$syncJobMapper));
    }

    public static function mapEventsToListeners(): array
    {
        return self::events()->mapWithKeys(fn($event) => [$event => [self::class]])->toArray();
    }

    public static function triggerMessage(string $name, array $data): void
    {
        $id = Str::uuid();

        (new self())->handle(
            new ReceivedMessage($id, [
                '_id' => $id,
                'name' => $name,
                'data' => json_encode($data, JSON_THROW_ON_ERROR),
            ])
        );
    }
}
