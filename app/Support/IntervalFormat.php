<?php

namespace App\Support;

use Carbon\CarbonInterval;

class IntervalFormat
{
    public static function toHoursMinutes(CarbonInterval $interval): string
    {
        $hours = (int) $interval->totalHours;
        $minutes = (int) ($interval->totalMinutes - $hours * 60);

        $hours = str_pad($hours, 2, '0', STR_PAD_LEFT);

        $minutes = str_pad($minutes, 2, '0', STR_PAD_LEFT);

        return "$hours:$minutes";
    }
}
