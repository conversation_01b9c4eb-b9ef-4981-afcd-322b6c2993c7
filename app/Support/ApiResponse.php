<?php

namespace App\Support;

use Exception;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Symfony\Component\HttpFoundation\Response;

class ApiResponse implements Responsable
{
    public function __construct(
        protected mixed $data = [],
        protected ?string $status = null,
        protected ?string $message = null,
        protected int $code = Response::HTTP_OK,
        protected array $headers = []
    ) {
    }

    public function toResponse($request): JsonResponse
    {
        if (
            $this->code !== Response::HTTP_OK &&
            !$this->message &&
            !isset($this->data['message']) &&
            app()->isLocal()
        ) {
            throw new Exception(
                'Response message is required when the response code is not 200 for: ' .
                    '[' .
                    $request->url() .
                    ']'
            );
        }

        // when the response is a collection, we need to get the data from the collection
        // because unless we do, the pagination data will not be included in the response
        if (
            $this->data instanceof ResourceCollection &&
            $this->data->resource instanceof LengthAwarePaginator
        ) {
            $this->data = $this->data->response()->getData(true);
        }

        return response()->json(
            data: [
                'data' => $this->data,
                'status' => $this->status,
                'message' => $this->message,
            ],
            status: $this->code,
            headers: $this->headers
        );
    }
}
