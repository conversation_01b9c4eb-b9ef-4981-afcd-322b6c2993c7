<?php

namespace App\Support;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Symfony\Component\HttpFoundation\Response;

class ApiResponse implements Responsable
{
    public function __construct(
        protected mixed $data = [],
        protected ?string $status = null,
        protected ?string $message = null,
        protected int $code = Response::HTTP_OK,
        protected array $headers = [],
        // todo: this property exists to support the old response format in auth controller, which will be replaced with SSO
        protected bool $spreadResponseContent = false
    ) {
    }

    public function code(): int
    {
        return $this->code;
    }

    public function toResponse($request): JsonResponse
    {
        if ($this->data instanceof ResourceCollection) {
            // when the response is a `resource collection`, we need to get the `data` from the collection
            // so we can insert metadata (links, current_page, ...) and additional data inside `data`
            // so response will be like this:
            // {
            //     "data": [
            //         "data": [{ ... }, { ... }],
            //         "meta": { ... },
            //         "my_custom_additional_data": { ... },
            //],
            //     "status": "success",
            //     "message": "Data retrieved successfully",
            // }

            $isPagination = $this->data->resource instanceof LengthAwarePaginator;
            if ($isPagination || $this->hasAdditional()) {
                $this->data = $this->data->response()->getData(true);
            }
        }

        if ($this->hasAdditional()) {
            // when the response has additional data, we need to merge it with the response data
            // so response will be like this:
            // {
            //     "data": [
            //         { ... }
            //         "my_custom_additional_data": { ... },
            //     ],
            //     "status": "success",
            //     "message": "Data retrieved successfully",
            // }
            $this->data = [
                ...$this->data->response()->getData(true)['data'],
                ...$this->data->additional,
            ];
        }

        // if it's not a resource collection
        // response will be like this:
        // {
        //     "data": { ... },
        //     "status": "success",
        //     "message": "Data retrieved successfully",
        // }

        return response()->json(
            data: [
                ...$this->spreadResponseContent ? $this->data : ['data' => $this->data],
                'status' => $this->status,
                'message' => $this->message,
            ],
            status: $this->code,
            headers: $this->headers
        );
    }

    public function hasAdditional(): bool
    {
        return $this->data instanceof JsonResource && count($this->data->additional) > 0;
    }
}
