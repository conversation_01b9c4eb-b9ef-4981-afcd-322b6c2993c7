<?php

namespace App\Support;

use App\Authenticators\OneTimePasswordAuthenticator;
use Exception;
use Symfony\Component\HttpFoundation\Response;

class RedirectToOtpAuthenticator
{
    public static function handle(bool $force = false): Response
    {
        if (!($tenant = loginSession()->resolveTenant())) {
            throw new Exception('Tenant should be resolved before handling OTP');
        }

        if ($tenant->hasExternalAuthenticator() && !$force) {
            throw new Exception(
                'External authenticator should not be handled by RedirectToOtpAuthenticator'
            );
        }

        (new OneTimePasswordAuthenticator())->sendOtp($tenant, loginSession()->resolveIdentifier());

        return redirect()->route('otp.create');
    }
}
