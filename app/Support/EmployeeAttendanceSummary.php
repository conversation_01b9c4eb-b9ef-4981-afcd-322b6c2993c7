<?php

namespace App\Support;

use App\Calculations\EarlyLateCalculator;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

readonly class EmployeeAttendanceSummary
{
    public function __construct(
        private Employee $employee,
        private Carbon $startDate,
        private Carbon $endDate
    ) {
    }

    public function attendanceQuery()
    {
        return $this->employee
            ->attendances()
            ->whereBetween('date', [$this->startDate, $this->endDate]);
    }

    public function checkInOutSummary(): array
    {
        $attendancesSummary = $this->attendanceQuery()
            ->select([
                DB::raw('count(id) as total_checkins'),
                DB::raw('SUM(TIME_TO_SEC( `net_hours` )) as total_seconds'),
                DB::raw('SEC_TO_TIME(AVG(TIME_TO_SEC(TIME(check_in)))) AS avg_checkin'),
                DB::raw('SEC_TO_TIME(AVG(TIME_TO_SEC(TIME(check_out)))) AS avg_checkout'),
            ])
            ->present()
            ->first();

        return $attendancesSummary
            ? [
                'total_checkins' => $attendancesSummary->total_checkins,
                'total_hours' => $attendancesSummary->total_seconds / 3600,
                'average_check_in' => $attendancesSummary->avg_checkin,
                'average_check_out' => $attendancesSummary->avg_checkout,
            ]
            : [
                'total_checkins' => 0,
                'total_hours' => 0,
                'average_check_in' => '00:00:00',
                'average_check_out' => '00:00:00',
            ];
    }

    public function getAvgCheckInMood(): int
    {
        $query = $this->attendanceQuery()
            ->select('in_mood')
            ->groupBy('in_mood')
            ->orderByRaw('COUNT(in_mood) DESC')
            ->present()
            ->whereNotNull('in_mood')
            ->first();

        return $query ? $query->in_mood : 0;
    }

    public function getAvgCheckOutMood(): int
    {
        $query = $this->attendanceQuery()
            ->select('out_mood')
            ->groupBy('out_mood')
            ->orderByRaw('COUNT(out_mood) DESC')
            ->present()
            ->whereNotNull('out_mood')
            ->first();

        return $query ? $query->out_mood : 0;
    }

    public function committedHours(): array
    {
        if (!$this->employee->shift) {
            return [
                'total_committed_hours' => 0,
                'total_working_days' => 0,
            ];
        }

        $workingHours = $this->employee->shift->working_hours;
        $committedHours = 0;
        $totalWorkingDays = 0;

        foreach ($workingHours['weekdays'] as $workingHour) {
            if (!$workingHour) {
                continue;
            }
            $from = Carbon::createFromFormat('H:i', $workingHour['from']);
            $to = Carbon::createFromFormat('H:i', $workingHour['to']);
            $committedHours += $from->diffInHours($to);
            $totalWorkingDays++;
        }

        $daysOfMonth = $this->startDate->daysInMonth; // FIXME: not logical

        switch ($daysOfMonth) {
            case 31:
                $totalCommittedHours = $committedHours * 4.4;
                $totalWorkingDays = $totalWorkingDays * 4.4;
                break;
            case 30:
                $totalCommittedHours = $committedHours * 4.2;
                $totalWorkingDays = $totalWorkingDays * 4.2;
                break;
            default:
                $totalCommittedHours = $committedHours * 4;
                $totalWorkingDays = $totalWorkingDays * 4;
                break;
        }

        return [
            'total_committed_hours' => round($totalCommittedHours, 2),
            'total_working_days' => $totalWorkingDays,
        ];
    }

    public function approvalRequests(): array
    {
        $requestsOfMonth = $this->employee
            ->approvalRequests()
            ->select('id', 'employee_id', 'status')
            ->whereBetween('created_at', [$this->startDate, $this->endDate])
            ->get();

        return [
            'total_requests' => $requestsOfMonth->count(),
            'total_approved_requests' => $requestsOfMonth
                ->where('status', ApprovalRequest::APPROVED)
                ->count(),
            'total_rejected_requests' => $requestsOfMonth
                ->where('status', ApprovalRequest::REJECTED)
                ->count(),
        ];
    }

    public function attendance()
    {
        return $this->attendanceQuery()->get();
    }

    public function totalAbsentDays(): int
    {
        return $this->attendanceQuery()->absent()->count();
    }

    public function totalPresentDays(): int
    {
        return $this->attendanceQuery()->present()->count();
    }

    public function calculateEarlyIn(): string
    {
        return (new EarlyLateCalculator())
            ->calculateEarlyIn($this->attendanceQuery()->get())
            ->format('%H:%I:%S');
    }

    public function calculateLateIn(): string
    {
        return (new EarlyLateCalculator())
            ->calculateLateIn($this->attendanceQuery()->get())
            ->format('%H:%I:%S');
    }

    public function formatForResponse(): array
    {
        $checkInOutSummary = $this->checkInOutSummary();
        $committedHours = $this->committedHours();
        $approvedRejectedRequests = $this->approvalRequests();

        return [
            'total_checkins' => $checkInOutSummary['total_checkins'],
            'total_hours' => $checkInOutSummary['total_hours'],
            'average_check_in_time' => Carbon::parse(
                $checkInOutSummary['average_check_in']
            )->format('h:i a'),
            'average_check_out_time' => Carbon::parse(
                $checkInOutSummary['average_check_out']
            )->format('h:i a'),
            'average_check_in_mood' => $this->getAvgCheckInMood(),
            'average_check_out_mood' => $this->getAvgCheckOutMood(),
            'total_committed_hours' => $committedHours['total_committed_hours'],
            'total_working_days' => $committedHours['total_working_days'],
            'total_requests' => $approvedRejectedRequests['total_requests'],
            'total_approved_requests' => $approvedRejectedRequests['total_approved_requests'],
            'total_rejected_requests' => $approvedRejectedRequests['total_rejected_requests'],
        ];
    }

    public static function get(Employee $employee, Carbon $startDate, Carbon $endDate): self
    {
        return new self($employee, $startDate, $endDate);
    }
}
