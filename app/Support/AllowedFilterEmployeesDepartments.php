<?php

namespace App\Support;

use Spatie\QueryBuilder\AllowedFilter;

class AllowedFilterEmployeesDepartments
{
    public static function filters(): array
    {
        return [
            AllowedFilter::callback('employees', fn($q, $v) => $q->whereIn('employee_id', $v)),
            AllowedFilter::callback(
                'departments',
                fn($q, $v) => $q->whereHas(
                    'employee.department',
                    fn($q) => $q->whereIn('departments.id', $v)
                )
            ),
        ];
    }
}
