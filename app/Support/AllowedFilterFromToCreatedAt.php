<?php

namespace App\Support;

use Carbon\Carbon;
use Spatie\QueryBuilder\AllowedFilter;

// until we find another name
class AllowedFilterFromToCreatedAt
{
    public static function filters(): array
    {
        return [
            AllowedFilter::callback(
                'from',
                fn($q, $v) => $q->where('created_at', '>=', Carbon::parse($v)->startOfDay())
            ),
            AllowedFilter::callback(
                'to',
                fn($q, $v) => $q->where('created_at', '<=', Carbon::parse($v)->endOfDay())
            ),
        ];
    }
}
