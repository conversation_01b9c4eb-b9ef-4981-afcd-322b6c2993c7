<?php

namespace App\Support;

use App\DTOs\Identifier;
use App\Enums\IdentifierType;
use App\Models\Employee;
use App\Models\Otp;
use App\Models\Tenant;
use Exception;
use Illuminate\Support\Collection;

class LoginSession
{
    const IDENTIFIER = 'identifier';
    const SELECTED_TENANT_ID = 'selected_tenant_id';
    const SELECTED_IDENTIFIER_TYPE = 'selected_identifier_type';

    public function start(string $identifier): void
    {
        $this->clear();

        session([self::IDENTIFIER => $identifier]);
    }

    public function clear(): void
    {
        session()->forget([
            self::IDENTIFIER,
            self::SELECTED_TENANT_ID,
            self::SELECTED_IDENTIFIER_TYPE,
        ]);
    }

    public function resolveTenant(): ?Tenant
    {
        // 1. Tenant selected by user
        if ($tenantId = session(self::SELECTED_TENANT_ID)) {
            return Tenant::findOrFail($tenantId);
        }

        // 2. Tenant got from identifier if only one tenant is found
        if ($this->employeesOfIdentifier()->containsOneItem()) {
            return $this->employeesOfIdentifier()->first()->tenant;
        }

        // 3. Tenant got from domain
        if ($this->domain()) {
            return Tenant::query()
                ->with('authenticatorConfig')
                ->active()
                ->firstWhere('domain', $this->domain());
        }

        return null;
    }

    public function resolveIdentifier(): Identifier
    {
        $employee = $this->employee();

        if (!$employee) {
            throw new Exception('Employee must be set in the session to resolve identifier.');
        }

        $valueFromEmployee = match ($this->identifierType()) {
            IdentifierType::Email => $employee->email,
            IdentifierType::Phone => $employee->phone ?? $employee->email,
        };

        return new Identifier($valueFromEmployee);
    }

    public function domain(): ?string
    {
        return $this->rawIdentifier()?->domain();
    }

    public function employee(): ?Employee
    {
        $rawIdentifier = $this->rawIdentifier();

        if (!$rawIdentifier || !$this->resolveTenant()) {
            throw new Exception(
                'Tenant and identifier must be set in the session to get employee.'
            );
        }

        return $this->resolveTenant()->employees()->identifier($rawIdentifier)->first();
    }

    public function resolveOtp(): ?Otp
    {
        if (!($employee = $this->employee())) {
            throw new Exception('Employee must be set in the session to get OTP.');
        }

        $identifier = $this->resolveIdentifier();

        if ($identifier->isTestIdentifier()) {
            return (new Otp())->forceFill([
                'value' => '1234',
                'identifier' => $identifier->value(),
                'identifier_type' => $identifier->type(),
                'tenant_id' => $employee->tenant_id,
                'employee_id' => $employee->id,
                'expires_at' => now()->addMinutes(5),
                'sent_at' => now(),
                'created_at' => now(),
            ]);
        }

        return Otp::get(employee: $employee, identifier: $identifier);
    }

    public function setIdentifierType(?IdentifierType $identifierType = null): void
    {
        if (!$identifierType) {
            session()->forget(self::SELECTED_IDENTIFIER_TYPE);
            return;
        }

        $identifierType = $this->resolveTenant()?->allow_sms
            ? $identifierType
            : IdentifierType::Email; // force email when sms is not allowed

        session([self::SELECTED_IDENTIFIER_TYPE => $identifierType->value]);
    }

    public function setTenant(Tenant $tenant): void
    {
        session([self::SELECTED_TENANT_ID => $tenant->id]);
    }

    public function employeesOfIdentifier(bool $activeOnly = true): Collection
    {
        $query = Employee::query()
            ->whereRelation('tenant', 'is_active', true)
            ->with('tenant')
            ->identifier($this->rawIdentifier());

        $employees = $activeOnly ? $query->active()->get() : $query->get();

        return $employees->unique('tenant_id');
    }

    public function hasMultipleTenants(): bool
    {
        return $this->employeesOfIdentifier()->count() > 1;
    }

    public function allEmployeesInactive(): bool
    {
        $employees = $this->employeesOfIdentifier(activeOnly: false);

        return $employees->isNotEmpty() &&
            $employees->every(fn($employee) => !$employee->is_active);
    }

    protected function identifierType(): IdentifierType
    {
        if ($this->resolveTenant()?->allow_sms) {
            // when a tenant allows sms, we will send to phone unless the user selects email
            return IdentifierType::from(
                session(self::SELECTED_IDENTIFIER_TYPE) ?? IdentifierType::Phone->value
            );
        }

        return IdentifierType::Email;
    }

    public function rawIdentifier(): ?Identifier
    {
        return session(self::IDENTIFIER) ? new Identifier(session(self::IDENTIFIER)) : null;
    }
}
