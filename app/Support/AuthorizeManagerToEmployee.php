<?php

namespace App\Support;

use App\Models\Employee;

class AuthorizeManagerToEmployee
{
    public static function authorize(Employee $employee, Employee $manager = null): void
    {
        $manager ??= auth()->user();

        abort_if(
            $manager
                ->managedEmployees()
                ->active()
                ->where('id', $employee->id)
                ->doesntExist(),
            403
        );

        // we may add delegates check here later...
    }
}
