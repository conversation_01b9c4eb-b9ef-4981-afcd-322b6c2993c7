<?php

namespace App\Support;

use App\Enums\DurationStatus;
use Illuminate\Support\Collection;

class FormatLocationsForEmployees
{
    public static function format(Collection $locations): Collection
    {
        return $locations
            ->map(function (array $location) {
                $isPermanent = $location['type'] === DurationStatus::PERMANENT->value;

                return [
                    'location_id' => $location['id'],
                    'permanent' => $isPermanent,
                    'start_date' => $isPermanent ? null : $location['start_date'],
                    'end_date' => $isPermanent ? null : $location['end_date'],
                ];
            })
            ->unique(
                fn($location) => $location['location_id'] .
                    $location['permanent'] .
                    ($location['start_date'] ?? '') .
                    ($location['end_date'] ?? '')
            );
    }
}
