<?php

namespace App\Services;

use App\Enums\RequestStatus;
use App\Events\ApprovalRequestRejected;
use App\Jobs\DeleteNotificationJob;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Notifications\ApprovalRequestRejectedNotification;
use App\Notifications\NewApprovalRequestNotification;
use DB;

class RejectApprovalRequestService
{
    public function __construct(
        private ApprovalRequest $approvalRequest,
        private string $rejectionReason,
        private Employee $decider
    ) {
    }

    public function handle(): void
    {
        DB::transaction(function () {
            $currentLayer = $this->approvalRequest->currentDecisionLayer();

            $this->approvalRequest->ensureEmployeeCanDecideOnLayer($this->decider, $currentLayer);

            $this->approvalRequest->updateOrCreateDecision(
                status: RequestStatus::Rejected,
                decisionLayer: $currentLayer,
                decider: $this->decider
            );

            $this->approvalRequest->update([
                'status' => ApprovalRequest::REJECTED,
                'rejection_reason' => $this->rejectionReason,
            ]);

            $this->approvalRequest->employee->notify(
                new ApprovalRequestRejectedNotification($this->approvalRequest)
            );

            event(new ApprovalRequestRejected($this->approvalRequest));

            DeleteNotificationJob::dispatch(
                notificationClass: NewApprovalRequestNotification::class,
                payloadId: $this->approvalRequest->id
            );
        }, 5);
    }
}
