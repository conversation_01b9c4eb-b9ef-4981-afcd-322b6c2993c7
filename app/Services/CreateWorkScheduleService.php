<?php

namespace App\Services;

use App\Models\WorkSchedule;
use DB;

class CreateWorkScheduleService
{
    public static function handle(array $data): WorkSchedule
    {
        return DB::transaction(function () use ($data) {
            // Extract workdays data
            $workdaysData = $data['workdays'];
            unset($data['workdays']);

            // Create the work schedule
            $workSchedule = WorkSchedule::create($data);

            // Attach workdays with pivot data
            foreach ($workdaysData as $workdayData) {
                $workSchedule->workdays()->attach($workdayData['workday_id'], [
                    'work_days_number' => $workdayData['work_days_number'] ?? null,
                    'off_days_number' => $workdayData['off_days_number'] ?? null,
                    'repetitions_number' => $workdayData['repetitions_number'] ?? null,
                ]);
            }

            return $workSchedule;
        });
    }
}
