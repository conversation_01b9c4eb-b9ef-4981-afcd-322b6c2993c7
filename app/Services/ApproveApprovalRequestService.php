<?php

namespace App\Services;

use App\Enums\RequestStatus;
use App\Events\ApprovalRequestApproved;
use App\Jobs\DeleteNotificationJob;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Employee;
use App\Notifications\ApprovalRequestApprovedNotification;
use App\Notifications\NewApprovalRequestNotification;
use DB;
use Illuminate\Support\Carbon;
use LogicException;

class ApproveApprovalRequestService
{
    public function __construct(private ApprovalRequest $approvalRequest, private Employee $decider)
    {
    }

    public function handle(): void
    {
        DB::transaction(function () {
            $currentLayer = $this->approvalRequest->currentDecisionLayer();

            $this->approvalRequest->ensureEmployeeCanDecideOnLayer($this->decider, $currentLayer);

            $decision = $this->approvalRequest->updateOrCreateDecision(
                status: RequestStatus::Approved,
                decisionLayer: $currentLayer,
                decider: $this->decider
            );

            $decision->sendNotificationToLayerTwoManagerIfApplicable();

            if ($this->approvalRequest->isFullyApproved()) {
                $this->finalizeApproval();
            }
        }, 5);
    }

    protected function finalizeApproval(): void
    {
        DeleteNotificationJob::dispatch(
            notificationClass: NewApprovalRequestNotification::class,
            payloadId: $this->approvalRequest->id
        );

        $this->approvalRequest->update(['status' => ApprovalRequest::APPROVED]);

        if ($this->approvalRequest->type === ApprovalRequest::PERMISSION) {
            return;
        }

        $this->updateAttendance();

        $this->approvalRequest->employee->notify(
            new ApprovalRequestApprovedNotification($this->approvalRequest)
        );

        event(new ApprovalRequestApproved($this->approvalRequest));
    }

    protected function updateAttendance(): void
    {
        $startDate = $this->approvalRequest->from_datetime;
        $endDate = $this->approvalRequest->to_datetime;

        $attendance = $this->approvalRequest->employee->attendances()->date($startDate)->first();

        if (!$attendance) {
            report(
                new LogicException(
                    "approving an approval request [{$this->approvalRequest->id}] without attendance record, date: [{$startDate->toDateString()}]"
                )
            );

            return;
        }

        $attendance->update([
            'status' => Attendance::PRESENT,
            'net_hours' => $startDate->diffAsCarbonInterval($endDate)->format('%H:%i:%s'),
            ...$this->attendanceTypeInputsToUpdate($startDate, $endDate),
            ...$this->typeInputsToUpdate(),
        ]);
    }

    protected function attendanceTypeInputsToUpdate(Carbon $startDate, Carbon $endDate): array
    {
        return match ($this->approvalRequest->attendance_type) {
            ApprovalRequest::CHECK_IN_OUT => ['check_in' => $startDate, 'check_out' => $endDate],
            ApprovalRequest::CHECK_IN => ['check_in' => $startDate, 'on_duty' => true],
            ApprovalRequest::CHECK_OUT => ['check_out' => $endDate],
            default => [],
        };
    }

    protected function typeInputsToUpdate(): array
    {
        return match ($this->approvalRequest->type) {
            ApprovalRequest::REGULARIZATION => [
                'in_type' => Activity::REGULATED_CHECK_IN,
                'out_type' => Activity::REGULATED_CHECK_OUT,
                'is_adjusted' => true,
            ],
            ApprovalRequest::REMOTE_WORK => [
                'in_type' => Activity::REMOTE_CHECK_IN,
                'out_type' => Activity::REMOTE_CHECK_OUT,
            ],
            default => [],
        };
    }
}
