<?php

namespace App\Services;

use App\Enums\RequestStatus;
use App\Jobs\DeleteNotificationJob;
use App\Models\Employee;
use App\Models\Leave;
use App\Notifications\LeaveRejectedNotification;
use App\Notifications\NewLeaveRequestNotification;
use DB;

class RejectLeaveService
{
    public function __construct(
        private Leave $leave,
        private string $rejectionReason,
        private Employee $decider
    ) {
    }

    public function handle(): void
    {
        DB::transaction(function () {
            $currentLayer = $this->leave->currentDecisionLayer();

            $this->leave->ensureEmployeeCanDecideOnLayer($this->decider, $currentLayer);

            $this->leave->updateOrCreateDecision(
                status: RequestStatus::Rejected,
                decisionLayer: $currentLayer,
                decider: $this->decider
            );

            $this->leave->update([
                'status' => RequestStatus::Rejected,
                'rejection_reason' => $this->rejectionReason,
            ]);

            $this->leave->employee->notify(new LeaveRejectedNotification($this->leave));

            DeleteNotificationJob::dispatch(
                notificationClass: NewLeaveRequestNotification::class,
                payloadId: $this->leave->id
            );
        }, 5);
    }
}
