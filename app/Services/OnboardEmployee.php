<?php

namespace App\Services;

use App\Jobs\PrepareEmployeeAttendanceRecord;
use App\Models\Employee;
use App\Notifications\WelcomeNotification;

class OnboardEmployee
{
    public function handle(Employee $employee, bool $sendWelcomeNotification = false): Employee
    {
        $this->attachDefaultShiftLocation($employee);

        if ($sendWelcomeNotification) {
            $employee->notify(new WelcomeNotification());
        }

        PrepareEmployeeAttendanceRecord::dispatch($employee);

        $employee->update(['is_ready' => true]);

        return $employee;
    }

    public function attachDefaultShiftLocation(Employee $employee): Employee
    {
        if ($employee->shifts()->doesntExist()) {
            $employee->shifts()->attach($employee->team->firstOrCreateDefaultShift());
        }

        if ($employee->team->defaultLocation && $employee->locations()->doesntExist()) {
            $employee
                ->locations()
                ->attach($employee->team->defaultLocation->id, ['permanent' => true]);
        }

        return $employee;
    }
}
