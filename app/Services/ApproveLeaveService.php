<?php

namespace App\Services;

use App\Enums\RequestStatus;
use App\Jobs\DeleteNotificationJob;
use App\Models\Employee;
use App\Models\Leave;
use App\Notifications\NewLeaveRequestNotification;
use DB;

class ApproveLeaveService
{
    public function __construct(private Leave $leave, private Employee $decider)
    {
    }

    public function handle(): void
    {
        DB::transaction(function () {
            $currentLayer = $this->leave->currentDecisionLayer();

            $this->leave->ensureEmployeeCanDecideOnLayer($this->decider, $currentLayer);

            $decision = $this->leave->updateOrCreateDecision(
                status: RequestStatus::Approved,
                decisionLayer: $currentLayer,
                decider: $this->decider
            );

            $decision->sendNotificationToLayerTwoManagerIfApplicable();

            if ($this->leave->isFullyApproved()) {
                $this->finalizeApproval();
            }
        }, 5);
    }

    protected function finalizeApproval(): void
    {
        $this->leave->update([
            'status' => RequestStatus::Approved,
        ]);

        (new SyncLeaveAttendanceService())->setAttendanceToLeave($this->leave);

        DeleteNotificationJob::dispatch(
            notificationClass: NewLeaveRequestNotification::class,
            payloadId: $this->leave->id
        );
    }
}
