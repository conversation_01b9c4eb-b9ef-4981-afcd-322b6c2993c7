<?php

namespace App\Services;

use App\DTOs\EarlyLateMailData;
use App\Enums\EarlyLateNestingPolicy;
use App\Enums\EarlyLatePeriodPolicy;
use App\Models\Department;
use App\Models\Employee;
use App\Workflows\GenerateEarlyLatePeriodicalMailWorkflow;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

class PeriodicalEarlyLateReportService
{
    public function sendMonthlyReport(CarbonPeriod $period): void
    {
        $this->processDirectDepartments($period, EarlyLatePeriodPolicy::Monthly);
        $this->processNestedDepartments($period, EarlyLatePeriodPolicy::Monthly);
        $this->processDirectManagers($period, EarlyLatePeriodPolicy::Monthly);
    }

    public function sendWeeklyReport(CarbonPeriod $period): void
    {
        $this->processDirectDepartments($period, EarlyLatePeriodPolicy::Weekly);
        $this->processNestedDepartments($period, EarlyLatePeriodPolicy::Weekly);
        $this->processDirectManagers($period, EarlyLatePeriodPolicy::Weekly);
    }

    /**
     * process managers where team config is direct departments alongside direct employees of manager
     */
    private function processDirectDepartments(
        CarbonPeriod $period,
        EarlyLatePeriodPolicy $periodType
    ): void {
        Department::query()
            ->belongToActiveTeam()
            ->hasActiveManager()
            ->with('manager.team')
            ->whereHas(
                'team',
                fn($query) => $query
                    ->earlyLateEnabled()
                    ->whereIn('early_late_config->period_policy', [
                        $periodType,
                        EarlyLatePeriodPolicy::WeeklyMonthly,
                    ])
                    ->where(
                        'early_late_config->nesting_policy',
                        EarlyLateNestingPolicy::DirectDepartment
                    )
            )
            ->get()
            ->groupBy('manager_id')
            ->map(
                fn($departments) => [
                    'manager' => $departments->first()->manager,
                    'departments' => $departments,
                ]
            )
            ->each(function (array $group) use ($periodType, $period) {
                $this->startWorkflow(
                    manager: $group['manager'],
                    departmentsIDs: $group['departments']->pluck('id')->toArray(),
                    period: $period,
                    periodType: $periodType
                );
            });
    }

    /**
     * process managers where team config is nested departments alongside direct employees of manager
     */
    private function processNestedDepartments(
        CarbonPeriod $period,
        EarlyLatePeriodPolicy $periodType
    ): void {
        Department::query()
            ->select('id', 'parent_id', 'manager_id')
            ->belongToActiveTeam()
            ->hasActiveManager()
            ->with(['manager.team', 'descendantsAndSelf:id,parent_id,path'])
            ->whereHas(
                'team',
                fn($query) => $query
                    ->earlyLateEnabled()
                    ->whereIn('early_late_config->period_policy', [
                        $periodType,
                        EarlyLatePeriodPolicy::WeeklyMonthly,
                    ])
                    ->where(
                        'early_late_config->nesting_policy',
                        EarlyLateNestingPolicy::NestedDepartments
                    )
            )
            ->get()
            ->reduce(function (Collection $groups, Department $department) {
                return $groups->put($department->manager->id, [
                    'manager' => $department->manager,
                    'departmentsIDs' => $department->descendantsAndSelf->pluck('id')->toArray(),
                ]);
            }, collect())
            ->each(function (array $group) use ($periodType, $period) {
                $this->startWorkflow(
                    manager: $group['manager'],
                    departmentsIDs: $group['departmentsIDs'],
                    period: $period,
                    periodType: $periodType
                );
            });
    }

    /**
     * process direct managers who are not department managers
     */
    private function processDirectManagers(
        CarbonPeriod $period,
        EarlyLatePeriodPolicy $periodType
    ): void {
        Employee::query()
            ->belongToActiveTeam()
            ->withWhereHas(
                'team',
                fn($query) => $query
                    ->earlyLateEnabled()
                    ->whereIn('early_late_config->period_policy', [
                        $periodType,
                        EarlyLatePeriodPolicy::WeeklyMonthly,
                    ])
            )
            ->has('directlyManagedEmployees')
            ->doesntHave('managedEmployeesByDepartment')
            ->each(function (Employee $manager) use ($periodType, $period) {
                $this->startWorkflow(
                    manager: $manager,
                    departmentsIDs: [],
                    period: $period,
                    periodType: $periodType
                );
            });
    }

    protected function startWorkflow(
        Employee $manager,
        array $departmentsIDs,
        CarbonPeriod $period,
        EarlyLatePeriodPolicy $periodType
    ): void {
        $data = new EarlyLateMailData(
            manager: $manager,
            departmentsIDs: $departmentsIDs,
            period: $period,
            periodType: $periodType
        );

        (new GenerateEarlyLatePeriodicalMailWorkflow($data))->run();
    }
}
