<?php

namespace App\Services;

use App\Enums\AttendanceDayType;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\Shift;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;

class AttendanceService
{
    public function __construct()
    {
    }

    public function handle(): void
    {
    }

    private function overlappingTemporaryShift(Shift $shift, CarbonImmutable $day): bool
    {
        return !$shift->pivot->permanent &&
            CarbonPeriodImmutable::dates($day, $day)->overlaps(
                Carbon::parse($shift->pivot->start_at)->subDay(),
                Carbon::parse($shift->pivot->end_at)->addDay()
            );
    }

    public function dayShift(Employee $employee, CarbonImmutable $day): ?Shift
    {
        return $employee->upcomingShifts
            ->filter(
                fn(Shift $upcomingShift) => $this->overlappingTemporaryShift($upcomingShift, $day)
            )
            ->first() ?? $employee->upcomingShifts->where('pivot.permanent', true)->first();
    }
    public function dayType(
        Shift $shift,
        Employee $employee,
        CarbonImmutable $day
    ): AttendanceDayType {
        $isHoliday = Holiday::query()
            ->where('team_id', $employee->team_id)
            ->date($day)
            ->exists();

        $onLeave = $employee->leaves()->date($day)->approved()->exists();
        $isWeekend = !$shift->getDayShift($day);
        $vacationWeekendPolicy = $employee->team->vacation_weekend;

        if ($onLeave) {
            return $isWeekend && !$vacationWeekendPolicy
                ? AttendanceDayType::WEEKEND
                : AttendanceDayType::LEAVE;
        } elseif ($isHoliday) {
            return AttendanceDayType::HOLIDAY;
        } else {
            return $isWeekend ? AttendanceDayType::WEEKEND : AttendanceDayType::WEEKDAY;
        }
    }
}
