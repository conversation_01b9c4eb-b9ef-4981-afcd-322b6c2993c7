<?php

namespace App\Services;

use App\Enums\DelegationType;
use App\Models\Delegation;
use App\Models\Employee;

class UpdateDelegationsService
{
    public function handle(Employee $employee, array $data): void
    {
        foreach ($data as $key => $delegatedID) {
            $delegationType = DelegationType::requestInputToType($key);

            if (!$delegatedID) {
                $employee->managerDelegations()->where('type', $delegationType)->delete();
                continue;
            }

            Delegation::updateOrCreate(
                ['type' => $delegationType, 'delegatee_id' => $employee->id],
                ['delegated_id' => $delegatedID]
            );
        }
    }
}
