<?php

namespace App\Services;

use App\DTOs\ApprovalLayer;
use App\DTOs\RequestApprovers;
use App\Enums\ApprovalType;
use App\Models\Employee;

class GetRequestApproversService
{
    public function __construct(protected Employee $employee)
    {
    }

    public function approvers(): RequestApprovers
    {
        $employee = $this->employee;

        $approvalType = $employee->team->approval_type;

        $firstLayer = new ApprovalLayer(
            leaveApprover: $employee->manager?->receiverOfLeaveRequest(),
            permissionApprover: $employee->manager?->receiverOfPermissionRequest(),
            regularizationApprover: $employee->manager?->receiverOfRegularizationRequest(),
            remoteWorkApprover: $employee->manager?->receiverOfRemoteWorkRequest()
        );

        if ($approvalType === ApprovalType::OneLayer) {
            return new RequestApprovers(firstLayer: $firstLayer);
        }

        $secondLayer = new ApprovalLayer(
            leaveApprover: $employee->departmentManager?->receiverOfLeaveRequest(),
            permissionApprover: $employee->departmentManager?->receiverOfPermissionRequest(),
            regularizationApprover: $employee->departmentManager?->receiverOfRegularizationRequest(),
            remoteWorkApprover: $employee->departmentManager?->receiverOfRemoteWorkRequest()
        );

        return new RequestApprovers(firstLayer: $firstLayer, secondLayer: $secondLayer);
    }
}
