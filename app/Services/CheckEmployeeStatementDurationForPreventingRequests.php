<?php

namespace App\Services;

use App\Models\Attendance;
use App\Models\Employee;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;

class CheckEmployeeStatementDurationForPreventingRequests
{
    public function __construct(protected Employee $employee, protected CarbonPeriod $period)
    {
    }

    public function validate(): void
    {
        if (
            !$this->employee->team->employee_statement_config->enabled ||
            !$this->employee->team->employee_statement_config->preventRequestsEnabled
        ) {
            return;
        }

        $affectedAttendances = $this->affectedAttendance();

        if ($affectedAttendances->isEmpty()) {
            return;
        }

        $oldestAllowedDate = $this->oldestAllowedDate($affectedAttendances);

        $hasOldRecords = $affectedAttendances->contains(
            fn(Attendance $attendance) => $attendance->date->lessThan($oldestAllowedDate)
        );

        if ($hasOldRecords) {
            throw ValidationException::withMessages([
                'date' => __(
                    'You can not request leave or permission or regularization older than :days days',
                    [
                        'days' =>
                            $this->employee->team->employee_statement_config
                                ->daysBeforePreventingRequests,
                    ]
                ),
            ]);
        }
    }

    public function oldestAllowedDate(Collection $affectedAttendances): Carbon
    {
        $allowedDaysDuration =
            $this->employee->team->employee_statement_config->daysBeforePreventingRequests;

        // we need to add the number of weekends days to the allowed days duration
        $weekendsCount = $affectedAttendances
            ->filter(fn(Attendance $attendance) => $attendance->is_weekend)
            ->count();

        return now()
            ->subDays($allowedDaysDuration + $weekendsCount)
            ->startOfDay();
    }

    public function affectedAttendance(): Collection
    {
        return $this->employee
            ->attendances()
            ->where('date', '>=', $this->period->getStartDate()->toDateString())
            ->where('date', '<=', $this->period->getEndDate()->toDateString())
            ->orderBy('date')
            ->get()
            ->filter(fn(Attendance $attendance) => $attendance->employee_statement_enabled);
    }
}
