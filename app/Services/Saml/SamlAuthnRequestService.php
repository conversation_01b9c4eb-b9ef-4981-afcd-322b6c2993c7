<?php

namespace App\Services\Saml;

use App\Models\TenantAuthenticatorConfig;
use LightSaml\Credential\KeyHelper;
use LightSaml\Credential\X509Certificate;

class SamlAuthnRequestService
{
    public static function make(
        TenantAuthenticatorConfig $tenantAuthenticatorConfig,
        $method = 'get',
        $doNotRedirect = false
    ): \Illuminate\Routing\Redirector|\Illuminate\Http\RedirectResponse|string {
        $authnRequest = new \LightSaml\Model\Protocol\AuthnRequest();
        $authnRequest
            ->setAssertionConsumerServiceURL(
                route('saml.acs', $tenantAuthenticatorConfig->tenant_id)
            )
            ->setProtocolBinding(\LightSaml\SamlConstants::BINDING_SAML2_HTTP_POST)
            ->setID(\LightSaml\Helper::generateID())
            ->setIssueInstant(new \DateTime())
            ->setDestination($tenantAuthenticatorConfig->idpLoginUrl)
            ->setIssuer(new \LightSaml\Model\Assertion\Issuer(config('app.url')));

        $certificate = new X509Certificate();
        $certificate->loadPem(config('saml.x509cert'));
        $privateKey = KeyHelper::createPrivateKey(config('saml.privateKey'), '');
        $authnRequest->setSignature(
            new \LightSaml\Model\XmlDSig\SignatureWriter($certificate, $privateKey)
        );

        $bindingFactory = new \LightSaml\Binding\BindingFactory();
        $redirectBinding = $bindingFactory->create(
            \LightSaml\SamlConstants::BINDING_SAML2_HTTP_REDIRECT
        );

        $messageContext = new \LightSaml\Context\Profile\MessageContext();
        $messageContext->setMessage($authnRequest);

        /** @var \Symfony\Component\HttpFoundation\RedirectResponse $httpResponse */
        $httpResponse = $redirectBinding->send($messageContext);

        if (!$doNotRedirect) {
            return redirect($httpResponse->getTargetUrl());
        }

        return $httpResponse->getTargetUrl();
    }
}
