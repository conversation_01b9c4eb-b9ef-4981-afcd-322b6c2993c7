<?php

namespace App\Services;

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Leave;
use Illuminate\Support\Collection;

class SyncLeaveAttendanceService
{
    public function setAttendanceToLeave(Leave $leave, Collection $attendances = null): void
    {
        if ($attendances) {
            $attendances->each(fn($attendance) => $this->approveAttendance($attendance));

            $leave->update([
                'synced_to_attendance_at' => now(),
            ]);

            return;
        }

        $leave->employee
            ->attendances()
            ->whereBetween('date', [$leave->from_date, $leave->to_date])
            ->whereIn('status', AttendanceStatus::statusesShouldBeLeaveByTeam($leave->team))
            // chunking and updating individually to enable logging
            ->each(fn($attendance) => $this->approveAttendance($attendance));

        $leave->update([
            'synced_to_attendance_at' => now(),
        ]);
    }

    public function approveAttendance(Attendance $attendance): void
    {
        $attendance->update(['status' => AttendanceStatus::LEAVE]);
    }

    public function revertAttendanceToAbsent(Leave $leave): void
    {
        Attendance::ofLeave($leave)
            ->where('status', AttendanceStatus::LEAVE->value)
            // chunking and updating individually to enable logging
            ->each(fn($attendance) => $attendance->update(['status' => AttendanceStatus::ABSENT]));
    }
}
