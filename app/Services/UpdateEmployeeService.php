<?php

namespace App\Services;

use App\DTOs\RandomProofNotificationConfig;
use App\Enums\DurationStatus;
use App\Events\EmployeeUpdated;
use App\Models\Employee;
use App\Models\Shift;
use App\Notifications\NewPermanentShiftAssignedNotification;
use App\Notifications\NewTemporaryShiftAssignedNotification;
use App\Notifications\PermanentShiftAssignedChangedNotification;
use App\Notifications\TemporaryShiftChangedNotification;
use App\Support\FormatLocationsForEmployees;
use Carbon\CarbonImmutable;
use DB;

class UpdateEmployeeService
{
    public static function handle(Employee $employee, array $data): void
    {
        $existingPermanentShift = $employee->shifts()->where('permanent', true)->first();
        $existingTemporaryShifts = $employee->shifts()->where('permanent', false)->get();

        DB::transaction(function () use ($data, $employee) {
            $employee->update(array_except($data, ['locations', 'shifts', 'random_notification']));

            // convert old random_notification to new random_proof_notification_config for external API v1
            if (isset($data['random_notification'])) {
                $employee->update([
                    'random_proof_notification_config' => RandomProofNotificationConfig::oldValueToNewConfig(
                        $data['random_notification'],
                        $employee->random_proof_notification_config
                    ),
                ]);
            }

            self::attachShifts($data, $employee);

            $formattedLocations = FormatLocationsForEmployees::format(
                locations: collect($data['locations'])
            );

            // for some reason, sync doesn't work here and it's buggy
            $employee->locations()->detach();

            $employee->locations()->attach($formattedLocations);

            event(new EmployeeUpdated($employee));
        });

        $newPermanentShift = $employee->shifts()->where('permanent', true)->first();
        $newTemporaryShifts = $employee->shifts()->where('permanent', false)->get();

        if (is_null($existingPermanentShift) && $newPermanentShift) {
            # new permanent shift added
            $employee->notify(new NewPermanentShiftAssignedNotification($newPermanentShift));
        }

        # permanent shift changed
        if (
            $existingPermanentShift &&
            $newPermanentShift &&
            $existingPermanentShift->id !== $newPermanentShift->id
        ) {
            $employee->notify(new PermanentShiftAssignedChangedNotification($newPermanentShift));
        }

        # there is a change in temporary shifts
        if ($existingTemporaryShifts !== $newTemporaryShifts) {
            # only new temporary shift assigned, no shift deleted
            if ($existingTemporaryShifts->diff($newTemporaryShifts)->isEmpty()) {
                foreach (
                    $newTemporaryShifts->diff($existingTemporaryShifts)
                    as $newTemporaryShift
                ) {
                    $employee->notify(
                        new NewTemporaryShiftAssignedNotification(
                            $newTemporaryShift,
                            CarbonImmutable::parse($newTemporaryShift->pivot->start_at),
                            CarbonImmutable::parse($newTemporaryShift->pivot->end_at)
                        )
                    );
                }
            } else {
                # existing temporary shift changed (shift unassigned only or shift unassigned and new shift assigned)
                $employee->notify(new TemporaryShiftChangedNotification());
            }
        }
    }

    private static function attachShifts(array $data, Employee $employee): void
    {
        if ($data['shift_id'] ?? null) {
            /* @see \App\Http\Controllers\External\Employee\EmployeeController::update logic for this endpoint */
            $temporaryShiftsIds = $employee->shifts()->where('permanent', false)->pluck('id');

            $permanentShiftId =
                $data['shift_id'] ??
                ($employee->shifts()->firstWhere('permanent', true) ?? Shift::value('id'));

            $employee->shifts()->sync([...$temporaryShiftsIds, $permanentShiftId]);
        } else {
            /* @see \App\Http\Controllers\Frontend\EmployeeController::update logic for this endpoint */
            $formatShifts = collect($data['shifts'] ?? [])
                ->unique(
                    fn($shift) => $shift['id'] .
                        $shift['type'] .
                        ($shift['start_date'] ?? '') .
                        ($shift['end_date'] ?? '')
                )
                ->map(
                    fn($shift) => [
                        'shift_id' => $shift['id'],
                        'permanent' => $shift['type'] === DurationStatus::PERMANENT->value,
                        'start_at' =>
                            $shift['type'] === DurationStatus::TEMPORARY->value
                                ? $shift['start_date']
                                : null,
                        'end_at' =>
                            $shift['type'] === DurationStatus::TEMPORARY->value
                                ? $shift['end_date']
                                : null,
                    ]
                );

            $employee->shifts()->sync($formatShifts);
        }
    }
}
