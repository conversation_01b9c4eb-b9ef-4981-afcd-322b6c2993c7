<?php

namespace App\Services;

use App\Models\WorkSchedule;
use DB;

class UpdateWorkScheduleService
{
    public static function handle(WorkSchedule $workSchedule, array $data): WorkSchedule
    {
        return DB::transaction(function () use ($workSchedule, $data) {
            // Extract workdays data
            $workdaysData = $data['workdays'];
            unset($data['workdays']);

            // Update the work schedule
            $workSchedule->update($data);

            // Detach all existing workdays
            $workSchedule->workdays()->detach();

            // Attach new workdays with pivot data
            foreach ($workdaysData as $workdayData) {
                $workSchedule->workdays()->attach($workdayData['workday_id'], [
                    'work_days_number' => $workdayData['work_days_number'] ?? null,
                    'off_days_number' => $workdayData['off_days_number'] ?? null,
                    'repetitions_number' => $workdayData['repetitions_number'] ?? null,
                ]);
            }

            return $workSchedule;
        });
    }
}
