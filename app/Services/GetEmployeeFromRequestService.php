<?php

namespace App\Services;

use App\Models\Employee;

readonly class GetEmployeeFromRequestService
{
    public function __construct(private bool $required = true)
    {
    }

    public function handle(): ?Employee
    {
        $identifier = request()->route('employee');

        if ($identifier instanceof Employee) {
            return $identifier;
        }

        if ($identifier) {
            $employee = currentTenant()
                ?->employees()
                ->whereAny(['id', 'email', 'number'], $identifier)
                ->first();

            abort_if(!$employee && $this->required, 404);

            return $employee;
        }

        abort_if($this->required, 404);

        return null;
    }
}
