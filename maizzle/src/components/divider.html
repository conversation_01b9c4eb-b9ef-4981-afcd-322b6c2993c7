<script props>
  // https://maizzle.com/docs/components/divider
  module.exports = {
    height: props.height || "1px",
    color: props.color, // any CSS color value
    top: props.top, // top margin
    bottom: props.bottom, // bottom margin
    left: props.left, // left margin
    right: props.right, // right margin
    spaceY: props["space-y"] || "24px", // top and bottom margin
    spaceX: props["space-x"], // right and left margin
    hasBgClass: props.class && props.class.split(" ").some((c) => c.startsWith("bg-")),
  };
</script>

<div
  role="separator"
  class="[[ (!color && !hasBgClass) && 'bg-slate-300' ]]"
  style="height: [[ height ]];
    line-height: [[ height ]];
    [[ color && `background-color: ${color}` ]];
    margin: 0;
    [[ spaceY && `margin-top: ${spaceY}; margin-bottom: ${spaceY}` ]];
    [[ spaceX && `margin-left: ${spaceX}; margin-right: ${spaceX}` ]];
    [[ top && `margin-top: ${top}` ]];
    [[ bottom && `margin-bottom: ${bottom}` ]];
    [[ left && `margin-left: ${left}` ]];
    [[ right && `margin-right: ${right}` ]];
  "
>
  &zwj;
</div>
