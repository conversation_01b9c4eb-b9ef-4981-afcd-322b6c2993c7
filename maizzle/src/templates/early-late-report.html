---
title: "Early Late Report"
bodyClass: bg-gray-50
---

@php
  use App\Enums\EarlyLatePeriodPolicy;

/** @var \App\Models\Employee $receiver */
/**@var \App\DTOs\Stats\AttendanceStatusStats[] $attendancesStats */
/**@var \App\DTOs\Stats\EarlyLateStats[] $earlyLateStats */
/**@var \App\DTOs\Stats\EmployeesStats $employeesStats */
/**@var \App\Calculations\MostLeastCommittedAttendanceCalculator $mostLeastCommitted */
/** @var int $employeesCount */
/** @var EarlyLatePeriodPolicy $periodType */
/** @var array{key: string, value: float, emoji: string, title: string, description: string} $inMood */
/** @var array{key: string, value: float, emoji: string, title: string, description: string} $outMood */
@endphp

<x-main>
  <div class="bg-slate-50 sm:px-4 font-sans p-8 shadow-sm">
    <table align="center">
      <tr>
        <td class="bg-white shadow">
          <div class="mt-12 mb-6 sm:mt-8 sm:mb-4 text-center">
            <img
              class="block mx-auto h-11 w-auto"
              src="{{asset('images/logo.png')}}"
              alt="Nawart"
            />
          </div>

          <table class="w-full text-center max-w-7xl">
            <tr>
              <td class="px-6 py-12 text-base text-gray-900 bg-white rounded shadow-sm">
                <h1 class="mb-3 text-3xl sm:leading-8 text-gray-900 font-semibold">
                  {{ $periodType === EarlyLatePeriodPolicy::Monthly ? 'تقرير الفريق الشهري' : 'تقرير الفريق الأسبوعي' }}
                </h1>
                <h1 class="mb-10 text-3xl sm:leading-8 text-gray-900 font-semibold">
                  {{ $periodType === EarlyLatePeriodPolicy::Monthly ? 'Monthly Team Report' : 'Weekly Team Report' }}
                </h1>

                <div dir="rtl" class="block mb-4 text-start w-auto text-sm text-gray-600 font-normal">
                  مرحبا “<strong>{{$receiver->name}}</strong>”,
                </div>
                <div dir="rtl" class="block text-start w-auto text-sm text-gray-600 font-normal mb-3">
                  تقرير يضم {{$employeesCount}} موظفًا،
                  عن {{$periodType === EarlyLatePeriodPolicy::Monthly ?  'الشهر': 'الأسبوع'}} الماضي
                  <span>({{$from}}
                  , {{$to}})</span> جاهز الآن
                  للمراجعة، مع عرض متوسط ابرز الارقام.
                </div>

                <div class="block mb-4 text-start w-auto text-sm text-gray-600 font-normal ">
                  Hello “<strong>{{$receiver->name}}</strong>”,
                </div>
                <div class="block text-start w-auto text-sm text-gray-600 font-normal">
                  {{$employeesCount}} employees updates for
                  last {{$periodType === EarlyLatePeriodPolicy::Monthly ?  'month': 'week'}}
                  ({{$from}}, {{$to}}) showing the
                  average, Is here
                  for you to review!
                </div>

                <div class="flex justify-center gap-x-6 mt-10">
                  <!-- Attendance Card -->
                  <div dir="rtl"
                       class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start">
                    <div class="text-gray-600 leading-8 text-xl">إجمالي الحضور</div>
                    <div class="text-gray-600 leading-8 text-xl">Total Attendance</div>
                    <div class="text-2xl mt-2 font-bold">{{$attendancesStats['sum']->presentIncludingNonWorkDays() }}
                      %
                    </div>
                  </div>

                  <!-- Leave Card -->
                  <div dir="rtl"
                       class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start">
                    <div class="text-gray-600 leading-8 text-xl">الإجازات</div>
                    <div class="text-gray-600 leading-8 text-xl">Leave</div>
                    <div class="text-2xl mt-2 font-bold">{{$attendancesStats['sum']->leave}}%</div>
                  </div>

                  <!-- Absence Card -->
                  <div dir="rtl"
                       class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start">
                    <div class="text-gray-600 leading-8 text-xl">الغياب</div>
                    <div class="text-gray-600 leading-8 text-xl">Absence</div>
                    <div class="text-2xl mt-2 font-bold">{{$attendancesStats['sum']->absent}}%</div>
                  </div>
                </div>
                @if($periodType === EarlyLatePeriodPolicy::Weekly)
                  <div class="border border-solid border-gray-200 rounded-lg p-4 mt-10">
                    <div
                      class="flex gap-x-3">
                      <div class="flex flex-col gap-y-6 text-nowrap font-medium text-start text-gray-500 -mt-1 text-xs">
                        <span>100%</span>
                        <span>80%</span>
                        <span>60%</span>
                        <span>40%</span>
                        <span>20%</span>
                        <span>0%</span>
                      </div>
                      <div class="bg-gray-200 h-[200px] w-px"></div>
                      <div>
                        <div class="flex gap-x-10 text-nowrap h-[200px]">
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['sunday']->present}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['sunday']->present, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['sunday']->leave}}
                                  %
                                </div>
                                <div class="w-3 bg-blue-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['sunday']->leave, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['sunday']->absent}}
                                  %
                                </div>
                                <div class="w-3 bg-gray-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['sunday']->absent, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Sun</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['monday']->present}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['monday']->present, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['monday']->leave}}
                                  %
                                </div>
                                <div class="w-3 bg-blue-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['monday']->leave, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['monday']->absent}}
                                  %
                                </div>
                                <div class="w-3 bg-gray-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['monday']->absent, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Mon</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['tuesday']->present}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['tuesday']->present, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['tuesday']->leave}}
                                  %
                                </div>
                                <div class="w-3 bg-blue-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['tuesday']->leave, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['tuesday']->absent}}
                                  %
                                </div>
                                <div class="w-3 bg-gray-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['tuesday']->absent, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Tue</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['wednesday']->present}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['wednesday']->present, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['wednesday']->leave}}
                                  %
                                </div>
                                <div class="w-3 bg-blue-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['wednesday']->leave, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['wednesday']->absent}}
                                  %
                                </div>
                                <div class="w-3 bg-gray-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['wednesday']->absent, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Wed</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['thursday']->present}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['thursday']->present, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['thursday']->leave}}
                                  %
                                </div>
                                <div class="w-3 bg-blue-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['thursday']->leave, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$attendancesStats['thursday']->absent}}
                                  %
                                </div>
                                <div class="w-3 bg-gray-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($attendancesStats['thursday']->absent, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Thu</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="w-full h-px bg-gray-200 mt-3 mb-2"></div>

                    <div class="flex justify-center space-x-6">
                      <!-- Attendance Dot -->
                      <div class="flex items-center space-x-2">
                        <div class="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
                        <div class="flex flex-col text-start text-gray-500">
                          <span class=" leading-5 text-sm">الحضور</span>
                          <span class=" leading-5 text-sm">Attendance</span>
                        </div>
                      </div>

                      <!-- Leave Dot -->
                      <div class="flex items-center space-x-2">
                        <div class="w-2.5 h-2.5 bg-blue-500 rounded-full"></div>
                        <div class="flex flex-col text-start text-gray-500">
                          <span class="leading-5 text-sm">الإجازات</span>
                          <span class="leading-5 text-sm">Leave</span>
                        </div>
                      </div>

                      <!-- Absence Dot -->
                      <div class="flex items-center space-x-2">
                        <div class="w-2.5 h-2.5 bg-gray-400 rounded-full"></div>
                        <div class="flex flex-col text-start text-gray-500">
                          <span class="leading-5 text-sm">الغياب</span>
                          <span class="leading-5 text-sm">Absence</span>
                        </div>
                      </div>
                    </div>
                  </div>
                @endif
                <div class="flex justify-center gap-x-6 mt-10">
                  <!-- On-Time Card -->
                  <div dir="rtl"
                       class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start">
                    <div class="text-gray-600 leading-8 text-xl">منتظم</div>
                    <div class="text-gray-600 leading-8 text-xl">On-Time</div>

                    <div class="text-2xl mt-2 font-bold">{{$earlyLateStats['sum']->onTime}}
                      %
                    </div>
                  </div>

                  <!-- Late Check-in Card -->
                  <div dir="rtl"
                       class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start">
                    <div class="text-gray-600 leading-8 text-xl">حضور متأخر</div>
                    <div class="text-gray-600 leading-8 text-xl">Late Check-in</div>

                    <div class="text-2xl mt-2 font-bold">{{$earlyLateStats['sum']->lateIn}}%</div>
                  </div>

                  <!-- Early Check-out Card -->
                  <div dir="rtl"
                       class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start">
                    <div class="text-gray-600 leading-8 text-xl">انصراف مبكر</div>
                    <div class="text-gray-600 leading-8 text-xl">Early Check-out</div>
                    <div class="text-2xl mt-2 font-bold">{{$earlyLateStats['sum']->earlyOut}}%</div>
                  </div>
                </div>
                @if($periodType === EarlyLatePeriodPolicy::Weekly)
                  <div class="border border-solid border-gray-200 rounded-lg p-3 mt-10">
                    <div
                      class="flex gap-x-3">
                      <div class="flex flex-col gap-y-6 text-nowrap font-medium text-start text-gray-500 text-xs">
                        <span>100%</span>
                        <span>80%</span>
                        <span>60%</span>
                        <span>40%</span>
                        <span>20%</span>
                        <span>0%</span>
                      </div>
                      <div class="bg-gray-200 h-[200px] w-px"></div>
                      <div>
                        <div class="flex gap-x-10 text-nowrap h-[200px]">
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div
                                  class="text-gray-500 text-xs mb-2">{{$earlyLateStats['sunday']->onTime}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['sunday']->onTime, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['sunday']->lateIn}}
                                  %
                                </div>
                                <div class="w-3 bg-orange-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['sunday']->lateIn, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['sunday']->earlyOut}}
                                  %
                                </div>
                                <div class="w-3 bg-red-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['sunday']->earlyOut, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Sun</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div
                                  class="text-gray-500 text-xs mb-2">{{$earlyLateStats['monday']->onTime}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['monday']->onTime, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['monday']->lateIn}}
                                  %
                                </div>
                                <div class="w-3 bg-orange-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['monday']->lateIn, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['monday']->earlyOut}}
                                  %
                                </div>
                                <div class="w-3 bg-red-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['monday']->earlyOut, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Mon</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div
                                  class="text-gray-500 text-xs mb-2">{{$earlyLateStats['tuesday']->onTime}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['tuesday']->onTime, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['tuesday']->lateIn}}
                                  %
                                </div>
                                <div class="w-3 bg-orange-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['tuesday']->lateIn, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['tuesday']->earlyOut}}
                                  %
                                </div>
                                <div class="w-3 bg-red-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['tuesday']->earlyOut, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Tue</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div
                                  class="text-gray-500 text-xs mb-2">{{$earlyLateStats['wednesday']->onTime}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['wednesday']->onTime, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['wednesday']->lateIn}}
                                  %
                                </div>
                                <div class="w-3 bg-orange-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['wednesday']->lateIn, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['wednesday']->earlyOut}}
                                  %
                                </div>
                                <div class="w-3 bg-red-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['wednesday']->earlyOut, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Wed</div>
                          </div>
                          <div class="flex flex-col items-center space-y-2 justify-end">
                            <!-- Percentage Labels for Each Bar -->

                            <!-- Bar Row -->
                            <div class="flex gap-x-1 items-end">
                              <!-- Attendance Bar -->
                              <div class="flex flex-col items-center">
                                <div
                                  class="text-gray-500 text-xs mb-2">{{$earlyLateStats['thursday']->onTime}}
                                  %
                                </div>
                                <div class="w-3 bg-green-500 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['thursday']->onTime, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Leave Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['thursday']->lateIn}}
                                  %
                                </div>
                                <div class="w-3 bg-orange-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['thursday']->lateIn, 100, min: 0.05)}});"></div>
                              </div>

                              <!-- Absence Bar -->
                              <div class="flex flex-col items-center">
                                <div class="text-gray-500 text-xs mb-2">{{$earlyLateStats['thursday']->earlyOut}}
                                  %
                                </div>
                                <div class="w-3 bg-red-400 rounded-t-full"
                                     style="height: calc(160px * {{divide($earlyLateStats['thursday']->earlyOut, 100, min: 0.05)}});"></div>
                              </div>
                            </div>

                            <!-- Day Label -->
                            <div class="text-gray-700 text-sm mt-2">Thu</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="w-full h-px bg-gray-200 mt-3 mb-2"></div>

                    <div class="flex justify-center space-x-6">
                      <!-- On-Time Dot -->
                      <div class="flex items-center space-x-2">
                        <div class="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
                        <div class="flex flex-col text-start text-gray-500">
                          <span class=" leading-5 text-sm">منتظم</span>
                          <span class=" leading-5 text-sm">On-Time</span>
                        </div>
                      </div>

                      <!-- Late Check-in Dot -->
                      <div class="flex items-center space-x-2">
                        <div class="w-2.5 h-2.5 bg-orange-400 rounded-full"></div>
                        <div class="flex flex-col text-start text-gray-500">
                          <span class=" leading-5 text-sm">حضور متأخر</span>
                          <span class=" leading-5 text-sm">Late Check-in</span>
                        </div>
                      </div>

                      <!-- Early Check-out Dot -->
                      <div class="flex items-center space-x-2">
                        <div class="w-2.5 h-2.5 bg-red-400 rounded-full"></div>
                        <div class="flex flex-col text-start text-gray-500">
                          <span class=" leading-5 text-sm">انصراف مبكر</span>
                          <span class=" leading-5 text-sm">Early Check-out</span>
                        </div>
                      </div>
                    </div>

                  </div>
                @endif

                <div class="flex justify-center gap-x-6 mt-10">
                  <!-- Attendance Card -->
                  <div
                    dir="rtl"
                    class="border border-solid size-72 border-gray-200 rounded-lg flex flex-col gap-x-6 mt-10 p-3 flex-1 text-start">
                    <div class="text-gray-600 font-medium leading-7 text-xl">العمل لساعات إضافية
                    </div>
                    <div class="text-gray-600 font-medium leading-7 text-xl">Additional Hours</div>

                    <div class="flex flex-col items-center">
                      <!-- Title -->

                      <div class="relative flex items-center justify-center size-56">
                        <!-- Circular Progress Bar -->
                        <svg class="w-full h-full" viewBox="0 0 36 36">
                          <!-- Background Circle -->
                          <circle
                            class="text-gray-300"
                            stroke="currentColor"
                            stroke-width="2.5"
                            fill="transparent"
                            r="16"
                            cx="18"
                            cy="18"
                          />
                          <!-- Progress Circle -->
                          <circle
                            class="text-blue-500"
                            stroke="currentColor"
                            stroke-width="2.5"
                            stroke-dasharray="{{percentage($employeesStats->employeesHasAdditionalHoursCount, $employeesStats->total)}}, 100"
                            fill="transparent"
                            r="16"
                            cx="18"
                            cy="18"
                            transform="rotate(-90 18 18)"
                          />
                        </svg>

                        <!-- Centered Text -->
                        <div class="absolute text-center">
                          <span class="text-2xl font-bold text-gray-800">{{percentage($employeesStats->employeesHasAdditionalHoursCount, $employeesStats->total)}}%</span>
                          <p class="text-gray-500 text-xs">of the team worked<br>additional
                            Hours</p>
                        </div>
                      </div>

                    </div>
                  </div>

                  <div
                    dir="rtl"
                    class="border border-solid size-72 border-gray-200 rounded-lg flex flex-col gap-x-6 mt-10 p-3 flex-1 text-start">
                    <div class="text-gray-600 font-medium leading-7 text-xl">طلبات الإستئذان</div>
                    <div class="text-gray-600 font-medium leading-7 text-xl">Permission Requests
                    </div>

                    <div class="flex flex-col items-center">
                      <!-- Title -->

                      <div class="relative flex items-center justify-center size-56">
                        <!-- Circular Progress Bar -->
                        <svg class="w-full h-full" viewBox="0 0 36 36">
                          <!-- Background Circle -->
                          <circle
                            class="text-gray-300"
                            stroke="currentColor"
                            stroke-width="2.5"
                            fill="transparent"
                            r="16"
                            cx="18"
                            cy="18"
                          />
                          <!-- Progress Circle -->
                          <circle
                            class="text-blue-500"
                            stroke="currentColor"
                            stroke-width="2.5"
                            stroke-dasharray="{{percentage($employeesStats->employeesHasPermissionRequestsCount, $employeesStats->total)}}, 100"
                            fill="transparent"
                            r="16"
                            cx="18"
                            cy="18"
                            transform="rotate(-90 18 18)"
                          />
                        </svg>

                        <!-- Centered Text -->
                        <div class="absolute text-center">
                          <span class="text-2xl font-bold text-gray-800">{{percentage($employeesStats->employeesHasPermissionRequestsCount, $employeesStats->total)}}%</span>
                          <p class="text-gray-500 text-xs">
                            of the team submitted
                            <br>
                            a permission request
                          </p>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>

                <div dir="rtl"
                     class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start mt-10">
                  <!-- Title -->
                  <div class="text-gray-600 leading-7 text-xl font-bold">أفضل 5 موظفين التزاماً</div>
                  <div class="text-gray-600 leading-7 text-xl font-bold mb-7">Top 5 Committed
                    Employees
                  </div>

                  <!-- Table -->
                  <div>
                    <table dir="rtl"
                           class="min-w-full bg-white border overflow-hidden rounded-lg border-solid border-gray-200">
                      <!-- Table Header -->
                      <thead class="bg-gray-100 font-bold">
                      <tr>
                        <th
                          class="py-2 px-4 text-start text-gray-600 text-xs">
                          الاسم
                          <br>
                          Name
                        </th>
                        <th
                          class="py-2 px-4 text-start text-gray-600 text-xs">
                          معدل الحضور
                          <br>
                          ATTENDANCE RATE
                        </th>
                        <th
                          class="py-2 px-4 text-start text-gray-600 text-xs">
                          الساعات غير المكتملة
                          <br>
                          INCOMPLETE HOURS
                        </th>
                      </tr>
                      </thead>

                      <!-- Table Body -->
                      <tbody>
                      @foreach($mostLeastCommitted->mostCommittedList() as $stats)
                        <tr>
                          <td class="py-2 px-4 border-0 border-b border-solid border-gray-200 text-gray-800 text-sm">
                            {{$stats->employee->name}}
                          </td>
                          <td class="py-2 px-4 border-0 border-b border-solid border-gray-200 text-gray-800 text-sm">
                            {{$stats->attendanceRatePercentage}}%
                          </td>
                          <td
                            class="py-2 px-4 border-0 border-b border-solid border-gray-200 text-gray-800 text-sm">
                            {{(int) round($stats->missingHours->totalHours)}}
                          </td>
                        </tr>
                      @endforeach

                      </tbody>
                    </table>
                  </div>
                </div>
                <div dir="rtl"
                     class="border border-solid border-gray-200 rounded-lg p-3 flex-1 text-start mt-10">
                  <!-- Title -->
                  <div class="text-gray-600 leading-7 text-xl font-bold">أقل 5 موظفين التزاماً</div>
                  <div class="text-gray-600 leading-7 text-xl font-bold mb-7">Least 5 Committed
                    Employees
                  </div>

                  <!-- Table -->
                  <div>
                    <table dir="rtl"
                           class="min-w-full bg-white border overflow-hidden rounded-lg border-solid border-gray-200">
                      <!-- Table Header -->
                      <thead class="bg-gray-100 font-bold">
                      <tr>
                        <th
                          class="py-2 px-4 text-start text-gray-600 text-xs">
                          الاسم
                          <br>
                          Name
                        </th>
                        <th
                          class="py-2 px-4 text-start text-gray-600 text-xs">
                          معدل الحضور
                          <br>
                          ATTENDANCE RATE
                        </th>
                        <th
                          class="py-2 px-4 text-start text-gray-600 text-xs">
                          الساعات غير المكتملة
                          <br>
                          INCOMPLETE HOURS
                        </th>
                      </tr>
                      </thead>

                      <!-- Table Body -->
                      <tbody>

                      <tbody>
                      @foreach($mostLeastCommitted->leastCommittedList() as $stats)
                        <tr>
                          <td class="py-2 px-4 border-0 border-b border-solid border-gray-200 text-gray-800 text-sm">
                            {{$stats->employee->name}}
                          </td>
                          <td class="py-2 px-4 border-0 border-b border-solid border-gray-200 text-gray-800 text-sm">
                            {{$stats->attendanceRatePercentage}}%
                          </td>
                          <td
                            class="py-2 px-4 border-0 border-b border-solid border-gray-200 text-gray-800 text-sm">
                            {{(int) round($stats->missingHours->totalHours)}}
                          </td>
                        </tr>
                      @endforeach
                      </tbody>
                    </table>
                  </div>
                </div>

                <div class="flex justify-center gap-x-6 mt-10">
                  <!-- Mood Card -->
                  <div
                    dir="rtl"
                    class="border border-solid border-gray-200 rounded-lg flex flex-col gap-x-6 mt-10 p-3 flex-1 text-start">
                    <div class="h-20">
                      <div class="text-gray-600 font-normal leading-7 text-xl">الحالة المزاجية للموظفين عند الخروج</div>
                      <div class="text-gray-600 font-normal leading-7 text-xl">Employees Check-out Mood</div>
                    </div>
                    <div class="flex flex-col items-center">
                      <!-- Title -->

                      <div class="relative flex items-center gap-x-2 justify-center size-56 mt-6">
                        <div class="flex flex-col items-center">
                          <img src="{{$outMood['emoji']}}" width="120" height="120">
                          <span class="text-gray-900 text-4xl font-bold">
                            {{$outMood['title']}}
                          </span>
                          <span class="text-gray-500 text-xs font-normal text-nowrap mt-4">
                            {{$outMood['description']}}
                          </span>
                        </div>
                      </div>

                    </div>
                  </div>
                  <div
                    dir="rtl"
                    class="border border-solid border-gray-200 rounded-lg flex flex-col gap-x-6 mt-10 p-3 flex-1 text-start">
                    <div class="h-20">
                      <div class="text-gray-600 font-normal leading-7 text-xl">الحالة المزاجية للموظفين عند الدخول</div>
                      <div class="text-gray-600 font-normal leading-7 text-xl">Employees Check-in Mood</div>

                    </div>
                    <div class="flex flex-col items-center">
                      <!-- Title -->

                      <div class="relative flex items-center gap-x-2 justify-center size-56 mt-6">
                        <div class="flex flex-col items-center">
                          <img src="{{$inMood['emoji']}}" width="120" height="120">
                          <span class="text-gray-900 text-4xl font-bold">
                            {{$inMood['title']}}
                          </span>
                          <span class="text-gray-500 text-xs font-normal text-nowrap mt-4">
                            {{$inMood['description']}}
                          </span>
                        </div>
                      </div>

                    </div>
                  </div>

                </div>

              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</x-main>
