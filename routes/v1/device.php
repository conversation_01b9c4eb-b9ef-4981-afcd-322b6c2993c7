<?php

use App\Http\Controllers\Device\Auth\LoginController;
use App\Http\Controllers\Device\CheckInOut\CheckInController;
use App\Http\Controllers\Device\CheckInOut\CheckoutController;
use App\Http\Controllers\Device\EmployeeController;

Route::post('auth/login', LoginController::class)->withoutMiddleware([
    'active-tenant',
    'auth:device',
]);

Route::get('employees/{identifier}', EmployeeController::class);
Route::post('checkin', CheckInController::class);
Route::post('checkout', CheckoutController::class);
