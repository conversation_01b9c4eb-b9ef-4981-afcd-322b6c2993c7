<?php

use <PERSON><PERSON><PERSON>hi\LaravelExternalJwtGuard\Middleware\CheckJwtRoles;
use App\Http\Controllers\Frontend\ActivityController;
use App\Http\Controllers\Frontend\ApprovalRequestController;
use App\Http\Controllers\Frontend\AttachLocationToEmployeesController;
use App\Http\Controllers\Frontend\AttachShiftToEmployeesController;
use App\Http\Controllers\Frontend\AttendanceController;
use App\Http\Controllers\Frontend\AuditController;
use App\Http\Controllers\Frontend\DashboardStatsController;
use App\Http\Controllers\Frontend\DefaultShiftController;
use App\Http\Controllers\Frontend\DepartmentController;
use App\Http\Controllers\Frontend\DeviceController;
use App\Http\Controllers\Frontend\EmployeeController;
use App\Http\Controllers\Frontend\EmployeesExportController;
use App\Http\Controllers\Frontend\EmployeeSummaryController;
use App\Http\Controllers\Frontend\HolidayController;
use App\Http\Controllers\Frontend\LeaveController;
use App\Http\Controllers\Frontend\LocationController;
use App\Http\Controllers\Frontend\PolicyController;
use App\Http\Controllers\Frontend\ProofController;
use App\Http\Controllers\Frontend\Report\DailyAttendanceStatsController;
use App\Http\Controllers\Frontend\Report\EarlyLateController;
use App\Http\Controllers\Frontend\Report\EmployeeStatementController;
use App\Http\Controllers\Frontend\Report\Exports\ExportEarlyLateController;
use App\Http\Controllers\Frontend\Report\Exports\ExportLeaveController;
use App\Http\Controllers\Frontend\Report\Exports\ExportMoodController;
use App\Http\Controllers\Frontend\Report\Exports\ExportPermissionController;
use App\Http\Controllers\Frontend\Report\Exports\ExportPresentAbsentController;
use App\Http\Controllers\Frontend\Report\Exports\ExportProofStatusController;
use App\Http\Controllers\Frontend\Report\Exports\ExportUsageRateController;
use App\Http\Controllers\Frontend\Report\GeospatialAttendanceController;
use App\Http\Controllers\Frontend\Report\LocationAttendanceRateController;
use App\Http\Controllers\Frontend\Report\MoodController;
use App\Http\Controllers\Frontend\Report\PermissionController;
use App\Http\Controllers\Frontend\Report\PresentAbsentController;
use App\Http\Controllers\Frontend\Report\ProofStatusController;
use App\Http\Controllers\Frontend\Report\ReportTask\RegenerateReportTaskController;
use App\Http\Controllers\Frontend\Report\ReportTask\ReportTaskController;
use App\Http\Controllers\Frontend\Report\ReportTask\SendReportTaskController;
use App\Http\Controllers\Frontend\RequestProofController;
use App\Http\Controllers\Frontend\RevokeEmployeeDeviceIDController;
use App\Http\Controllers\Frontend\ShiftController;
use App\Http\Controllers\Frontend\TagController;
use App\Http\Controllers\Frontend\TenantController;
use App\Http\Controllers\Frontend\TimezoneController;
use App\Http\Controllers\Frontend\TokenController;
use App\Http\Controllers\Frontend\UpdateSettingController;
use App\Http\Controllers\Frontend\WebhookController;
use App\Http\Controllers\Frontend\WebhookEventsController;
use App\Http\Controllers\Frontend\WorkScheduleController;
use App\Http\Controllers\WorkdayController;
use Illuminate\Support\Facades\Route;

Route::middleware(CheckJwtRoles::class . ':attendance-hr|attendance-dashboard-viewer')->group(
    function () {
        Route::get('dashboard/stats', DashboardStatsController::class);
        Route::get('departments', [DepartmentController::class, 'index']);
        Route::get('employees', [EmployeeController::class, 'index']);
        Route::get('shifts', [ShiftController::class, 'index']);
        Route::get('workdays', [WorkdayController::class, 'index']);
        Route::get('work-schedules', [WorkScheduleController::class, 'index']);
        Route::get('locations', [LocationController::class, 'index']);
    }
);

Route::middleware(CheckJwtRoles::class . ':attendance-hr')->group(function () {
    Route::get('tenant', TenantController::class);

    Route::get('audits', AuditController::class);
    Route::get('departments/{department}', [DepartmentController::class, 'show']);
    Route::put('departments/{department}', [DepartmentController::class, 'update']);

    Route::get('employees/export', EmployeesExportController::class);
    Route::post('employees/{employee}/request-proof', RequestProofController::class);

    Route::put('employees/attach-location/{location?}', AttachLocationToEmployeesController::class);
    Route::put('employees/attach-shift/{shift}', AttachShiftToEmployeesController::class);
    Route::put('employees/{employee}/revoke-device', RevokeEmployeeDeviceIDController::class);

    Route::get('employees/{employee}', [EmployeeController::class, 'show']);
    Route::get('employees/{employee}/summary', [EmployeeSummaryController::class, 'index']);
    Route::put('employees/{employee}', [EmployeeController::class, 'update']);

    Route::get('attendances', [AttendanceController::class, 'index']);
    Route::get('leaves', [LeaveController::class, 'index']);
    Route::get('proofs', [ProofController::class, 'index']);
    Route::get('activities', [ActivityController::class, 'index']);
    Route::get('approval-requests', [ApprovalRequestController::class, 'index']);
    Route::get('report-tasks', [ReportTaskController::class, 'index']);
    Route::get('report-tasks/{reportTask}', [ReportTaskController::class, 'show']);
    Route::get('report-tasks/{reportTask}/send', SendReportTaskController::class);
    Route::get('report-tasks/{reportTask}/regenerate', RegenerateReportTaskController::class);

    Route::get('shifts/default', DefaultShiftController::class);

    Route::get('shifts/{shift}', [ShiftController::class, 'show']);
    Route::post('shifts', [ShiftController::class, 'store']);
    Route::put('shifts/{shift}', [ShiftController::class, 'update']);
    Route::delete('shifts/{shift}', [ShiftController::class, 'destroy']);

    Route::get('workdays/{workday}', [WorkdayController::class, 'show']);
    Route::post('workdays', [WorkdayController::class, 'store']);
    Route::put('workdays/{workday}', [WorkdayController::class, 'update']);
    Route::delete('workdays/{workday}', [WorkdayController::class, 'destroy']);

    Route::get('work-schedules/{workSchedule}', [WorkScheduleController::class, 'show']);
    Route::post('work-schedules', [WorkScheduleController::class, 'store']);
    Route::put('work-schedules/{workSchedule}', [WorkScheduleController::class, 'update']);
    Route::delete('work-schedules/{workSchedule}', [WorkScheduleController::class, 'destroy']);

    Route::get('locations/{location}', [LocationController::class, 'show']);
    Route::post('locations', [LocationController::class, 'store']);
    Route::put('locations/{location}', [LocationController::class, 'update']);
    Route::delete('locations/{location}', [LocationController::class, 'destroy']);

    Route::get('holidays', [HolidayController::class, 'index']);
    Route::get('holidays/{holiday}', [HolidayController::class, 'show']);
    Route::post('holidays', [HolidayController::class, 'store']);
    Route::put('holidays/{holiday}', [HolidayController::class, 'update']);
    Route::delete('holidays/{holiday}', [HolidayController::class, 'destroy']);
    Route::get('policies', PolicyController::class);

    Route::prefix('reports')->group(function () {
        Route::get('daily-attendance-stats', DailyAttendanceStatsController::class);

        Route::get('early-late', EarlyLateController::class);
        Route::get('early-late/export', ExportEarlyLateController::class);

        Route::get('present-absent', PresentAbsentController::class);
        Route::get('present-absent/export', ExportPresentAbsentController::class);

        Route::get('proof-status', ProofStatusController::class);
        Route::get('proof-status/export', ExportProofStatusController::class);

        Route::get('permission', PermissionController::class);
        Route::get('permission/export', ExportPermissionController::class);

        Route::get('location-attendance-rate', [LocationAttendanceRateController::class, 'index']);
        Route::get('geospatial-attendance', [GeospatialAttendanceController::class, 'index']);

        Route::get('usage-rate/export', ExportUsageRateController::class);

        Route::get('mood', MoodController::class);
        Route::get('mood/export', ExportMoodController::class);

        Route::get('leave/export', ExportLeaveController::class);

        Route::get('employee-statements', [EmployeeStatementController::class, 'index']);
    });

    Route::get('tags', TagController::class);
    Route::put('settings/update', UpdateSettingController::class);

    Route::get('devices', [DeviceController::class, 'index']);
    Route::get('devices/{device}', [DeviceController::class, 'show']);
    Route::post('devices', [DeviceController::class, 'store']);
    Route::put('devices/{device}', [DeviceController::class, 'update']);
    Route::delete('devices/{device}', [DeviceController::class, 'destroy']);
});

Route::middleware(CheckJwtRoles::class . ':attendance-developer')->group(function () {
    Route::get('webhooks/events', WebhookEventsController::class);

    Route::get('webhooks', [WebhookController::class, 'index']);
    Route::get('webhooks/{webhook}', [WebhookController::class, 'show']);
    Route::post('webhooks', [WebhookController::class, 'store']);
    Route::put('webhooks/{webhook}', [WebhookController::class, 'update']);
    Route::delete('webhooks/{webhook}', [WebhookController::class, 'destroy']);

    Route::get('tokens', [TokenController::class, 'index']);
    Route::post('tokens', [TokenController::class, 'store']);
    Route::delete('tokens/{token}', [TokenController::class, 'destroy']);
});

Route::get('timezones', TimezoneController::class);
