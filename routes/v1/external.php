<?php

use App\Http\Controllers\External\DepartmentController;
use App\Http\Controllers\External\Employee\EmployeeController;
use App\Http\Controllers\External\EmployeeAttendanceController;
use App\Http\Controllers\External\LeaveController;
use App\Http\Controllers\External\LocationController;
use App\Http\Controllers\External\ShiftController;
use Illuminate\Support\Facades\Route;

Route::prefix('employees')->group(function () {
    Route::get('', [EmployeeController::class, 'index']);
    Route::get('get', [EmployeeController::class, 'show']);
});

Route::prefix('leaves')->group(function () {
    Route::get('{employeeNawartUuid?}', [LeaveController::class, 'index']);
    Route::post('', [LeaveController::class, 'store']);
    Route::put('{leave?}', [LeaveController::class, 'update']);
    Route::delete('{leave?}', [LeaveController::class, 'destroy']);
});

Route::prefix('departments')->group(function () {
    Route::get('', [DepartmentController::class, 'index']);
});

Route::prefix('locations')->group(function () {
    Route::get('', [LocationController::class, 'index']);
});

Route::prefix('shifts')->group(function () {
    Route::get('', [ShiftController::class, 'index']);
});

Route::prefix('attendances')->group(function () {
    Route::get('employees/{employee}', EmployeeAttendanceController::class);
});
