<?php

use App\Http\Controllers\MonitorCheckinController;
use App\Http\Controllers\MonitorCheckoutController;
use App\Models\Employee;
use App\Support\ApiResponse;
use Illuminate\Support\Facades\Route;

Route::redirect('/', config('services.frontend_url'));

Route::get('up', function () {
    Employee::exists();

    return new ApiResponse();
});

Route::get('checkin/up', MonitorCheckinController::class);
Route::get('checkout/up', MonitorCheckoutController::class);
