<?php

use App\Http\Controllers\Mobile\V2\ActiveAttendanceRecordController;
use App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController;
use App\Http\Controllers\Mobile\V2\ApprovalRequests\CurrentEmployeeApprovalRequestController;
use App\Http\Controllers\Mobile\V2\ApprovalRequests\GetMyApprovalRequestsController;
use App\Http\Controllers\Mobile\V2\AttendanceController;
use App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController;
use App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckoutController;
use App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController;
use App\Http\Controllers\Mobile\V2\CheckInOut\CheckoutController;
use App\Http\Controllers\Mobile\V2\CurrentEmployeeAttendanceController;
use App\Http\Controllers\Mobile\V2\DeleteAllNotificationsController;
use App\Http\Controllers\Mobile\V2\EmployeeDetailsController;
use App\Http\Controllers\Mobile\V2\Leaves\CreateLeaveRequestController;
use App\Http\Controllers\Mobile\V2\Leaves\CurrentEmployeeLeavesController;
use App\Http\Controllers\Mobile\V2\Leaves\GetMyLeaveRequestsController;
use App\Http\Controllers\Mobile\V2\LocationController;
use App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController;
use App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApproveApprovalRequestController;
use App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\GetPendingApprovalRequestsController;
use App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\MarkApprovalRequestAsApprovedController;
use App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\MarkApprovalRequestAsRejectedController;
use App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\RejectApprovalRequestController;
use App\Http\Controllers\Mobile\V2\Manager\AttachLocationToEmployeesController;
use App\Http\Controllers\Mobile\V2\Manager\AttachShiftToEmployeesController;
use App\Http\Controllers\Mobile\V2\Manager\AttendanceOverviewController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\DetachLocationFromEmployeeController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\DetachShiftFromEmployeeController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeApprovalRequestController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeAttendanceController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeBalanceController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeLeaveController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeSummaryController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\GetEmployeesListController;
use App\Http\Controllers\Mobile\V2\Manager\Employee\GetEmployeeSummaryController;
use App\Http\Controllers\Mobile\V2\Manager\Leaves\ApproveLeaveController;
use App\Http\Controllers\Mobile\V2\Manager\Leaves\GetPendingLeaveRequestsController;
use App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController;
use App\Http\Controllers\Mobile\V2\Manager\Leaves\MarkLeaveRequestAsApprovedController;
use App\Http\Controllers\Mobile\V2\Manager\Leaves\MarkLeaveRequestAsRejectedController;
use App\Http\Controllers\Mobile\V2\Manager\Leaves\RejectLeaveController;
use App\Http\Controllers\Mobile\V2\MarkAllNotificationAsReadController;
use App\Http\Controllers\Mobile\V2\MobileVersionController;
use App\Http\Controllers\Mobile\V2\NotificationController;
use App\Http\Controllers\Mobile\V2\ProofController;
use App\Http\Controllers\Mobile\V2\ProofOfDateController;
use App\Http\Controllers\Mobile\V2\RequestApproversController;
use App\Http\Controllers\Mobile\V2\RespondToProofController;
use App\Http\Controllers\Mobile\V2\ScheduledLocationsController;
use App\Http\Controllers\Mobile\V2\ScheduledShiftsController;
use App\Http\Controllers\Mobile\V2\ShiftController;
use App\Http\Controllers\Mobile\V2\SummaryOfMonthController;
use App\Http\Controllers\Mobile\V2\SwitchLanguageController;
use App\Http\Controllers\Mobile\V2\TenantController;
use App\Http\Controllers\Mobile\V2\UnreadNotificationsCountController;
use App\Http\Middleware\SetSentryUser;
use App\Http\Middleware\VerifyTOTP;

Route::get('version', MobileVersionController::class);

Route::middleware([
    'auth:jwt',
    'localization',
    'active-employee',
    'valid-device-id',
    'track-employee-last-activity',
    SetSentryUser::class,
])->group(function () {
    Route::get('tenant', TenantController::class);

    Route::middleware(VerifyTOTP::class)->group(function () {
        Route::post('can_check_in', CanCheckInController::class);
        Route::post('checkin', CheckInController::class);
        Route::post('can_check_out', CanCheckoutController::class);
        Route::post('checkout', CheckoutController::class);
    });

    Route::put('in_mood', [AttendanceController::class, 'updateInMood']);
    Route::put('out_mood', [AttendanceController::class, 'updateOutMood']);

    Route::put('lang', SwitchLanguageController::class);

    Route::prefix('employees')->group(function () {
        Route::get('scheduled-locations', [ScheduledLocationsController::class, 'index']);
        Route::get('scheduled-shifts', [ScheduledShiftsController::class, 'index']);
        Route::get('current', EmployeeDetailsController::class);
        Route::get('attendances/active', ActiveAttendanceRecordController::class);
        Route::get('balance', EmployeeBalanceController::class);

        Route::get('request-approvers', RequestApproversController::class);

        Route::put('attach-shift/{shift}', AttachShiftToEmployeesController::class);
        Route::put('attach-location/{location?}', AttachLocationToEmployeesController::class);
    });

    Route::post('proofs/response', RespondToProofController::class);
    Route::get('proofs', [ProofController::class, 'index']);
    Route::get('proofs-of-date', [ProofOfDateController::class, 'index']);

    Route::get('summary/weekly_average', [AttendanceController::class, 'weeklyAttendanceSummary']);
    Route::get('summary/days', [AttendanceController::class, 'daysAttendanceSummary']);

    Route::get('attendances/current', [CurrentEmployeeAttendanceController::class, 'index']);

    Route::get('notifications', [NotificationController::class, 'index']);
    Route::get('notifications/unread-count', UnreadNotificationsCountController::class);
    Route::put('notifications/mark-as-read', MarkAllNotificationAsReadController::class);
    Route::delete('notifications', DeleteAllNotificationsController::class);
    Route::get('shifts', [ShiftController::class, 'index']);
    Route::get('locations', [LocationController::class, 'index']);

    Route::get('approval_requests/employee_requests', GetMyApprovalRequestsController::class);
    Route::get('approval-requests/current', [
        CurrentEmployeeApprovalRequestController::class,
        'index',
    ]);

    Route::post(
        'approval_requests/create_approval_request',
        CreateApprovalRequestController::class
    );

    Route::post('leaves/create_leave_request', CreateLeaveRequestController::class);
    Route::post('leaves', [\App\Http\Controllers\Mobile\V2\Leaves\LeaveController::class, 'store']);

    Route::get('leaves/employee_requests', GetMyLeaveRequestsController::class);
    Route::get('leaves/current', [CurrentEmployeeLeavesController::class, 'index']);

    Route::get('summary/of-month', SummaryOfMonthController::class);

    // Manager APIs
    Route::get('leaves/pending', GetPendingLeaveRequestsController::class);
    Route::put('leaves/{leave}/reject', MarkLeaveRequestAsRejectedController::class);
    Route::put('leaves/{leave}/approve', MarkLeaveRequestAsApprovedController::class);

    Route::get('approval_requests/pending', GetPendingApprovalRequestsController::class);
    Route::put(
        'approval_requests/{approvalRequest}/approve',
        MarkApprovalRequestAsApprovedController::class
    );
    Route::put(
        'approval_requests/{approvalRequest}/reject',
        MarkApprovalRequestAsRejectedController::class
    );

    Route::prefix('manager')->group(function () {
        Route::get('get-employees-list', GetEmployeesListController::class);

        Route::prefix('employees')->group(function () {
            Route::get('', [EmployeeController::class, 'index']);
            Route::get('{employee}', [EmployeeController::class, 'show']);

            Route::delete(
                '{employee}/locations/{locationableID}',
                DetachLocationFromEmployeeController::class
            );

            Route::get('{employee}/approval-requests', [
                EmployeeApprovalRequestController::class,
                'index',
            ]);
            Route::delete(
                '{employee}/shifts/{employeeShiftID}',
                DetachShiftFromEmployeeController::class
            );

            Route::get('{employee}/attendances', [EmployeeAttendanceController::class, 'index']);
            Route::get('{employee}/leaves', [EmployeeLeaveController::class, 'index']);
        });

        Route::get('employee-summary/{employee}', EmployeeSummaryController::class);
        Route::get('get-employee-summary', GetEmployeeSummaryController::class);

        Route::get('attendance-overview', AttendanceOverviewController::class);

        Route::prefix('approval-requests')->group(function () {
            Route::get('', [ApprovalRequestController::class, 'index']);

            Route::put('approve', ApproveApprovalRequestController::class);
            Route::put('reject', RejectApprovalRequestController::class);
        });

        Route::prefix('leaves')->group(function () {
            Route::get('', [LeaveController::class, 'index']);

            Route::put('approve', ApproveLeaveController::class);
            Route::put('reject', RejectLeaveController::class);
        });
    });
});
