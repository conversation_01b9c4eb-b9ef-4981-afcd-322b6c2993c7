{"compilerOptions": {"allowJs": true, "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-jsx", "strict": true, "skipDefaultLibCheck": true, "skipLibCheck": true, "isolatedModules": true, "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "paths": {"@/*": ["./resources/js/*"], "ziggy-js": ["./vendor/tightenco/ziggy"]}}, "include": ["resources/js/**/*.ts", "resources/js/**/*.tsx", "resources/js/**/*.d.ts"], "exclude": ["node_modules", "public"]}