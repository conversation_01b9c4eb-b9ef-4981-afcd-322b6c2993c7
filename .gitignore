/node_modules
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.phpunit.result.cache
docker-compose.override.yml
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/.idea
/.vscode
/.blueprint
/public/js/app.js.LICENSE.txt
*.DS_Store
/auth.json
/public/js/
/public/css/
# exclude PHPStorm run configuration
/.run/
/tests/Feature/Exports/test-report*.xlsx
/.scribe
/resources/views/scribe/index.blade.php
/public/vendor/scribe
