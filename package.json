{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@prettier/plugin-php": "^0.22.2", "@tailwindcss/forms": "^0.5.2", "@types/react": "^18.3.11", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^1.6.8", "flowbite": "^2.2.0", "laravel-vite-plugin": "^1.0.2", "postcss": "^8.4.38", "prettier": "^3.3.3", "tailwindcss": "^3.1.0", "vite": "^5.2.10", "vite-plugin-mkcert": "^1.17.5"}, "dependencies": {"@inertiajs/react": "^1.0.16", "@vitejs/plugin-react": "^4.2.1", "hugeicons-react": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-otp-input": "^3.1.1", "react-timer-hook": "^3.0.7"}}