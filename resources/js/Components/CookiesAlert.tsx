import React from "react";
import { useTranslation } from "@/translations.js";

export default function CookiesAlert() {
    const { t } = useTranslation();

    return (
        <div>
            <div>
                <div
                    className="mt-4 bg-red-100 border border-red-300 text-red-700 text-sm px-2 py-2 rounded-lg relative"
                    role="alert"
                >
                    <div className="block sm:inline font-medium">
                        {t(
                            "Cookies are disabled in your browser. Please enable cookies to continue.",
                        )}
                    </div>
                    <div className="mt-4 text-sm">{t("How to enable cookies")}</div>

                    <a
                        href="https://support.google.com/accounts/answer/61416?hl=ar&co=GENIE.Platform%3DAndroid&oco=1"
                        target="_blank"
                        className="underline inline-block mb-1"
                    >
                        {t("Chrome")}
                    </a>
                    <br />
                    <a
                        href="https://www.apple.com/legal/privacy/ar/cookies"
                        target="_blank"
                        className="underline"
                    >
                        {t("Safari")}
                    </a>
                </div>
            </div>
        </div>
    );
}
