import { usePage } from "@inertiajs/react";

export const dictionaries = {
    Welcome: {
        en: "Welcome!",
        ar: "مرحبا!",
    },
    "Login with your company credentials": {
        en: "Login with your company credentials",
        ar: "قم بتسجيل الدخول ببيانات اعتماد شركتك ",
    },
    "Email or Mobile number": {
        en: "Email or Mobile number",
        ar: "البريد الإلكتروني أو رقم الجوال",
    },
    Email: {
        en: "Email",
        ar: "البريد الإلكتروني",
    },
    Phone: {
        en: "Phone",
        ar: "رقم الجوال",
    },
    Login: {
        en: "Login",
        ar: "تسجيل الدخول",
    },
    "Verify your number": {
        en: "Verify your number",
        ar: "تحقق من رقمك",
    },
    "Verify your email": {
        en: "Verify your email",
        ar: "تحقق من بريدك الإلكتروني",
    },
    "Enter the verification code that was sent to": {
        en: "Enter the verification code that was sent to",
        ar: "أدخل رمز التحقق الذي تم إرساله إلى",
    },
    "Resend Code in": {
        en: "Resend Code in",
        ar: "إعادة إرسال الرمز في",
    },
    Seconds: {
        en: "Seconds",
        ar: "ثواني",
    },
    "Resend Code": {
        en: "Resend Code",
        ar: "إعادة إرسال الرمز",
    },
    "Resend Code via Email": {
        en: "Resend Code via Email",
        ar: "إعادة إرسال الرمز عبر البريد الإلكتروني",
    },
    "Cookies are disabled in your browser. Please enable cookies to continue.": {
        en: "Cookies are disabled in your browser. Please enable cookies to continue.",
        ar: "ملفات تعريف الارتباط (الكوكيز Cookies) معطلة في متصفحك. يرجى تفعيلها للمتابعة.",
    },
    "How to enable cookies": {
        en: "How to enable cookies",
        ar: "كيفية تفعيل ملفات تعريف الارتباط (الكوكيز Cookies)",
    },
    Chrome: {
        en: "Chrome",
        ar: "كروم",
    },
    Safari: {
        en: "Safari",
        ar: "سفاري",
    },
    "Select Company": {
        en: "Select Company",
        ar: "اختر الشركة",
    },
} as const;

export const useTranslation = () => {
    const locale = usePage().props.locale as "ar" | "en";

    const t = (key: keyof typeof dictionaries) => dictionaries[key]?.[locale] ?? key;

    return { t };
};
