import React, { useEffect, useState } from "react";
import GuestLayout from "../../Layouts/GuestLayout";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";
import { Head, useForm } from "@inertiajs/react";
import CookiesAlert from "@/Components/CookiesAlert";
import { useTranslation } from "@/translations";

export default function IdentifierForm() {
    const [showCookieError, setShowCookieError] = useState(false);

    const { data, setData, post, errors } = useForm({ identifier: "" });

    const { t } = useTranslation();

    const submit = (e) => {
        e.preventDefault();

        post(route("identifier-form.store"));
    };

    useEffect(() => {
        try {
            // Create cookie
            document.cookie = "cookietest=1";

            const result = document.cookie.indexOf("cookietest=") !== -1;
            // Delete cookie
            document.cookie = "cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT";

            setTimeout(() => setShowCookieError(!result), 750);
        } catch (e) {
            setShowCookieError(true);
        }
    }, []);

    return (
        <GuestLayout>
            <Head title={t("Login")} />
            <div className="mb-6">
                <p className="text-2xl">{t("Welcome")}</p>
                <p className="text-sm leading-tight font-normal text-gray-500">
                    {t("Login with your company credentials")}
                </p>
                {showCookieError && <CookiesAlert />}
            </div>

            <form onSubmit={submit}>
                <div>
                    <div>
                        <label
                            htmlFor={"identifier"}
                            className={`block font-medium text-sm text-gray-700`}
                        >
                            {t("Email or Mobile number")}
                        </label>
                        <TextInput
                            id="identifier"
                            dir="ltr"
                            className="block mt-1 w-full"
                            type="identifier"
                            name="identifier"
                            isFocused={true}
                            value={data.identifier}
                            autoComplete="username"
                            onChange={(e) => setData("identifier", e.target.value)}
                        />
                        <InputError message={errors.identifier} className="mt-2" />
                    </div>
                </div>
                <div className="mt-9">
                    <PrimaryButton className="w-full">{t("Login")}</PrimaryButton>
                </div>
            </form>
        </GuestLayout>
    );
}
