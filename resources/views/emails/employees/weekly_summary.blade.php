@php
    $direction = $employee->preferred_language == 'ar'? 'rtl' : 'ltr';
    $direction_start = $employee->preferred_language == 'ar'? 'right' : 'left';
    $direction_end = $employee->preferred_language == 'ar'? 'left' : 'right';
@endphp

        <!doctype html>
<html lang="{{$employee->preferred_language}}" dir="{{$direction}}"
      xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
      xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <title>{{__('email_template.weekly_summary.title', locale: $employee->preferred_language)}}
        - {{ $periodStart->format('F dS, Y') }}</title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type="text/css">
        #outlook a {
            padding: 0;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        p {
            display: block;
            margin: 13px 0;
        }

    </style>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG />
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <!--[if lte mso 11]>
    <style type="text/css">
        .mj-outlook-group-fix {
            width: 100% !important;
        }
    </style>
    <![endif]-->
    <!--[if !mso]><!-->
    <link href="https://fonts.googleapis.com/css?family=Lato:400,700" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700" rel="stylesheet" type="text/css">
    <style type="text/css">
        @import url(https://fonts.googleapis.com/css?family=Lato:400,700);
        @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);

    </style>
    <!--<![endif]-->
    <style type="text/css">
        @media only screen and (min-width: 480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }

            .mj-column-per-30 {
                width: 30% !important;
                max-width: 30%;
            }

            .mj-column-per-70 {
                width: 70% !important;
                max-width: 70%;
            }

            .mj-column-per-50 {
                width: 50% !important;
                max-width: 50%;
            }

            .mj-column-per-33-333333333333336 {
                width: 33.333333333333336% !important;
                max-width: 33.333333333333336%;
            }
        }

    </style>
    <style media="screen and (min-width:480px)">
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%;
        }

        .moz-text-html .mj-column-per-30 {
            width: 30% !important;
            max-width: 30%;
        }

        .moz-text-html .mj-column-per-70 {
            width: 70% !important;
            max-width: 70%;
        }

        .moz-text-html .mj-column-per-50 {
            width: 50% !important;
            max-width: 50%;
        }

        .moz-text-html .mj-column-per-33-333333333333336 {
            width: 33.333333333333336% !important;
            max-width: 33.333333333333336%;
        }

    </style>
    <style type="text/css">
        @media only screen and (max-width: 479px) {
            table.mj-full-width-mobile {
                width: 100% !important;
            }

            td.mj-full-width-mobile {
                width: auto !important;
            }
        }

    </style>
    <style type="text/css">
        h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
        }

    </style>
</head>

<body style="word-spacing:normal;background-color:#eeeeee;">
<div style="background-color:#eeeeee;" lang="{{$employee->preferred_language}}"
     dir="{{$direction}}">
    {{-- start of margin before mail body --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="#eeeeee">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:#eeeeee;background-color:#eeeeee;margin:0px auto;border-radius:0;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:#eeeeee;background-color:#eeeeee;width:100%;border-radius:0;">
            <tbody>
            <tr>
                <td style="border-left:0;border-right:0;direction:{{$direction}};font-size:0px;padding:0;padding-bottom:0;padding-left:49px;padding-right:49px;padding-top:0;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="" style="vertical-align:top;width:502px;"><![endif]-->
                    <div class="mj-column-per-100 mj-outlook-group-fix"
                         style="font-size:0px;text-align:left; direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td style="font-size:0px;word-break:break-word;">
                                    <div style="height:15px;line-height:15px;">&#8202;</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of margin before mail body --}}

    {{-- start of mail body --}}

    {{-- start of header section --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="white">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto;border-radius:4px;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:white;background-color:white;width:100%;border-radius:4px;">
            <tbody>
            <tr>
                <td style="border-bottom:1px solid #dee7eb;border-left:1px solid #c7d0d4;border-right:1px solid #c7d0d4;border-top:1px solid #c7d0d4;direction:{{$direction}};font-size:0px;padding:20px 0;padding-bottom:33px;padding-left:49px;padding-right:49px;padding-top:33px;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr><![endif]-->

                    <!--[if mso | IE]>
                    <td class="" style="vertical-align:top;width:150px;"><![endif]-->
                    <div class="mj-column-per-30 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td align="{{$employee->preferred_language == 'ar'? 'right' : 'left'}}"
                                    style="font-size:0px;padding:0;word-break:break-word;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                                           style="border-collapse:collapse;border-spacing:0px;">
                                        <tbody>
                                        <tr>
                                            <td style="width:125px;">
                                                <img alt="Nawart" src="{{url('logo.svg')}}"
                                                     style="border:0;display:block;outline:none;text-decoration:none;height:29px;width:100%;font-size:13px;"
                                                     width="125px" height="29px" />
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td>
                    <td class="" style="vertical-align:top;width:350px;"><![endif]-->
                    <div class="mj-column-per-70 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$employee->preferred_language == 'ar'? 'left' : 'right'}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td align:{{$employee->preferred_language == 'ar'? 'left' : 'right'}} style="font-size:0px;padding:0;word-break:break-word;">
                                    <div
                                            style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1.22;text-align:{{$direction_end}};color:#2f2936;">
                                        <b> {{__('email_template.weekly_summary.heading', ['name' => $employee->name], $employee->preferred_language)}} </b>
                                        <br> {{ $periodStart->format('F dS, Y') }} - {{ $periodEnd->format('F dS, Y') }}
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td><![endif]-->
                    <!--[if mso | IE]></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of header section --}}

    {{-- start of Attendance summary section heading --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="white">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto;border-radius:4px;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:white;background-color:white;width:100%;border-radius:4px;">
            <tbody>
            <tr>
                <td style="border-left:1px solid #c7d0d4;border-right:1px solid #c7d0d4;direction:{{$direction}};font-size:0px;padding:20px 0;padding-bottom:30px;padding-left:49px;padding-right:49px;padding-top:30px;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr><![endif]-->

                    {{-- LTR --}}
                    <!--[if mso | IE]>
                    <td class="" style="vertical-align:top;width:250px;"><![endif]-->
                    <div class="mj-column-per-50 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$employee->preferred_language == 'ar'? 'right' : 'left'}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td align="{{$employee->preferred_language == 'ar'? 'right' : 'left'}}"
                                    style="font-size:0px;padding:0;word-break:break-word;">
                                    <div
                                            style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:{{$direction_start}};color:#2f2936;">
                                        <h4>{{__('email_template.weekly_summary.attendanceSummary.header.heading', locale: $employee->preferred_language)}}</h4>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td>
                    <td class="" style="vertical-align:top;width:250px;"><![endif]-->
                    <div class="mj-column-per-50 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$employee->preferred_language == 'ar'? 'left' : 'right'}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td align="{{$employee->preferred_language == 'ar'? 'left' : 'right'}}"
                                    style="font-size:0px;padding:0;word-break:break-word;">
                                    <div
                                            style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:{{$direction_end}};color:#2f2936;">
                                        <span
                                                style="border-radius: 3px; vertical-align: bottom; color: #fff; width: 16px; height: 16px; margin:{{$employee->preferred_language == 'ar'? '0 15px 0 5px' : '0 5px 0 15px'}}; display: inline-block; background-color: #34d399"></span>
                                        {{__('email_template.weekly_summary.attendanceSummary.header.present', ['count' => $presentCount], $employee->preferred_language)}}
                                        <span
                                                style="border-radius: 3px; vertical-align: bottom; color: #fff; width: 16px; height: 16px; margin:{{$employee->preferred_language == 'ar'? '0 15px 0 5px' : '0 5px 0 15px'}}; display: inline-block; background-color: #f87171">
                          </span> {{__('email_template.weekly_summary.attendanceSummary.header.absent', ['count' => $absentCount], $employee->preferred_language)}}
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td><![endif]-->
                    <!--[if mso | IE]></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of Attendance summary section heading --}}

    {{-- start of Attendance summary section content --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="white">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto;border-radius:4px;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:white;background-color:white;width:100%;border-radius:4px;">
            <tbody>
            <tr>
                <td style="border-left:1px solid #c7d0d4;border-right:1px solid #c7d0d4;direction:{{$direction}};font-size:0px;padding:20px 0;padding-bottom:20px;padding-left:49px;padding-right:49px;padding-top:0;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr><![endif]-->


                    <!--[if mso | IE]>
                    <td class="" style="vertical-align:bottom;width:166.66666666666669px;"><![endif]-->
                    <div class="mj-column-per-33-333333333333336 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:bottom;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
                            <tbody>
                            <tr>
                                <td style="vertical-align:bottom;padding:{{$employee->preferred_language == 'ar'? '0 0 0 15px' : '0 15px 0 0'}};">
                                    <table border="0" cellpadding="0" cellspacing="0" role="presentation" style=""
                                           width="100%">
                                        <tbody>
                                        @if(count($days))
                                            <tr>
                                                <td align="{{$direction_start}}"
                                                    style="font-size:0px;padding:0;word-break:break-word;">
                                                    <table cellpadding="0" cellspacing="0" width="100%" border="0"
                                                           style="color:#000000;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;table-layout:fixed;width:100%;border:none;">
                                                        <tr valign="bottom">
                                                            @foreach($days as $day)
                                                                <td style="padding:{{$employee->preferred_language == 'ar'? '0 0 0 5px' : '0 5px 0 0'}}">
                                                                    <div
                                                                            title="{{__('email_template.weekly_summary.attendanceSummary.body.lastWeekGraph.Data.present', locale: $employee->preferred_language)}}"
                                                                            style="background-color:#34d399;height:{{ $day['present'] }}px;"></div>
                                                                    <div
                                                                            title="{{__('email_template.weekly_summary.attendanceSummary.body.lastWeekGraph.Data.absent', locale: $employee->preferred_language)}}"
                                                                            style="background-color:#f87171;height:{{ $day['absent'] }}px;"></div>
                                                                </td>
                                                            @endforeach
                                                        </tr>
                                                        <tr style="color:#968ba0;font-size:smaller;"
                                                            align="center">
                                                            @foreach($days as $day)
                                                                <td style="padding:{{$employee->preferred_language == 'ar'? '3px 0 0 5px' : '3px 5px 0 0'}}">
                                                                    <small>{{ $day['label'] }}</small>
                                                                </td>
                                                            @endforeach
                                                        </tr>
                                                    </table>
                                                </td>
                                            </tr>
                                        @else
                                            <tr>
                                                <td align="{{$direction_start}}"
                                                    style="font-size:0px;padding:0;word-break:break-word">
                                                    <div
                                                            style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:{{$direction_start}};color:#968ba0;">
                                                        <small style="display:block;text-align:center;line-height:1.22">
                                                            {{__('email_template.weekly_summary.attendanceSummary.body.lastWeekGraph.noData', locale: $employee->preferred_language)}}
                                                        </small>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td>
                    <td class="" style="vertical-align:middle;width:166.66666666666669px;"><![endif]-->
                    <div class="mj-column-per-33-333333333333336 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:middle;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
                            <tbody>
                            <tr>
                                <td style="border-left:1px solid #dee7eb;border-right:1px solid #dee7eb;vertical-align:middle;padding-top:5px;padding-right:20px;padding-left:20px;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="presentation" style=""
                                           width="100%">
                                        <tbody>
                                        <tr>
                                            <td align="{{$direction_start}}"
                                                style="font-size:0px;padding:0;padding-bottom:5px;word-break:break-word;">
                                                <div
                                                        style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:35px;font-weight:300;line-height:1;text-align:{{$direction_start}};color:#2f2936;">
                                                    {{ $averageHours }}h
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="{{$direction_start}}"
                                                style="font-size:0px;padding:0;word-break:break-word;">
                                                <div
                                                        style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:{{$direction_start}};color:#968ba0;">
                                                    <small>{{__('email_template.weekly_summary.attendanceSummary.body.averageHours', locale: $employee->preferred_language)}}</small>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td>
                    <td class="" style="vertical-align:middle;width:166.66666666666669px;"><![endif]-->
                    <div class="mj-column-per-33-333333333333336 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_end}};direction:{{$direction}};display:inline-block;vertical-align:middle;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
                            <tbody>
                            <tr>
                                <td style="vertical-align:middle;padding:0;padding-top:5px;">
                                    <table border="0" cellpadding="0" cellspacing="0" role="presentation" style=""
                                           width="100%">
                                        <tbody>
                                        <tr>
                                            <td align="{{$direction_end}}"
                                                style="font-size:0px;padding:0;word-break:break-word;">
                                                <div
                                                        style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:35px;font-weight:300;line-height:1;text-align:{{$direction_end}};color:#2f2936;">
                                                    @if($percentChange > 0)
                                                        <image src="{{url('images/email/arrow-increase.png')}}"
                                                               alt="{{__('email_template.weekly_summary.attendanceSummary.body.percentageChange.increase.alt', locale: $employee->preferred_language)}}"
                                                               width="20px" height="10px"
                                                               style="vertical-align:middle"></image>
                                                    @elseif($percentChange < 0)
                                                        <image src="{{url('images/email/arrow-decrease.png')}}"
                                                               alt="{{__('email_template.weekly_summary.attendanceSummary.body.percentageChange.decrease.alt', locale: $employee->preferred_language)}}"
                                                               width="20px" height="10px"
                                                               style="vertical-align:middle"></image>
                                                    @endif
                                                    {{__('email_template.weekly_summary.attendanceSummary.body.percentageChange.percentageText', ['percentage' => $percentChange], $employee->preferred_language)}}
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="{{$direction_end}}"
                                                style="font-size:0px;padding:0;padding-top:5px;word-break:break-word;">
                                                <div
                                                        style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:{{$direction_end}};color:#968ba0;">
                                                    @if ($percentChange > 0)
                                                        <small>{{__('email_template.weekly_summary.attendanceSummary.body.percentageChange.increase.description', locale: $employee->preferred_language)}}</small>
                                                    @elseif ($percentChange < 0)
                                                        <small>{{__('email_template.weekly_summary.attendanceSummary.body.percentageChange.decrease.description', locale: $employee->preferred_language)}}</small>
                                                    @else
                                                        <small>{{__('email_template.weekly_summary.attendanceSummary.body.percentageChange.same.description', locale: $employee->preferred_language)}}</small>
                                                    @endif

                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td><![endif]-->
                    <!--[if mso | IE]></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of Attendance summary section content --}}

    {{-- start of Attendance details section header --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="white">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto;border-radius:4px;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:white;background-color:white;width:100%;border-radius:4px;">
            <tbody>
            <tr>
                <td style="border-left:1px solid #c7d0d4;border-right:1px solid #c7d0d4;direction:{{$direction}};font-size:0px;padding:0;padding-bottom:20px;padding-left:49px;padding-right:49px;padding-top:20px;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="" style="vertical-align:top;width:500px;"><![endif]-->
                    <div class="mj-column-per-100 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>

                            <tr>
                                <td align="{{$direction_start}}" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div
                                            style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:{{$direction_start}};color:#2f2936;">
                                        <h4>{{__('email_template.weekly_summary.attendanceDetails.header', locale: $employee->preferred_language)}}</h4>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of Attendance summary section header --}}

    {{-- start of Attendance details section content --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="white">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto;border-radius:4px;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:white;background-color:white;width:100%;border-radius:4px;">
            <tbody>
            <tr>
                <td style="border-left:1px solid #c7d0d4;border-right:1px solid #c7d0d4;direction:{{$direction}};font-size:0px;padding:20px 0;padding-bottom:20px;padding-left:49px;padding-right:49px;padding-top:0;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="" style="vertical-align:top;width:500px;"><![endif]-->
                    <div class="mj-column-per-100 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td align="{{$direction_start}}" style="font-size:0px;padding:0;word-break:break-word;">
                                    <table cellpadding="0" cellspacing="0" width="100%" border="0"
                                           style="color:#2f2936;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:16px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                        <thead>
                                        <tr style="text-align: {{$direction_start}}; background-color: #dddddd">
                                            <th style="border: 1px solid #dddddd;padding: 8px"> {{__('email_template.weekly_summary.attendanceDetails.body.table.heading.day', locale: $employee->preferred_language)}}</th>
                                            <th style="border: 1px solid #dddddd;padding: 8px"> {{__('email_template.weekly_summary.attendanceDetails.body.table.heading.firstIn', locale: $employee->preferred_language)}}</th>
                                            <th style="border: 1px solid #dddddd;padding: 8px"> {{__('email_template.weekly_summary.attendanceDetails.body.table.heading.lastOut', locale: $employee->preferred_language)}}</th>
                                            <th style="border: 1px solid #dddddd;padding: 8px"> {{__('email_template.weekly_summary.attendanceDetails.body.table.heading.totalHours', locale: $employee->preferred_language)}}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @if(count($days))
                                            @foreach($days as $day)
                                                <tr>
                                                    <td style="border: 1px solid #dddddd;padding: 8px;text-align: {{$direction_start}}">{{ $day['longLabel'] }}</td>

                                                    @if($day['absent'] != 0)
                                                        <td colspan="3"
                                                            style="border: 1px solid #dddddd;padding: 8px;text-align: center">
                                                            {{ __('email_template.weekly_summary.attendanceSummary.body.lastWeekGraph.Data.absent', locale: $employee->preferred_language) }}
                                                        </td>
                                                    @else
                                                        <td style="border: 1px solid #dddddd;padding: 8px;text-align: center">
                                                            {{ ($day['row']->in_type) ? $day['row']->check_in->format('h:i A') : '-' }}
                                                            @if ($day['row']->early_in != '-')
                                                                (
                                                                <span
                                                                        style="color: #22C55E">{{ $day['row']->early_in }}</span>
                                                                )
                                                            @elseif ($day['row']->late_in != '-')
                                                                (
                                                                <span
                                                                        style="color: #EF4444">{{ $day['row']->late_in }}</span>
                                                                )
                                                            @endif
                                                        </td>
                                                        <td style="border: 1px solid #dddddd;padding: 8px;text-align: center">
                                                            {{ $day['row']->out_type && $day['row']->check_out ? $day['row']->check_out->format('h:i A') : '-' }}
                                                            @if ($day['row']->early_out != '-')
                                                                (
                                                                <span
                                                                        style="color: #EF4444">{{ $day['row']->early_out }}</span>
                                                                )
                                                            @elseif ($day['row']->late_out != '-')
                                                                (
                                                                <span
                                                                        style="color: #22C55E">{{ $day['row']->late_out }}</span>
                                                                )
                                                            @endif
                                                        </td>
                                                        <td style="border: 1px solid #dddddd;padding: 8px;text-align: center">
                                                            {{ $day['row']->out_type && $day['row']->net_hours ? $day['row']->net_hours->format('H:i') : '-' }}
                                                        </td>
                                                    @endif
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="4" style="text-align: center">
                                                    <small
                                                            style="color:#968ba0;display:block;text-align:center;font-size: 13px">{{__('email_template.weekly_summary.attendanceDetails.body.table.body.noData', locale: $employee->preferred_language)}}</small>
                                                </td>
                                            </tr>
                                        @endif
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of Attendance details section content --}}

    {{-- start of 3 months history section header --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="white">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto;border-radius:4px;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:white;background-color:white;width:100%;border-radius:4px;">
            <tbody>
            <tr>
                <td style="border-left:1px solid #c7d0d4;border-right:1px solid #c7d0d4;direction:{{$direction}};font-size:0px;padding:20px 0;padding-bottom:25px;padding-left:49px;padding-right:49px;padding-top:20px;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="" style="vertical-align:top;width:250px;"><![endif]-->
                    <div class="mj-column-per-50 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td align="{{$direction_start}}" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div
                                            style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:{{$direction_start}};color:#2f2936;">
                                        <h4>{{__('email_template.weekly_summary.pastThreeYears.header.heading', locale: $employee->preferred_language)}}</h4>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td>
                    <td class="" style="vertical-align:bottom;width:250px;"><![endif]-->
                    <div class="mj-column-per-50 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:bottom;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:bottom;" width="100%">
                            <tbody>
                            <tr>
                                <td align="{{$direction_end}}" style="font-size:0px;padding:0;word-break:break-word;">
                                    <div
                                            style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:12px;font-weight:300;line-height:1;text-align:{{$direction_end}};color:#555555;">
                                        <p style="padding: 0; margin: 0">
                                            {{__('email_template.weekly_summary.pastThreeYears.header.fewerHours', locale: $employee->preferred_language)}}
                                            <span class="range"
                                                  style="font-size: 0; vertical-align: middle; margin: 0 6px; display: inline-block">
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #82f6cc; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #79eac0; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #70deb5; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #67d2a9; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #5dc79e; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #54bb93; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #4bb088; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #42a57d; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #399972; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #308e68; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #26845e; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #1c7953; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #106e4a; height: 5px"> &nbsp; </span>
                              <span
                                      style="width: 5px; font-weight: 300; display: inline-block; background-color: #016440; height: 5px"> &nbsp; </span>
                            </span> {{__('email_template.weekly_summary.pastThreeYears.header.moreHours', locale: $employee->preferred_language)}}
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of 3 months history section header --}}

    {{-- start of 3 months history section body --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="white">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto;border-radius:4px;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:white;background-color:white;width:100%;border-radius:4px;">
            <tbody>
            <tr>
                <td style="border-bottom:1px solid #c7d0d4;border-left:1px solid #c7d0d4;border-right:1px solid #c7d0d4;direction:{{$direction}};font-size:0px;padding:20px 0;padding-bottom:0;padding-left:49px;padding-right:49px;padding-top:0;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="" style="vertical-align:top;width:166.66666666666669px;"><![endif]-->
                    @foreach($calendar as $month)

                        @if(!$loop->first)
                        <!--[if mso | IE]></td>
                    <td class="" style="vertical-align:top;width:166.66666666666669px;"><![endif]-->
                    @endif

                    <div class="mj-column-per-33-333333333333336 mj-outlook-group-fix"
                         style="font-size:0px;text-align:{{$direction_start}};direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
                            <tbody>
                            <tr>
                                <td style="vertical-align:top;padding:{{$employee->preferred_language == 'ar'? '0 0 20px 10px' : '0 10px 20px 0'}}">
                                    <table border="0" cellpadding="0" cellspacing="0" role="presentation" style=""
                                           width="100%">
                                        <tbody>
                                        <tr>
                                            <td align="center"
                                                style="font-size:0px;padding:0;padding-bottom:10px;word-break:break-word;">
                                                <div
                                                        style="font-family:'Lato', 'Helvetica Neue', helvetica, sans-serif;font-size:14px;font-weight:300;line-height:1;text-align:center;color:#2f2936;">
                                                    <h5 style="margin: 0;font-size: 14px; font-weight: 700">
                                                        {{ $month['label'] }}
                                                    </h5>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="{{$direction_start}}"
                                                style="font-size:0px;padding:0;word-break:break-word;">
                                                <table cellpadding="0" cellspacing="0" width="100%" border="0"
                                                       style="color:#848296;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:12px;line-height:22px;table-layout:fixed;width:100%;border:none;">
                                                    <tbody>
                                                    <tr style="text-align: center">
                                                        @foreach($dayLabels as $label)
                                                            <th style="padding: 0 0 10px; font-weight: 300">{{ $label }}</th>
                                                        @endforeach
                                                    </tr>

                                                    @foreach(array_chunk($month['days'], count($dayLabels)) as $week)
                                                        <tr style="font-weight: 300;text-align: {{$direction_start}}">
                                                            @foreach($week as $day)
                                                                @if($day == -1)
                                                                    <td style="padding:{{$employee->preferred_language == 'ar'? '0 0 5px 5px' : '0 5px 5px 0'}}; width: 14.2%; margin: 0"></td>
                                                                @else
                                                                    <td title="{{$day["label"]}}: {{ $day['value'] }}       "
                                                                        style="padding:{{$employee->preferred_language == 'ar'? '0 0 5px 5px' : '0 5px 5px 0'}}; width: 14.2%; margin: 0">
                                                                        <span style="width: 100%; display: inline-block; background-color: {{$day['color']}}; height: 20px"> &nbsp; </span>
                                                                    </td>
                                                                @endif
                                                            @endforeach
                                                        </tr>
                                                    @endforeach
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    @endforeach
                    <!--[if mso | IE]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of 3 months history section body --}}

    {{-- start of margin after mail body --}}
    <!--[if mso | IE]>
    <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:600px;"
           width="600" bgcolor="#eeeeee">
        <tr>
            <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    {{-- end of margin after mail body --}}

    <div style="background:#eeeeee;background-color:#eeeeee;margin:0px auto;border-radius:0;max-width:600px;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
               style="background:#eeeeee;background-color:#eeeeee;width:100%;border-radius:0;">
            <tbody>
            <tr>
                <td style="border-left:0;border-right:0;direction:{{$direction}};font-size:0px;padding:0;padding-bottom:0;padding-left:49px;padding-right:49px;padding-top:0;text-align:center;">
                    <!--[if mso | IE]>
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="" style="vertical-align:top;width:502px;"><![endif]-->
                    <div class="mj-column-per-100 mj-outlook-group-fix"
                         style="font-size:0px;text-align:left;direction:{{$direction}};display:inline-block;vertical-align:top;width:100%;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                               style="vertical-align:top;" width="100%">
                            <tbody>
                            <tr>
                                <td style="font-size:0px;word-break:break-word;">
                                    <div style="height:15px;line-height:15px;">&#8202;</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--[if mso | IE]></td></tr></table><![endif]-->
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    {{-- end of mail body --}}
</div>
</body>

</html>
