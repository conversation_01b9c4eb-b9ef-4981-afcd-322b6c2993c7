<div>
    <div class="border p-2 rounded overflow-scroll" style="overflow: auto">
        <div class="font-bold border-b text-sm">Saml Config</div>
        <div class="p-2">
            <strong>idp_entity_id</strong>
            <p class="pl-1">Example https://example.com/saml/metadata</p>
        </div>
        <div class="p-2">
            <strong>idp_login_url</strong>
            <p class="pl-1">Example https://example.com/saml/metadata</p>
        </div>
        <div class="p-2">
            <strong>name_id_format</strong>
            <p class="pl-1">Example https://example.com/saml/metadata</p>
        </div>
        <div class="p-2">
            <strong>idp_x509_cert</strong>
            <p class="pl-1">The idp x509 certificate * Without -----BEGIN CERTIFICATE----- and -----<PERSON><PERSON> CERTIFICATE----- *</p>
        </div>
        <div class="p-2">
            <strong>sign_message</strong>
            <p class="pl-1">Default <span style="color: blue">true</span></p>
        </div>
        <div class="p-2">
            <strong>email_claim</strong>
            <p class="pl-1">Default http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</p>
        </div>
        <div class="p-2">
            <strong>use_name_id_for_authentication</strong>
            <p class="pl-1">Default <span style="color: blue">true</span></p>
        </div>
        <div class="p-2">
            <strong>first_name_claim</strong>
            <p class="pl-1">Default http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</p>
        </div>
        <div class="p-2">
            <strong>last_name_claim</strong>
            <p class="pl-1">the last name claim (string)</p>
        </div>
        <div class="p-2">
            <strong>employee_number_claim</strong>
            <p class="pl-1">the employee number of in the company (string)</p>
        </div>
        <div class="p-2">
            <strong>position_claim</strong>
            <p class="pl-1">the position of the employee (string)</p>
        </div>
        <div class="p-2">
            <strong>phone_claim</strong>
            <p class="pl-1">the phone number of the employeee (string)</p>
        </div>
        <div class="p-2">
            <strong>department_name_claim</strong>
            <p class="pl-1">the department name ( will search database for department name and attachet to employee (string)</p>
        </div>
    </div>
</div>
