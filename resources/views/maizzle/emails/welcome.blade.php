<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: "Segoe UI", sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
  <title>Welcome - نورتنا</title>
  <style>
    @media (max-width: 600px) {
      .sm-mb-4 {
        margin-bottom: 16px !important
      }
      .sm-mt-8 {
        margin-top: 32px !important
      }
      .sm-px-4 {
        padding-left: 16px !important;
        padding-right: 16px !important
      }
      .sm-px-6 {
        padding-left: 24px !important;
        padding-right: 24px !important
      }
      .sm-leading-8 {
        line-height: 32px !important
      }
    }
  </style>
</head>
<body style="margin: 0; width: 100%; background-color: #f9fafb; padding: 0; -webkit-font-smoothing: antialiased; word-break: break-word">
  <div role="article" aria-roledescription="email" aria-label="Welcome - نورتنا">
    <div class="sm-px-4" style="background-color: #f8fafc; padding: 32px; font-family: ui-sans-serif, system-ui, -apple-system, 'Segoe UI', sans-serif; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
      <table align="center" cellpadding="0" cellspacing="0" role="none">
        <tr>
          <td style="width: 552px; max-width: 100%; background-color: #fffffe; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)">
            <div class="sm-mt-8 sm-mb-4" style="margin-bottom: 24px; margin-top: 48px; text-align: center">
              <img src="{{asset('images/logo.png')}}" alt="Nawart" style="max-width: 100%; vertical-align: middle; margin-left: auto; margin-right: auto; display: block; height: 44px; width: auto">
            </div>
            <table style="width: 100%; text-align: center;" cellpadding="0" cellspacing="0" role="none">
              <tr>
                <td class="sm-px-6" style="border-radius: 4px; background-color: #fffffe; padding: 48px; font-size: 16px; color: #334155; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
                  <h1 class="sm-leading-8" style="margin: 0 0 24px; font-size: 30px; font-weight: 600; color: #111827">نورتنا</h1>
                  <p style="font-size: 18px; font-weight: 400; color: #6b7280">
                    {{"اهلا بك $employee->first_name، تمت دعوتك لتطبيق نورت ونتطلع لوجودك معنا."}}
                  </p>
                  @if($todayInAr && $todayOutAr && $todayHours)
                  <p style="font-size: 14px; font-weight: 400; color: #6b7280">
                    {{"فترتك من: " . $todayInAr . " - " . $todayOutAr . " ($todayHours ساعات)"}}
                  </p>
                  @endif
                  <p style="font-size: 18px; font-weight: 700; color: #4b5563">{{$employee->team->name}}</p>
                  <div role="separator" style="line-height: 56px">
                    &zwj;
                  </div>
                  <h1 class="sm-leading-8" style="margin: 0 0 24px; font-size: 30px; font-weight: 600; color: #111827">Nawartna</h1>
                  <p style="font-size: 18px; font-weight: 400; color: #6b7280;">
                    {{"Hi $employee->first_name You have been added to the Nawart app, and we are
                  delighted to have you on board with us"}}
                  </p>
                  @if($todayInEn && $todayOutEn && $todayHours)
                  <p style="font-size: 14px; font-weight: 400; color: #6b7280;">
                    {{"Your shift: $todayInEn - $todayOutEn ($todayHours hours)"}}
                  </p>
                  @endif
                  <p style="font-size: 18px; font-weight: 700; color: #4b5563;">{{$employee->team->name}}</p>
                  <div role="separator" style="line-height: 79px">
                    &zwj;
                  </div>
                  <p style="font-size: 14px; color: #6b7280;">
                    قم بتحميل التطبيق لبدء رحلتك
                    <br>
                    <br>
                    To start your journey, download the app
                  </p>
                  <div role="separator" style="line-height: 24px">
                    &zwj;
                  </div> <a href="https://apps.apple.com/sa/app/%D9%86%D9%88%D8%B1%D8%AA/id1584154882">
                    <img src="/images/appstore.svg" width="120px" height="40px" alt="Download on the App Store" style="max-width: 100%; vertical-align: middle;">
                  </a>
                  <a href="https://play.google.com/store/apps/details?id=sa.tamkeentech.attendance.app&hl=ar&gl=US">
                    <img src="/images/playstore.svg" width="135px" height="40px" alt="Get it on Google Play" style="max-width: 100%; vertical-align: middle;">
                  </a>
                  <a href="https://appgallery.huawei.com/app/C109421831">
                    <img src="/images/appgallery.svg" width="133px" height="40px" alt="Explore it on AppGallery" style="max-width: 100%; vertical-align: middle;">
                  </a>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </div>
</body>
</html>