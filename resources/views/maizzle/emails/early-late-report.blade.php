@php
use App\Enums\EarlyLatePeriodPolicy;
/** @var \App\Models\Employee $receiver */
/**@var \App\DTOs\Stats\AttendanceStatusStats[] $attendancesStats */
/**@var \App\DTOs\Stats\EarlyLateStats[] $earlyLateStats */
/**@var \App\DTOs\Stats\EmployeesStats $employeesStats */
/**@var \App\Calculations\MostLeastCommittedAttendanceCalculator $mostLeastCommitted */
/** @var int $employeesCount */
/** @var EarlyLatePeriodPolicy $periodType */
/** @var array{key: string, value: float, emoji: string, title: string, description: string} $inMood */
/** @var array{key: string, value: float, emoji: string, title: string, description: string} $outMood */
@endphp
<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: "Segoe UI", sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
  <title>Early Late Report</title>
  <style>
    .space-x-2 > :not([hidden]) ~ :not([hidden]) {
      --tw-space-x-reverse: 0 !important;
      margin-right: calc(8px * var(--tw-space-x-reverse)) !important;
      margin-left: calc(8px * calc(1 - var(--tw-space-x-reverse))) !important
    }
    .space-x-6 > :not([hidden]) ~ :not([hidden]) {
      --tw-space-x-reverse: 0 !important;
      margin-right: calc(24px * var(--tw-space-x-reverse)) !important;
      margin-left: calc(24px * calc(1 - var(--tw-space-x-reverse))) !important
    }
    .space-y-2 > :not([hidden]) ~ :not([hidden]) {
      --tw-space-y-reverse: 0 !important;
      margin-top: calc(8px * calc(1 - var(--tw-space-y-reverse))) !important;
      margin-bottom: calc(8px * var(--tw-space-y-reverse)) !important
    }
    @media (max-width: 600px) {
      .sm-mb-4 {
        margin-bottom: 16px !important
      }
      .sm-mt-8 {
        margin-top: 32px !important
      }
      .sm-px-4 {
        padding-left: 16px !important;
        padding-right: 16px !important
      }
      .sm-leading-8 {
        line-height: 32px !important
      }
    }
  </style>
</head>
<body style="margin: 0; width: 100%; background-color: #f9fafb; padding: 0; -webkit-font-smoothing: antialiased; word-break: break-word">
  <div role="article" aria-roledescription="email" aria-label="Early Late Report">
    <div class="sm-px-4" style="background-color: #f8fafc; padding: 32px; font-family: ui-sans-serif, system-ui, -apple-system, 'Segoe UI', sans-serif; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
      <table align="center" cellpadding="0" cellspacing="0" role="none">
        <tr>
          <td style="background-color: #fffffe; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)">
            <div class="sm-mt-8 sm-mb-4" style="margin-bottom: 24px; margin-top: 48px; text-align: center">
              <img src="{{asset('images/logo.png')}}" alt="Nawart" style="max-width: 100%; vertical-align: middle; margin-left: auto; margin-right: auto; display: block; height: 44px; width: auto">
            </div>
            <table style="width: 100%; max-width: 640px; text-align: center" cellpadding="0" cellspacing="0" role="none">
              <tr>
                <td style="border-radius: 4px; background-color: #fffffe; padding: 48px 24px; font-size: 16px; color: #111827; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
                  <h1 class="sm-leading-8" style="margin-bottom: 12px; font-size: 30px; font-weight: 600; color: #111827">
                    {{ $periodType === EarlyLatePeriodPolicy::Monthly ? 'تقرير الفريق الشهري' : 'تقرير الفريق الأسبوعي' }}
                  </h1>
                  <h1 class="sm-leading-8" style="margin-bottom: 40px; font-size: 30px; font-weight: 600; color: #111827">
                    {{ $periodType === EarlyLatePeriodPolicy::Monthly ? 'Monthly Team Report' : 'Weekly Team Report' }}
                  </h1>
                  <div dir="rtl" style="margin-bottom: 16px; display: block; width: auto; text-align: start; font-size: 14px; font-weight: 400; color: #4b5563">
                    مرحبا “<strong>{{$receiver->name}}</strong>”,
                  </div>
                  <div dir="rtl" style="margin-bottom: 12px; display: block; width: auto; text-align: start; font-size: 14px; font-weight: 400; color: #4b5563;">
                    تقرير يضم {{$employeesCount}} موظفًا،
                    عن {{$periodType === EarlyLatePeriodPolicy::Monthly ?  'الشهر': 'الأسبوع'}} الماضي
                    <span>({{$from}}
                  , {{$to}})</span> جاهز الآن
                    للمراجعة، مع عرض متوسط ابرز الارقام.
                  </div>
                  <div style="margin-bottom: 16px; display: block; width: auto; text-align: start; font-size: 14px; font-weight: 400; color: #4b5563;">
                    Hello “<strong>{{$receiver->name}}</strong>”,
                  </div>
                  <div style="display: block; width: auto; text-align: start; font-size: 14px; font-weight: 400; color: #4b5563;">
                    {{$employeesCount}} employees updates for
                    last {{$periodType === EarlyLatePeriodPolicy::Monthly ?  'month': 'week'}}
                    ({{$from}}, {{$to}}) showing the
                    average, Is here
                    for you to review!
                  </div>
                  <div style="margin-top: 40px; display: flex; justify-content: center; column-gap: 24px">
                    <div dir="rtl" style="flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start">
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563">إجمالي الحضور</div>
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">Total Attendance</div>
                      <div style="margin-top: 8px; font-size: 24px; font-weight: 700">{{$attendancesStats['sum']->presentIncludingNonWorkDays() }}
                        %
                      </div>
                    </div>
                    <div dir="rtl" style="flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">الإجازات</div>
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">Leave</div>
                      <div style="margin-top: 8px; font-size: 24px; font-weight: 700;">{{$attendancesStats['sum']->leave}}%</div>
                    </div>
                    <div dir="rtl" style="flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">الغياب</div>
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">Absence</div>
                      <div style="margin-top: 8px; font-size: 24px; font-weight: 700;">{{$attendancesStats['sum']->absent}}%</div>
                    </div>
                  </div>
                  @if($periodType === EarlyLatePeriodPolicy::Weekly)
                  <div style="margin-top: 40px; border-radius: 8px; border: 1px solid #e5e7eb; padding: 16px">
                    <div style="display: flex; column-gap: 12px">
                      <div style="margin-top: -4px; display: flex; flex-direction: column; row-gap: 24px; text-wrap: nowrap; text-align: start; font-size: 12px; font-weight: 500; color: #6b7280">
                        <span>100%</span>
                        <span>80%</span>
                        <span>60%</span>
                        <span>40%</span>
                        <span>20%</span>
                        <span>0%</span>
                      </div>
                      <div style="height: 200px; width: 1px; background-color: #e5e7eb"></div>
                      <div>
                        <div style="display: flex; height: 200px; column-gap: 40px; text-wrap: nowrap">
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280">{{$attendancesStats['sunday']->present}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['sunday']->present, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['sunday']->leave}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['sunday']->leave, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #3b82f6"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['sunday']->absent}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['sunday']->absent, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #9ca3af"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0)">Sun</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['monday']->present}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['monday']->present, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['monday']->leave}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['monday']->leave, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #3b82f6;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['monday']->absent}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['monday']->absent, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #9ca3af;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Mon</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['tuesday']->present}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['tuesday']->present, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['tuesday']->leave}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['tuesday']->leave, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #3b82f6;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['tuesday']->absent}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['tuesday']->absent, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #9ca3af;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Tue</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['wednesday']->present}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['wednesday']->present, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['wednesday']->leave}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['wednesday']->leave, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #3b82f6;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['wednesday']->absent}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['wednesday']->absent, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #9ca3af;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Wed</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['thursday']->present}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['thursday']->present, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['thursday']->leave}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['thursday']->leave, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #3b82f6;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$attendancesStats['thursday']->absent}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($attendancesStats['thursday']->absent, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #9ca3af;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Thu</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div style="margin-bottom: 8px; margin-top: 12px; height: 1px; width: 100%; background-color: #e5e7eb"></div>
                    <div class="space-x-6" style="display: flex; justify-content: center;">
                      <div class="space-x-2" style="display: flex; align-items: center;">
                        <div style="height: 10px; width: 10px; border-radius: 9999px; background-color: #22c55e"></div>
                        <div style="display: flex; flex-direction: column; text-align: start; color: #6b7280; margin-right: calc(8px * 0); margin-left: calc(8px * calc(1 - 0));">
                          <span style="font-size: 14px; line-height: 20px">الحضور</span>
                          <span style="font-size: 14px; line-height: 20px;">Attendance</span>
                        </div>
                      </div>
                      <div class="space-x-2" style="display: flex; align-items: center; margin-right: calc(24px * 0); margin-left: calc(24px * calc(1 - 0));">
                        <div style="height: 10px; width: 10px; border-radius: 9999px; background-color: #3b82f6;"></div>
                        <div style="display: flex; flex-direction: column; text-align: start; color: #6b7280; margin-right: calc(8px * 0); margin-left: calc(8px * calc(1 - 0));">
                          <span style="font-size: 14px; line-height: 20px;">الإجازات</span>
                          <span style="font-size: 14px; line-height: 20px;">Leave</span>
                        </div>
                      </div>
                      <div class="space-x-2" style="display: flex; align-items: center; margin-right: calc(24px * 0); margin-left: calc(24px * calc(1 - 0));">
                        <div style="height: 10px; width: 10px; border-radius: 9999px; background-color: #9ca3af;"></div>
                        <div style="display: flex; flex-direction: column; text-align: start; color: #6b7280; margin-right: calc(8px * 0); margin-left: calc(8px * calc(1 - 0));">
                          <span style="font-size: 14px; line-height: 20px;">الغياب</span>
                          <span style="font-size: 14px; line-height: 20px;">Absence</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  @endif
                  <div style="margin-top: 40px; display: flex; justify-content: center; column-gap: 24px;">
                    <div dir="rtl" style="flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">منتظم</div>
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">On-Time</div>
                      <div style="margin-top: 8px; font-size: 24px; font-weight: 700;">{{$earlyLateStats['sum']->onTime}}
                        %
                      </div>
                    </div>
                    <div dir="rtl" style="flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">حضور متأخر</div>
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">Late Check-in</div>
                      <div style="margin-top: 8px; font-size: 24px; font-weight: 700;">{{$earlyLateStats['sum']->lateIn}}%</div>
                    </div>
                    <div dir="rtl" style="flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">انصراف مبكر</div>
                      <div style="font-size: 20px; line-height: 32px; color: #4b5563;">Early Check-out</div>
                      <div style="margin-top: 8px; font-size: 24px; font-weight: 700;">{{$earlyLateStats['sum']->earlyOut}}%</div>
                    </div>
                  </div>
                  @if($periodType === EarlyLatePeriodPolicy::Weekly)
                  <div style="margin-top: 40px; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px;">
                    <div style="display: flex; column-gap: 12px;">
                      <div style="display: flex; flex-direction: column; row-gap: 24px; text-wrap: nowrap; text-align: start; font-size: 12px; font-weight: 500; color: #6b7280;">
                        <span>100%</span>
                        <span>80%</span>
                        <span>60%</span>
                        <span>40%</span>
                        <span>20%</span>
                        <span>0%</span>
                      </div>
                      <div style="height: 200px; width: 1px; background-color: #e5e7eb;"></div>
                      <div>
                        <div style="display: flex; height: 200px; column-gap: 40px; text-wrap: nowrap;">
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['sunday']->onTime}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['sunday']->onTime, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['sunday']->lateIn}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['sunday']->lateIn, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #fb923c"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['sunday']->earlyOut}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['sunday']->earlyOut, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #f87171"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Sun</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['monday']->onTime}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['monday']->onTime, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['monday']->lateIn}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['monday']->lateIn, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #fb923c;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['monday']->earlyOut}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['monday']->earlyOut, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #f87171;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Mon</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['tuesday']->onTime}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['tuesday']->onTime, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['tuesday']->lateIn}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['tuesday']->lateIn, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #fb923c;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['tuesday']->earlyOut}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['tuesday']->earlyOut, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #f87171;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Tue</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['wednesday']->onTime}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['wednesday']->onTime, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['wednesday']->lateIn}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['wednesday']->lateIn, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #fb923c;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['wednesday']->earlyOut}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['wednesday']->earlyOut, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #f87171;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Wed</div>
                          </div>
                          <div class="space-y-2" style="display: flex; flex-direction: column; align-items: center; justify-content: flex-end;">
                            <div style="display: flex; align-items: flex-end; column-gap: 4px;">
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['thursday']->onTime}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['thursday']->onTime, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #22c55e;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['thursday']->lateIn}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['thursday']->lateIn, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #fb923c;"></div>
                              </div>
                              <div style="display: flex; flex-direction: column; align-items: center;">
                                <div style="margin-bottom: 8px; font-size: 12px; color: #6b7280;">{{$earlyLateStats['thursday']->earlyOut}}
                                  %
                                </div>
                                <div style="height: calc(160px * {{divide($earlyLateStats['thursday']->earlyOut, 100, min: 0.05)}}); width: 12px; border-top-left-radius: 9999px; border-top-right-radius: 9999px; background-color: #f87171;"></div>
                              </div>
                            </div>
                            <div style="font-size: 14px; color: #374151; margin-top: calc(8px * calc(1 - 0)); margin-bottom: calc(8px * 0);">Thu</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div style="margin-bottom: 8px; margin-top: 12px; height: 1px; width: 100%; background-color: #e5e7eb;"></div>
                    <div class="space-x-6" style="display: flex; justify-content: center;">
                      <div class="space-x-2" style="display: flex; align-items: center;">
                        <div style="height: 10px; width: 10px; border-radius: 9999px; background-color: #22c55e;"></div>
                        <div style="display: flex; flex-direction: column; text-align: start; color: #6b7280; margin-right: calc(8px * 0); margin-left: calc(8px * calc(1 - 0));">
                          <span style="font-size: 14px; line-height: 20px;">منتظم</span>
                          <span style="font-size: 14px; line-height: 20px;">On-Time</span>
                        </div>
                      </div>
                      <div class="space-x-2" style="display: flex; align-items: center; margin-right: calc(24px * 0); margin-left: calc(24px * calc(1 - 0));">
                        <div style="height: 10px; width: 10px; border-radius: 9999px; background-color: #fb923c;"></div>
                        <div style="display: flex; flex-direction: column; text-align: start; color: #6b7280; margin-right: calc(8px * 0); margin-left: calc(8px * calc(1 - 0));">
                          <span style="font-size: 14px; line-height: 20px;">حضور متأخر</span>
                          <span style="font-size: 14px; line-height: 20px;">Late Check-in</span>
                        </div>
                      </div>
                      <div class="space-x-2" style="display: flex; align-items: center; margin-right: calc(24px * 0); margin-left: calc(24px * calc(1 - 0));">
                        <div style="height: 10px; width: 10px; border-radius: 9999px; background-color: #f87171;"></div>
                        <div style="display: flex; flex-direction: column; text-align: start; color: #6b7280; margin-right: calc(8px * 0); margin-left: calc(8px * calc(1 - 0));">
                          <span style="font-size: 14px; line-height: 20px;">انصراف مبكر</span>
                          <span style="font-size: 14px; line-height: 20px;">Early Check-out</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  @endif
                  <div style="margin-top: 40px; display: flex; justify-content: center; column-gap: 24px;">
                    <div dir="rtl" style="margin-top: 40px; display: flex; width: 288px; height: 288px; flex: 1 1 0%; flex-direction: column; column-gap: 24px; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start">
                      <div style="font-size: 20px; font-weight: 500; line-height: 28px; color: #4b5563">العمل لساعات إضافية
                      </div>
                      <div style="font-size: 20px; font-weight: 500; line-height: 28px; color: #4b5563;">Additional Hours</div>
                      <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="position: relative; display: flex; width: 224px; height: 224px; align-items: center; justify-content: center"> <svg viewBox="0 0 36 36" style="height: 100%; width: 100%">
                            <circle stroke="currentColor" stroke-width="2.5" fill="transparent" r="16" cx="18" cy="18" style="color: #d1d5db"></circle>
                            <circle stroke="currentColor" stroke-width="2.5" stroke-dasharray="{{percentage($employeesStats->employeesHasAdditionalHoursCount, $employeesStats->total)}}, 100" fill="transparent" r="16" cx="18" cy="18" transform="rotate(-90 18 18)" style="color: #3b82f6"></circle>
                          </svg>
                          <div style="position: absolute; text-align: center">
                            <span style="font-size: 24px; font-weight: 700; color: #1f2937">{{percentage($employeesStats->employeesHasAdditionalHoursCount, $employeesStats->total)}}%</span>
                            <p style="font-size: 12px; color: #6b7280;">of the team worked<br>additional
                              Hours</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div dir="rtl" style="margin-top: 40px; display: flex; width: 288px; height: 288px; flex: 1 1 0%; flex-direction: column; column-gap: 24px; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="font-size: 20px; font-weight: 500; line-height: 28px; color: #4b5563;">طلبات الإستئذان</div>
                      <div style="font-size: 20px; font-weight: 500; line-height: 28px; color: #4b5563;">Permission Requests
                      </div>
                      <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="position: relative; display: flex; width: 224px; height: 224px; align-items: center; justify-content: center;"> <svg viewBox="0 0 36 36" style="height: 100%; width: 100%;">
                            <circle stroke="currentColor" stroke-width="2.5" fill="transparent" r="16" cx="18" cy="18" style="color: #d1d5db;"></circle>
                            <circle stroke="currentColor" stroke-width="2.5" stroke-dasharray="{{percentage($employeesStats->employeesHasPermissionRequestsCount, $employeesStats->total)}}, 100" fill="transparent" r="16" cx="18" cy="18" transform="rotate(-90 18 18)" style="color: #3b82f6;"></circle>
                          </svg>
                          <div style="position: absolute; text-align: center;">
                            <span style="font-size: 24px; font-weight: 700; color: #1f2937;">{{percentage($employeesStats->employeesHasPermissionRequestsCount, $employeesStats->total)}}%</span>
                            <p style="font-size: 12px; color: #6b7280;">
                              of the team submitted
                              <br>
                              a permission request
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div dir="rtl" style="margin-top: 40px; flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                    <div style="font-size: 20px; font-weight: 700; line-height: 28px; color: #4b5563;">أفضل 5 موظفين التزاماً</div>
                    <div style="margin-bottom: 28px; font-size: 20px; font-weight: 700; line-height: 28px; color: #4b5563">Top 5 Committed
                      Employees
                    </div>
                    <div>
                      <table dir="rtl" style="min-width: 100%; overflow: hidden; border-radius: 8px; border: 1px solid #e5e7eb; background-color: #fffffe" cellpadding="0" cellspacing="0" role="none">
                        <thead style="background-color: #f3f4f6; font-weight: 700">
                          <tr>
                            <th style="padding: 8px 16px; text-align: start; font-size: 12px; color: #4b5563">
                              الاسم
                              <br>
                              Name
                            </th>
                            <th style="padding: 8px 16px; text-align: start; font-size: 12px; color: #4b5563;">
                              معدل الحضور
                              <br>
                              ATTENDANCE RATE
                            </th>
                            <th style="padding: 8px 16px; text-align: start; font-size: 12px; color: #4b5563;">
                              الساعات غير المكتملة
                              <br>
                              INCOMPLETE HOURS
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          @foreach($mostLeastCommitted->mostCommittedList() as $stats)
                          <tr>
                            <td style="border: solid #e5e7eb; border-width: 0px 0px 1px; padding: 8px 16px; font-size: 14px; color: #1f2937">
                              {{$stats->employee->name}}
                            </td>
                            <td style="border: solid #e5e7eb; border-width: 0px 0px 1px; padding: 8px 16px; font-size: 14px; color: #1f2937;">
                              {{$stats->attendanceRatePercentage}}%
                            </td>
                            <td style="border: solid #e5e7eb; border-width: 0px 0px 1px; padding: 8px 16px; font-size: 14px; color: #1f2937;">
                              {{(int) round($stats->missingHours->totalHours)}}
                            </td>
                          </tr>
                          @endforeach
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div dir="rtl" style="margin-top: 40px; flex: 1 1 0%; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                    <div style="font-size: 20px; font-weight: 700; line-height: 28px; color: #4b5563;">أقل 5 موظفين التزاماً</div>
                    <div style="margin-bottom: 28px; font-size: 20px; font-weight: 700; line-height: 28px; color: #4b5563;">Least 5 Committed
                      Employees
                    </div>
                    <div>
                      <table dir="rtl" style="min-width: 100%; overflow: hidden; border-radius: 8px; border: 1px solid #e5e7eb; background-color: #fffffe;" cellpadding="0" cellspacing="0" role="none">
                        <thead style="background-color: #f3f4f6; font-weight: 700;">
                          <tr>
                            <th style="padding: 8px 16px; text-align: start; font-size: 12px; color: #4b5563;">
                              الاسم
                              <br>
                              Name
                            </th>
                            <th style="padding: 8px 16px; text-align: start; font-size: 12px; color: #4b5563;">
                              معدل الحضور
                              <br>
                              ATTENDANCE RATE
                            </th>
                            <th style="padding: 8px 16px; text-align: start; font-size: 12px; color: #4b5563;">
                              الساعات غير المكتملة
                              <br>
                              INCOMPLETE HOURS
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                        </tbody>
                        <tbody>
                          @foreach($mostLeastCommitted->leastCommittedList() as $stats)
                          <tr>
                            <td style="border: solid #e5e7eb; border-width: 0px 0px 1px; padding: 8px 16px; font-size: 14px; color: #1f2937;">
                              {{$stats->employee->name}}
                            </td>
                            <td style="border: solid #e5e7eb; border-width: 0px 0px 1px; padding: 8px 16px; font-size: 14px; color: #1f2937;">
                              {{$stats->attendanceRatePercentage}}%
                            </td>
                            <td style="border: solid #e5e7eb; border-width: 0px 0px 1px; padding: 8px 16px; font-size: 14px; color: #1f2937;">
                              {{(int) round($stats->missingHours->totalHours)}}
                            </td>
                          </tr>
                          @endforeach
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div style="margin-top: 40px; display: flex; justify-content: center; column-gap: 24px;">
                    <div dir="rtl" style="margin-top: 40px; display: flex; flex: 1 1 0%; flex-direction: column; column-gap: 24px; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="height: 80px">
                        <div style="font-size: 20px; font-weight: 400; line-height: 28px; color: #4b5563;">الحالة المزاجية للموظفين عند الخروج</div>
                        <div style="font-size: 20px; font-weight: 400; line-height: 28px; color: #4b5563;">Employees Check-out Mood</div>
                      </div>
                      <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="position: relative; margin-top: 24px; display: flex; width: 224px; height: 224px; align-items: center; justify-content: center; column-gap: 8px">
                          <div style="display: flex; flex-direction: column; align-items: center;">
                            <img src="{{$outMood['emoji']}}" width="120" height="120" style="max-width: 100%; vertical-align: middle;" alt="">
                            <span style="font-size: 36px; font-weight: 700; color: #111827">
                            {{$outMood['title']}}
                            </span>
                            <span style="margin-top: 16px; text-wrap: nowrap; font-size: 12px; font-weight: 400; color: #6b7280">
                            {{$outMood['description']}}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div dir="rtl" style="margin-top: 40px; display: flex; flex: 1 1 0%; flex-direction: column; column-gap: 24px; border-radius: 8px; border: 1px solid #e5e7eb; padding: 12px; text-align: start;">
                      <div style="height: 80px;">
                        <div style="font-size: 20px; font-weight: 400; line-height: 28px; color: #4b5563;">الحالة المزاجية للموظفين عند الدخول</div>
                        <div style="font-size: 20px; font-weight: 400; line-height: 28px; color: #4b5563;">Employees Check-in Mood</div>
                      </div>
                      <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="position: relative; margin-top: 24px; display: flex; width: 224px; height: 224px; align-items: center; justify-content: center; column-gap: 8px;">
                          <div style="display: flex; flex-direction: column; align-items: center;">
                            <img src="{{$inMood['emoji']}}" width="120" height="120" style="max-width: 100%; vertical-align: middle;" alt="">
                            <span style="font-size: 36px; font-weight: 700; color: #111827;">
                            {{$inMood['title']}}
                            </span>
                            <span style="margin-top: 16px; text-wrap: nowrap; font-size: 12px; font-weight: 400; color: #6b7280;">
                            {{$inMood['description']}}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </div>
</body>
</html>