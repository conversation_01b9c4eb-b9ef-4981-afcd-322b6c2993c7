@php
/** @var \App\Models\Employee $employee */
/** @var \App\Models\ReportTask $reportTask */
@endphp
<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: "Segoe UI", sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
  <title>تقرير</title>
  <style>
    @media (max-width: 600px) {
      .sm-mb-4 {
        margin-bottom: 16px !important
      }
      .sm-mt-8 {
        margin-top: 32px !important
      }
      .sm-px-4 {
        padding-left: 16px !important;
        padding-right: 16px !important
      }
      .sm-px-6 {
        padding-left: 24px !important;
        padding-right: 24px !important
      }
      .sm-leading-8 {
        line-height: 32px !important
      }
    }
  </style>
</head>
<body style="margin: 0; width: 100%; background-color: #f9fafb; padding: 0; -webkit-font-smoothing: antialiased; word-break: break-word">
  <div role="article" aria-roledescription="email" aria-label="تقرير">
    <div class="sm-px-4" style="background-color: #f8fafc; padding: 32px; font-family: ui-sans-serif, system-ui, -apple-system, 'Segoe UI', sans-serif; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
      <table align="center" cellpadding="0" cellspacing="0" role="none">
        <tr>
          <td style="max-width: 100%; background-color: #fffffe; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)">
            <div class="sm-mt-8 sm-mb-4" style="margin-bottom: 24px; margin-top: 48px; text-align: center">
              <img src="{{asset('images/logo.png')}}" alt="Nawart" style="max-width: 100%; vertical-align: middle; margin-left: auto; margin-right: auto; display: block; height: 44px; width: auto">
            </div>
            <table style="width: 100%;" cellpadding="0" cellspacing="0" role="none">
              <tr>
                <td class="sm-px-6" style="border-radius: 4px; background-color: #fffffe; padding: 48px; font-size: 16px; color: #334155; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
                  <h1 class="sm-leading-8" style="margin: 0 0 24px; text-align: center; font-size: 30px; font-weight: 600; color: #111827">{{$reportTask->report->name->displayName()}}</h1>
                  <p style="font-weight: 400; color: #4b5563">
                    {!! __('Welcome <strong>:name</strong>', ['name' => $employee->name])!!},
                  </p>
                  <p style="font-weight: 400; color: #4b5563;">
                    @if($reportTask->data?->period)
                    {!!__('The report <strong>:report</strong> for the period <strong>(:from, :to)</strong>, is here for you to review!', [
                    'report' => $reportTask->report->name->displayName(),
                    'from' => $reportTask->data->period->start->format('Y-m-d'),
                    'to' => $reportTask->data->period->end->format('Y-m-d'),
                    ])!!}
                    @endif
                    <br>
                    {{__('Please find the details in the attached file.')}}
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </div>
</body>
</html>