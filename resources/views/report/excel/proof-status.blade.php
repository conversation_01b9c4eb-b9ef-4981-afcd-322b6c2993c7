<table>
    <thead>
    <tr>
        <th>Employee</th>
        <th>Employee ID</th>
        <th>Department</th>
        <th>status</th>
        <th>Notification Type</th>
        <th>Notification Method</th>
        <th>Notification sent</th>
        <th>Date</th>
    </tr>

    </thead>
    <tbody>
    @if (count($proofs) > 0)
        @foreach($proofs as $proof)
            <tr>
                <td>{{ $proof->employee->name }}</td>
                <td>{{ $proof->employee->number }}</td>
                <td>{{ $proof->employee->department->name }}</td>
                <td style="text-align: center;"> {{ $proof->status }}</td>
                <td style="text-align: center;">Proof of Attendance</td>
                <td style="text-align: center;"> {{ $proof->method }}</td>
                <td style="text-align: center;">{{$proof->created_at->diffForHumans()}}</td>
                <td>{{ $proof->employee->created_at }}</td>
            </tr>
        @endforeach
    @else
        <tr>
            <td colspan="9">No Data</td>
        </tr>
    @endif
    </tbody>
</table>
