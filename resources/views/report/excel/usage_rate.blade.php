<table>
    <thead>
    <tr>
        <th>Employee</th>
        <th>Email</th>
        <th>employee ID</th>
        <th>Tags</th>
        <th>Department</th>
        <th>Shifts</th>
        <th>Locations</th>
        <th>Usage Status</th>
        <th>Latest Activity</th>
        <th>Registered</th>
    </tr>

    </thead>
    <tbody>
    @if (count($employees) > 0)
        @foreach($employees as $employee)
            <tr>
                <td>{{ $employee->name }}</td>
                <td>{{ $employee->email }}</td>
                <td>{{ $employee->number }}</td>
                <td>{{ $employee->tags->pluck('name')->implode(', ') }}</td>
                <td>{{ $employee->department_name }}</td>
                <td>{{ $employee->shifts->pluck('name')->implode(', ') }}</td>
                <td>{{ $employee->locations->pluck('name')->implode(', ') }}</td>
                <td style="text-align: center; @if ($employee->last_activity_at) color: #22C55E; @else color: #ef4444; @endif">@if ($employee->last_activity_at)
                        Used
                    @else
                        Not Used
                    @endif</td>
                <td style="text-align: center;">
                    {{ $employee->last_activity_at?->diffForHumans() ?? '-' }}
                </td>
                <td style="text-align: center;">{{$employee->first_login_at?->diffForHumans()}}</td>
            </tr>
        @endforeach
    @else
        <tr>
            <td colspan="9">No Data</td>
        </tr>
    @endif
    </tbody>
</table>
