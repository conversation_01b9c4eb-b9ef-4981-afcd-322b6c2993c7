@php
    /** @var \App\Models\Leave[] $leaves */
    /** @var \Carbon\CarbonPeriod $period */
@endphp
<table>
    <tbody>

    <!-- Print Date -->
    <tr>
        <td align="center"
            valign="center"
            style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px">
            Print Date
        </td>
        <td valign="center"
            style="border: 1px solid black; font-size: 10px"
            colspan="2">{{ now()->format('d/m/Y H:i') }}</td>
        <td></td>
        <td align="center"
            valign="center"
            colspan="2"
            style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px">
            Period
        </td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td align="center"
            valign="center"
            style="border: 1px solid black; font-size: 10px"
            colspan="2"
        >{{ $period->start->format('d/m/Y') }} - {{$period->end->format('d/m/Y')}}</td>
    </tr>

    <tr></tr>

    <!-- Report Title -->
    <tr>
        <td colspan="11"
            style="font-weight: bold; font-size: 24px"
            align="center"
            valign="center">Leaves Report
        </td>
    </tr>

    <tr></tr>

    <!-- Table Headers -->
    <tr>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px" width="15" height="40"
            align="center"
            valign="center">
            Request Creation
            Date<br>تاريخ إنشاء
            لطلب
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">
            Employee<br>الموظف
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">
            Employee ID<br>الرقم
            الوظيفي
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">
            Tag<br>التصنيف
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">
            Department<br>الإدارة
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">
            Type<br>النوع
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">
            From<br>من
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">To<br>إلى
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">Total Days<br>إجمالي الأيام
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">Status<br>الحالة
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">Reason<br>السبب
        </th>
        <th style="border: 1px solid black; background-color: #D9D9D9; font-size: 10px"
            width="15"
            height="40"
            align="center"
            valign="center">Rejection Reason<br>سبب الرفض
        </th>
    </tr>

    <!-- Leaves Data -->
    @foreach($leaves as $leave)
        <tr>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->created_at->format('d-M-y') }}</td>
            <td style="border: 1px solid black; font-size: 10px" width="15" height="20"
                align="center"
                valign="center">{{ $leave->employee->name }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->employee->number }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15" height="20" align="center"
                valign="center">{{ $leave->employee->tags->pluck('name')->implode(', ') }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->employee->department?->name }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->reason }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->from_date->format('d-M-y') }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->to_date->format('d-M-y') }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->total_days }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->status->displayName() }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->reason }}</td>
            <td style="border: 1px solid black; font-size: 10px"
                width="15"
                height="20"
                align="center"
                valign="center">{{ $leave->rejection_reason }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
