<table>
    @php
        /**
        * @var array $days
        * @var \App\DTOs\ReportData $reportData
        * @var \App\Models\Employee[] $employees
        */
    @endphp
    <thead>
    <tr>
        <th>Employee</th>
        <th>employee ID</th>
        <th>Department</th>
        @foreach($days as $day)
            <th>
                {{ $day['month'] .' '. $day['day']}}
            </th>
        @endforeach
    </tr>
    </thead>
    <tbody>
    @foreach ($employees as $employee)
        <tr>
            <td>
                {{ $employee->name }}
            </td>
            <td>
                {{ $employee->number }}
            </td>
            <td>
                {{ $employee->department->name }}
            </td>
            @php
                $attendances = $employee
                ->attendances
                ->mapWithKeys(fn($attendance) => [$attendance->date->dayOfMonth => $attendance->status_name])
                ->toArray();
            @endphp
            @foreach($days as $k => $day)
                @php
                    $status = $attendances[$k] ?? '-';
                @endphp
                <td style="text-align: center; @if($status === 'W') color: #CA8A04; @endif @if($status === 'H') color: #00a7e1; @endif @if($status === 'P') color: #22C55E; @endif @if($status === 'RW') color: #4ade80; @endif @if($status === 'A') color: #ef4444; @endif  @if($status === 'L') color: #0F85B0; @endif">
                    {{ $status }}
                </td>
            @endforeach
        </tr>
    @endforeach
    </tbody>
</table>
