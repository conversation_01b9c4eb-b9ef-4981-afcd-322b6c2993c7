include:
  - project: devops/gitlab/shared-pipeline/configuration-templates
    ref: v1.0.0
    file:
      - templates/rule/git.yaml
      - templates/service/mysql.yaml
      - templates/build/container-image/docker.yaml
      - templates/execute/container-image/docker.yaml
      - templates/scan/static-code-analysis/sonar.yaml
      - templates/scan/security/trivy-container-vulnerability-scan.yaml
      - templates/deploy/kubernetes/app-config.yaml

base:
  extends:
    - .build-container-image-using-docker-template
  variables:
    BUILD_TARGET: base
    BUILD_ARGS: |
      [
        "NOVA_TOKEN=$NOVA_TOKEN"
      ]

deployment:
  extends:
    - .build-container-image-using-docker-template
  variables:
    BUILD_ARGS: |
      [
        "NOVA_TOKEN=$NOVA_TOKEN"
      ]

testing:
  extends:
    - .build-container-image-using-docker-template
  variables:
    BUILD_TARGET: testing
    BUILD_ARGS: |
      [
        "NOVA_TOKEN=$NOVA_TOKEN"
      ]

node:
  extends:
    - .build-container-image-using-docker-template
  variables:
    BUILD_TARGET: node
    BUILD_ARGS: |
      [
        "NOVA_TOKEN=$NOVA_TOKEN"
      ]

style:
  extends:
    - .execute-container-using-docker-template
  services:
    - !reference [.docker-in-docker-service, services]
  needs:
    - node
  variables:
    BUILD_TARGET: node
    CONTAINER_COMMAND: "npx prettier . --check"

test:
  extends:
    - .execute-container-using-docker-template
    - .mysql-service
  services:
    - !reference [.docker-in-docker-service, services]
    - !reference [.mysql-service, services]
  needs:
    - base
  variables:
    BUILD_TARGET: testing
    RUN_ARGS: |
      [
        "DB_CONNECTION=mysql",
        "DB_HOST=$$MYSQL_HOST",
        "DB_PORT=$MYSQL_PORT",
        "DB_DATABASE=$MYSQL_DATABASE",
        "DB_USERNAME=$MYSQL_ROOT_USER",
        "DB_PASSWORD=$MYSQL_ROOT_PASSWORD"
      ]
    CONTAINER_COMMAND: "
      cp .env.example .env &&
      php artisan key:generate &&
      php artisan migrate --force &&
      php artisan passport:install &&
      php artisan passport:keys --force &&
      php artisan test --parallel --coverage --log-junit 'reports/unitreport.xml' --coverage-cobertura 'reports/cobertura.xml' --coverage-clover=reports/clover.xml -c phpunit.xml
      "
    EXTRACT_ARTIFACTS: "true"
    SOURCE_PATH: /var/www/html/reports
    DESTINATION_PATH: reports
  coverage: '/Total: \.*\s*(\d+\.\d+?)\s*%/'
  artifacts:
    paths:
      - reports
    reports:
      junit: reports/unitreport.xml
      coverage_report:
        coverage_format: cobertura
        path: reports/cobertura.xml

run code quality check:
  extends:
    - .static-code-analysis-scan-using-sonar-template
  needs: []
  variables:
    SONAR_QUALITYGATE_WAIT: "false"

scan container for critical and high vulnerabilities:
  extends:
    - .container-vulnerability-scan-using-trivy-template
  needs:
    - base
  variables:
    TRIVY_SEVERITY: HIGH,CRITICAL
    TRIVY_FAIL: "false"

scan container for non critical and high vulnerabilities:
  extends:
    - .container-vulnerability-scan-using-trivy-template
  needs:
    - base
  variables:
    TRIVY_SEVERITY: UNKNOWN,LOW,MEDIUM
    TRIVY_FAIL: "false"

# This will use the latest stage in dockerfile which is `deployment`
deploy to development:
  extends:
    - .deploy-to-kubernetes-using-app-config-template
    - .on-develop-branch
  needs:
    - style
    - test
  variables:
    APP_NAME: nawart-start
    ENVIRONMENT: development

# This will use the latest stage in dockerfile which is `deployment`
deploy to production:
  extends:
    - .deploy-to-kubernetes-using-app-config-template
    - .on-main-branch-manual
  needs:
    - style
    - test
  variables:
    BUILD_TARGET: deployment
    APP_NAME: nawart-start
    ENVIRONMENT: production
