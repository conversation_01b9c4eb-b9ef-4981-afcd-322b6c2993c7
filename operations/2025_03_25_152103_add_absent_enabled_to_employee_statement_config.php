<?php

use App\Models\Team;
use TimoKoerber\LaravelOneTimeOperations\OneTimeOperation;

return new class extends OneTimeOperation {
    /**
     * Determine if the operation is being processed asynchronously.
     */
    protected bool $async = true;

    /**
     * The queue that the job will be dispatched to.
     */
    protected string $queue = 'default';

    /**
     * A tag name, that this operation can be filtered by.
     */
    protected ?string $tag = null;

    /**
     * Process the operation.
     */
    public function process(): void
    {
        Team::get()->each(function ($team) {
            $array = $team->employee_statement_config->toArray();
            $array['absent_enabled'] = false;
            $team->employee_statement_config = $array;
            $team->save();
        });
    }
};
