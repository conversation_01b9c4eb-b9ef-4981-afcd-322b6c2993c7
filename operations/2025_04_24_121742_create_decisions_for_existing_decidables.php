<?php

use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Leave;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\LaravelOneTimeOperations\OneTimeOperation;

return new class extends OneTimeOperation {
    public function process(): void
    {
        DB::transaction(function () {
            Leave::query()
                ->with(['employee.manager', 'team'])
                ->notPending()
                ->each(function (Leave $leave) {
                    $leave->updateOrCreateDecision(
                        RequestStatus::fromStringOrInstance($leave->status),
                        DecisionLayer::First,
                        decider: $leave->employee->manager ?? $leave->team->resolveAdmin()
                    );
                });

            ApprovalRequest::query()
                ->with(['employee.manager', 'team'])
                ->notPending()
                ->each(function (ApprovalRequest $request) {
                    $request->updateOrCreateDecision(
                        RequestStatus::fromStringOrInstance($request->status),
                        DecisionLayer::First,
                        decider: $request->employee->manager ?? $request->team->resolveAdmin()
                    );
                });
        });
    }
};
