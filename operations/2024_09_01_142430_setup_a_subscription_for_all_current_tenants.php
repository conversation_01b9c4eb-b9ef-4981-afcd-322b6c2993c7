<?php

use App\Enums\SoftwareCode;
use App\Enums\SoftwarePackageCode;
use App\Models\Plan;
use App\Models\PlanItem;
use App\Models\Role;
use App\Models\Software;
use App\Models\SoftwareFeature;
use App\Models\SoftwarePackage;
use App\Models\Subscription;
use App\Models\SubscriptionItem;
use App\Models\Tenant;
use Database\Seeders\RolesAndPermissions;
use Database\Seeders\SetupSoftwareTablesSeeder;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\LaravelOneTimeOperations\OneTimeOperation;

return new class extends OneTimeOperation {
    public function process(): void
    {
        DB::transaction(function () {
            $this->clearTables();

            (new SetupSoftwareTablesSeeder())->run(); // this will add software to other Nawart products
            (new RolesAndPermissions())->run();

            Tenant::each(function (Tenant $tenant) {
                Subscription::createSubscription(
                    tenant: $tenant,
                    softwareData: [
                        [
                            'software_code' => SoftwareCode::Start,
                            'software_package_code' => SoftwarePackageCode::Enterprise1,
                        ],
                        [
                            'software_code' => SoftwareCode::Attendance,
                            'software_package_code' => SoftwarePackageCode::Enterprise1,
                        ],
                        [
                            'software_code' => SoftwareCode::Drive,
                            'software_package_code' => SoftwarePackageCode::Enterprise1,
                        ],
                    ]
                );
            });
        });
    }

    public function clearTables(): void
    {
        SubscriptionItem::query()->delete();
        Subscription::query()->delete();

        PlanItem::query()->delete();
        Plan::query()->delete();

        SoftwareFeature::query()->delete();
        SoftwarePackage::query()->delete();

        Role::query()->update(['software_id' => null]);

        Software::query()->delete();
    }
};
