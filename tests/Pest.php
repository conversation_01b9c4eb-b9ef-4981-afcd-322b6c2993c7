<?php

use App\Models\Department;
use App\Models\Employee;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Excel as ExcelWriter;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

/*
|--------------------------------------------------------------------------
| Test Case
|--------------------------------------------------------------------------
|
| The closure you provide to your test functions is always bound to a specific PHPUnit test
| case class. By default, that class is "PHPUnit\Framework\TestCase". Of course, you may
| need to change it using the "uses()" function to bind a different classes or traits.
|
*/

uses(TestCase::class, RefreshDatabase::class)->in('Feature', 'Unit');

/*
|--------------------------------------------------------------------------
| Functions
|--------------------------------------------------------------------------
|
| While Pest is very powerful out-of-the-box, you may have some testing code specific to your
| project that you don't want to repeat in every file. Here you can also expose helpers as
| global functions to help you to reduce the number of lines of code in your test files.
|
*/

function createTestExcelFile(array $data): UploadedFile
{
    $filePath = 'test-employees.xlsx';

    Excel::store(
        new class ($data) implements FromCollection, WithHeadings {
            public function __construct(public $data)
            {
            }

            function collection()
            {
                return collect($this->data);
            }

            public function headings(): array
            {
                return [
                    'first_name',
                    'last_name',
                    'manager_email',
                    'phone',
                    'email',
                    'position',
                    'number',
                    'department',
                ];
            }
        },
        $filePath,
        'local',
        ExcelWriter::XLSX
    );

    return new UploadedFile(Storage::disk('local')->path($filePath), $filePath, null, null, true);
}

function createDefaultEmployee(array $attributes = [])
{
    return Employee::factory()
        ->for(test()->tenant)
        ->create($attributes);
}

function createDefaultDepartment(array $attributes = [])
{
    return Department::factory()
        ->for(test()->tenant)
        ->create($attributes);
}
