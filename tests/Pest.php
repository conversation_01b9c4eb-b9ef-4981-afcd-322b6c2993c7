<?php

use App\Enums\PreferredLanguage;
use App\Models\Employee;
use App\Models\Team;
use Database\Seeders\ReportSeeder;
use Database\Seeders\SoftwarePackageSeeder;
use Illuminate\Foundation\Testing\LazilyRefreshDatabase;
use function Pest\Laravel\freezeTime;

pest()
    ->extend(Tests\TestCase::class)
    ->use(LazilyRefreshDatabase::class)
    ->beforeEach(function () {
        $this->seed([SoftwarePackageSeeder::class, ReportSeeder::class]);

        $this->tenant = Team::factory()->enterprise()->create();
        $this->user = Employee::factory()
            ->for($this->tenant)
            ->create(['preferred_language' => PreferredLanguage::English]);

        freezeTime();
    })
    ->in('Feature');
