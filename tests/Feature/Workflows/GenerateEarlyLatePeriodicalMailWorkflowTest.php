<?php

use App\DTOs\EarlyLateMailData;
use App\Enums\EarlyLatePeriodPolicy;
use App\Jobs\MergeExcelPartsJob;
use App\Jobs\SendEarlyLateReportMailJob;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Workflows\GenerateEarlyLatePeriodicalMailWorkflow;
use Carbon\CarbonPeriod;

test('workflow generates reports for departments and direct employees', function () {
    // Arrange
    $manager = createDefaultEmployee();

    $department1 = createDefaultDepartment();
    $department2 = createDefaultDepartment();

    Employee::factory()
        ->for($this->tenant)
        ->for($department1)
        ->count(3)
        ->create();

    Employee::factory()
        ->for($this->tenant)
        ->for($department2)
        ->count(2)
        ->create();

    // Create direct employees (no department)
    Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->count(2)
        ->create();

    $period = CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth());

    $mailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: [$department1->id, $department2->id],
        period: $period,
        periodType: EarlyLatePeriodPolicy::Weekly,
        reportTasksIds: collect()
    );

    // Act
    $workflow = GenerateEarlyLatePeriodicalMailWorkflow::test($mailData);

    // Assert
    // Verify report tasks were created
    expect($mailData->reportTasksIds)->toHaveCount(3);

    // Each report task should exist in database
    foreach ($mailData->reportTasksIds as $taskId) {
        expect(ReportTask::find($taskId))->not->toBeNull();
    }

    // Verify send mail job is scheduled after all reports
    $workflow->assertJobExistsWithDependencies(SendEarlyLateReportMailJob::class, [
        'generate-excel-files_1.' . MergeExcelPartsJob::class,
        'generate-excel-files_2.' . MergeExcelPartsJob::class,
        'generate-excel-files_3.' . MergeExcelPartsJob::class,
    ]);

    expect($mailData->reportTasksIds)->toHaveCount(3);
});

test('workflow handles case with no direct employees', function () {
    $manager = createDefaultEmployee();

    // Only create department employees
    $department = createDefaultDepartment();
    Employee::factory()
        ->for($this->tenant)
        ->for($department)
        ->count(2)
        ->create();

    $period = CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth());

    $mailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: [$department->id],
        period: $period,
        periodType: EarlyLatePeriodPolicy::Weekly,
        reportTasksIds: collect()
    );

    $workflow = GenerateEarlyLatePeriodicalMailWorkflow::test($mailData);

    $workflow->assertJobExistsWithDependencies(SendEarlyLateReportMailJob::class, [
        'generate-excel-files_1.' . MergeExcelPartsJob::class,
    ]);

    expect($mailData->reportTasksIds)->toHaveCount(1);
});

test('workflow handles case with only direct employees', function () {
    $manager = createDefaultEmployee();

    // Only create direct employees
    Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->count(2)
        ->create();

    $period = CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth());

    $mailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: [],
        period: $period,
        periodType: EarlyLatePeriodPolicy::Weekly,
        reportTasksIds: collect()
    );

    $workflow = GenerateEarlyLatePeriodicalMailWorkflow::test($mailData);

    $workflow->assertJobExistsWithDependencies(SendEarlyLateReportMailJob::class, [
        'generate-excel-files_1.' . MergeExcelPartsJob::class,
    ]);

    expect($mailData->reportTasksIds)->toHaveCount(1);
});
