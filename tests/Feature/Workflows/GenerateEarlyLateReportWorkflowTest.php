<?php

namespace Tests\Feature\Workflows;

use App\Enums\SheetMode;
use App\Jobs\MergeExcelPartsJob;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Models\Team;
use App\Workflows\GenerateEarlyLateReportWorkflow;
use Carbon\CarbonPeriod;

test('workflow creates correct job chain with multiple parts', function () {
    $team = Team::factory()->create();
    $employees = Employee::factory()->for($team)->count(15)->create();
    $period = CarbonPeriod::create('2024-01-01', '2024-01-03');

    $employees->each(function (Employee $employee) use ($period, $team) {
        Attendance::factory()->for($employee)->for($team)->createFromPeriod($period);
    });

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
            'sheet_mode' => SheetMode::MultiSheet->value,
        ])
        ->create();

    GenerateEarlyLateReportWorkflow::test($reportTask)
        ->assertJobExists('parts_1')
        ->assertJobExists('parts_2')
        ->assertJobExistsWithDependencies(MergeExcelPartsJob::class, ['parts_1', 'parts_2']);
});

test('workflow creates correct job chain with single part', function () {
    $team = Team::factory()->create();
    $employees = Employee::factory()->for($team)->count(5)->create();
    $period = CarbonPeriod::create('2024-01-01', '2024-01-03');

    $employees->each(function (Employee $employee) use ($period, $team) {
        Attendance::factory()->for($employee)->for($team)->createFromPeriod($period);
    });

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
            'sheet_mode' => SheetMode::MultiSheet->value,
        ])
        ->create();

    GenerateEarlyLateReportWorkflow::test($reportTask)
        ->assertJobExists('parts_1')
        ->assertJobExistsWithDependencies(MergeExcelPartsJob::class, ['parts_1']);
});

test('workflow handles different sheet modes correctly', function (SheetMode $sheetMode) {
    $team = Team::factory()->create();
    $employees = Employee::factory()->for($team)->count(11)->create();
    $period = CarbonPeriod::create('2024-01-01', '2024-01-03');

    $employees->each(function (Employee $employee) use ($period, $team) {
        Attendance::factory()->for($employee)->for($team)->createFromPeriod($period);
    });

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
            'sheet_mode' => $sheetMode->value,
        ])
        ->create();

    $workflow = GenerateEarlyLateReportWorkflow::test($reportTask);

    if ($sheetMode === SheetMode::SingleSheet) {
        // Single sheet: one part per day (3 days)
        $partNames = collect($period)
            ->map(fn($date, $index) => 'parts_' . ($index + 1))
            ->tap(fn($names) => $names->each(fn($name) => $workflow->assertJobExists($name)))
            ->all();

        $workflow->assertJobExistsWithDependencies(MergeExcelPartsJob::class, $partNames);
    } else {
        // Other modes: parts based on employee count (11 employees = 2 parts)
        $workflow
            ->assertJobExists('parts_1')
            ->assertJobExists('parts_2')
            ->assertJobExistsWithDependencies(MergeExcelPartsJob::class, ['parts_1', 'parts_2']);
    }
})->with(SheetMode::cases());
