<?php

use App\Exports\Sheets\EarlyLateSheet;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Support\Collection;

it('calculates total additional hours correctly [employee worked exactly 40 hours]', function () {
    // Given an employee with default shift 5 days a week and 8 hours a day = 40 hours a week
    $shift = Shift::factory()->for($this->tenant);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->has($shift)
        ->create();

    // and worked 9 hours on Monday
    $attendances = Collection::times(5)->map(
        fn(int $number) => Attendance::factory()
            ->date(Carbon::parse('2024-08-04')->addDays($number - 1))
            ->for($employee)
            ->for($shift)
            ->for($this->tenant)
            ->create([
                'net_hours' => '08:00:00',
            ])
    );

    $sums = (new EarlyLateSheet($attendances, $employee, '2023-01-01'))->getSums();

    expect($sums['totalAdditionalHours']->totalHours)->toBe(0.0);
    expect($sums['totalAdditionalHoursDayByDay']->totalHours)->toBe(0.0);
});

it(
    'calculates total additional hours correctly [employee worked additional hours in Monday but absent in Wednesday]',
    function () {
        // Given an employee with default shift 5 days a week and 8 hours a day = 40 hours a week
        $shift = Shift::factory()->for($this->tenant);

        $employee = Employee::factory()
            ->for($this->tenant)
            ->has($shift)
            ->create();

        // and worked 9 hours on Monday
        $attendances = Collection::times(5)->map(function (int $number) use ($employee, $shift) {
            $isMonday = $number === 1;
            $isWednesday = $number === 3;
            return Attendance::factory()
                ->date(Carbon::parse('2024-08-04')->addDays($number - 1))
                ->for($employee)
                ->for($shift)
                ->for($this->tenant)
                ->create([
                    'net_hours' => $isWednesday
                        ? '00:00:00'
                        : ($isMonday
                            ? '09:00:00'
                            : '08:00:00'),
                ]);
        });

        $sums = (new EarlyLateSheet($attendances, $employee, '2023-01-01'))->getSums();

        // it should be 0 because we consider all days
        expect($sums['totalAdditionalHours']->totalHours)->toBe(0.0);

        // it should be 1 because we consider day by day so being absent on Wednesday should not affect Monday
        expect($sums['totalAdditionalHoursDayByDay']->totalHours)->toBe(1.0);
    }
);
