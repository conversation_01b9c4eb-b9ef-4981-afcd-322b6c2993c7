<?php

use App\Exports\SingleSheet\SingleSheetEarlyLatePart;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Models\Tag;

test('fetches attendances for specific date', function () {
    // Arrange
    $targetDate = today();
    $otherDate = today()->addDay();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    // Create attendance record for target date
    $targetAttendance = Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($targetDate)
        ->create();

    // Create attendance record for another date
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($otherDate)
        ->create();

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'start_date' => $targetDate->format('Y-m-d'),
            'end_date' => $otherDate->format('Y-m-d'),
        ])
        ->create();

    // Act
    $result = (new SingleSheetEarlyLatePart(offset: $targetDate->format('Y-m-d')))->fetchPartValues(
        $reportTask
    );

    // Assert
    expect($result)
        ->toHaveCount(1)
        ->first()
        ->id->toBe($targetAttendance->id)
        ->and($result->first()->date->format('Y-m-d'))
        ->toBe($targetDate->format('Y-m-d'));
});

test('includes related employee data', function () {
    // Arrange
    $date = today();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    // Create and attach a tag
    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();
    $employee->tags()->attach($tag);

    $attendance = Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($date)
        ->create();

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'start_date' => $date->format('Y-m-d'),
            'end_date' => $date->format('Y-m-d'),
        ])
        ->create();

    // Act
    $result = (new SingleSheetEarlyLatePart(offset: $date->format('Y-m-d')))->fetchPartValues(
        $reportTask
    );

    // Assert
    expect($result)
        ->toHaveCount(1)
        ->first()
        ->employee->toBeInstanceOf(Employee::class)
        ->and($result->first()->employee->id)
        ->toBe($employee->id)
        ->and($result->first()->employee->tags)
        ->not->toBeEmpty();
});

test('returns empty collection when no attendances for date', function () {
    // Arrange
    $date = today();
    $otherDate = today()->addDay();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    // Create attendance record for another date only
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($otherDate)
        ->create();

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'start_date' => $date->format('Y-m-d'),
            'end_date' => $otherDate->format('Y-m-d'),
        ])
        ->create();

    // Act
    $result = (new SingleSheetEarlyLatePart(offset: $date->format('Y-m-d')))->fetchPartValues(
        $reportTask
    );

    // Assert
    expect($result)->toBeEmpty();
});
