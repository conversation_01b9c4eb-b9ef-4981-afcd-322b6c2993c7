<?php

use App\Enums\SheetMode;
use App\Exports\SingleSheet\SingleSheetEarlyLatePartsMerger;
use App\Models\Department;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Models\Team;
use Illuminate\Support\Facades\Storage;
use OpenSpout\Reader\XLSX\Reader;
use Spatie\SimpleExcel\SimpleExcelWriter;

test('merger creates excel file with correct data', function () {
    Storage::fake('local');
    $filePath = createLocalFile('tests/Feature/Exports/test-report.xlsx');
    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();

    $department = Department::factory()
        ->for($team)
        ->create([
            'name' => 'Engineering',
            'manager_id' => Employee::factory()
                ->for($team)
                ->create([
                    'first_name' => 'Manager',
                    'last_name' => 'Smith',
                ])->id,
        ]);

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSheet->value,
        ])
        ->create([
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ]);

    $values = collect([
        [
            'date' => now()->format('j/m/Y'),
            'employee_number' => 'EMP001',
            'employee_name' => 'John Doe',
            'department' => 'Engineering',
            'check_in' => '09:30',
            'check_out' => '17:00',
            'total_hours' => '7:30',
            'early_in' => '00:00',
            'late_in' => '00:30',
            'early_out' => '00:00',
            'late_out' => '00:00',
            'shift' => 'Morning Shift',
            'check_in_location' => 'Office A',
            'check_out_location' => 'Office B',
            'status' => 'PRESENT',
        ],
    ]);

    $merger = new SingleSheetEarlyLatePartsMerger();
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(1);

    $firstSheet = array_first($sheets);
    $rows = iterator_to_array($firstSheet->getRowIterator());

    expect($rows[1]->getCells()[0]->getValue())
        ->toBe(now()->format('j/m/Y'))
        ->and($rows[1]->getCells()[1]->getValue())
        ->toBe('EMP001')
        ->and($rows[1]->getCells()[2]->getValue())
        ->toBe('John Doe');

    $reader->close();
    unlink($filePath);
});

test('merger handles multiple records correctly', function () {
    Storage::fake('local');
    $filePath = createLocalFile('tests/Feature/Exports/test-report-multiple.xlsx');
    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSheet->value,
        ])
        ->create([
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ]);

    $values = collect([
        [
            'date' => now()->format('j/m/Y'),
            'employee_number' => 'EMP001',
            'employee_name' => 'John Doe',
            'department' => 'Engineering',
            'check_in' => '09:30',
            'check_out' => '17:00',
            'total_hours' => '7:30',
            'early_in' => '00:00',
            'late_in' => '00:30',
            'early_out' => '00:00',
            'late_out' => '00:00',
            'shift' => 'Morning Shift',
            'check_in_location' => 'Office A',
            'check_out_location' => 'Office B',
            'status' => 'PRESENT',
        ],
        [
            'date' => now()->format('j/m/Y'),
            'employee_number' => 'EMP002',
            'employee_name' => 'Jane Smith',
            'department' => 'Marketing',
            'check_in' => '09:00',
            'check_out' => '17:00',
            'total_hours' => '8:00',
            'early_in' => '00:00',
            'late_in' => '00:00',
            'early_out' => '00:00',
            'late_out' => '00:00',
            'shift' => 'Morning Shift',
            'check_in_location' => 'Office A',
            'check_out_location' => 'Office B',
            'status' => 'PRESENT',
        ],
    ]);

    $merger = new SingleSheetEarlyLatePartsMerger();
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(1);

    $firstSheet = array_first($sheets);
    $rows = iterator_to_array($firstSheet->getRowIterator());

    // Check first employee data
    expect($rows[1]->getCells()[1]->getValue())
        ->toBe('EMP001')
        ->and($rows[1]->getCells()[2]->getValue())
        ->toBe('John Doe');

    // Check second employee data
    expect($rows[2]->getCells()[1]->getValue())
        ->toBe('EMP002')
        ->and($rows[2]->getCells()[2]->getValue())
        ->toBe('Jane Smith');

    $reader->close();
    unlink($filePath);
});

test('merger handles empty data gracefully', function () {
    Storage::fake('local');
    $filePath = createLocalFile('tests/Feature/Exports/test-report-empty.xlsx');
    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSheet->value,
        ])
        ->create([
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ]);

    $values = collect([]);

    $merger = new SingleSheetEarlyLatePartsMerger();
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(1);

    $firstSheet = array_first($sheets);
    $rows = iterator_to_array($firstSheet->getRowIterator());

    expect($rows)->toBeEmpty();

    $reader->close();
    unlink($filePath);
});
