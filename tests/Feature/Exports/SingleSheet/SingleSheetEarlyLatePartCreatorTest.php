<?php

use App\Enums\AttendanceStatus;
use App\Enums\SheetMode;
use App\Exports\SingleSheet\SingleSheetEarlyLatePart;
use App\Exports\SingleSheet\SingleSheetEarlyLatePartCreator;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Device;
use App\Models\Employee;
use App\Models\Location;
use App\Models\ReportTask;
use App\Models\Shift;

test('creates part with correct attendance data', function () {
    // Arrange
    $creator = new SingleSheetEarlyLatePartCreator();
    $date = now();

    $shift = Shift::factory()->create(['name' => 'Morning Shift']);
    $checkInLocation = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'Office A']);
    $checkoutLocation = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'Office B']);
    $checkInDevice = Device::factory()
        ->for($this->tenant)
        ->create(['name' => 'Device A']);
    $checkoutDevice = Device::factory()
        ->for($this->tenant)
        ->create(['name' => 'Device B']);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'number' => 'EMP001',
        ]);

    $attendance = Attendance::factory()
        ->for($this->tenant)
        ->create([
            'date' => $date,
            'team_id' => $this->tenant->id,
            'shift_id' => $shift->id,
            'employee_id' => $employee->id,
            'status' => AttendanceStatus::PRESENT->value,
            'shift_from' => $date->copy()->setTime(9, 0),
            'shift_to' => $date->copy()->setTime(17, 0),
            'check_in' => $date->copy()->setTime(9, 15), // 15 minutes late
            'check_out' => $date->copy()->setTime(17, 0),
            'net_hours' => '7:45:00',
            'in_type' => Activity::CHECK_IN,
            'out_type' => Activity::CHECK_OUT,
            'check_in_location_id' => $checkInLocation->id,
            'check_out_location_id' => $checkoutLocation->id,
            'check_in_device_id' => $checkInDevice->id,
            'check_out_device_id' => $checkoutDevice->id,
            'have_location' => true,
            'flexible_hours' => 0,
        ]);

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::SingleSheet->value])
        ->create();

    // Mock the part to return our attendance
    $mockPart = Mockery::mock(SingleSheetEarlyLatePart::class);
    $mockPart
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$attendance]));

    // Act
    $result = $creator->createPart($reportTask, $mockPart);

    // Assert
    expect($result)->toBeCollection()->toHaveCount(1);

    $row = $result->first();

    expect($row)
        ->toBeArray()
        ->and($row[0])
        ->toBe($date->format('Y-m-d')) // Date
        ->and($row[1])
        ->toBe('EMP001') // Employee number
        ->and($row[2])
        ->toBe('John Doe') // Employee name
        ->and($row[7])
        ->toBe('08:00') // Shift hours
        ->and($row[8])
        ->toBe('09:15:00') // Check in time
        ->and($row[9])
        ->toBe('17:00:00') // Check out time
        ->and($row[10])
        ->toBe('07:45:00') // Net hours
        ->and($row[13])
        ->toBe('00:15') // Late hours
        ->and($row[14])
        ->toBe('00:00') // Early hours
        ->and($row[15])
        ->toBe('+ 00:00') // Early In
        ->and($row[16])
        ->toBe('- 00:15') // Late In
        ->and($row[17])
        ->toBe('- 00:00') // Early Out
        ->and($row[18])
        ->toBe('+ 00:00') // Late Out
        ->and($row[19])
        ->toBe('Device A') // Check in device
        ->and($row[20])
        ->toBe('Office A') // Check in location
        ->and($row[21])
        ->toBe('Device B') // Check out device
        ->and($row[22])
        ->toBe('Office B') // Check out location
        ->and($row[23])
        ->toEqual('Present'); // Status
});

test('creates part with correct attendance data - show mobile app when no device', function () {
    // Arrange
    $creator = new SingleSheetEarlyLatePartCreator();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'number' => 'EMP001',
        ]);

    $attendance = Attendance::factory()
        ->for($this->tenant)
        ->create([
            'date' => now(),
            'team_id' => $this->tenant->id,
            'employee_id' => $employee->id,
            'in_type' => Activity::CHECK_IN,
            'out_type' => Activity::CHECK_OUT,
        ]);

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::SingleSheet->value])
        ->create();

    // Mock the part to return our attendance
    $mockPart = Mockery::mock(SingleSheetEarlyLatePart::class);
    $mockPart
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$attendance]));

    // Act
    $result = $creator->createPart($reportTask, $mockPart);

    // Assert
    expect($result)->toBeCollection()->toHaveCount(1);

    $row = $result->first();

    expect($row)
        ->toBeArray()
        ->and($row[19])
        ->toBe(config('app.default_device_name')) // Check in device
        ->and($row[21])
        ->toBe(config('app.default_device_name')); // Check out device
});

test('handles attendance with early departure', function () {
    // Arrange
    $creator = new SingleSheetEarlyLatePartCreator();
    $date = now();

    $shift = Shift::factory()->create(['name' => 'Morning Shift']);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'number' => 'EMP002',
        ]);

    $attendance = Attendance::factory()
        ->for($this->tenant)
        ->create([
            'date' => $date,
            'team_id' => $this->tenant->id,
            'shift_id' => $shift->id,
            'employee_id' => $employee->id,
            'status' => AttendanceStatus::PRESENT->value,
            'shift_from' => $date->copy()->setTime(9, 0),
            'shift_to' => $date->copy()->setTime(17, 0),
            'check_in' => $date->copy()->setTime(9, 0),
            'check_out' => $date->copy()->setTime(16, 45), // 15 minutes early
            'net_hours' => '7:45:00',
            'in_type' => Activity::CHECK_IN,
            'out_type' => Activity::CHECK_OUT,
        ]);

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::SingleSheet->value])
        ->create();

    $mockPart = Mockery::mock(SingleSheetEarlyLatePart::class);
    $mockPart
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$attendance]));

    // Act
    $result = $creator->createPart($reportTask, $mockPart);

    // Assert
    $row = $result->first();

    expect($row)
        ->toBeArray()
        ->and($row[2])
        ->toBe('Jane Smith') // Employee name
        ->and($row[9])
        ->toBe('16:45:00') // Check out time
        ->and($row[14])
        ->toBe('00:00'); // Early hours
});

test('handles absent employees correctly', function () {
    // Arrange
    $creator = new SingleSheetEarlyLatePartCreator();
    $date = now();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'Absent',
            'last_name' => 'Employee',
            'number' => 'EMP003',
        ]);

    $attendance = Attendance::factory()
        ->for($this->tenant)
        ->create([
            'date' => $date,
            'team_id' => $this->tenant->id,
            'employee_id' => $employee->id,
            'status' => AttendanceStatus::ABSENT->value,
            'check_in' => null,
            'check_out' => null,
        ]);

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::SingleSheet->value])
        ->create();

    $mockPart = Mockery::mock(SingleSheetEarlyLatePart::class);
    $mockPart
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$attendance]));

    // Act
    $result = $creator->createPart($reportTask, $mockPart);

    // Assert
    $row = $result->first();

    expect($row)
        ->toBeArray()
        ->and($row[2])
        ->toBe('Absent Employee') // Employee name
        ->and($row[23]->value)
        ->toBe('Absent'); // Status
});
