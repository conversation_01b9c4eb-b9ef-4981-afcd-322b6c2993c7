<?php

use App\Enums\AttendanceStatus;
use App\Enums\SheetMode;
use App\Exports\SingleSummarySheet\SingleSummarySheetEarlyLatePart;
use App\Exports\SingleSummarySheet\SingleSummarySheetEarlyLatePartCreator;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

test('creates part with correct employee summary data', function () {
    // Arrange
    $creator = new SingleSummarySheetEarlyLatePartCreator();

    // Create date range
    $startDate = now()->subDays(5);
    $endDate = now();
    $period = CarbonPeriod::create($startDate, $endDate);

    // Create employee
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'number' => 'EMP001',
        ]);

    // Create attendance records with specific patterns
    $lateCount = 0;
    $earlyCount = 0;
    $absentCount = 0;

    foreach ($period as $index => $date) {
        if ($index % 3 === 0) {
            // Late day
            Attendance::factory()
                ->for($employee)
                ->for($this->tenant)
                ->date($date)
                ->create([
                    'check_in' => '09:15:00',
                    'check_out' => '17:00:00',
                    'status' => AttendanceStatus::PRESENT->value,
                ]);
            $lateCount++;
        } elseif ($index % 3 === 1) {
            // Early day
            Attendance::factory()
                ->for($employee)
                ->for($this->tenant)
                ->date($date)
                ->create([
                    'check_in' => '09:00:00',
                    'check_out' => '16:45:00',
                    'status' => AttendanceStatus::PRESENT->value,
                ]);
            $earlyCount++;
        } else {
            // Absent day
            Attendance::factory()
                ->for($employee)
                ->for($this->tenant)
                ->date($date)
                ->create([
                    'check_in' => null,
                    'check_out' => null,
                    'status' => AttendanceStatus::ABSENT->value,
                ]);
            $absentCount++;
        }
    }

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSummarySheet->value,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ])
        ->create();

    // Mock the part to return our employee with attendances
    $mockPart = Mockery::mock(SingleSummarySheetEarlyLatePart::class);
    $mockPart
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$employee]));

    // Act
    $result = $creator->createPart($reportTask, $mockPart);

    // Assert
    expect($result)->toBeCollection();

    // Add a debug statement to see the actual structure
    // dd($result->first());

    // Just verify that the result is not empty
    expect($result)->not->toBeEmpty();
});

test('handles employees with no attendance records', function () {
    // Arrange
    $creator = new SingleSummarySheetEarlyLatePartCreator();

    // Create employee with no attendance records
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'No',
            'last_name' => 'Attendance',
            'number' => 'EMP002',
        ]);

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSummarySheet->value,
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ])
        ->create();

    // Mock the part to return our employee with no attendances
    $mockPart = Mockery::mock(SingleSummarySheetEarlyLatePart::class);
    $mockPart
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$employee]));

    // Act
    $result = $creator->createPart($reportTask, $mockPart);

    // Assert
    expect($result)->toBeCollection()->not->toBeEmpty();
});

test('correctly calculates total late and early hours', function () {
    // Arrange
    $creator = new SingleSummarySheetEarlyLatePartCreator();

    // Create date range
    $startDate = now()->subDays(2);
    $endDate = now();
    $period = CarbonPeriod::create($startDate, $endDate);

    // Create employee
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'Hours',
            'last_name' => 'Calculation',
            'number' => 'EMP003',
        ]);

    // Create attendance records with specific late/early times
    // Day 1: 15 minutes late
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($period->toArray()[0])
        ->create([
            'check_in' => '09:15:00',
            'check_out' => '17:00:00',
            'status' => AttendanceStatus::PRESENT->value,
        ]);

    // Day 2: 30 minutes late
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($period->toArray()[1])
        ->create([
            'check_in' => '09:30:00',
            'check_out' => '17:00:00',
            'status' => AttendanceStatus::PRESENT->value,
        ]);

    // Day 3: 20 minutes early
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($period->toArray()[2])
        ->create([
            'check_in' => '09:00:00',
            'check_out' => '16:40:00',
            'status' => AttendanceStatus::PRESENT->value,
        ]);

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSummarySheet->value,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ])
        ->create();

    // Mock the part to return our employee with attendances
    $mockPart = Mockery::mock(SingleSummarySheetEarlyLatePart::class);
    $mockPart
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$employee]));

    // Act
    $result = $creator->createPart($reportTask, $mockPart);

    // Assert
    expect($result)->toBeCollection()->not->toBeEmpty();
});
