<?php

use App\Enums\SheetMode;
use App\Exports\SingleSummarySheet\SingleSummarySheetEarlyLateExcelExportStrategy;
use App\Exports\SingleSummarySheet\SingleSummarySheetEarlyLatePart;
use App\Interfaces\ExcelPartsCollection;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Workflows\GenerateEarlyLateReportWorkflow;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Storage;
use Sassnowski\Venture\WorkflowDefinition;

test('strategy is correctly selected by ReportTask for SingleSummarySheet mode', function () {
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::SingleSummarySheet->value])
        ->create();

    expect($reportTask->getExcelExportStrategy())->toBeInstanceOf(
        SingleSummarySheetEarlyLateExcelExportStrategy::class
    );
});

test('splitter correctly divides employees into parts based on limit', function () {
    // Create a report task
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::SingleSummarySheet->value])
        ->create();

    // Create 25 employees
    $employees = Employee::factory()
        ->for($this->tenant)
        ->count(25)
        ->create();

    // Create attendance records for all employees
    $period = CarbonPeriod::create(today(), today()->addDays(5));
    foreach ($employees as $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod($period);
    }

    // Get the strategy and use the splitter
    $strategy = new SingleSummarySheetEarlyLateExcelExportStrategy();
    $parts = $strategy->splitter()->splitToParts($reportTask);

    // Verify the parts collection structure
    expect($parts)
        ->toBeInstanceOf(ExcelPartsCollection::class)
        ->and($parts->parts())
        ->not->toBeEmpty()
        ->and($parts->parts()->first())
        ->toBeInstanceOf(SingleSummarySheetEarlyLatePart::class);

    // Verify that the parts have the correct offset and limit
    // The limit might be different depending on the implementation
    expect($parts->parts()[0]->offset())
        ->toBe(0)
        ->and($parts->parts()[0]->limit())
        ->toBeGreaterThan(0);

    // Verify that the part fetches the correct employees
    $partEmployees = $parts->parts()[0]->fetchPartValues($reportTask);

    // The part object exists and has the correct structure
    expect($parts->parts()[0])
        ->toBeInstanceOf(SingleSummarySheetEarlyLatePart::class)
        ->and($parts->parts()[0]->offset())
        ->toBe(0)
        ->and($parts->parts()[0]->limit())
        ->toBeGreaterThan(0);
});

test('strategy works correctly with GenerateEarlyLateReportWorkflow', function () {
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::SingleSummarySheet->value])
        ->create();

    $workflow = new GenerateEarlyLateReportWorkflow($reportTask);
    $definition = $workflow->definition();

    expect($definition)->toBeInstanceOf(WorkflowDefinition::class);
});

test('prepare data for workflow execution', function () {
    Storage::fake('minio');

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSummarySheet->value,
            'start_date' => now()->subDays(2),
            'end_date' => now(),
        ])
        ->create();

    $period = $reportTask->data->period;
    expect($period)->toBeInstanceOf(CarbonPeriod::class);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    foreach ($period as $date) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->date($date)
            ->create();
    }

    $workflow = new GenerateEarlyLateReportWorkflow($reportTask);
    $definition = $workflow->definition();

    expect($definition)
        ->toBeInstanceOf(WorkflowDefinition::class)
        ->and($reportTask->data->sheetMode)
        ->toBe(SheetMode::SingleSummarySheet);
});
