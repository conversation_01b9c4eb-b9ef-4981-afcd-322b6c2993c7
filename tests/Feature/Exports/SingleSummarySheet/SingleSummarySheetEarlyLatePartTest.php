<?php

use App\Exports\SingleSummarySheet\SingleSummarySheetEarlyLatePart;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Models\Team;
use Carbon\CarbonPeriod;

test('fetches employees with their attendances within period', function () {
    // Arrange
    $period = CarbonPeriod::create(today(), today()->addDays(5));

    // Create a new team to isolate this test
    $team = Team::factory()->create();

    $employee = Employee::factory()->for($team)->create();

    // Create attendance records within period
    Attendance::factory()->for($employee)->for($team)->createFromPeriod($period);

    // Create attendance record outside period
    Attendance::factory()
        ->for($employee)
        ->for($team)
        ->date($period->end->copy()->addDays(1))
        ->create();

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
        ])
        ->create();

    // Act
    $result = (new SingleSummarySheetEarlyLatePart(offset: 0, limit: 10))->fetchPartValues(
        $reportTask
    );

    // Assert
    expect($result)
        ->not->toBeEmpty()
        ->and($result->contains('id', $employee->id))
        ->toBeTrue()
        ->and($result->firstWhere('id', $employee->id)->attendances)
        ->toHaveCount($period->count());
});

test('respects limit and offset', function () {
    $period = CarbonPeriod::create(today(), today()->addDay());

    // Create a new team to isolate this test
    $team = Team::factory()->create();

    $employees = Employee::factory()->for($team)->count(15)->create();

    $employees->each(function (Employee $employee) use ($period, $team) {
        Attendance::factory()->for($employee)->for($team)->createFromPeriod($period);
    });

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
        ])
        ->create();

    $result = (new SingleSummarySheetEarlyLatePart(offset: 5, limit: 5))->fetchPartValues(
        $reportTask
    );

    expect($result)->toHaveCount(5)->and($result->pluck('id'))->toHaveCount(5);
});

test('returns employees based on limit and offset', function () {
    // This is a simplified version of the previous test that doesn't rely on excluded tags
    $period = CarbonPeriod::create(today(), today()->addDay());

    $team = Team::factory()->create();

    // Create 20 employees
    $employees = Employee::factory()->for($team)->count(20)->create();

    // Create attendance records for all employees
    foreach ($employees as $employee) {
        Attendance::factory()->for($employee)->for($team)->createFromPeriod($period);
    }

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
        ])
        ->create();

    // Get first 5 employees
    $firstBatch = (new SingleSummarySheetEarlyLatePart(offset: 0, limit: 5))->fetchPartValues(
        $reportTask
    );

    // Get next 5 employees
    $secondBatch = (new SingleSummarySheetEarlyLatePart(offset: 5, limit: 5))->fetchPartValues(
        $reportTask
    );

    // Assert
    expect($firstBatch)
        ->toHaveCount(5)
        ->and($secondBatch)
        ->toHaveCount(5)
        ->and($firstBatch->pluck('id'))
        ->not->toContain($secondBatch->pluck('id')->toArray());
});
