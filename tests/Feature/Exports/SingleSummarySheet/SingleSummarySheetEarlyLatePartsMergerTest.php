<?php

use App\Enums\SheetMode;
use App\Exports\SingleSummarySheet\SingleSummarySheetEarlyLatePartsMerger;
use App\Models\Department;
use App\Models\ReportTask;
use App\Models\Team;
use Illuminate\Support\Facades\Storage;
use OpenSpout\Reader\XLSX\Reader;
use Spatie\SimpleExcel\SimpleExcelWriter;

test('merger creates excel file with correct summary data', function () {
    Storage::fake('local');

    $filePath = createLocalFile('tests/Feature/Exports/test-report.xlsx');

    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();

    $department = Department::factory()
        ->for($team)
        ->create([
            'name' => 'Engineering',
        ]);

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSummarySheet->value,
        ])
        ->create([
            'start_date' => '2023-01-01',
            'end_date' => '2023-01-05',
        ]);

    $values = collect([
        [
            'employeeID' => 'EMP001',
            'name' => '<PERSON> Do<PERSON>',
            'department' => 'Engineering',
            'absent' => '1 day',
            'leaves' => '0 day',
            'permission' => '2 hours',
            'regularizationRequests' => 1,
            'forgotToCheckout' => 0,
            'lateIn' => '00:30',
            'earlyOut' => '00:15',
            'totalActualHours' => '39:00',
            'nonCompleteWorkingHours' => '01:00',
        ],
    ]);

    $merger = new SingleSummarySheetEarlyLatePartsMerger();
    $merger->beforeMerging($writer, $reportTask);
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(1);

    $firstSheet = array_first($sheets);
    $rows = iterator_to_array($firstSheet->getRowIterator());

    expect($rows[4]->getCells())->sequence(
        fn($cell) => $cell->getValue()->toBe(''),
        fn($cell) => $cell->getValue()->toBe('EMP001'),
        fn($cell) => $cell->getValue()->toBe('John Doe'),
        fn($cell) => $cell->getValue()->toBe('Engineering'),
        fn($cell) => $cell->getValue()->toBe('1 day'),
        fn($cell) => $cell->getValue()->toBe('0 day'),
        fn($cell) => $cell->getValue()->toBe('2 hours'),
        fn($cell) => $cell->getValue()->toBe(1),
        fn($cell) => $cell->getValue()->toBe(0),
        fn($cell) => $cell->getValue()->toBe('00:30'),
        fn($cell) => $cell->getValue()->toBe('00:15'),
        fn($cell) => $cell->getValue()->toBe('39:00'),
        fn($cell) => $cell->getValue()->toBe('01:00')
    );

    $reader->close();
    unlink($filePath);
});

test('merger handles empty data gracefully', function () {
    Storage::fake('local');
    $filePath = createLocalFile('tests/Feature/Exports/test-report-empty.xlsx');
    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();
    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::SingleSummarySheet->value,
        ])
        ->create([
            'start_date' => '2023-01-01',
            'end_date' => '2023-01-05',
        ]);

    $values = collect([]);

    $merger = new SingleSummarySheetEarlyLatePartsMerger();
    $merger->beforeMerging($writer, $reportTask);
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(1);

    $firstSheet = array_first($sheets);
    $rows = iterator_to_array($firstSheet->getRowIterator());

    expect($rows)->toHaveCount(3); // Only headers

    $reader->close();
    unlink($filePath);
});
