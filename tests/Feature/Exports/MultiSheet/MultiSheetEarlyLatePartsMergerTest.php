<?php

use App\Enums\SheetMode;
use App\Exports\MultiSheet\MultiSheetEarlyLatePartsMerger;
use App\Models\Department;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Models\Team;
use Illuminate\Support\Facades\Storage;
use OpenSpout\Reader\XLSX\Reader;
use Spatie\SimpleExcel\SimpleExcelWriter;

test('merger creates excel file with correct sheets and data', function () {
    Storage::fake('local');
    $filePath = createLocalFile('tests/Feature/Exports/test-report.xlsx');
    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();

    $department = Department::factory()
        ->for($team)
        ->create([
            'name' => 'Engineering',
            'manager_id' => Employee::factory()
                ->for($team)
                ->create([
                    'first_name' => 'Manager',
                    'last_name' => 'Smith',
                ])->id,
        ]);

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::MultiSheet->value,
        ])
        ->create([
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ]);

    $values = collect([
        [
            'employee' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'number' => 'EMP001',
                'department_name' => $department->name,
                'manager_name' => 'Manager Smith',
            ],
            'summary' => [
                'totalLateIn' => '02:30',
                'totalEarlyOut' => '01:15',
                'leaveCount' => 1,
                'absentCount' => 1,
                'presentCount' => 3,
                'totalDays' => 5,
            ],
            'attendance_rows' => [
                [
                    now()->format('j/m/Y'),
                    '09:30',
                    '17:00',
                    '7:30',
                    '00:00',
                    '00:30',
                    '00:00',
                    '00:00',
                    'Morning Shift',
                    'Office A',
                    'Office B',
                    'PRESENT',
                    null,
                ],
            ],
        ],
    ]);

    $merger = new MultiSheetEarlyLatePartsMerger();
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(2);

    $rows = iterator_to_array($sheets[1]->getRowIterator());
    expect($rows[2]->getCells()[2]->getValue())
        ->toBe('John Doe')
        ->and($rows[3]->getCells()[2]->getValue())
        ->toBe('EMP001')
        ->and($rows[4]->getCells()[2]->getValue())
        ->toBe('<EMAIL>');

    $reader->close();

    unlink($filePath);
});

test('merger handles multiple employees correctly', function () {
    Storage::fake('local');
    $filePath = createLocalFile('tests/Feature/Exports/test-report-multiple.xlsx');
    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();

    $department = Department::factory()
        ->for($team)
        ->create([
            'name' => 'Engineering',
            'manager_id' => Employee::factory()
                ->for($team)
                ->create([
                    'first_name' => 'Manager',
                    'last_name' => 'Smith',
                ])->id,
        ]);

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::MultiSheet->value,
        ])
        ->create([
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ]);

    $values = collect([
        [
            'employee' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'number' => 'EMP001',
                'department_name' => $department->name,
                'manager_name' => 'Manager Smith',
            ],
            'summary' => [
                'totalLateIn' => '02:30',
                'totalEarlyOut' => '01:15',
                'leaveCount' => 1,
                'absentCount' => 1,
                'presentCount' => 3,
                'totalDays' => 5,
            ],
            'attendance_rows' => [
                [
                    now()->format('j/m/Y'),
                    '09:30',
                    '17:00',
                    '7:30',
                    '00:00',
                    '00:30',
                    '00:00',
                    '00:00',
                    'Morning Shift',
                    'Office A',
                    'Office B',
                    'PRESENT',
                    null,
                ],
            ],
        ],
        [
            'employee' => [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'number' => 'EMP002',
                'department_name' => 'Marketing',
                'manager_name' => 'Manager Brown',
            ],
            'summary' => [
                'totalLateIn' => '00:00',
                'totalEarlyOut' => '00:00',
                'leaveCount' => 0,
                'absentCount' => 0,
                'presentCount' => 5,
                'totalDays' => 5,
            ],
            'attendance_rows' => [
                [
                    now()->format('j/m/Y'),
                    '09:00',
                    '17:00',
                    '8:00',
                    '00:00',
                    '00:00',
                    '00:00',
                    '00:00',
                    'Morning Shift',
                    'Office A',
                    'Office B',
                    'PRESENT',
                    null,
                ],
            ],
        ],
    ]);

    $merger = new MultiSheetEarlyLatePartsMerger();
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(3);

    $rows = iterator_to_array($sheets[1]->getRowIterator());
    expect($rows[2]->getCells()[2]->getValue())
        ->toBe('John Doe')
        ->and($rows[3]->getCells()[2]->getValue())
        ->toBe('EMP001')
        ->and($rows[4]->getCells()[2]->getValue())
        ->toBe('<EMAIL>');

    $rows = iterator_to_array($sheets[2]->getRowIterator());
    expect($rows[2]->getCells()[2]->getValue())
        ->toBe('Jane Smith')
        ->and($rows[3]->getCells()[2]->getValue())
        ->toBe('EMP002')
        ->and($rows[4]->getCells()[2]->getValue())
        ->toBe('<EMAIL>');

    $reader->close();

    unlink($filePath);
});

test('merger handles empty data gracefully', function () {
    Storage::fake('local');
    $filePath = createLocalFile('tests/Feature/Exports/test-report-empty.xlsx');
    $writer = SimpleExcelWriter::create($filePath);

    $team = Team::factory()->create();

    $reportTask = ReportTask::factory()
        ->for($team)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::MultiSheet->value,
        ])
        ->create([
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ]);

    $values = collect([]);

    $merger = new MultiSheetEarlyLatePartsMerger();
    $merger->mergePart($writer, $values, $reportTask);
    $writer->close();

    expect(file_exists($filePath))->toBeTrue();

    $reader = new Reader();
    $reader->open($filePath);

    $sheets = iterator_to_array($reader->getSheetIterator());
    expect($sheets)->toHaveCount(1);

    $reader->close();

    unlink($filePath);
});
