<?php

use App\Exports\MultiSheet\MultiSheetEarlyLatePart;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use Carbon\CarbonPeriod;

test('fetches employees with their attendances within period', function () {
    // Arrange
    $period = CarbonPeriod::create(today(), today()->addDays(5));

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    // Create attendance records within period
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->createFromPeriod($period);

    // Create attendance record outside period
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date($period->end->copy()->addDays(1))
        ->create();

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
        ])
        ->create();

    // Act
    $result = (new MultiSheetEarlyLatePart(offset: 0, limit: 10))->fetchPartValues($reportTask);

    // Assert
    expect($result)
        ->toHaveCount(1)
        ->first()
        ->id->toBe($employee->id)
        ->and($result->first()->attendances)
        ->toHaveCount($period->count());
});

test('respects limit and offset', function () {
    $period = CarbonPeriod::create(today(), today()->addDay());

    $employees = Employee::factory()
        ->for($this->tenant)
        ->count(10)
        ->create();

    $employees->each(function (Employee $employee) use ($period) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod($period);
    });

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'start_date' => $period->start->format('Y-m-d'),
            'end_date' => $period->end->format('Y-m-d'),
        ])
        ->create();

    $result = (new MultiSheetEarlyLatePart(offset: 5, limit: 5))->fetchPartValues($reportTask);

    expect($result)
        ->toHaveCount(5)
        ->pluck('id')
        ->every(fn($id) => $employees->pluck('id')->contains($id));
});
