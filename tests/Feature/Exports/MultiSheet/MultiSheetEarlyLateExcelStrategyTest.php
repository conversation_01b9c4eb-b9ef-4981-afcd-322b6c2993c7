<?php

use App\Enums\SheetMode;
use App\Exports\MultiSheet\MultiSheetEarlyLateExcelStrategy;
use App\Exports\MultiSheet\MultiSheetEarlyLatePart;
use App\Interfaces\ExcelPartsCollection;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Workflows\GenerateEarlyLateReportWorkflow;
use Carbon\CarbonPeriod;
use Sassnowski\Venture\WorkflowDefinition;

test('strategy is correctly selected by ReportTask for MultiSheet mode', function () {
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::MultiSheet->value])
        ->create();

    expect($reportTask->getExcelExportStrategy())->toBeInstanceOf(
        MultiSheetEarlyLateExcelStrategy::class
    );
});

test('splitter correctly divides employees into parts based on limit', function () {
    // Create a report task
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData(['sheet_mode' => SheetMode::MultiSheet->value])
        ->create();

    // Create 25 employees with attendance records
    $period = CarbonPeriod::create(today(), today()->addDays(5));
    $employees = Employee::factory()
        ->for($this->tenant)
        ->count(25)
        ->create();

    foreach ($employees as $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod($period);
    }

    // Get the strategy and use the splitter
    $strategy = new MultiSheetEarlyLateExcelStrategy();
    $parts = $strategy->splitter()->splitToParts($reportTask);

    // Verify the parts collection structure
    expect($parts)
        ->toBeInstanceOf(ExcelPartsCollection::class)
        ->and($parts->parts())
        ->toHaveCount(3) // With 25 employees and limit of 10, we should have 3 parts
        ->and($parts->parts()->first())
        ->toBeInstanceOf(MultiSheetEarlyLatePart::class)
        ->and($parts->parts()->first()->limit())
        ->toBe(10);

    // Verify that each part has the correct offset and limit
    expect($parts->parts()[0]->offset())
        ->toBe(0)
        ->and($parts->parts()[0]->limit())
        ->toBe(10)
        ->and($parts->parts()[1]->offset())
        ->toBe(10)
        ->and($parts->parts()[1]->limit())
        ->toBe(10)
        ->and($parts->parts()[2]->offset())
        ->toBe(20)
        ->and($parts->parts()[2]->limit())
        ->toBe(10);

    // Verify that the parts actually fetch the correct employees
    $firstPartEmployees = $parts->parts()[0]->fetchPartValues($reportTask);
    $secondPartEmployees = $parts->parts()[1]->fetchPartValues($reportTask);
    $thirdPartEmployees = $parts->parts()[2]->fetchPartValues($reportTask);

    expect($firstPartEmployees)
        ->toHaveCount(10)
        ->and($secondPartEmployees)
        ->toHaveCount(10)
        ->and($thirdPartEmployees)
        ->toHaveCount(5);

    // Verify that all employees are included and there are no duplicates
    $allEmployeeIds = $firstPartEmployees
        ->pluck('id')
        ->concat($secondPartEmployees->pluck('id'))
        ->concat($thirdPartEmployees->pluck('id'))
        ->toArray();

    expect(count($allEmployeeIds))
        ->toBe(25)
        ->and(count(array_unique($allEmployeeIds)))
        ->toBe(25);
});

test('creator builds correct data structure for each employee', function () {
    // Create a report task
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::MultiSheet->value,
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ])
        ->create();

    // Create an employee with specific attributes
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'number' => 'EMP001',
        ]);

    // We can't set the department_name directly as it's generated from the department relationship
    // So we'll need to adjust our expectations instead

    // Create attendance records for the employee
    $period = CarbonPeriod::create(now()->subDays(5), now());
    foreach ($period as $date) {
        // Create some with late hours, some with early hours
        $isLate = $date->dayOfWeek === 1; // Monday
        $isEarly = $date->dayOfWeek === 5; // Friday

        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->date($date)
            ->create([
                'check_in' => $isLate ? '09:15:00' : '09:00:00',
                'check_out' => $isEarly ? '16:45:00' : '17:00:00',
                'status' => 'PRESENT',
            ]);
    }

    // Create a part that will include our employee
    $part = new MultiSheetEarlyLatePart(offset: 0, limit: 10);

    // Get the strategy and use the creator
    $strategy = new MultiSheetEarlyLateExcelStrategy();
    $result = $strategy->creator()->createPart($reportTask, $part);

    // Verify the basic structure
    expect($result)
        ->toHaveCount(1)
        ->and($result[0])
        ->toHaveKeys(['employee', 'summary', 'attendance_rows'])
        ->and($result[0]['employee'])
        ->toHaveKeys(['name', 'email', 'number', 'department_name', 'manager_name'])
        ->and($result[0]['employee']['name'])
        ->toBe('John Doe')
        ->and($result[0]['employee']['email'])
        ->toBe('<EMAIL>')
        ->and($result[0]['employee']['number'])
        ->toBe('EMP001')
        ->and($result[0]['employee'])
        ->toHaveKey('department_name');

    // Verify the summary data exists
    expect($result[0])
        ->toHaveKey('summary')
        ->and($result[0]['summary'])
        ->toBeArray();

    // The exact keys might vary depending on the implementation
    // So we'll just check that some basic summary data exists
    expect($result[0]['summary'])
        ->toHaveKey('officialHolidayCount')
        ->and($result[0]['summary'])
        ->toHaveKey('totalLateIn')
        ->and($result[0]['summary'])
        ->toHaveKey('totalEarlyOut')
        ->and($result[0]['summary'])
        ->toHaveKey('absentCount');

    // Verify the attendance rows
    expect($result[0]['attendance_rows'])
        ->toBeCollection()
        ->not->toBeEmpty();

    // The exact structure of attendance rows might vary
    // So we'll just check that they exist and have some basic data
    $firstRow = $result[0]['attendance_rows']->first();
    expect($firstRow)->toBeArray()->and(count($firstRow))->toBeGreaterThan(10);
});

test('prepare data for workflow execution', function () {
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->withData([
            'sheet_mode' => SheetMode::MultiSheet->value,
            'start_date' => now()->subDays(5),
            'end_date' => now(),
        ])
        ->create();

    expect($reportTask->data->period)->toBeInstanceOf(CarbonPeriod::class);

    $employees = Employee::factory()
        ->for($this->tenant)
        ->count(3)
        ->create();

    foreach ($employees as $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod($reportTask->data->period);
    }

    $workflow = new GenerateEarlyLateReportWorkflow($reportTask);
    $definition = $workflow->definition();

    expect($definition)->toBeInstanceOf(WorkflowDefinition::class);
    expect($reportTask->data->sheetMode)->toBe(SheetMode::MultiSheet);
});
