<?php

use App\Enums\AttendanceStatus;
use App\Exports\MultiSheet\MultiSheetEarlyLatePart;
use App\Exports\MultiSheet\MultiSheetEarlyLatePartCreator;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Device;
use App\Models\Employee;
use App\Models\Location;
use App\Models\ReportTask;
use App\Models\Shift;

test('creates part with correct employee data', function () {
    $creator = new MultiSheetEarlyLatePartCreator();

    // Arrange
    $department = createDefaultDepartment([
        'name' => 'Engineering',
        'manager_id' => createDefaultEmployee()->id,
    ]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->for($department)
        ->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'number' => 'EMP001',
        ]);
    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->create();

    $part = Mockery::mock(MultiSheetEarlyLatePart::class);
    $part
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$employee]));

    // Act
    $result = $creator->createPart($reportTask, $part);

    // Assert
    expect($result)
        ->toHaveCount(1)
        ->and($result[0]['employee'])
        ->email->toBe('<EMAIL>')
        ->number->toBe('EMP001')
        ->department_name->toBe('Engineering')
        ->manager_name->toBe($department->manager->name);
});

test('builds attendance cells with all attributes correctly', function () {
    $creator = new MultiSheetEarlyLatePartCreator();

    // Arrange
    $date = now();
    $shift = Shift::factory()->create(['name' => 'Morning Shift']);
    $checkInLocation = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'Office A']);
    $checkoutLocation = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'Office B']);
    $checkInDevice = Device::factory()
        ->for($this->tenant)
        ->create(['name' => 'Device A']);
    $checkoutDevice = Device::factory()
        ->for($this->tenant)
        ->create(['name' => 'Device B']);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $attendance = Attendance::factory()
        ->for($this->tenant)
        ->create([
            'date' => $date,
            'team_id' => $this->tenant->id,
            'shift_id' => $shift->id,
            'employee_id' => $employee->id,
            'in_mood' => 5,
            'out_mood' => 4,
            'status' => AttendanceStatus::PRESENT->value,
            'shift_from' => $date->copy()->setTime(9, 0),
            'shift_to' => $date->copy()->setTime(17, 0),
            'check_in' => $date->copy()->setTime(9, 0),
            'check_out' => $date->copy()->setTime(17, 0),
            'net_hours' => '8:00:00',
            'in_type' => Activity::CHECK_IN,
            'out_type' => Activity::CHECK_OUT,
            'force_checkout_time' => null,
            'on_duty' => true,
            'active_until' => $date->copy()->addHours(9),
            'flexible_hours' => 30,
            'check_in_location_id' => $checkInLocation->id,
            'check_out_location_id' => $checkoutLocation->id,
            'check_in_device_id' => $checkInDevice->id,
            'check_out_device_id' => $checkoutDevice->id,
            'have_location' => true,
        ]);

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->create();

    $part = Mockery::mock(MultiSheetEarlyLatePart::class);
    $part
        ->shouldReceive('fetchPartValues')
        ->once()
        ->andReturn(collect([$employee]));

    // Act
    $result = $creator->createPart($reportTask, $part);

    // Assert
    expect($result[0]['attendance_rows'][0])->toEqual([
        $date->format('j/m/Y'),
        '9:00 am', // check in
        '5:00 pm', // check out
        '8:00', // net hours
        '+ 00:00', // early in
        '- 00:00', // late in
        '- 00:00', // early out
        '+ 00:00', // late out
        'Morning Shift', // shift
        'Device A', // check in device
        'Office A', // check in location
        'Device B', // check out device
        'Office B', // check out location
        'PRESENT', // status
        null, // color
    ]);
});

test(
    'builds attendance cells with all attributes correctly - show mobile app when no device',
    function () {
        $creator = new MultiSheetEarlyLatePartCreator();

        // Arrange
        $employee = Employee::factory()
            ->for($this->tenant)
            ->create();

        $attendance = Attendance::factory()
            ->for($this->tenant)
            ->create([
                'date' => now(),
                'team_id' => $this->tenant->id,
                'employee_id' => $employee->id,
            ]);

        $reportTask = ReportTask::factory()
            ->for($this->tenant)
            ->earlyLate()
            ->create();

        $part = Mockery::mock(MultiSheetEarlyLatePart::class);
        $part
            ->shouldReceive('fetchPartValues')
            ->once()
            ->andReturn(collect([$employee]));

        // Act
        $result = $creator->createPart($reportTask, $part);

        // Assert
        expect($result[0]['attendance_rows'][0][9])->toEqual(config('app.default_device_name'));
        expect($result[0]['attendance_rows'][0][11])->toEqual(config('app.default_device_name'));
    }
);
