<?php

use App\Models\Employee;
use App\Models\Passport\Client;
use App\Services\RevokeTokenService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Laravel\Passport\Passport;
use Laravel\Passport\RefreshToken;
use Laravel\Passport\Token;

beforeEach(function () {
    // Fake HTTP requests
    Http::fake();
});

it('revokes token and refresh token with browser id', function () {
    // Arrange
    $browserId = 'test-browser-id';
    $logoutUrl = 'https://example.com/logout';

    $employee = Employee::factory()->create();

    Passport::actingAs($employee);

    // Create a client with a logout URL
    $client = Client::create([
        'id' => Str::uuid(),
        'name' => 'Test Client',
        'redirect' => 'http://localhost',
        'personal_access_client' => false,
        'password_client' => false,
        'revoked' => false,
        'logout_url' => $logoutUrl,
    ]);

    // Create a token
    $token = Token::create([
        'id' => Str::uuid(),
        'user_id' => $employee->id,
        'client_id' => $client->id,
        'name' => 'Test Token',
        'scopes' => [],
        'revoked' => false,
    ]);

    // Create a refresh token associated with the token
    $refreshToken = RefreshToken::create([
        'id' => Str::uuid(),
        'access_token_id' => $token->id,
        'revoked' => false,
        'expires_at' => now()->addDays(30),
    ]);

    $service = new RevokeTokenService($token, $browserId);

    // Act
    $service->handle();

    // Assert
    Http::assertSent(function ($request) use ($logoutUrl, $browserId) {
        return $request->url() === $logoutUrl && $request->data()['browser_id'] === $browserId;
    });

    // Verify that the token is revoked
    expect(Token::find($token->id)->revoked)->toBeTrue();

    // Verify that the refresh token is revoked
    expect(RefreshToken::find($refreshToken->id)->revoked)->toBeTrue();
});

it('revokes token and refresh token without browser id', function () {
    // Arrange
    $logoutUrl = 'https://example.com/logout';

    $employee = Employee::factory()->create();

    Passport::actingAs($employee);

    // Create a client with a logout URL
    $client = Client::create([
        'id' => Str::uuid(),
        'name' => 'Test Client',
        'redirect' => 'http://localhost',
        'personal_access_client' => false,
        'password_client' => false,
        'revoked' => false,
        'logout_url' => $logoutUrl,
    ]);

    // Create a token
    $token = Token::create([
        'id' => Str::uuid(),
        'user_id' => $employee->id,
        'client_id' => $client->id,
        'name' => 'Test Token',
        'scopes' => [],
        'revoked' => false,
    ]);

    // Create a refresh token associated with the token
    $refreshToken = RefreshToken::create([
        'id' => Str::uuid(),
        'access_token_id' => $token->id,
        'revoked' => false,
        'expires_at' => now()->addDays(30),
    ]);

    $service = new RevokeTokenService($token); // No browser ID

    // Act
    $service->handle();

    // Assert
    Http::assertNothingSent();

    // Verify that the token is revoked
    expect(Token::find($token->id)->revoked)->toBeTrue();

    // Verify that the refresh token is revoked
    expect(RefreshToken::find($refreshToken->id)->revoked)->toBeTrue();
});

it('revokes token and refresh token when client has no logout url', function () {
    // Arrange
    $browserId = 'test-browser-id';

    $employee = Employee::factory()->create();

    Passport::actingAs($employee);

    // Create a client with no logout URL
    $client = Client::create([
        'id' => Str::uuid(),
        'name' => 'Test Client',
        'redirect' => 'http://localhost',
        'personal_access_client' => false,
        'password_client' => false,
        'revoked' => false,
        'logout_url' => null, // No logout URL
    ]);

    // Create a token
    $token = Token::create([
        'id' => Str::uuid(),
        'user_id' => $employee->id,
        'client_id' => $client->id,
        'name' => 'Test Token',
        'scopes' => [],
        'revoked' => false,
    ]);

    // Create a refresh token associated with the token
    $refreshToken = RefreshToken::create([
        'id' => Str::uuid(),
        'access_token_id' => $token->id,
        'revoked' => false,
        'expires_at' => now()->addDays(30),
    ]);

    $service = new RevokeTokenService($token, $browserId);

    // Act
    $service->handle();

    // Assert
    Http::assertNothingSent();

    // Verify that the token is revoked
    expect(Token::find($token->id)->revoked)->toBeTrue();

    // Verify that the refresh token is revoked
    expect(RefreshToken::find($refreshToken->id)->revoked)->toBeTrue();
});
