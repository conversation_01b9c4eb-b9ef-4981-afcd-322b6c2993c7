<?php

use App\Enums\ApprovalType;
use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Events\ApprovalRequestApproved;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Notifications\ApprovalRequestApprovedNotification;
use App\Notifications\NewApprovalRequestNotification;
use App\Services\ApproveApprovalRequestService;
use Carbon\Carbon;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;

test('approves request in single layer approval system', function () {
    // Set up a single layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $manager
    ))->handle();

    // Refresh the model
    $approvalRequest->refresh();

    // Assert the request is approved
    expect($approvalRequest->status)->toBe(ApprovalRequest::APPROVED);

    // Assert a decision was created
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $approvalRequest->id,
        'decidable_type' => $approvalRequest::class,
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::First,
        'decider_id' => $manager->id,
    ]);
});

test('approves first layer in two layer approval system', function () {
    // Set up a two layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a department manager
    $departmentManager = Employee::factory()->for($team)->create();
    $department->update(['manager_id' => $departmentManager->id]);

    // Create a pending approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $manager
    ))->handle();

    // Refresh the model
    $approvalRequest->refresh();

    // Assert the request is still pending (waiting for second layer)
    expect($approvalRequest->status)->toBe(ApprovalRequest::PENDING);

    // Assert a decision was created for the first layer
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $approvalRequest->id,
        'decidable_type' => $approvalRequest::class,
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::First,
        'decider_id' => $manager->id,
    ]);
});

test('finalizes approval in two layer approval system', function () {
    // Set up a two layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a department manager
    $departmentManager = Employee::factory()->for($team)->create();
    $department->update(['manager_id' => $departmentManager->id]);

    // Create a pending approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Create a first layer decision
    $approvalRequest->updateOrCreateDecision(
        status: RequestStatus::Approved,
        decisionLayer: DecisionLayer::First,
        decider: $manager
    );

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $departmentManager
    ))->handle();

    // Refresh the model
    $approvalRequest->refresh();

    // Assert the request is approved
    expect($approvalRequest->status)->toBe(ApprovalRequest::APPROVED);

    // Assert a decision was created for the second layer
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $approvalRequest->id,
        'decidable_type' => $approvalRequest::class,
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::Second,
        'decider_id' => $departmentManager->id,
    ]);
});

test('finalizes approval for permission request', function () {
    // Set up a single layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending permission approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Create a notification to be deleted
    $notification = DatabaseNotification::create([
        'id' => \Illuminate\Support\Str::uuid(),
        'type' => NewApprovalRequestNotification::class,
        'notifiable_type' => get_class($manager),
        'notifiable_id' => $manager->id,
        'data' => ['payload' => ['id' => $approvalRequest->id]],
    ]);

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $manager
    ))->handle();

    // Refresh the model
    $approvalRequest->refresh();

    // Assert the request is approved
    expect($approvalRequest->status)->toBe(ApprovalRequest::APPROVED);

    // Assert the notification was deleted
    $this->assertDatabaseMissing('notifications', ['id' => $notification->id]);

    // For permission requests, attendance should not be updated
    $this->assertDatabaseCount('attendances', 0);
});

test('finalizes approval for regularization request with check in out', function () {
    // Set up a single layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a date for the request
    $startDate = Carbon::parse('2023-01-01 09:00:00');
    $endDate = Carbon::parse('2023-01-01 17:00:00');

    // Create an attendance record
    $attendance = Attendance::factory()
        ->for($team)
        ->for($employee)
        ->create([
            'date' => $startDate->toDateString(),
            'status' => Attendance::YET,
        ]);

    // Create a pending regularization approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::REGULARIZATION,
            'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
            'from_datetime' => $startDate,
            'to_datetime' => $endDate,
        ]);

    // Mock event and notification
    Event::fake([ApprovalRequestApproved::class]);
    Notification::fake();

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $manager
    ))->handle();

    // Refresh the models
    $approvalRequest->refresh();
    $attendance->refresh();

    // Assert the request is approved
    expect($approvalRequest->status)->toBe(ApprovalRequest::APPROVED);

    // Assert the attendance was updated
    expect($attendance->status)->toBe(Attendance::PRESENT);
    expect($attendance->check_in)->toEqual($startDate);
    expect($attendance->check_out)->toEqual($endDate);
    expect($attendance->net_hours->format('H:i:s'))->toContain('08:00:00');
    expect($attendance->in_type)->toBe(Activity::REGULATED_CHECK_IN);
    expect($attendance->out_type)->toBe(Activity::REGULATED_CHECK_OUT);
    expect($attendance->is_adjusted)->toBeTrue();

    // Assert notification was sent
    Notification::assertSentTo($employee, ApprovalRequestApprovedNotification::class);

    // Assert event was fired
    Event::assertDispatched(ApprovalRequestApproved::class);
});

test('finalizes approval for remote work request with check in', function () {
    // Set up a single layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a date for the request
    $startDate = Carbon::parse('2023-01-01 09:00:00');

    // Create an attendance record
    $attendance = Attendance::factory()
        ->for($team)
        ->for($employee)
        ->create([
            'date' => $startDate->toDateString(),
            'status' => Attendance::YET,
        ]);

    // Create a pending remote work approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::REMOTE_WORK,
            'attendance_type' => ApprovalRequest::CHECK_IN,
            'from_datetime' => $startDate,
            'to_datetime' => $startDate->copy()->addHours(8),
        ]);

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $manager
    ))->handle();

    // Refresh the models
    $approvalRequest->refresh();
    $attendance->refresh();

    // Assert the request is approved
    expect($approvalRequest->status)->toBe(ApprovalRequest::APPROVED);

    // Assert the attendance was updated
    expect($attendance->status)->toBe(Attendance::PRESENT);
    expect($attendance->check_in)->toEqual($startDate);
    expect($attendance->in_type)->toBe(Activity::REMOTE_CHECK_IN);
    expect($attendance->on_duty)->toBeTrue();
});

test('finalizes approval for regularization request with check out', function () {
    // Set up a single layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a date for the request
    $endDate = Carbon::parse('2023-01-01 17:00:00');

    // Create an attendance record
    $attendance = Attendance::factory()
        ->for($team)
        ->for($employee)
        ->create([
            'date' => $endDate->toDateString(),
            'status' => Attendance::YET,
        ]);

    // Create a pending regularization approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::REGULARIZATION,
            'attendance_type' => ApprovalRequest::CHECK_OUT,
            'from_datetime' => $endDate->copy()->subHours(8),
            'to_datetime' => $endDate,
        ]);

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $manager
    ))->handle();

    // Refresh the models
    $approvalRequest->refresh();
    $attendance->refresh();

    // Assert the request is approved
    expect($approvalRequest->status)->toBe(ApprovalRequest::APPROVED);

    // Assert the attendance was updated
    expect($attendance->status)->toBe(Attendance::PRESENT);
    expect($attendance->check_out)->toEqual($endDate);
    expect($attendance->in_type)->toBe(Activity::REGULATED_CHECK_IN);
    expect($attendance->out_type)->toBe(Activity::REGULATED_CHECK_OUT);
    expect($attendance->is_adjusted)->toBeTrue();
});

test('does not send notification to layer two manager when first layer approves', function () {
    // Set up a two layer approval system
    $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);

    // Create department with a manager (department manager = second layer)
    $departmentManager = Employee::factory()->for($team)->create();
    $department = Department::factory()
        ->for($team)
        ->create(['manager_id' => $departmentManager->id]);

    // Create direct manager (first layer) and employee
    $directManager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $directManager->id]);

    // Create a pending approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Mock notifications
    Notification::fake();

    // Execute the service
    (new ApproveApprovalRequestService(
        approvalRequest: $approvalRequest,
        decider: $directManager
    ))->handle();

    // Assert notification was NOT sent to department manager (second layer)
    // This is because the shouldSendLayerTwoNotification method checks if the decision is for the second layer
    Notification::assertNotSentTo($departmentManager, NewApprovalRequestNotification::class);

    // Verify that a first layer decision was created
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $approvalRequest->id,
        'decidable_type' => get_class($approvalRequest),
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::First,
        'decider_id' => $directManager->id,
    ]);
});
