<?php

use App\Enums\ApprovalType;
use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Leave;
use App\Models\Team;
use App\Notifications\LeaveRejectedNotification;
use App\Notifications\NewLeaveRequestNotification;
use App\Services\RejectLeaveService;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Notification;

test('rejects leave with reason', function () {
    // Set up
    $team = Team::factory()->create();
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending leave
    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Pending,
        ]);

    // Create a notification to be deleted
    $notification = DatabaseNotification::create([
        'id' => \Illuminate\Support\Str::uuid(),
        'type' => NewLeaveRequestNotification::class,
        'notifiable_type' => $manager::class,
        'notifiable_id' => $manager->id,
        'data' => ['payload' => ['id' => $leave->id]],
    ]);

    // Mock notification
    Notification::fake();

    // Execute the service
    $rejectionReason = 'Not valid leave request';
    (new RejectLeaveService(
        leave: $leave,
        rejectionReason: $rejectionReason,
        decider: $manager
    ))->handle();

    // Refresh the model
    $leave->refresh();

    // Assert the leave is rejected
    expect($leave->status)->toBe(RequestStatus::Rejected);
    expect($leave->rejection_reason)->toBe($rejectionReason);

    // Assert a decision was created
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $leave->id,
        'decidable_type' => $leave::class,
        'status' => RequestStatus::Rejected,
        'layer' => DecisionLayer::First,
        'decider_id' => $manager->id,
    ]);

    // Assert notification was sent
    Notification::assertSentTo($employee, LeaveRejectedNotification::class);

    // Assert the notification was deleted
    $this->assertDatabaseMissing('notifications', ['id' => $notification->id]);
});

test('rejects leave in second layer', function () {
    // Set up
    $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a department manager for second layer
    $departmentManager = Employee::factory()->for($team)->create();
    $department->update(['manager_id' => $departmentManager->id]);

    // Create a pending leave
    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Pending,
        ]);

    // Create a first layer decision
    $leave->updateOrCreateDecision(
        status: RequestStatus::Approved,
        decisionLayer: DecisionLayer::First,
        decider: $manager
    );

    // Execute the service
    $rejectionReason = 'Rejected in second layer';
    (new RejectLeaveService(
        leave: $leave,
        rejectionReason: $rejectionReason,
        decider: $departmentManager
    ))->handle();

    // Refresh the model
    $leave->refresh();

    // Assert the leave is rejected
    expect($leave->status)->toBe(RequestStatus::Rejected);
    expect($leave->rejection_reason)->toBe($rejectionReason);

    // Assert a decision was created for the second layer
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $leave->id,
        'decidable_type' => $leave::class,
        'status' => RequestStatus::Rejected,
        'layer' => DecisionLayer::Second,
        'decider_id' => $departmentManager->id,
    ]);
});

test('sends detailed rejection notification to employee with leave rejection reason', function () {
    // Set up
    $team = Team::factory()->create();
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending leave
    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Pending,
            'from_date' => now(),
            'to_date' => now()->addDays(3),
        ]);

    // Mock notifications
    Notification::fake();

    // Execute the service with a specific rejection reason
    $rejectionReason = 'Insufficient leave balance available';
    (new RejectLeaveService(
        leave: $leave,
        rejectionReason: $rejectionReason,
        decider: $manager
    ))->handle();

    // Assert notification was sent to the employee with the correct rejection reason
    Notification::assertSentTo($employee, LeaveRejectedNotification::class, function (
        $notification
    ) use ($leave, $rejectionReason) {
        return $notification->leave->id === $leave->id &&
            $notification->leave->rejection_reason === $rejectionReason;
    });

    // Assert the leave is rejected with the correct reason
    $leave->refresh();
    expect($leave->status)->toBe(RequestStatus::Rejected);
    expect($leave->rejection_reason)->toBe($rejectionReason);
});
