<?php

use App\Enums\ApprovalType;
use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Events\ApprovalRequestRejected;
use App\Models\ApprovalRequest;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Notifications\ApprovalRequestRejectedNotification;
use App\Notifications\NewApprovalRequestNotification;
use App\Services\RejectApprovalRequestService;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;

test('rejects approval request with reason', function () {
    // Set up
    $team = Team::factory()->create();
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Create a notification to be deleted
    $notification = DatabaseNotification::create([
        'id' => \Illuminate\Support\Str::uuid(),
        'type' => NewApprovalRequestNotification::class,
        'notifiable_type' => $manager::class,
        'notifiable_id' => $manager->id,
        'data' => ['payload' => ['id' => $approvalRequest->id]],
    ]);

    // Mock event and notification
    Event::fake([ApprovalRequestRejected::class]);
    Notification::fake();

    // Execute the service
    $rejectionReason = 'Not valid request';

    (new RejectApprovalRequestService(
        approvalRequest: $approvalRequest,
        rejectionReason: $rejectionReason,
        decider: $manager
    ))->handle();

    // Refresh the model
    $approvalRequest->refresh();

    // Assert the request is rejected
    expect($approvalRequest->status)->toBe(ApprovalRequest::REJECTED);
    expect($approvalRequest->rejection_reason)->toBe($rejectionReason);

    // Assert a decision was created
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $approvalRequest->id,
        'decidable_type' => $approvalRequest::class,
        'status' => RequestStatus::Rejected,
        'layer' => DecisionLayer::First,
        'decider_id' => $manager->id,
    ]);

    // Assert notification was sent
    Notification::assertSentTo($employee, ApprovalRequestRejectedNotification::class);

    // Assert event was fired
    Event::assertDispatched(ApprovalRequestRejected::class);

    // Assert the notification was deleted
    $this->assertDatabaseMissing('notifications', ['id' => $notification->id]);
});

test('rejects approval request in second layer', function () {
    // Set up
    $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a department manager for second layer
    $departmentManager = Employee::factory()->for($team)->create();
    $department->update(['manager_id' => $departmentManager->id]);

    // Create a pending approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Create a first layer decision
    $approvalRequest->updateOrCreateDecision(
        status: RequestStatus::Approved,
        decisionLayer: DecisionLayer::First,
        decider: $manager
    );

    // Execute the service
    $rejectionReason = 'Rejected in second layer';

    (new RejectApprovalRequestService(
        approvalRequest: $approvalRequest,
        rejectionReason: $rejectionReason,
        decider: $departmentManager
    ))->handle();

    // Refresh the model
    $approvalRequest->refresh();

    // Assert the request is rejected
    expect($approvalRequest->status)->toBe(ApprovalRequest::REJECTED);
    expect($approvalRequest->rejection_reason)->toBe($rejectionReason);

    // Assert a decision was created for the second layer
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $approvalRequest->id,
        'decidable_type' => $approvalRequest::class,
        'status' => RequestStatus::Rejected,
        'layer' => DecisionLayer::Second,
        'decider_id' => $departmentManager->id,
    ]);
});

test('sends detailed rejection notification to employee with reason', function () {
    // Set up
    $team = Team::factory()->create();
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending approval request
    $approvalRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => ApprovalRequest::PENDING,
            'type' => ApprovalRequest::PERMISSION,
        ]);

    // Mock notifications and events
    Notification::fake();

    // Execute the service with a specific rejection reason
    $rejectionReason = 'This request conflicts with team schedule';
    (new RejectApprovalRequestService(
        approvalRequest: $approvalRequest,
        rejectionReason: $rejectionReason,
        decider: $manager
    ))->handle();

    // Assert notification was sent to the employee with the correct rejection reason
    Notification::assertSentTo($employee, ApprovalRequestRejectedNotification::class, function (
        $notification
    ) use ($approvalRequest, $rejectionReason) {
        return $notification->approvalRequest->id === $approvalRequest->id &&
            $notification->approvalRequest->rejection_reason === $rejectionReason;
    });

    // Assert the request is rejected with the correct reason
    $approvalRequest->refresh();
    expect($approvalRequest->status)->toBe(ApprovalRequest::REJECTED);
    expect($approvalRequest->rejection_reason)->toBe($rejectionReason);
});
