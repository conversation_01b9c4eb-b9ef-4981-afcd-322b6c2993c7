<?php

use App\Enums\ApprovalType;
use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Leave;
use App\Models\Team;
use App\Notifications\NewLeaveRequestNotification;
use App\Services\ApproveLeaveService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Facades\Notification;

test('approves leave in single layer approval system', function () {
    // Set up
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending leave
    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Pending,
            'from_date' => Carbon::parse('2023-01-01'),
            'to_date' => Carbon::parse('2023-01-03'),
        ]);

    // Create attendance records for the leave period
    $period = CarbonPeriod::create('2023-01-01', '2023-01-03');
    $attendances = [];
    foreach ($period as $date) {
        $attendances[] = Attendance::factory()
            ->for($team)
            ->for($employee)
            ->create([
                'date' => $date->toDateString(),
                'status' => Attendance::ABSENT,
            ]);
    }

    // Create a notification to be deleted
    $notification = DatabaseNotification::create([
        'id' => \Illuminate\Support\Str::uuid(),
        'type' => NewLeaveRequestNotification::class,
        'notifiable_type' => $manager::class,
        'notifiable_id' => $manager->id,
        'data' => ['payload' => ['id' => $leave->id]],
    ]);

    // Execute the service
    (new ApproveLeaveService($leave, $manager))->handle();

    // Refresh the model
    $leave->refresh();

    // Assert the leave is approved
    expect($leave->status)->toBe(RequestStatus::Approved);

    // Assert a decision was created
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $leave->id,
        'decidable_type' => $leave::class,
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::First,
        'decider_id' => $manager->id,
    ]);

    // Assert the notification was deleted
    $this->assertDatabaseMissing('notifications', ['id' => $notification->id]);

    // Assert attendance records were updated to leave status
    foreach ($attendances as $attendance) {
        $attendance->refresh();
        expect($attendance->status)->toBe(Attendance::LEAVE);
    }
});

test('approves first layer in two layer approval system', function () {
    // Set up
    $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a department manager
    $departmentManager = Employee::factory()->for($team)->create();
    $department->update(['manager_id' => $departmentManager->id]);

    // Create a pending leave
    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Pending,
            'from_date' => Carbon::parse('2023-01-01'),
            'to_date' => Carbon::parse('2023-01-03'),
        ]);

    // Execute the service
    (new ApproveLeaveService($leave, $manager))->handle();

    // Refresh the model
    $leave->refresh();

    // Assert the leave is still pending (waiting for second layer)
    expect($leave->status)->toBe(RequestStatus::Pending);

    // Assert a decision was created for the first layer
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $leave->id,
        'decidable_type' => $leave::class,
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::First,
        'decider_id' => $manager->id,
    ]);

    // Assert attendance records were not updated yet
    $this->assertDatabaseMissing('attendances', [
        'employee_id' => $employee->id,
        'status' => Attendance::LEAVE,
    ]);
});

test('finalizes approval in two layer approval system', function () {
    // Set up
    $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a department manager
    $departmentManager = Employee::factory()->for($team)->create();
    $department->update(['manager_id' => $departmentManager->id]);

    // Create a pending leave
    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Pending,
            'from_date' => Carbon::parse('2023-01-01'),
            'to_date' => Carbon::parse('2023-01-03'),
        ]);

    // Create attendance records for the leave period
    $period = CarbonPeriod::create('2023-01-01', '2023-01-03');
    $attendances = [];
    foreach ($period as $date) {
        $attendances[] = Attendance::factory()
            ->for($team)
            ->for($employee)
            ->create([
                'date' => $date->toDateString(),
                'status' => Attendance::ABSENT,
            ]);
    }

    // Create a first layer decision
    $leave->updateOrCreateDecision(
        status: RequestStatus::Approved,
        decisionLayer: DecisionLayer::First,
        decider: $manager
    );

    // Execute the service
    (new ApproveLeaveService($leave, $departmentManager))->handle();

    // Refresh the model
    $leave->refresh();

    // Assert the leave is approved
    expect($leave->status)->toBe(RequestStatus::Approved);

    // Assert a decision was created for the second layer
    $this->assertDatabaseHas('decisions', [
        'decidable_id' => $leave->id,
        'decidable_type' => $leave::class,
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::Second,
        'decider_id' => $departmentManager->id,
    ]);

    // Assert attendance records were updated to leave status
    foreach ($attendances as $attendance) {
        $attendance->refresh();
        expect($attendance->status)->toBe(Attendance::LEAVE);
    }
});

test('syncs attendance records when leave is approved', function () {
    // Set up
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);
    $department = Department::factory()->for($team)->create();
    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create a pending leave
    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Pending,
            'from_date' => Carbon::parse('2023-01-01'),
            'to_date' => Carbon::parse('2023-01-03'),
        ]);

    // Create attendance records for the leave period
    $period = CarbonPeriod::create('2023-01-01', '2023-01-03');
    $attendances = [];
    foreach ($period as $date) {
        $attendances[] = Attendance::factory()
            ->for($team)
            ->for($employee)
            ->create([
                'date' => $date->toDateString(),
                'status' => Attendance::ABSENT,
            ]);
    }

    // Execute the service
    (new ApproveLeaveService($leave, $manager))->handle();

    // Refresh the model
    $leave->refresh();

    // Assert the leave is approved
    expect($leave->status)->toBe(RequestStatus::Approved);

    // Assert attendance records were updated to leave status
    foreach ($attendances as $attendance) {
        $attendance->refresh();
        expect($attendance->status)->toBe(Attendance::LEAVE);
    }

    // Assert the leave has been synced to attendance
    expect($leave->synced_to_attendance_at)->not->toBeNull();
});

test(
    'does not send notification to layer two manager when first layer approves leave',
    function () {
        // Set up a two layer approval system
        $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);

        // Create department with a manager (department manager = second layer)
        $departmentManager = Employee::factory()->for($team)->create();
        $department = Department::factory()
            ->for($team)
            ->create(['manager_id' => $departmentManager->id]);

        // Create direct manager (first layer) and employee
        $directManager = Employee::factory()->for($team)->create();
        $employee = Employee::factory()
            ->for($team)
            ->for($department)
            ->create(['manager_id' => $directManager->id]);

        // Create a pending leave
        $leave = Leave::factory()
            ->for($team)
            ->for($employee)
            ->for($department)
            ->create([
                'status' => RequestStatus::Pending,
                'from_date' => Carbon::parse('2023-01-01'),
                'to_date' => Carbon::parse('2023-01-03'),
            ]);

        // Mock notifications
        Notification::fake();

        // Execute the service
        (new ApproveLeaveService($leave, $directManager))->handle();

        // Assert notification was NOT sent to department manager (second layer)
        // This is because the shouldSendLayerTwoNotification method checks if the decision is for the second layer
        Notification::assertNotSentTo($departmentManager, NewLeaveRequestNotification::class);

        // Verify that a first layer decision was created
        $this->assertDatabaseHas('decisions', [
            'decidable_id' => $leave->id,
            'decidable_type' => get_class($leave),
            'status' => RequestStatus::Approved,
            'layer' => DecisionLayer::First,
            'decider_id' => $directManager->id,
        ]);
    }
);
