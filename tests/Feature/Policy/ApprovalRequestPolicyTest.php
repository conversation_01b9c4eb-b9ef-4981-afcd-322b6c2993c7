<?php

use App\Enums\DelegationType;
use App\Models\ApprovalRequest;
use App\Models\Delegation;
use App\Models\Employee;
use App\Policies\ApprovalRequestPolicy;

dataset('approval request types', [
    [
        'approvalRequestType' => ApprovalRequest::PERMISSION,
        'delegationType' => DelegationType::PermissionRequest,
    ],
    [
        'approvalRequestType' => ApprovalRequest::REMOTE_WORK,
        'delegationType' => DelegationType::RemoteWorkRequest,
    ],
    [
        'approvalRequestType' => ApprovalRequest::REGULARIZATION,
        'delegationType' => DelegationType::RegularizationRequest,
    ],
]);

describe('approve approval request', function () {
    test(
        'department manager can approve regularization approval requests if approval requests are not delegated to another employee',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $department = createDefaultDepartment();

            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create();

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $approvalRequest = ApprovalRequest::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            # act
            $this->assertTrue((new ApprovalRequestPolicy())->approve($manager, $approvalRequest));
        }
    )->with('approval request types');

    test(
        'line manager can approve regularization approval requests if approval requests are not delegated to another employee',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $tenant = $this->tenant;

            $department = createDefaultDepartment();

            $employee = Employee::factory()->for($tenant)->for($department)->create();

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $approvalRequest = ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            # act
            $this->assertTrue((new ApprovalRequestPolicy())->approve($manager, $approvalRequest));
        }
    )->with('approval request types');

    test(
        'department manager can not approve regularization approval requests if approval requests are delegated to another employee',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $department = createDefaultDepartment();

            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create();

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $delegated = createDefaultEmployee();

            $approvalRequest = ApprovalRequest::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            Delegation::factory()->create([
                'type' => $delegationType,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new ApprovalRequestPolicy())->approve($manager, $approvalRequest));
        }
    )->with('approval request types');

    test(
        'line manager can not approve regularization approval requests if approval requests are delegated to another employee',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $department = createDefaultDepartment();

            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create();

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $delegated = createDefaultEmployee();

            $approvalRequest = ApprovalRequest::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            Delegation::factory()->create([
                'type' => $delegationType,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new ApprovalRequestPolicy())->approve($manager, $approvalRequest));
        }
    )->with('approval request types');

    test('delegated employee can approve regularization approval requests', function (
        string $approvalRequestType,
        DelegationType $delegationType
    ) {
        # prepare
        $department = createDefaultDepartment();
        $employee = Employee::factory()
            ->for($this->tenant)
            ->for($department)
            ->create();

        $manager = createDefaultEmployee();
        $department->update(['manager_id' => $manager->id]);

        $delegated = createDefaultEmployee();

        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employee)
            ->for($department)
            ->create([
                'type' => $approvalRequestType,
                'status' => ApprovalRequest::PENDING,
                'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
            ]);

        Delegation::factory()->create([
            'type' => $delegationType,
            'delegated_id' => $delegated->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        $this->assertTrue((new ApprovalRequestPolicy())->approve($delegated, $approvalRequest));
    })->with('approval request types');

    test(
        'non-delegated non-manager employee can not approve regularization approval requests',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $department = createDefaultDepartment();
            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create();

            $manager = createDefaultEmployee();
            $department->update(['manager_id' => $manager->id]);

            $approvalRequest = ApprovalRequest::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            # act
            $this->assertFalse((new ApprovalRequestPolicy())->approve($employee, $approvalRequest));
        }
    )->with('approval request types');
});

describe('reject approval request', function () {
    test(
        'department manager can reject regularization approval requests if approval requests are not delegated to another employee',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $department = createDefaultDepartment();
            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create();

            $manager = createDefaultEmployee();
            $department->update(['manager_id' => $manager->id]);

            $approvalRequest = ApprovalRequest::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            # act
            $this->assertTrue((new ApprovalRequestPolicy())->reject($manager, $approvalRequest));
        }
    )->with('approval request types');

    test(
        'line manager can reject regularization approval requests if approval requests are not delegated to another employee',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $tenant = $this->tenant;
            $department = createDefaultDepartment();
            $employee = Employee::factory()->for($tenant)->for($department)->create();

            $manager = createDefaultEmployee();
            $department->update(['manager_id' => $manager->id]);

            $approvalRequest = ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            # act
            $this->assertTrue((new ApprovalRequestPolicy())->reject($manager, $approvalRequest));
        }
    )->with('approval request types');

    test(
        'department manager can not reject regularization approval requests if approval requests are delegated to another employee',
        function () {
            # prepare
            $tenant = $this->tenant;
            $department = createDefaultDepartment();
            $employee = Employee::factory()->for($tenant)->for($department)->create();

            $manager = createDefaultEmployee();
            $department->update(['manager_id' => $manager->id]);

            $delegated = createDefaultEmployee();

            $approvalRequest = ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => ApprovalRequest::REGULARIZATION,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            Delegation::factory()->create([
                'type' => DelegationType::RegularizationRequest,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new ApprovalRequestPolicy())->reject($manager, $approvalRequest));
        }
    );

    test(
        'line manager can not reject regularization approval requests if approval requests are delegated to another employee',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $department = createDefaultDepartment();
            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create();

            $manager = createDefaultEmployee();
            $department->update(['manager_id' => $manager->id]);

            $delegated = createDefaultEmployee();

            $approvalRequest = ApprovalRequest::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            Delegation::factory()->create([
                'type' => $delegationType,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new ApprovalRequestPolicy())->reject($manager, $approvalRequest));
        }
    )->with('approval request types');

    test('delegated employee can reject regularization approval requests', function (
        string $approvalRequestType,
        DelegationType $delegationType
    ) {
        # prepare
        $department = createDefaultDepartment();
        $employee = Employee::factory()
            ->for($this->tenant)
            ->for($department)
            ->create();

        $manager = createDefaultEmployee();
        $department->update(['manager_id' => $manager->id]);

        $delegated = createDefaultEmployee();

        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employee)
            ->for($department)
            ->create([
                'type' => $approvalRequestType,
                'status' => ApprovalRequest::PENDING,
                'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
            ]);

        Delegation::factory()->create([
            'type' => $delegationType,
            'delegated_id' => $delegated->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        $this->assertTrue((new ApprovalRequestPolicy())->reject($delegated, $approvalRequest));
    })->with('approval request types');

    test(
        'non-delegated non-manager employee can not reject regularization approval requests',
        function (string $approvalRequestType, DelegationType $delegationType) {
            # prepare
            $department = createDefaultDepartment();
            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create();

            $manager = createDefaultEmployee();
            $department->update(['manager_id' => $manager->id]);

            $approvalRequest = ApprovalRequest::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create([
                    'type' => $approvalRequestType,
                    'status' => ApprovalRequest::PENDING,
                    'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
                ]);

            # act
            $this->assertFalse((new ApprovalRequestPolicy())->reject($employee, $approvalRequest));
        }
    )->with('approval request types');
});
