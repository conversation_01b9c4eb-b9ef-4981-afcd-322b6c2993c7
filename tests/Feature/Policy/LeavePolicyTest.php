<?php

use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Models\Delegation;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Leave;
use App\Policies\LeavePolicy;

describe('approve leave request', function () {
    test(
        'department manager can approve leave requests if leave requests are not delegated to another employee',
        function () {
            # prepare
            $tenant = $this->tenant;
            $department = Department::factory()->for($tenant)->create();
            $employee = Employee::factory()
                ->for($tenant)
                ->for($department)
                ->create(['is_active' => true, 'manager_id' => null]);

            $manager = Employee::factory()
                ->for($tenant)
                ->create(['is_active' => true, 'manager_id' => null]);
            $department->update(['manager_id' => $manager->id]);

            $leave = Leave::factory()
                ->for($tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            # act
            $this->assertTrue((new LeavePolicy())->approve($manager, $leave));
        }
    );

    test(
        'line manager can approve leave requests if leave requests are not delegated to another employee',
        function () {
            # prepare
            $department = createDefaultDepartment();

            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create(['manager_id' => null]);

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $leave = Leave::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            # act
            $this->assertTrue((new LeavePolicy())->approve($manager, $leave));
        }
    );

    test(
        'department manager can not approve leave requests if leave requests are delegated to another employee',
        function () {
            # prepare
            $tenant = $this->tenant;
            $department = Department::factory()->for($tenant)->create();
            $employee = Employee::factory()
                ->for($tenant)
                ->for($department)
                ->create(['is_active' => true, 'manager_id' => null]);

            $manager = Employee::factory()
                ->for($tenant)
                ->create(['is_active' => true, 'manager_id' => null]);
            $department->update(['manager_id' => $manager->id]);

            $delegated = Employee::factory()
                ->for($tenant)
                ->create(['is_active' => true, 'manager_id' => null]);

            $leave = Leave::factory()
                ->for($tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            Delegation::factory()->create([
                'type' => DelegationType::LeaveRequest,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new LeavePolicy())->approve($manager, $leave));
        }
    );

    test(
        'line manager can not approve leave requests if leave requests are delegated to another employee',
        function () {
            # prepare
            $department = createDefaultDepartment();

            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create(['manager_id' => null]);

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $delegated = createDefaultEmployee();

            $leave = Leave::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            Delegation::factory()->create([
                'type' => DelegationType::LeaveRequest,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new LeavePolicy())->approve($manager, $leave));
        }
    );

    test('delegated employee can approve leave requests', function () {
        # prepare
        $tenant = $this->tenant;
        $department = Department::factory()->for($tenant)->create();
        $employee = Employee::factory()
            ->for($tenant)
            ->for($department)
            ->create(['is_active' => true, 'manager_id' => null]);

        $manager = Employee::factory()
            ->for($tenant)
            ->create(['is_active' => true, 'manager_id' => null]);
        $department->update(['manager_id' => $manager->id]);

        $delegated = Employee::factory()
            ->for($tenant)
            ->create(['is_active' => true, 'manager_id' => null]);

        $leave = Leave::factory()
            ->for($tenant)
            ->for($employee)
            ->for($department)
            ->create(['status' => RequestStatus::Pending->value]);

        Delegation::factory()->create([
            'type' => DelegationType::LeaveRequest,
            'delegated_id' => $delegated->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        $this->assertTrue((new LeavePolicy())->approve($delegated, $leave));
    });

    test('non-delegated non-manager employee can not approve leave requests', function () {
        # prepare
        $department = createDefaultDepartment();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->for($department)
            ->create(['manager_id' => createDefaultEmployee()->id]);

        $manager = createDefaultEmployee();

        $leave = Leave::factory()
            ->for($this->tenant)
            ->for($employee)
            ->for($department)
            ->create(['status' => RequestStatus::Pending->value]);

        # act
        $this->assertFalse((new LeavePolicy())->approve($manager, $leave));
    });
});

describe('reject leave request', function () {
    test(
        'department manager can reject leave requests if leave requests are not delegated to another employee',
        function () {
            # prepare
            $tenant = $this->tenant;
            $department = Department::factory()->for($tenant)->create();
            $employee = Employee::factory()
                ->for($tenant)
                ->for($department)
                ->create(['is_active' => true, 'manager_id' => null]);

            $manager = Employee::factory()
                ->for($tenant)
                ->create(['is_active' => true, 'manager_id' => null]);
            $department->update(['manager_id' => $manager->id]);

            $leave = Leave::factory()
                ->for($tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            # act
            $this->assertTrue((new LeavePolicy())->reject($manager, $leave));
        }
    );

    test(
        'line manager can reject leave requests if leave requests are not delegated to another employee',
        function () {
            # prepare
            $department = createDefaultDepartment();

            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create(['manager_id' => null]);

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $leave = Leave::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            # act
            $this->assertTrue((new LeavePolicy())->reject($manager, $leave));
        }
    );

    test(
        'department manager can not reject leave requests if leave requests are delegated to another employee',
        function () {
            # prepare
            $tenant = $this->tenant;
            $department = Department::factory()->for($tenant)->create();
            $employee = Employee::factory()
                ->for($tenant)
                ->for($department)
                ->create(['is_active' => true, 'manager_id' => null]);

            $manager = Employee::factory()
                ->for($tenant)
                ->create(['is_active' => true, 'manager_id' => null]);
            $department->update(['manager_id' => $manager->id]);

            $delegated = Employee::factory()
                ->for($tenant)
                ->create(['is_active' => true, 'manager_id' => null]);

            $leave = Leave::factory()
                ->for($tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            Delegation::factory()->create([
                'type' => DelegationType::LeaveRequest,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new LeavePolicy())->reject($manager, $leave));
        }
    );

    test(
        'line manager can not reject leave requests if leave requests are delegated to another employee',
        function () {
            # prepare
            $department = createDefaultDepartment();

            $employee = Employee::factory()
                ->for($this->tenant)
                ->for($department)
                ->create(['manager_id' => null]);

            $manager = createDefaultEmployee();

            $department->update(['manager_id' => $manager->id]);

            $delegated = createDefaultEmployee();

            $leave = Leave::factory()
                ->for($this->tenant)
                ->for($employee)
                ->for($department)
                ->create(['status' => RequestStatus::Pending->value]);

            Delegation::factory()->create([
                'type' => DelegationType::LeaveRequest,
                'delegated_id' => $delegated->id,
                'delegatee_id' => $manager->id,
            ]);

            # act
            $this->assertFalse((new LeavePolicy())->reject($manager, $leave));
        }
    );

    test('delegated employee can reject leave requests', function () {
        # prepare
        $tenant = $this->tenant;
        $department = Department::factory()->for($tenant)->create();
        $employee = Employee::factory()
            ->for($tenant)
            ->for($department)
            ->create(['is_active' => true, 'manager_id' => null]);

        $manager = Employee::factory()
            ->for($tenant)
            ->create(['is_active' => true, 'manager_id' => null]);
        $department->update(['manager_id' => $manager->id]);

        $delegated = Employee::factory()
            ->for($tenant)
            ->create(['is_active' => true, 'manager_id' => null]);

        $leave = Leave::factory()
            ->for($tenant)
            ->for($employee)
            ->for($department)
            ->create(['status' => RequestStatus::Pending->value]);

        Delegation::factory()->create([
            'type' => DelegationType::LeaveRequest,
            'delegated_id' => $delegated->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        $this->assertTrue((new LeavePolicy())->reject($delegated, $leave));
    });

    test('non-delegated non-manager employee can not reject leave requests', function () {
        # prepare
        $department = createDefaultDepartment();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->for($department)
            ->create(['manager_id' => createDefaultEmployee()->id]);

        $manager = createDefaultEmployee();

        $leave = Leave::factory()
            ->for($this->tenant)
            ->for($employee)
            ->for($department)
            ->create(['status' => RequestStatus::Pending->value]);

        # act
        $this->assertFalse((new LeavePolicy())->reject($manager, $leave));
    });
});
