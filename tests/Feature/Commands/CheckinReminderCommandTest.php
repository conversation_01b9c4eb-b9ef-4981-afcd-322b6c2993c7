<?php

use App\Console\Commands\CheckinReminderCommand;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Team;
use App\Notifications\CheckinReminderNotification;
use Illuminate\Support\Facades\Notification;
use function Pest\Laravel\assertDatabaseCount;
use function Pest\Laravel\travelTo;

test('it sends checkin reminder for active employees with attendance', function () {
    // Mock the notification facade
    Notification::fake();

    // Set a fixed time for testing
    travelTo('2023-01-01 08:00:00');

    $team = Team::factory()->enterprise()->create();

    $employee = Employee::factory()->for($team)->active()->defaultShift()->create();

    Attendance::factory()
        ->yet()
        ->for($employee)
        ->for($team)
        ->date(
            date: '2023-01-01 08:00:00',
            shiftFrom: '2023-01-01 08:00:00',
            shiftTo: '2023-01-01 16:00:00'
        )
        ->create();

    // Run the command
    $this->artisan(CheckinReminderCommand::class)->assertSuccessful();

    // Assert the notification was sent to the employee
    Notification::assertSentTo($employee, CheckinReminderNotification::class);
});

test('dont send for inactive team or employee', function () {
    // Mock the notification facade
    Notification::fake();

    // Set a fixed time for testing
    travelTo('2023-01-01 08:00:00');

    // Create an inactive team
    $inactiveTeam = Team::factory()->inactive()->create();
    $employeeInInactiveTeam = Employee::factory()
        ->for($inactiveTeam)
        ->active()
        ->defaultShift()
        ->create();

    // Create active team with inactive employee
    $activeTeam = Team::factory()->enterprise()->create();
    $inactiveEmployee = Employee::factory()->for($activeTeam)->inactive()->defaultShift()->create();

    // Create attendances
    Attendance::factory()
        ->yet()
        ->for($employeeInInactiveTeam)
        ->for($inactiveTeam)
        ->date(
            date: '2023-01-01 08:00:00',
            shiftFrom: '2023-01-01 08:00:00',
            shiftTo: '2023-01-01 16:00:00'
        )
        ->create();

    Attendance::factory()
        ->yet()
        ->for($inactiveEmployee)
        ->for($activeTeam)
        ->date(
            date: '2023-01-01 08:00:00',
            shiftFrom: '2023-01-01 08:00:00',
            shiftTo: '2023-01-01 16:00:00'
        )
        ->create();

    // Run the command
    $this->artisan(CheckinReminderCommand::class)->assertSuccessful();

    // Assert no notifications were sent
    Notification::assertNothingSent();
});

test('dont sent two notification for same employee', function () {
    // Set a fixed time for testing
    travelTo('2023-01-01 08:00:00');

    $team = Team::factory()->enterprise()->create();
    $employee = Employee::factory()->for($team)->active()->defaultShift()->create();

    // Create first attendance and send notification
    Attendance::factory()
        ->yet()
        ->for($employee)
        ->for($team)
        ->date(
            date: '2023-01-01 08:00:00',
            shiftFrom: '2023-01-01 08:00:00',
            shiftTo: '2023-01-01 16:00:00'
        )
        ->create();

    $this->artisan(CheckinReminderCommand::class)->assertSuccessful();

    $this->artisan(CheckinReminderCommand::class)->assertSuccessful();

    assertDatabaseCount('notifications', 1);
});

test('send only on shift start time', function () {
    // Mock the notification facade
    Notification::fake();

    // Set a fixed time for testing
    travelTo('2023-01-01 08:30:00');

    $team = Team::factory()->enterprise()->create();
    $employee = Employee::factory()->for($team)->active()->defaultShift()->create();

    // Create attendance with shift start at 8:00
    Attendance::factory()
        ->yet()
        ->for($employee)
        ->for($team)
        ->date(
            date: '2023-01-01 08:00:00',
            shiftFrom: '2023-01-01 08:00:00',
            shiftTo: '2023-01-01 16:00:00'
        )
        ->create();

    // Run command at 8:30 - should not send notification
    $this->artisan(CheckinReminderCommand::class)->assertSuccessful();

    Notification::assertNothingSent();

    // Travel to exact shift start time
    travelTo('2023-01-01 08:00:00');

    // Run command at shift start - should send notification
    $this->artisan(CheckinReminderCommand::class)->assertSuccessful();

    Notification::assertSentTo($employee, CheckinReminderNotification::class);
});
