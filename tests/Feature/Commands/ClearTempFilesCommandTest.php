<?php

use function Pest\Laravel\travelBack;
use function Pest\Laravel\travelTo;

test('delete temp files older than 24 hours', function () {
    Storage::fake();

    // 1. test that files older than 24 hours are deleted
    Storage::put('temp/old.txt', 'content', 'public');

    travelTo(now()->addHours(25));

    $this->artisan('clear:temp-files')->assertSuccessful();

    Storage::assertMissing('temp/old.txt');

    // 2. test that files newer than 24 hours are not deleted
    travelBack();

    Storage::put('temp/new.txt', 'content', 'public');

    $this->artisan('clear:temp-files')->assertSuccessful();

    Storage::assertExists('temp/new.txt');
});
