<?php

use App\Enums\ReportTaskStatus;
use App\Models\ReportTask;
use function Pest\Laravel\artisan;

test('marks stale report tasks as failed', function () {
    // Create a stale pending report task (older than 2 hour)
    $staleTask = ReportTask::factory()
        ->pending()
        ->create(['created_at' => now()->subHours(3)]);

    // Create a recent pending report task (within 2 hour)
    $recentTask = ReportTask::factory()
        ->pending()
        ->create(['created_at' => now()->subMinutes(30)]);

    // Create an already completed task
    $completedTask = ReportTask::factory()->create([
        'created_at' => now()->subHours(2),
        'status' => ReportTaskStatus::Success,
    ]);

    // Run the command
    artisan('report-tasks:mark-stale-as-failed')->assertSuccessful();

    // Assert stale task was marked as failed
    expect($staleTask->fresh())
        ->status->toBe(ReportTaskStatus::Failed)
        ->error_message->toBe('failed because it was not completed in 2 hour');

    // Assert recent task was not affected
    expect($recentTask->fresh())->status->toBe(ReportTaskStatus::Pending);

    // Assert completed task was not affected
    expect($completedTask->fresh())->status->toBe(ReportTaskStatus::Success);
});
