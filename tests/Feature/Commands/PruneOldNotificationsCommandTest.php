<?php

use App\Models\Employee;
use Illuminate\Notifications\DatabaseNotification;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\travelBack;

it('prunes notifications older than 3 months', function () {
    // Create a notification older than 3 months
    $oldNotification = DatabaseNotification::create([
        'id' => uuid_create(),
        'type' => 'App\Notifications\TestNotification',
        'notifiable_type' => Employee::class,
        'notifiable_id' => 1,
        'data' => '{"message":"Test notification"}',
        'created_at' => now()->subMonths(4),
        'updated_at' => now()->subMonths(4),
    ]);

    // Create a notification newer than 3 months
    $newNotification = DatabaseNotification::create([
        'id' => uuid_create(),
        'type' => 'App\Notifications\TestNotification',
        'notifiable_type' => Employee::class,
        'notifiable_id' => 1,
        'data' => '{"message":"Test notification"}',
        'created_at' => now()->subMonths(2),
        'updated_at' => now()->subMonths(2),
    ]);

    // Run the prune command
    $this->artisan('old-notifications:prune')->assertSuccessful();

    // Assert the old notification was pruned and new one remains
    assertDatabaseMissing('notifications', ['id' => $oldNotification->id]);
    assertDatabaseHas('notifications', ['id' => $newNotification->id]);

    travelBack();
});
