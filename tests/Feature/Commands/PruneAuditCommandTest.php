<?php

use App\Models\Employee;
use App\Models\Team;
use OwenIt\Auditing\Models\Audit;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\travelBack;
use function Pest\Laravel\travelTo;

it('prunes audits older than 3 months', function () {
    // Freeze time
    travelTo(now());

    $team = Team::factory()->create();
    $employee = Employee::factory()->for($team)->create();

    // Create an audit older than 3 months
    $oldAudit = Audit::create([
        'event' => 'created',
        'auditable_id' => $employee->id,
        'auditable_type' => Employee::class,
        'user_id' => $employee->id,
        'user_type' => Employee::class,
        'team_id' => $team->id,
        'old_values' => [],
        'new_values' => [],
        'created_at' => now()->subMonths(4),
        'updated_at' => now()->subMonths(4),
    ]);

    // Create an audit newer than 3 months
    $newAudit = Audit::create([
        'event' => 'updated',
        'auditable_id' => $employee->id,
        'auditable_type' => Employee::class,
        'user_id' => $employee->id,
        'user_type' => Employee::class,
        'team_id' => $team->id,
        'old_values' => ['foo' => 'bar'],
        'new_values' => ['foo' => 'baz'],
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    // Run the prune command
    $this->artisan('audit:prune')->assertSuccessful();

    // Assert the old audit was pruned and new one remains
    assertDatabaseMissing('audits', ['id' => $oldAudit->id]);
    assertDatabaseHas('audits', ['id' => $newAudit->id]);

    travelBack();
});
