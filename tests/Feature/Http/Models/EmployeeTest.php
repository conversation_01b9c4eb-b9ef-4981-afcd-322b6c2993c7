<?php

use App\Enums\DelegationType;
use App\Models\Delegation;
use App\Models\Employee;
use App\Models\Tag;
use App\Models\Team;
use function PHPUnit\Framework\assertTrue;

test('test delegations relation in employee model', function () {
    $manager = createDefaultEmployee();

    $remoteWorkEmployee = createDefaultEmployee();

    $remoteWorkDelegation = Delegation::factory()
        ->for($remoteWorkEmployee, 'delegated')
        ->for($manager, 'delegatee')
        ->create([
            'type' => DelegationType::RemoteWorkRequest,
        ]);

    $leaveEmployee = createDefaultEmployee();

    $LeaveDelegation = Delegation::factory()
        ->for($leaveEmployee, 'delegated')
        ->for($manager, 'delegatee')
        ->create([
            'type' => DelegationType::LeaveRequest,
        ]);

    $regularizationEmployee = createDefaultEmployee();

    $regularizationDelegation = Delegation::factory()
        ->for($regularizationEmployee, 'delegated')
        ->for($manager, 'delegatee')
        ->create([
            'type' => DelegationType::RegularizationRequest,
        ]);

    $permissionEmployee = createDefaultEmployee();

    $permissionDelegation = Delegation::factory()
        ->for($permissionEmployee, 'delegated')
        ->for($manager, 'delegatee')
        ->create([
            'type' => DelegationType::PermissionRequest,
        ]);

    $earlyLateEmployee = createDefaultEmployee();

    $earlyLateDelegation = Delegation::factory()
        ->for($earlyLateEmployee, 'delegated')
        ->for($manager, 'delegatee')
        ->create([
            'type' => DelegationType::EarlyLate,
        ]);

    assertTrue($remoteWorkEmployee->is($manager->remoteWorkDelegatedEmployee));
    assertTrue($leaveEmployee->is($manager->leaveDelegatedEmployee));
    assertTrue($regularizationEmployee->is($manager->regularizationDelegatedEmployee));
    assertTrue($permissionEmployee->is($manager->permissionDelegatedEmployee));
    assertTrue($earlyLateEmployee->is($manager->earlyLateDelegatedEmployee));

    assertTrue($remoteWorkEmployee->remoteWorkDelegateeEmployee->is($manager));
    assertTrue($leaveEmployee->leaveDelegateeEmployee->is($manager));
    assertTrue($regularizationEmployee->regularizationDelegateeEmployee->is($manager));
    assertTrue($permissionEmployee->permissionDelegateeEmployee->is($manager));
    assertTrue($earlyLateEmployee->earlyLateDelegateeEmployee->is($manager));
});

test('filter employees by report excluded tags', function () {
    $tenant = Team::factory()->basic()->active()->create();

    $employeeWithExcludedTag = Employee::factory()->for($tenant)->create();
    $employeeWithIncludedTag = Employee::factory()->for($tenant)->create();
    $employeeWithoutTags = Employee::factory()->for($tenant)->create();

    $excludedTag = Tag::factory()->for($tenant)->create();

    $employeeWithIncludedTag->tags()->attach(Tag::factory()->for($tenant)->create()->id);
    $employeeWithExcludedTag->tags()->attach($excludedTag->id);

    $tenant->early_late_config->excludedTags = [$excludedTag->id];

    $tenant->update([
        'early_late_config' => $tenant->early_late_config,
    ]);

    $includedEmployeesOnly = $tenant
        ->employees()
        ->with('tags')
        ->excludeTags($tenant->early_late_config->excludedTags)
        ->get();

    expect($includedEmployeesOnly)
        ->toHaveCount(2)
        ->and($includedEmployeesOnly->pluck('id'))
        ->toContain($employeeWithoutTags->id, $employeeWithIncludedTag->id);
});
