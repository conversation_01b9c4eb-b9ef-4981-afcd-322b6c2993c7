<?php

use App\Models\Tenant;
use function Pest\Laravel\actingAs;

uses()->group('external');

it('only active tenant can access external API', function () {
    $activeTenant = Tenant::factory()->create(['is_active' => true]);

    $inactiveTenant = Tenant::factory()->create(['is_active' => false]);

    actingAs($activeTenant)->getJson('api/v1/external/departments')->assertOk();

    actingAs($inactiveTenant)->getJson('api/v1/external/departments')->assertForbidden();
});
