<?php

use App\Models\Team;
use function Pest\Laravel\actingAs;

uses()->group('external');

it('only active tenant can access external API', function () {
    $activeTeam = Team::factory()->create(['active' => true]);

    $inactiveTeam = Team::factory()->create(['active' => false]);

    actingAs($activeTeam)->getJson('api/v1/external/departments')->assertOk();

    actingAs($inactiveTeam)->get<PERSON><PERSON>('api/v1/external/departments')->assertForbidden();
});
