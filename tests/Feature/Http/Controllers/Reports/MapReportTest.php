<?php

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Location;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Testing\Fluent\AssertableJson;

test('map report - search by location name', function () {
    $includedLocation = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'included']);
    $excludedLocation = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'excluded']);

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/reports/location-attendance-rate?' .
                http_build_query([
                    'filter' => ['search' => 'included'],
                    'date' => today()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJsonCount(1, 'data')
        ->assertJson(
            fn(AssertableJson $json) => $json
                ->count('data', 1)
                ->where('data.0.id', $includedLocation->id)
                ->etc()
        );
});

test('map report handles employees assigned permanently to multiple locations', function () {
    # prepare
    $firstLocation = Location::factory()
        ->for($this->tenant)
        ->create();
    $secondLocation = Location::factory()
        ->for($this->tenant)
        ->create();
    $firstLocationEmployees = Employee::factory()
        ->for($this->tenant)
        ->createMany(7);
    $secondLocationEmployees = Employee::factory()
        ->for($this->tenant)
        ->createMany(7);

    $bothLocationsEmployee = Employee::factory()
        ->for($this->tenant)
        ->create();
    $bothLocationsEmployee
        ->locations()
        ->attach($firstLocation, ['permanent' => true, 'start_date' => null, 'end_date' => null]);
    $bothLocationsEmployee
        ->locations()
        ->attach($secondLocation, ['permanent' => true, 'start_date' => null, 'end_date' => null]);

    foreach ($firstLocationEmployees as $employee) {
        $employee->locations()->attach($firstLocation, [
            'permanent' => true,
            'start_date' => null,
            'end_date' => null,
        ]);
    }

    foreach ($secondLocationEmployees as $employee) {
        $employee->locations()->attach($secondLocation, [
            'permanent' => true,
            'start_date' => null,
            'end_date' => null,
        ]);
    }

    $dates = [Carbon::today()->subDay(), Carbon::today()];

    foreach ($dates as $date) {
        foreach ([...$secondLocationEmployees, ...$firstLocationEmployees] as $key => $employee) {
            $states = [
                [
                    'status' => AttendanceStatus::PRESENT->value,
                    'check_in_location_id' => $employee->locations()->first()->id,
                ],
                ['status' => AttendanceStatus::PRESENT->value],
                ['status' => AttendanceStatus::ABSENT->value],
                ['status' => AttendanceStatus::HOLIDAY->value],
                ['status' => AttendanceStatus::YET->value],
                ['status' => AttendanceStatus::LEAVE->value],
                ['status' => AttendanceStatus::WEEKEND->value],
            ];

            Attendance::factory()
                ->for($employee)
                ->for($this->tenant)
                ->date($date)
                ->create($states[$key % 7]);
        }
    }

    foreach ($dates as $date) {
        Attendance::factory()
            ->for($bothLocationsEmployee)
            ->for($this->tenant)
            ->date($date)
            ->present()
            ->create([
                'check_in_location_id' => $firstLocation->id,
            ]);
    }

    # test
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/reports/location-attendance-rate?date=' . today()->toDateString()
        )
        ->assertOk()
        ->assertJson([
            'data' => [
                [
                    'id' => $firstLocation->id,
                    'employees_count' => 4,
                    'present_count' => 2,
                ],
                [
                    'id' => $secondLocation->id,
                    'employees_count' => 3,
                    'present_count' => 1,
                ],
            ],
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/reports/location-attendance-rate?date=' .
                today()->subDay()->toDateString()
        )
        ->assertOk()
        ->assertJson([
            'data' => [
                [
                    'id' => $firstLocation->id,
                    'employees_count' => 4,
                    'present_count' => 2,
                ],
                [
                    'id' => $secondLocation->id,
                    'employees_count' => 3,
                    'present_count' => 1,
                ],
            ],
        ]);
});

test('map report handles temporary assigned location', function () {
    $today = CarbonImmutable::now();
    $yesterday = CarbonImmutable::now()->subDay();

    $firstLocation = Location::factory()
        ->for($this->tenant)
        ->create();

    $secondLocation = Location::factory()
        ->for($this->tenant)
        ->create();

    $firstLocationEmployees = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($firstLocation)
        ->createMany(7);

    $secondLocationEmployees = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($secondLocation)
        ->createMany(7);

    $temporaryLocationEmployee = Employee::factory()
        ->for($this->tenant)
        ->temporaryLocation(
            $firstLocation,
            CarbonPeriod::create($today->subDays(3), $today->subDays(2))
        )
        ->temporaryLocation($secondLocation, CarbonPeriod::create($today, $today->addDay()))
        ->create();

    foreach ([$yesterday, $today] as $date) {
        foreach ([...$secondLocationEmployees, ...$firstLocationEmployees] as $key => $employee) {
            $states = [
                [
                    'status' => AttendanceStatus::PRESENT->value,
                    'check_in_location_id' => $employee->locations()->first()->id,
                ],
                ['status' => AttendanceStatus::PRESENT->value],
                ['status' => AttendanceStatus::ABSENT->value],
                ['status' => AttendanceStatus::HOLIDAY->value],
                ['status' => AttendanceStatus::YET->value],
                ['status' => AttendanceStatus::LEAVE->value],
                ['status' => AttendanceStatus::WEEKEND->value],
            ];

            Attendance::factory()
                ->for($employee)
                ->for($this->tenant)
                ->date($date)
                ->create($states[$key % 7]);
        }
    }

    Attendance::factory()
        ->for($temporaryLocationEmployee)
        ->for($this->tenant)
        ->date($yesterday)
        ->present()
        ->create([
            'check_in_location_id' => $firstLocation->id,
        ]);

    Attendance::factory()
        ->for($temporaryLocationEmployee)
        ->for($this->tenant)
        ->absent()
        ->date($today)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            "api/v1/frontend/reports/location-attendance-rate?date={$yesterday->toDateString()}"
        )
        ->assertOk()
        ->assertJson([
            'data' => [
                [
                    'id' => $firstLocation->id,
                    'employees_count' => 5,
                    'present_count' => 3,
                ],
                [
                    'id' => $secondLocation->id,
                    'employees_count' => 3,
                    'present_count' => 1,
                ],
            ],
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson("api/v1/frontend/reports/location-attendance-rate?date={$today->toDateString()}")
        ->assertOk()
        ->assertJson([
            'data' => [
                [
                    'id' => $firstLocation->id,
                    'employees_count' => 4,
                    'present_count' => 2,
                ],
                [
                    'id' => $secondLocation->id,
                    'employees_count' => 4,
                    'present_count' => 1,
                ],
            ],
        ]);
})->skip('handle temporary assigned location');
