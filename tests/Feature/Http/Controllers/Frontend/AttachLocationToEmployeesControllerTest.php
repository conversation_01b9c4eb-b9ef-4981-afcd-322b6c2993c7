<?php

use App\Enums\DurationStatus;
use App\Jobs\EmployeeLocationJob;
use App\Models\Employee;
use App\Models\Location;

it('attach employees to a location', function (DurationStatus $durationStatus) {
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Frontend\AttachLocationToEmployeesController */
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/attach-location/$location->id", [
            'employees' => [$employee->id],
            ...durationStatusPayload($durationStatus),
        ])
        ->assertOk();

    expect($employee->locations->first()->id)->toBe($location->id);
})->with('duration-status');

it('attach employees to a location - search', function (DurationStatus $type) {
    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    $search = 'employee 1';

    $includedEmployee = createDefaultEmployee(['first_name' => $search]);

    $excludedEmployee = createDefaultEmployee(['first_name' => 'employee 2']);

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/attach-location/$location->id", [
            'all' => true,
            'filter' => ['search' => $search],
            ...durationStatusPayload($type),
        ])
        ->assertOk();

    if ($type === DurationStatus::PERMANENT) {
        $location = $includedEmployee->locations->first();

        expect($location)->not->toBeNull();

        expect($location->pivot->start_date)->toBeNull();
        expect($location->pivot->end_date)->toBeNull();
        expect($location->pivot->permanent)->toBeTruthy();
    } else {
        $location = $includedEmployee->locations->first(
            fn($location) => !$location->pivot->permanent
        );

        expect($location)->not->toBeNull();

        expect($location->pivot->start_date)
            ->not()
            ->toBeNull();
        expect($location->pivot->end_date)
            ->not()
            ->toBeNull();
        expect($location->pivot->permanent)->toBeFalsy();
    }

    expect($includedEmployee->locations->pluck('id'))->toContain($location->id);

    expect($excludedEmployee->locations->pluck('id'))->not->toContain($location->id);
})->with('duration-status');

it('attach employees to a location - queue if employees > 100', function (DurationStatus $type) {
    Queue::fake([EmployeeLocationJob::class]);

    $employees = Employee::factory()
        ->for($this->tenant)
        ->count(101)
        ->create();

    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/attach-location/$location->id", [
            'employees' => $employees->pluck('id'),
            ...durationStatusPayload($type),
        ])
        ->assertOk()
        ->assertJsonFragment([
            'message' => __(
                'The location was added to employees successfully, it may take a while'
            ),
        ]);

    expect($employees[0]->locations->pluck('id'))->not->toContain($location->id);

    Queue::assertPushed(EmployeeLocationJob::class);
})->with('duration-status');
