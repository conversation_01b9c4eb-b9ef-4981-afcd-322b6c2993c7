<?php

use App\Models\Employee;
use Illuminate\Support\Collection;
use Illuminate\Testing\Fluent\AssertableJson;

it('list employees', function () {
    $employee = createDefaultEmployee([
        'department_id' => createDefaultDepartment()->id,
    ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/employees?' .
                http_build_query([
                    'include' => ['department', 'shift', 'locations', 'tags'],
                ])
        )
        ->assertOk()
        ->assertJsonFragment([
            'id' => $employee->id,
            'first_name' => $employee->first_name,
            'last_name' => $employee->last_name,
            'email' => $employee->email,
        ]);
});

it('list employees filtered by ids', function () {
    $firstEmployee = createDefaultEmployee();
    $secondEmployee = createDefaultEmployee();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/employees?' .
                http_build_query([
                    'filter' => ['ids' => [$firstEmployee->id]],
                ])
        )
        ->assertOk()
        ->assertJsonCount(1, 'data.data');
});

it('list employees filtered by direct manager ids', function () {
    $excludedEmployee = createDefaultEmployee();

    $directManager = createDefaultEmployee();
    $includedEmployee = Employee::factory()
        ->for(test()->tenant)
        ->directManager($directManager)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/employees?' .
                http_build_query([
                    'filter' => ['direct_managers' => [$directManager->id]],
                ])
        )
        ->assertOk()
        ->assertJsonCount(1, 'data.data')
        ->assertJsonPath('data.data.0.id', $includedEmployee->id);
});

it('list employees filtered by is direct manager', function () {
    # prepare
    // assuming that $this->user doesn't have a direct manager
    $normalEmployee = $this->user;

    $directManager = createDefaultEmployee();
    $directEmployee = Employee::factory()
        ->for(test()->tenant)
        ->directManager($directManager)
        ->create();

    # act & assert
    // is direct manager filter is not sent
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson('api/v1/frontend/employees')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');

    // is direct manager filter is sent as true
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/employees?' .
                http_build_query([
                    'filter' => ['is_direct_manager' => true],
                ])
        )
        ->assertOk()
        ->assertJsonCount(1, 'data.data')
        ->assertJsonPath('data.data.0.id', $directManager->id);

    // is direct manager filter is sent as false
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/employees?' .
                http_build_query([
                    'filter' => ['is_direct_manager' => false],
                ])
        )
        ->assertOk()
        ->assertJsonCount(2, 'data.data')
        ->assertJson(
            fn(AssertableJson $json) => $json
                ->where(
                    'data.data',
                    fn(Collection $employees) => $employees->contains('id', $normalEmployee->id)
                )
                ->where(
                    'data.data',
                    fn(Collection $employees) => $employees->contains('id', $directEmployee->id)
                )
                ->etc()
        );
});
