<?php

it('shows an employee', function () {
    $employee = createDefaultEmployee();

    $employee->shifts()->sync([
        [
            'shift_id' => createDefaultShift()->id,
            'permanent' => false,
            'start_at' => now()->format('Y-m-d'),
            'end_at' => now()->addMonth()->format('Y-m-d'),
        ],
        [
            'shift_id' => createDefaultShift()->id,
            'permanent' => true,
        ],
    ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson("api/v1/frontend/employees/$employee->id")
        ->assertOk();
});
