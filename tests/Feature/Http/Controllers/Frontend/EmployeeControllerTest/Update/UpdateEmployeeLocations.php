<?php

use App\Enums\DurationStatus;
use App\Events\EmployeeUpdated;
use App\Models\Employee;

beforeEach(function () {
    $this->employee = Employee::factory()
        ->for($this->tenant)
        ->randomProofNotificationConfig(enabled: false, count: null)
        ->create(['remote_work' => 'allowed']);

    Event::fake([EmployeeUpdated::class]);
});

it('add multiple locations', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [
            [
                'id' => (string) createDefaultLocation()->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) createDefaultLocation()->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) createDefaultLocation()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ],
        ],
        'shifts' => [],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertOk();

    $this->employee->refresh();

    expect($this->employee->locations->count())->toBe(count($data['locations']));

    expect($this->employee->locations->pluck('id')->toArray())->toEqual([
        $data['locations'][0]['id'],
        $data['locations'][1]['id'],
        $data['locations'][2]['id'],
    ]);

    Event::assertDispatched(EmployeeUpdated::class);
});

it('permanent locations can not be duplicated', function () {
    $location = createDefaultLocation();

    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [
            [
                'id' => (string) $location->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) $location->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) createDefaultLocation()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ],
        ],
        'shifts' => [],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertOk();

    $this->employee->refresh();

    expect($this->employee->locations->count())->toBe(2);

    expect($this->employee->locations->pluck('id')->toArray())->toEqual([
        $data['locations'][0]['id'],
        $data['locations'][2]['id'],
    ]);
    Event::assertDispatched(EmployeeUpdated::class);
});
