<?php

use App\Enums\DurationStatus;
use App\Events\EmployeeUpdated;
use App\Models\Employee;
use App\Notifications\NewPermanentShiftAssignedNotification;
use App\Notifications\NewTemporaryShiftAssignedNotification;
use App\Notifications\PermanentShiftAssignedChangedNotification;
use App\Notifications\TemporaryShiftChangedNotification;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

beforeEach(function () {
    $this->employee = Employee::factory()
        ->for($this->tenant)
        ->randomProofNotificationConfig(enabled: false, count: null)
        ->create(['remote_work' => 'allowed']);

    Event::fake([EmployeeUpdated::class]);
});

it('requires shifts to be present', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertStatus(422);

    Event::assertNotDispatched(EmployeeUpdated::class);
});

it('prevents multiple permanent shifts', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertStatus(422);

    Event::assertNotDispatched(EmployeeUpdated::class);
});

it('prevents temporary shifts overlap at end of period', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ],
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->addMonth()->subDay()->format('Y-m-d'),
                'end_date' => now()->addMonths(2)->format('Y-m-d'),
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertStatus(422);

    Event::assertNotDispatched(EmployeeUpdated::class);
});

it('prevents temporary shifts overlap at start of period', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->addMonth()->format('Y-m-d'),
                'end_date' => now()->addMonths(2)->format('Y-m-d'),
            ],
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->addDays(15)->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertStatus(422);

    Event::assertNotDispatched(EmployeeUpdated::class);
});

it('adds shifts when none exist', function () {
    $firstShift = createDefaultShift();

    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [
            [
                'id' => (string) $firstShift->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertOk();

    $this->employee->refresh();

    expect($this->employee->random_proof_notification_config)
        ->enabled->toBe($data['random_proof_notification_config']['enabled'])
        ->inherited->toBe($data['random_proof_notification_config']['inherited'])
        ->count->toBe($data['random_proof_notification_config']['count']);

    $actualShifts = $this->employee->shifts
        ->map(
            fn($shift) => [
                'shift_id' => $shift->pivot->shift_id,
                'permanent' => (bool) $shift->pivot->permanent,
                'start_date' => is_null($shift->pivot->start_at)
                    ? null
                    : Carbon::parse($shift->pivot->start_at)->toDateString(),
                'end_date' => is_null($shift->pivot->end_at)
                    ? null
                    : Carbon::parse($shift->pivot->end_at)->toDateString(),
            ]
        )
        ->toArray();

    $expectedShifts = collect($data['shifts'])
        ->map(
            fn($shift) => [
                'shift_id' => (int) $shift['id'],
                'permanent' => $shift['type'] === DurationStatus::PERMANENT->value,
                'start_date' => $shift['start_date'] ?? null,
                'end_date' => $shift['end_date'] ?? null,
            ]
        )
        ->toArray();

    expect($actualShifts)->toEqual($expectedShifts, 'shifts update');

    Event::assertDispatched(EmployeeUpdated::class);
});

it('adds shifts while keeping existing ones', function () {
    $permanentShift = createDefaultShift();
    $existingTemporaryShift = createDefaultShift();
    $newTemporaryShift = createDefaultShift();

    $existingTemporaryShiftPeriod = CarbonPeriod::dates(now(), now()->addMonth());
    $newTemporaryShiftPeriod = CarbonPeriod::dates(
        now()->addMonth()->addDay(),
        now()->addMonths(2)
    );

    $this->employee->shifts()->attach($permanentShift->id, ['permanent' => true]);
    $this->employee->shifts()->attach($existingTemporaryShift->id, [
        'permanent' => false,
        'start_at' => $existingTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
        'end_at' => $existingTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
    ]);

    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [
            [
                'id' => (string) $permanentShift->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
            [
                'id' => (string) $existingTemporaryShift->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => $existingTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                'end_date' => $existingTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
            ],
            [
                'id' => (string) $newTemporaryShift->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => $newTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                'end_date' => $newTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertOk();

    $this->employee->refresh();

    expect($this->employee->random_proof_notification_config)
        ->enabled->toBe($data['random_proof_notification_config']['enabled'])
        ->inherited->toBe($data['random_proof_notification_config']['inherited'])
        ->count->toBe($data['random_proof_notification_config']['count']);

    $actualShifts = $this->employee->shifts
        ->map(
            fn($shift) => [
                'shift_id' => $shift->pivot->shift_id,
                'permanent' => (bool) $shift->pivot->permanent,
                'start_date' => is_null($shift->pivot->start_at)
                    ? null
                    : Carbon::parse($shift->pivot->start_at)->toDateString(),
                'end_date' => is_null($shift->pivot->end_at)
                    ? null
                    : Carbon::parse($shift->pivot->end_at)->toDateString(),
            ]
        )
        ->toArray();

    $expectedShifts = collect($data['shifts'])
        ->map(
            fn($shift) => [
                'shift_id' => (int) $shift['id'],
                'permanent' => $shift['type'] === DurationStatus::PERMANENT->value,
                'start_date' => $shift['start_date'] ?? null,
                'end_date' => $shift['end_date'] ?? null,
            ]
        )
        ->toArray();

    expect($actualShifts)->toEqual($expectedShifts, 'shifts update');

    Event::assertDispatched(EmployeeUpdated::class);
});

it('allows zero shifts', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertOk();

    $this->employee->refresh();

    expect($this->employee->random_proof_notification_config)
        ->enabled->toBe($data['random_proof_notification_config']['enabled'])
        ->inherited->toBe($data['random_proof_notification_config']['inherited'])
        ->count->toBe($data['random_proof_notification_config']['count']);

    expect($this->employee->shifts)->toHaveCount(0);

    Event::assertDispatched(EmployeeUpdated::class);
});

it('allows only temporary shifts', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::TEMPORARY->value,
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonth()->format('Y-m-d'),
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertOk();

    $this->employee->refresh();

    expect($this->employee->random_proof_notification_config)
        ->enabled->toBe($data['random_proof_notification_config']['enabled'])
        ->inherited->toBe($data['random_proof_notification_config']['inherited'])
        ->count->toBe($data['random_proof_notification_config']['count']);

    $actualShifts = $this->employee->shifts
        ->map(
            fn($shift) => [
                'shift_id' => $shift->pivot->shift_id,
                'permanent' => (bool) $shift->pivot->permanent,
                'start_date' => is_null($shift->pivot->start_at)
                    ? null
                    : Carbon::parse($shift->pivot->start_at)->toDateString(),
                'end_date' => is_null($shift->pivot->end_at)
                    ? null
                    : Carbon::parse($shift->pivot->end_at)->toDateString(),
            ]
        )
        ->toArray();

    $expectedShifts = collect($data['shifts'])
        ->map(
            fn($shift) => [
                'shift_id' => (int) $shift['id'],
                'permanent' => $shift['type'] === DurationStatus::PERMANENT->value,
                'start_date' => $shift['start_date'] ?? null,
                'end_date' => $shift['end_date'] ?? null,
            ]
        )
        ->toArray();

    expect($actualShifts)->toEqual($expectedShifts, 'shifts update');

    Event::assertDispatched(EmployeeUpdated::class);
});

it('allows single permanent shift', function () {
    $data = [
        'remote_work' => 'inherited',
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 6,
        ],
        'locations' => [],
        'shifts' => [
            [
                'id' => (string) createDefaultShift()->id,
                'type' => DurationStatus::PERMANENT->value,
            ],
        ],
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
        ->assertOk();

    $this->employee->refresh();

    $actualShifts = $this->employee->shifts
        ->map(
            fn($shift) => [
                'shift_id' => $shift->pivot->shift_id,
                'permanent' => (bool) $shift->pivot->permanent,
                'start_date' => is_null($shift->pivot->start_at)
                    ? null
                    : Carbon::parse($shift->pivot->start_at)->toDateString(),
                'end_date' => is_null($shift->pivot->end_at)
                    ? null
                    : Carbon::parse($shift->pivot->end_at)->toDateString(),
            ]
        )
        ->toArray();

    $expectedShifts = collect($data['shifts'])
        ->map(
            fn($shift) => [
                'shift_id' => (int) $shift['id'],
                'permanent' => $shift['type'] === DurationStatus::PERMANENT->value,
                'start_date' => $shift['start_date'] ?? null,
                'end_date' => $shift['end_date'] ?? null,
            ]
        )
        ->toArray();

    expect($actualShifts)->toEqual($expectedShifts, 'shifts update');

    Event::assertDispatched(EmployeeUpdated::class);
});

describe('notify employee about shift changes', function () {
    it('notify employee about newly assigned shifts - initially no shift assigned', function () {
        // arrange
        $permanentShift = createDefaultShift();
        $firstTemporaryShift = createDefaultShift();
        $secondTemporaryShift = createDefaultShift();

        $firstTemporaryShiftPeriod = CarbonPeriod::dates(
            now()->setTime(0, 0),
            now()->addMonth()->setTime(0, 0)
        );
        $secondTemporaryShiftPeriod = CarbonPeriod::dates(
            now()->addMonth()->addDay()->setTime(0, 0),
            now()->addMonths(2)->setTime(0, 0)
        );

        $data = [
            'remote_work' => 'inherited',
            'random_proof_notification_config' => [
                'enabled' => true,
                'inherited' => true,
                'count' => 6,
            ],
            'locations' => [],
            'shifts' => [
                [
                    'id' => (string) $permanentShift->id,
                    'type' => DurationStatus::PERMANENT->value,
                ],
                [
                    'id' => (string) $firstTemporaryShift->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => $firstTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                    'end_date' => $firstTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                ],
                [
                    'id' => (string) $secondTemporaryShift->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => $secondTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                    'end_date' => $secondTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                ],
            ],
        ];

        // act
        $this->jwtActingAsAttendanceHR($this->employee)
            ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
            ->assertOk();

        // assert
        expect(
            $this->employee
                ->notifications()
                ->where('type', NewPermanentShiftAssignedNotification::class)
                ->where('data->shift->id', $permanentShift->id)
                ->exists()
        )->toBeTrue('a notification should be sent for the new permanent shift');

        expect(
            $this->employee
                ->notifications()
                ->where('type', NewTemporaryShiftAssignedNotification::class)
                ->where('data->shift->id', $firstTemporaryShift->id)
                ->where(
                    'data->startDate',
                    $firstTemporaryShiftPeriod->getStartDate()->toISOString()
                )
                ->where('data->endDate', $firstTemporaryShiftPeriod->getEndDate()->toISOString())
                ->exists()
        )->toBeTrue('a notification should be sent for the first new temporary shift');
        expect(
            $this->employee
                ->notifications()
                ->where('type', NewTemporaryShiftAssignedNotification::class)
                ->where('data->shift->id', $secondTemporaryShift->id)
                ->where(
                    'data->startDate',
                    $secondTemporaryShiftPeriod->getStartDate()->toISOString()
                )
                ->where('data->endDate', $secondTemporaryShiftPeriod->getEndDate()->toISOString())
                ->exists()
        )->toBeTrue('a notification should be sent for the second new temporary shift');

        expect($this->employee->notifications()->count())->toBe(
            3,
            'there should be exactly 3 notification sent to the employee'
        );
    });

    it('notify employee about permanent shift being changed', function () {
        // arrange
        $permanentShift = createDefaultShift();
        $newPermanentShift = createDefaultShift();

        $this->employee->shifts()->attach($permanentShift, ['permanent' => true]);

        $data = [
            'remote_work' => 'inherited',
            'random_proof_notification_config' => [
                'enabled' => true,
                'inherited' => true,
                'count' => 6,
            ],
            'locations' => [],
            'shifts' => [
                [
                    'id' => (string) $newPermanentShift->id,
                    'type' => DurationStatus::PERMANENT->value,
                ],
            ],
        ];

        // act
        $this->jwtActingAsAttendanceHR($this->employee)
            ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
            ->assertOk();

        // assert
        expect(
            $this->employee
                ->notifications()
                ->where('type', PermanentShiftAssignedChangedNotification::class)
                ->where('data->shift->id', $newPermanentShift->id)
                ->exists()
        )->toBeTrue('a notification should be sent for the new permanent shift');

        expect($this->employee->notifications()->count())->toBe(
            1,
            'there should be exactly one notification sent to the employee'
        );
    });

    it(
        'notify employee about newly temporary assigned shifts - there is already assigned temporary shifts',
        function () {
            // arrange
            $firstTemporaryShift = createDefaultShift();
            $secondTemporaryShift = createDefaultShift();

            $firstTemporaryShiftPeriod = CarbonPeriod::dates(
                now()->setTime(0, 0),
                now()->addMonth()->setTime(0, 0)
            );
            $secondTemporaryShiftPeriod = CarbonPeriod::dates(
                now()->addMonth()->addDay()->setTime(0, 0),
                now()->addMonths(2)->setTime(0, 0)
            );

            $this->employee->shifts()->attach($firstTemporaryShift->id, [
                'permanent' => false,
                'start_at' => $firstTemporaryShiftPeriod->getStartDate(),
                'end_at' => $firstTemporaryShiftPeriod->getEndDate(),
            ]);

            $data = [
                'remote_work' => 'inherited',
                'random_proof_notification_config' => [
                    'enabled' => true,
                    'inherited' => true,
                    'count' => 6,
                ],
                'locations' => [],
                'shifts' => [
                    [
                        'id' => (string) $firstTemporaryShift->id,
                        'type' => DurationStatus::TEMPORARY->value,
                        'start_date' => $firstTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                        'end_date' => $firstTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                    ],
                    [
                        'id' => (string) $secondTemporaryShift->id,
                        'type' => DurationStatus::TEMPORARY->value,
                        'start_date' => $secondTemporaryShiftPeriod
                            ->getStartDate()
                            ->format('Y-m-d'),
                        'end_date' => $secondTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                    ],
                ],
            ];

            // act
            $this->jwtActingAsAttendanceHR($this->employee)
                ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
                ->assertOk();

            // assert
            expect(
                $this->employee
                    ->notifications()
                    ->where('type', NewTemporaryShiftAssignedNotification::class)
                    ->where('data->shift->id', $firstTemporaryShift->id)
                    ->doesntExist()
            )->toBeTrue("a notification shouldn't be sent for the unchanged shift");

            expect(
                $this->employee
                    ->notifications()
                    ->where('type', NewTemporaryShiftAssignedNotification::class)
                    ->where('data->shift->id', $secondTemporaryShift->id)
                    ->where(
                        'data->startDate',
                        $secondTemporaryShiftPeriod->getStartDate()->toISOString()
                    )
                    ->where(
                        'data->endDate',
                        $secondTemporaryShiftPeriod->getEndDate()->toISOString()
                    )
                    ->exists()
            )->toBeTrue('a notification should be sent for the newly assigned shift');

            expect($this->employee->notifications()->count())->toBe(
                1,
                'there should be exactly one notification sent to the employee'
            );
        }
    );

    it('notify employee about temporary shifts being changed', function () {
        // arrange
        $unchangedTemporaryShift = createDefaultShift();
        $deletedTemporaryShift = createDefaultShift();
        $dateChangedTemporaryShift = createDefaultShift();
        $shiftChangedTemporaryShift = createDefaultShift();
        $newTemporaryShift = createDefaultShift();

        $unchangedTemporaryShiftPeriod = CarbonPeriod::dates(
            now()->setTime(0, 0),
            now()->addDays(5)->setTime(0, 0)
        );
        $deletedTemporaryShiftPeriod = CarbonPeriod::dates(
            now()->addDays(6)->setTime(0, 0),
            now()->addDays(10)->setTime(0, 0)
        );
        $dateChangedTemporaryShiftPeriod = CarbonPeriod::dates(
            now()->addDays(11)->setTime(0, 0),
            now()->addDays(15)->setTime(0, 0)
        );
        $shiftChangedTemporaryShiftPeriod = CarbonPeriod::dates(
            now()->addDays(16)->setTime(0, 0),
            now()->addDays(20)->setTime(0, 0)
        );

        $newTemporaryShiftPeriod = CarbonPeriod::dates(
            now()->addDays(21)->setTime(0, 0),
            now()->addDays(25)->setTime(0, 0)
        );

        $this->employee->shifts()->attach($unchangedTemporaryShift->id, [
            'permanent' => false,
            'start_at' => $unchangedTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
            'end_at' => $unchangedTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
        ]);
        $this->employee->shifts()->attach($deletedTemporaryShift->id, [
            'permanent' => false,
            'start_at' => $deletedTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
            'end_at' => $deletedTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
        ]);
        $this->employee->shifts()->attach($dateChangedTemporaryShift->id, [
            'permanent' => false,
            'start_at' => $dateChangedTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
            'end_at' => $dateChangedTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
        ]);
        $this->employee->shifts()->attach($shiftChangedTemporaryShift->id, [
            'permanent' => false,
            'start_at' => $shiftChangedTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
            'end_at' => $shiftChangedTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
        ]);

        $data = [
            'remote_work' => 'inherited',
            'random_proof_notification_config' => [
                'enabled' => true,
                'inherited' => true,
                'count' => 6,
            ],
            'locations' => [],
            'shifts' => [
                // unchanged shift
                [
                    'id' => (string) $unchangedTemporaryShift->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => $unchangedTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                    'end_date' => $unchangedTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                ],
                // date changed
                [
                    'id' => (string) $dateChangedTemporaryShift->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => now()->addDays(26)->setTime(0, 0)->format('Y-m-d'),
                    'end_date' => now()->addDays(30)->setTime(0, 0)->format('Y-m-d'),
                ],
                // shift changed
                [
                    'id' => (string) createDefaultShift()->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => $shiftChangedTemporaryShiftPeriod
                        ->getStartDate()
                        ->format('Y-m-d'),
                    'end_date' => $shiftChangedTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                ],
                // new temporary shift
                [
                    'id' => (string) $newTemporaryShift->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => $newTemporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                    'end_date' => $newTemporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                ],
            ],
        ];

        // act
        $this->jwtActingAsAttendanceHR($this->employee)
            ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
            ->assertOk();

        // assert
        expect(
            $this->employee
                ->notifications()
                ->where('type', TemporaryShiftChangedNotification::class)
                ->exists()
        )->toBeTrue(
            'a notification should be sent because some temporary shifts has been changed/deleted'
        );

        expect($this->employee->notifications()->count())->toBe(
            1,
            'there should be exactly one notification sent to the employee'
        );
    });

    it('do not notify employee when there is no change in assigned shifts', function () {
        // arrange
        $permanentShift = createDefaultShift();
        $temporaryShift = createDefaultShift();

        $temporaryShiftPeriod = CarbonPeriod::dates(
            now()->setTime(0, 0),
            now()->addMonth()->setTime(0, 0)
        );

        $this->employee->shifts()->attach($permanentShift->id, [
            'permanent' => true,
        ]);
        $this->employee->shifts()->attach($temporaryShift->id, [
            'permanent' => false,
            'start_at' => $temporaryShiftPeriod->getStartDate()->format('Y-m-d'),
            'end_at' => $temporaryShiftPeriod->getEndDate()->format('Y-m-d'),
        ]);

        $data = [
            'remote_work' => 'inherited',
            'random_proof_notification_config' => [
                'enabled' => true,
                'inherited' => true,
                'count' => 6,
            ],
            'locations' => [],
            'shifts' => [
                [
                    'id' => (string) $permanentShift->id,
                    'type' => DurationStatus::PERMANENT->value,
                ],
                [
                    'id' => (string) $temporaryShift->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => $temporaryShiftPeriod->getStartDate()->format('Y-m-d'),
                    'end_date' => $temporaryShiftPeriod->getEndDate()->format('Y-m-d'),
                ],
            ],
        ];

        // act
        $this->jwtActingAsAttendanceHR($this->employee)
            ->putJson("api/v1/frontend/employees/{$this->employee->id}", $data)
            ->assertOk();

        // assert
        expect($this->employee->notifications()->doesntExist())->toBeTrue(
            'no notification is sent when assigned shifts has not changed'
        );
    });
});
