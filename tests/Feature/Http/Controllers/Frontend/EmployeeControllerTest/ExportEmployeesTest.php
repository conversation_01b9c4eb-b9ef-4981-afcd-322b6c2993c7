<?php

use App\Enums\ReportTaskCreatedByType;
use App\Enums\ReportTaskStatus;
use App\Jobs\GenerateEmployeeExcelJob;
use App\Models\ReportTask;
use App\Models\Tag;
use Illuminate\Support\Str;

test('return forbidden for unauthorized users', function () {
    # not logged-in user
    $this->getJson('api/v1/frontend/employees/export')->assertUnauthorized();

    # Developer role
    $this->actioningAsAttendanceDeveloper($this->user)
        ->get<PERSON>son('api/v1/frontend/employees/export')
        ->assertForbidden();

    # attendance-dashboard-viewer
    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->getJson('api/v1/frontend/employees/export')
        ->assertForbidden();
});

test('return success response for HR user - no filter', function () {
    # arrange
    Queue::fake([GenerateEmployeeExcelJob::class]);

    Str::createUuidsUsing(fn() => 'uuid');
    Storage::fake('local');
    Storage::fake('minio');

    $includedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
    $excludedEmployees = collect([createDefaultEmployee(['is_active' => false])]);

    # assert
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson('api/v1/frontend/employees/export')
        ->assertOk();

    $this->assertDatabaseHas('report_tasks', [
        'file_name' => 'employees_' . today()->toDateString(),
        'team_id' => $this->tenant->id,
        'created_by_id' => $this->user->id,
        'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
        'status' => ReportTaskStatus::Pending,
    ]);
    $this->assertDatabaseCount('report_tasks', 1);

    $reportTask = ReportTask::first();
    $data = $reportTask->data;

    expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
    expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
    expect($data->employeesIds)->toEqual([]);
    expect($data->departmentsIds)->toEqual([]);
    expect($data->directManagers)->toEqual([]);
    expect($data->tags)->toEqual([]);
    expect($data->locations)->toEqual([]);
    expect($data->shifts)->toEqual([]);
    expect($data->showInactiveEmployees)->toBeFalse();

    Queue::assertPushed(
        fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
    );

    # assert employees method
    $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
    $employees = $job->employees()->collect();

    foreach ($includedEmployees as $includedEmployee) {
        expect($employees->contains('id', $includedEmployee->id));
    }

    foreach ($excludedEmployees as $excludedEmployee) {
        expect($employees->doesntContain('id', $excludedEmployee->id));
    }

    $job->handle();
    Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
    Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

    # trigger cleanup storage
    Storage::fake('local');
    Storage::fake('minio');

    Str::createUuidsNormally();
});

describe('return success response - with filtering', function () {
    test('by department', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedDepartment = createDefaultDepartment();
        $excludedDepartment = createDefaultDepartment();

        $includedEmployees = collect([
            createDefaultEmployee(['department_id' => $includedDepartment->id]),
            createDefaultEmployee(['department_id' => $includedDepartment->id]),
        ]);
        $excludedEmployees = collect([
            createDefaultEmployee(['department_id' => $excludedDepartment->id]),
        ]);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query(['departments_ids' => [$includedDepartment->id]])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual([]);
        expect($data->departmentsIds)->toEqual([$includedDepartment->id]);
        expect($data->directManagers)->toEqual([]);
        expect($data->tags)->toEqual([]);
        expect($data->locations)->toEqual([]);
        expect($data->shifts)->toEqual([]);
        expect($data->showInactiveEmployees)->toBeFalse();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        foreach ($excludedEmployees as $excludedEmployee) {
            expect($employees->doesntContain('id', $excludedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });

    test('by department - no employees found', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);
        Storage::fake('local');
        Storage::fake('minio');

        $includedDepartment = createDefaultDepartment();
        $excludedDepartment = createDefaultDepartment();

        $excludedEmployees = collect([
            createDefaultEmployee(['department_id' => $excludedDepartment->id]),
        ]);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query(['departments_ids' => [$includedDepartment->id]])
            )
            ->assertUnprocessable();

        $this->assertDatabaseMissing('report_tasks', [
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
        ]);

        Queue::assertNotPushed(GenerateEmployeeExcelJob::class);
    });

    test('by employee', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
        $excludedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query([
                        'employees_ids' => $includedEmployees->pluck('id')->toArray(),
                    ])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual($includedEmployees->pluck('id')->toArray());
        expect($data->departmentsIds)->toEqual([]);
        expect($data->directManagers)->toEqual([]);
        expect($data->tags)->toEqual([]);
        expect($data->locations)->toEqual([]);
        expect($data->shifts)->toEqual([]);
        expect($data->showInactiveEmployees)->toBeFalse();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        foreach ($excludedEmployees as $excludedEmployee) {
            expect($employees->doesntContain('id', $excludedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });

    test('by department and employee', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedDepartment = createDefaultDepartment();
        $excludedDepartment = createDefaultDepartment();

        $includedEmployees = collect([
            createDefaultEmployee(['department_id' => $includedDepartment->id]),
            createDefaultEmployee(['department_id' => $includedDepartment->id]),
        ]);
        $excludedEmployees = collect([
            createDefaultEmployee(['department_id' => $excludedDepartment->id]),
        ]);

        # assert
        $queryEmployees = [
            $includedEmployees[0]->id,
            $includedEmployees[1]->id,
            $excludedEmployees->first()->id, // this employee shouldn't show up
        ];

        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query([
                        'departments_ids' => [$includedDepartment->id],
                        'employees_ids' => $queryEmployees,
                    ])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual($queryEmployees);
        expect($data->departmentsIds)->toEqual([$includedDepartment->id]);
        expect($data->directManagers)->toEqual([]);
        expect($data->tags)->toEqual([]);
        expect($data->locations)->toEqual([]);
        expect($data->shifts)->toEqual([]);
        expect($data->showInactiveEmployees)->toBeFalse();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        foreach ($excludedEmployees as $excludedEmployee) {
            expect($employees->doesntContain('id', $excludedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });

    test('by direct manager', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedDirectManager = createDefaultEmployee();
        $excludedDirectManager = createDefaultEmployee();

        $includedEmployees = collect([
            createDefaultEmployee(['manager_id' => $includedDirectManager->id]),
            createDefaultEmployee(['manager_id' => $includedDirectManager->id]),
        ]);
        $excludedEmployees = collect([
            createDefaultEmployee(['manager_id' => $excludedDirectManager->id]),
            createDefaultEmployee(),
        ]);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query([
                        'direct_managers' => [$includedDirectManager->id],
                    ])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual([]);
        expect($data->departmentsIds)->toEqual([]);
        expect($data->directManagers)->toEqual([$includedDirectManager->id]);
        expect($data->tags)->toEqual([]);
        expect($data->locations)->toEqual([]);
        expect($data->shifts)->toEqual([]);
        expect($data->showInactiveEmployees)->toBeFalse();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        foreach ($excludedEmployees as $excludedEmployee) {
            expect($employees->doesntContain('id', $excludedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });

    test('by tag', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedTag = Tag::factory()
            ->for($this->tenant)
            ->create();
        $excludedTag = Tag::factory()
            ->for($this->tenant)
            ->create();

        $includedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
        foreach ($includedEmployees as $includedEmployee) {
            $includedEmployee->tags()->attach($includedTag);
        }

        $excludedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
        $excludedEmployees[0]->tags()->attach($excludedTag);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query([
                        'tags' => [$includedTag->id],
                    ])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual([]);
        expect($data->departmentsIds)->toEqual([]);
        expect($data->directManagers)->toEqual([]);
        expect($data->tags)->toEqual([$includedTag->id]);
        expect($data->locations)->toEqual([]);
        expect($data->shifts)->toEqual([]);
        expect($data->showInactiveEmployees)->toBeFalse();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        foreach ($excludedEmployees as $excludedEmployee) {
            expect($employees->doesntContain('id', $excludedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });

    test('by location', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedLocation = createDefaultLocation();
        $excludedLocation = createDefaultLocation();

        $includedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
        foreach ($includedEmployees as $includedEmployee) {
            $includedEmployee->locations()->attach($includedLocation, ['permanent' => true]);
        }

        $excludedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
        $excludedEmployees[0]->locations()->attach($excludedLocation, ['permanent' => true]);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query([
                        'locations' => [$includedLocation->id],
                    ])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual([]);
        expect($data->departmentsIds)->toEqual([]);
        expect($data->directManagers)->toEqual([]);
        expect($data->tags)->toEqual([]);
        expect($data->locations)->toEqual([$includedLocation->id]);
        expect($data->shifts)->toEqual([]);
        expect($data->showInactiveEmployees)->toBeFalse();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        foreach ($excludedEmployees as $excludedEmployee) {
            expect($employees->doesntContain('id', $excludedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });

    test('by shift', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedShift = createDefaultShift();
        $excludedShift = createDefaultShift();

        $includedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
        foreach ($includedEmployees as $includedEmployee) {
            $includedEmployee->shifts()->attach($includedShift, ['permanent' => true]);
        }

        $excludedEmployees = collect([createDefaultEmployee(), createDefaultEmployee()]);
        $excludedEmployees[0]->shifts()->attach($excludedShift, ['permanent' => true]);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query([
                        'shifts' => [$includedShift->id],
                    ])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual([]);
        expect($data->departmentsIds)->toEqual([]);
        expect($data->directManagers)->toEqual([]);
        expect($data->tags)->toEqual([]);
        expect($data->locations)->toEqual([]);
        expect($data->shifts)->toEqual([$includedShift->id]);
        expect($data->showInactiveEmployees)->toBeFalse();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        foreach ($excludedEmployees as $excludedEmployee) {
            expect($employees->doesntContain('id', $excludedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });

    test('include inactive employees', function () {
        # prepare
        Queue::fake([GenerateEmployeeExcelJob::class]);

        Str::createUuidsUsing(fn() => 'uuid');
        Storage::fake('local');
        Storage::fake('minio');

        $includedEmployees = collect([
            createDefaultEmployee(['is_active' => true]),
            createDefaultEmployee(['is_active' => false]),
            createDefaultEmployee(['is_active' => false]),
        ]);

        # assert
        $this->jwtActingAsAttendanceHR($this->user)
            ->getJson(
                'api/v1/frontend/employees/export?' .
                    http_build_query(['show_inactive_employees' => true])
            )
            ->assertOk();

        $this->assertDatabaseHas('report_tasks', [
            'file_name' => 'employees_' . today()->toDateString(),
            'team_id' => $this->tenant->id,
            'created_by_id' => $this->user->id,
            'created_by_type' => ReportTaskCreatedByType::EMPLOYEE,
            'status' => ReportTaskStatus::Pending,
        ]);
        $this->assertDatabaseCount('report_tasks', 1);

        $reportTask = ReportTask::first();
        $data = $reportTask->data;

        expect($data->period->start->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->period->end->format('Y-m-d'))->toEqual(today()->format('Y-m-d'));
        expect($data->employeesIds)->toEqual([]);
        expect($data->departmentsIds)->toEqual([]);
        expect($data->directManagers)->toEqual([]);
        expect($data->tags)->toEqual([]);
        expect($data->locations)->toEqual([]);
        expect($data->shifts)->toEqual([]);
        expect($data->showInactiveEmployees)->toBeTrue();

        Queue::assertPushed(
            fn(GenerateEmployeeExcelJob $job) => $job->reportTask->id === $reportTask->id
        );

        # assert employees method
        $job = new GenerateEmployeeExcelJob($this->user, $reportTask);
        $employees = $job->employees()->collect();

        foreach ($includedEmployees as $includedEmployee) {
            expect($employees->contains('id', $includedEmployee->id));
        }

        $job->handle();
        Storage::disk('local')->assertMissing('temp/' . Str::uuid() . '.xlsx');
        Storage::disk('minio')->assertExists('reports/employees/uuid.xlsx');

        # trigger cleanup storage
        Storage::fake('local');
        Storage::fake('minio');

        Str::createUuidsNormally();
    });
});
