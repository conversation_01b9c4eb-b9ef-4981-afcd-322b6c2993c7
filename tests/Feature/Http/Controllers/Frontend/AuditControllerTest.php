<?php

namespace Tests\Feature\Http\Controllers\Frontend;

use App\DTOs\RandomProofNotificationConfig;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use OwenIt\Auditing\Models\Audit;
use Str;

beforeEach(function () {
    config(['audit.queue.enable' => false]);
});

test('fetch audits', function () {
    Team::disableAuditing();
    Employee::disableAuditing();
    Department::disableAuditing();

    $team = Team::factory()->create();

    $employee = Employee::factory()->for($team)->create();

    Team::enableAuditing();
    Employee::enableAuditing();
    Department::enableAuditing();

    $this->actingAs($employee);

    // 1. Event: Employee creation
    $auditedEmployee = Employee::create([
        'nawart_uuid' => Str::uuid(),
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'number' => 'EMP001',
        'random_proof_notification_config' => new RandomProofNotificationConfig(),
        'team_id' => $team->id,
    ]);

    // 2. Event: Employee update
    $auditedEmployee->update(['first_name' => 'Jane']);

    /** @see \App\Http\Controllers\Frontend\AuditController */
    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/audits')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(2);

    // 1. Event: Employee creation
    expect($response->json('data.data.0.event'))->toBe('created');
    expect($response->json('data.data.0.old_values'))->toBe([]);
    expect($response->json('data.data.0.new_values.id'))->toBe($auditedEmployee->id);

    // 2. Event: Employee update
    expect($response->json('data.data.1.event'))->toBe('updated');
    expect($response->json('data.data.1.old_values.first_name'))->toBe('John');
    expect($response->json('data.data.1.new_values.first_name'))->toBe('Jane');
});

test('fetch audits - search by user', function () {
    Team::disableAuditing();
    Employee::disableAuditing();
    Department::disableAuditing();

    $team = Team::factory()->create();

    $includedEmployee = Employee::factory()->for($team)->create();
    $excludedEmployee = Employee::factory()->for($team)->create();

    $auditedEmployee = Employee::factory()->for($team)->create();

    $includedAudit = Audit::create([
        'event' => 'created',
        'auditable_id' => $auditedEmployee->id,
        'auditable_type' => Employee::class,
        'user_id' => $includedEmployee->id,
        'user_type' => Employee::class,
        'team_id' => $team->id,
    ]);

    $excludedAudit = Audit::create([
        'event' => 'created',
        'auditable_id' => $auditedEmployee->id,
        'auditable_type' => Employee::class,
        'user_id' => $excludedEmployee->id,
        'user_type' => Employee::class,
        'team_id' => $team->id,
    ]);

    Team::enableAuditing();
    Employee::enableAuditing();
    Department::enableAuditing();

    /** @see \App\Http\Controllers\Frontend\AuditController */
    $response = $this->jwtActingAsAttendanceHR($includedEmployee)
        ->getJson(
            'api/v1/frontend/audits?' .
                http_build_query(['filter[search]' => $includedEmployee->email])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);

    expect($response->json('data.data.0.id'))->toBe($includedAudit->id);
    expect($response->json('data.data.0.event'))->toBe('created');
});

test('search filter matches values in new_values and old_values', function () {
    $team = Team::factory()->create();
    $employee = Employee::factory()->for($team)->create();

    $this->actingAs($employee);

    // Create an audit with searchable content in new_values
    $auditWithNewValues = Audit::create([
        'user_type' => Employee::class,
        'user_id' => $employee->id,
        'event' => 'created',
        'auditable_type' => Employee::class,
        'auditable_id' => 1,
        'old_values' => [],
        'new_values' => ['name' => 'SEARCHABLE_TEST_STRING'],
        'team_id' => $team->id,
    ]);

    // Create an audit with searchable content in old_values
    $auditWithOldValues = Audit::create([
        'user_type' => Employee::class,
        'user_id' => $employee->id,
        'event' => 'updated',
        'auditable_type' => Employee::class,
        'auditable_id' => 1,
        'old_values' => ['name' => 'ANOTHER_SEARCHABLE_STRING'],
        'new_values' => ['name' => 'new name'],
        'team_id' => $team->id,
    ]);

    // Create an audit without matching content
    $auditWithoutMatch = Audit::create([
        'user_type' => Employee::class,
        'user_id' => $employee->id,
        'event' => 'updated',
        'auditable_type' => Employee::class,
        'auditable_id' => 1,
        'old_values' => ['name' => 'no match'],
        'new_values' => ['name' => 'still no match'],
        'team_id' => $team->id,
    ]);

    // Test search in new_values
    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/audits?filter[search]=SEARCHABLE_TEST_STRING')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($auditWithNewValues->id);

    // Test search in old_values
    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/audits?filter[search]=ANOTHER_SEARCHABLE_STRING')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($auditWithOldValues->id);

    // Test search with no matches
    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/audits?filter[search]=NO_MATCH_STRING')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(0);

    // Test partial string match
    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/audits?filter[search]=SEARCHABLE')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(2);
    expect($response->json('data.data.0.id'))->toBe($auditWithNewValues->id);
    expect($response->json('data.data.1.id'))->toBe($auditWithOldValues->id);
});
