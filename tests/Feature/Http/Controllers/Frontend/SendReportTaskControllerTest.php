<?php

use App\Enums\ReportTaskStatus;
use App\Mail\ReportMail;
use App\Models\ReportTask;
use Illuminate\Support\Facades\Mail;

it('sends the report via email when the report task is available', function () {
    Mail::fake();

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->create([
            'status' => ReportTaskStatus::Success,
            'file_path' => fake()->filePath(),
        ]);

    /** @see \App\Http\Controllers\Frontend\Report\ReportTask\SendReportTaskController */
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson("api/v1/frontend/report-tasks/$reportTask->id/send")
        ->assertOk()
        ->assertJson(['message' => __('The report will be sent to your email soon')]);

    Mail::assertQueued(
        ReportMail::class,
        fn(ReportMail $mail) => $mail->hasTo($this->user->email) &&
            $mail->reportTask->id === $reportTask->id
    );
});

it('returns 404 if the report task is not available', function () {
    Mail::fake();

    $reportTask = ReportTask::factory()
        ->for($this->tenant)
        ->earlyLate()
        ->create([
            'status' => ReportTaskStatus::Failed,
        ]);

    /** @see \App\Http\Controllers\Frontend\Report\ReportTask\SendReportTaskController */
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson("api/v1/frontend/report-tasks/$reportTask->id/send")
        ->assertNotFound();

    Mail::assertNotQueued(ReportMail::class);
});
