<?php

use App\Events\ShiftAdded;
use App\Events\ShiftDeleted;
use App\Events\ShiftUpdated;
use App\Models\Employee;
use App\Models\Shift;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;

function workingHoursAsRequestData(array $weekdays): array
{
    $newRequestWorkingHours = [];
    foreach ($weekdays as $day => $shiftTime) {
        $newRequestWorkingHours[] = $shiftTime
            ? [
                'value' => $day,
                'enabled' => true,
                'from' => $shiftTime['from'],
                'to' => $shiftTime['to'],
                'next_day_checkout' => false,
            ]
            : [
                'value' => $day,
                'enabled' => false,
                'next_day_checkout' => false,
            ];
    }
    return $newRequestWorkingHours;
}

test('list shifts - no filters', function () {
    Shift::factory()
        ->for($this->tenant)
        ->createMany(3);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/shifts')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list shifts - filter by name', function () {
    $search = 'searched_name';

    $includedShift = Shift::factory()
        ->for($this->tenant)
        ->create(['name' => $search]);

    $excludedShift = Shift::factory()
        ->for($this->tenant)
        ->create(['name' => 'another_name']);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/shifts?' . http_build_query(['filter[name]' => $search]))
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);

    expect($response->json('data.data.0.id'))->toEqual($includedShift->id);
});

test('list shifts - filter by employees', function () {
    $loggedInEmployee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $searchedEmployee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $includedShift = Shift::factory()
        ->for($this->tenant)
        ->hasAttached($searchedEmployee)
        ->create();

    $excludedShift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $response = $this->jwtActingAsAttendanceHR($loggedInEmployee)
        ->getJson(
            'api/v1/frontend/shifts?' .
                http_build_query(['filter[employees]' => [$searchedEmployee->id]])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);

    expect($response->json('data.data.0.id'))->toEqual($includedShift->id);
});

test('create shift', function () {
    $name = fake()->name();
    $forceCheckout = Carbon::today()->setTime(12, 55);

    $workingHours = [
        'weekdays' => [
            'sunday' => [
                'from' => '09:00',
                'to' => '15:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'monday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'tuesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'wednesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'thursday' => false,
            'friday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'saturday' => false,
        ],
        'flexible_hours' => '60',
    ];

    Event::fake([ShiftAdded::class]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->postJson('api/v1/frontend/shifts', [
            'name' => $name,
            'force_checkout' => $forceCheckout->format('H:i'),
            'is_default' => false,
            'flexible_hours' => $workingHours['flexible_hours'],
            'weekdays' => workingHoursAsRequestData($workingHours['weekdays']),
        ])
        ->assertOk();

    $shifts = Shift::query()->where('name', $name)->get();
    expect($shifts)->toHaveCount(1);
    $shift = $shifts->first();

    expect($shift->name)->toEqual($name, 'name');
    expect($shift->working_hours)->toEqual($workingHours, 'working_hours');
    expect($shift->is_default)->toEqual(false, 'is_default');
    expect($shift->force_checkout)->toEqual($forceCheckout, 'force_checkout');

    Event::assertDispatched(ShiftAdded::class, fn($event) => $event->shift->is($shift));
});

test('update shift', function () {
    $workingHours = [
        'weekdays' => [
            'sunday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'monday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'tuesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'wednesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'thursday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'friday' => false,
            'saturday' => false,
        ],
        'flexible_hours' => '0',
    ];
    $newWorkingHours = [
        'weekdays' => [
            'sunday' => [
                'from' => '09:00',
                'to' => '15:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'monday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'tuesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'wednesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'thursday' => false,
            'friday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'saturday' => false,
        ],
        'flexible_hours' => '60',
    ];

    $shift = Shift::factory()
        ->for($this->tenant)
        ->create(['working_hours' => $workingHours]);

    $name = fake()->name();
    $forceCheckout = Carbon::today()->setTime(12, 55);

    Event::fake();

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/shifts/$shift->id", [
            'name' => $name,
            'force_checkout' => $forceCheckout->format('H:i'),
            'is_default' => false,
            'flexible_hours' => $newWorkingHours['flexible_hours'],
            'weekdays' => workingHoursAsRequestData($newWorkingHours['weekdays']),
        ])
        ->assertOk();

    $shift->refresh();

    expect($shift->name)->toEqual($name, 'name');
    expect($shift->working_hours)->toEqual($newWorkingHours, 'working_hours');
    expect($shift->is_default)->toEqual(false, 'is_default');
    expect($shift->force_checkout)->toEqual($forceCheckout, 'force_checkout');

    Event::assertDispatched(ShiftUpdated::class, function ($event) use ($shift) {
        return $event->shift->is($shift);
    });
});

test('update current default shift', function () {
    $currentDefaultShift = Shift::factory()
        ->for($this->tenant)
        ->create(['is_default' => true]);

    $shift = Shift::factory()
        ->for($this->tenant)
        ->create([
            'is_default' => false,
            'force_checkout' => fake()->time(),
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/shifts/$shift->id", [
            'name' => $shift->name,
            'force_checkout' => $shift->force_checkout->format('H:i'),
            'flexible_hours' => $shift->working_hours['flexible_hours'],
            'weekdays' => workingHoursAsRequestData($shift->working_hours['weekdays']),
            'is_default' => true,
        ])
        ->assertOk();

    expect($currentDefaultShift->refresh()->is_default)->toBeFalse();
    expect($shift->refresh()->is_default)->toBeTrue();
});

test('destroy deletes and redirects', function () {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create(['is_default' => false]);

    Event::fake();

    $this->jwtActingAsAttendanceHR($this->user)
        ->deleteJson("api/v1/frontend/shifts/$shift->id")
        ->assertOk();

    $this->assertSoftDeleted($shift);

    Event::assertDispatched(ShiftDeleted::class, fn($event) => $event->shift->is($shift));
});

test('can not delete default shift', function () {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create(['is_default' => true]);

    Event::fake();

    $this->jwtActingAsAttendanceHR($this->user)
        ->deleteJson("api/v1/frontend/shifts/$shift->id")
        ->assertStatus(422);

    Event::assertNotDispatched(ShiftDeleted::class);
});
