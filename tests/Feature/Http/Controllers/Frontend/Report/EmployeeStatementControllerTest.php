<?php

use App\Enums\RequestType;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\EmployeeStatement;
use App\Models\Leave;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Tag;

test('list statements - no filters', function () {
    $employee = createDefaultEmployee();

    $leave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->create();

    EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employee)
        ->requestable($leave)
        ->sequence(
            [
                'attendance_id' => Attendance::factory()
                    ->for($employee)
                    ->for($this->tenant)
                    ->create(),
            ],
            [
                'attendance_id' => Attendance::factory()
                    ->for($employee)
                    ->for($this->tenant)
                    ->create(),
            ],
            [
                'attendance_id' => Attendance::factory()
                    ->for($employee)
                    ->for($this->tenant)
                    ->create(),
            ]
        )
        ->count(3)
        ->create();

    $employee = createDefaultEmployee();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/reports/employee-statements?include=requestable')
        ->assertOk()
        ->assertJsonCount(3, 'data.data')
        ->assertJsonPath('data.data.0.requestable.id', $leave->id)
        ->assertJsonPath('data.data.0.requestable.type', RequestType::Leave->value);
});

test('list statements - filter by date range', function () {
    $employee = createDefaultEmployee();

    // Create statements within range
    $inRangeStatements = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employee)
        ->sequence(
            [
                'attendance_id' => Attendance::factory()
                    ->for($employee)
                    ->for($this->tenant)
                    ->create(),
                'created_at' => '2024-01-15',
            ],
            [
                'attendance_id' => Attendance::factory()
                    ->for($employee)
                    ->for($this->tenant)
                    ->create(),
                'created_at' => '2024-01-15',
            ]
        )
        ->count(2)
        ->create();

    // Create statements outside range
    $outOfRangeStatements = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employee)
        ->sequence(
            [
                'attendance_id' => Attendance::factory()
                    ->for($employee)
                    ->for($this->tenant)
                    ->create(),
                'created_at' => '2024-02-15',
            ],
            [
                'attendance_id' => Attendance::factory()
                    ->for($employee)
                    ->for($this->tenant)
                    ->create(),
                'created_at' => '2024-02-15',
            ]
        )
        ->count(2)
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'filter' => [
                        'from' => '2024-01-01',
                        'to' => '2024-01-31',
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(2);
    $returnedIds = collect($response->json('data.data'))->pluck('id');
    expect($returnedIds)->toContain(...$inRangeStatements->pluck('id'));
    expect($returnedIds)->not->toContain(...$outOfRangeStatements->pluck('id'));
});

test('list statements - filter by employees', function () {
    $employee = createDefaultEmployee();
    $targetEmployee = createDefaultEmployee();
    $otherEmployee = createDefaultEmployee();

    $includedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($targetEmployee)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($targetEmployee)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $excludedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($otherEmployee)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($otherEmployee)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'filter' => [
                        'employees' => [$targetEmployee->id],
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($includedStatement->id);
});

test('list statements - filter by departments', function () {
    $employee = createDefaultEmployee();

    $department = Department::factory()
        ->for($this->tenant)
        ->create();

    $employeeInDepartment = Employee::factory()
        ->for($this->tenant)
        ->for($department)
        ->create();

    $employeeNotInDepartment = createDefaultEmployee();

    $includedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeInDepartment)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeInDepartment)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $excludedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeNotInDepartment)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeNotInDepartment)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'filter' => [
                        'departments' => [$department->id],
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($includedStatement->id);
});

test('list statements - with includes', function () {
    $employee = createDefaultEmployee();

    $department = Department::factory()
        ->for($this->tenant)
        ->create();

    $statementEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($department)
        ->create();

    EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($statementEmployee)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($statementEmployee)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'include' => ['employee', 'employee.department'],
                ])
        )
        ->assertOk();

    expect($response->json('data.data.0'))->toHaveKeys(['employee']);
    expect($response->json('data.data.0.employee'))->toHaveKeys(['department']);
});

test('validates date format', function () {
    $employee = createDefaultEmployee();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'filter' => [
                        'from' => 'invalid-date',
                        'to' => 'invalid-date',
                    ],
                ])
        )
        ->assertStatus(422);
});

test('list statements - filter by shifts', function () {
    $employee = createDefaultEmployee();

    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();

    // Create employee with shift
    $employeeWithShift = Employee::factory()
        ->for($this->tenant)
        ->create();
    $employeeWithShift->shifts()->attach($shift->id);

    // Create employee without shift
    $employeeWithoutShift = Employee::factory()
        ->for($this->tenant)
        ->create();

    // Create statement for employee with shift
    $includedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeWithShift)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeWithShift)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    // Create statement for employee without shift
    $excludedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeWithoutShift)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeWithoutShift)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'filter' => [
                        'shifts' => [$shift->id],
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($includedStatement->id);
});

test('list statements - filter by locations', function () {
    $employee = createDefaultEmployee();

    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    // Create employee with location
    $employeeWithLocation = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create();

    // Create employee without location
    $employeeWithoutLocation = Employee::factory()
        ->for($this->tenant)
        ->create();

    // Create statement for employee with location
    $includedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeWithLocation)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeWithLocation)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    // Create statement for employee without location
    $excludedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeWithoutLocation)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeWithoutLocation)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'filter' => [
                        'locations' => [$location->id],
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($includedStatement->id);
});

test('list statements - filter by tags', function () {
    $employee = createDefaultEmployee();

    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();

    // Create employee with tag
    $employeeWithTag = Employee::factory()
        ->for($this->tenant)
        ->tag($tag)
        ->create();

    // Create employee without tag
    $employeeWithoutTag = Employee::factory()
        ->for($this->tenant)
        ->create();

    // Create statement for employee with tag
    $includedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeWithTag)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeWithTag)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    // Create statement for employee without tag
    $excludedStatement = EmployeeStatement::factory()
        ->for($this->tenant)
        ->for($employeeWithoutTag)
        ->sequence([
            'attendance_id' => Attendance::factory()
                ->for($employeeWithoutTag)
                ->for($this->tenant)
                ->create(),
        ])
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/reports/employee-statements?' .
                http_build_query([
                    'filter' => [
                        'tags' => [$tag->id],
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);
    expect($response->json('data.data.0.id'))->toBe($includedStatement->id);
});
