<?php

use App\Enums\ReportName;
use App\Enums\ReportTaskStatus;
use App\Jobs\GeneratePermissionExcelJob;
use App\Models\Report;
use App\Models\ReportTask;
use Carbon\Carbon;
use function Pest\Laravel\assertDatabaseHas;

test('ok', function ($payload) {
    Queue::fake([GeneratePermissionExcelJob::class]);

    $payload['departments_ids'] = [createDefaultDepartment()->id];
    $payload['employees_ids'] = [createDefaultEmployee()->id];

    /** @see \App\Http\Controllers\Frontend\Report\Exports\ExportPermissionController */
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/reports/permission/export?' .
                http_build_query([
                    'type' => $payload['type'],
                    'start_date' => $payload['startDate']->format('Y-m-d'),
                    'end_date' => $payload['endDate']->format('Y-m-d'),
                    'employees_ids' => $payload['employees_ids'],
                    'departments_ids' => $payload['departments_ids'],
                ])
        )
        ->assertOk();

    assertDatabaseHas('report_tasks', [
        'file_name' => ReportName::Permission->fileName(
            Carbon::parse($payload['startDate'])->format('Y-m-d')
        ),
        'report_id' => Report::byName(ReportName::Permission)->id,
        'created_by_id' => test()->user->id,
        'team_id' => test()->user->team_id,
        'status' => ReportTaskStatus::Pending,
    ]);

    $data = ReportTask::first()->data;

    expect($data->period->start->format('Y-m-d'))->toEqual($payload['startDate']->format('Y-m-d'));
    expect($data->period->end->format('Y-m-d'))->toEqual($payload['endDate']->format('Y-m-d'));
    expect($data->employeesIds)->toEqual($payload['employees_ids']);
    expect($data->departmentsIds)->toEqual($payload['departments_ids']);
    expect($data->type->value)->toEqual($payload['type']);

    Queue::assertPushed(GeneratePermissionExcelJob::class);
})->with('monthly daily');
