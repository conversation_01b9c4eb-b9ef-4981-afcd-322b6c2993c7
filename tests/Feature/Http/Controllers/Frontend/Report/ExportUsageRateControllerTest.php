<?php

use App\Enums\ReportName;
use App\Enums\ReportTaskStatus;
use App\Jobs\GenerateUsageRateExcelJob;
use App\Models\Report;
use App\Models\ReportTask;
use App\Models\Tag;
use function Pest\Laravel\assertDatabaseHas;

test('ok', function () {
    Queue::fake([GenerateUsageRateExcelJob::class]);

    $payload = [];

    $payload['departments_ids'] = [createDefaultDepartment()->id];
    $payload['employees_ids'] = [createDefaultEmployee()->id];
    $payload['shifts'] = [createDefaultShift()->id];
    $payload['locations'] = [createDefaultLocation()->id];
    $payload['tags'] = [
        Tag::factory()
            ->for(test()->tenant)
            ->create()->id,
    ];
    $payload['usage_status'] = true;

    /** @see \App\Http\Controllers\Frontend\Report\Exports\ExportUsageRateController */
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/reports/usage-rate/export?' .
                http_build_query([
                    'employees_ids' => $payload['employees_ids'],
                    'departments_ids' => $payload['departments_ids'],
                    'shifts' => $payload['shifts'],
                    'locations' => $payload['locations'],
                    'tags' => $payload['tags'],
                    'usage_status' => $payload['usage_status'],
                ])
        )
        ->assertOk();

    assertDatabaseHas('report_tasks', [
        'file_name' => ReportName::UsageRate->fileName(),
        'report_id' => Report::byName(ReportName::UsageRate)->id,
        'created_by_id' => test()->user->id,
        'team_id' => test()->user->team_id,
        'status' => ReportTaskStatus::Pending,
    ]);

    $data = ReportTask::first()->data;

    expect($data->employeesIds)->toEqual($payload['employees_ids']);
    expect($data->departmentsIds)->toEqual($payload['departments_ids']);
    expect($data->shifts)->toEqual($payload['shifts']);
    expect($data->locations)->toEqual($payload['locations']);
    expect($data->tags)->toEqual($payload['tags']);
    expect($data->usageStatus)->toEqual($payload['usage_status']);

    Queue::assertPushed(GenerateUsageRateExcelJob::class);
});
