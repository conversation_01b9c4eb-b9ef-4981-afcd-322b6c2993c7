<?php

use App\Enums\ReportName;
use App\Enums\ReportTaskStatus;
use App\Jobs\GenerateMoodExcelJob;
use App\Models\Report;
use App\Models\ReportTask;
use Carbon\Carbon;
use function Pest\Laravel\assertDatabaseHas;

test('ok - with date', function (array $payload) {
    Queue::fake([GenerateMoodExcelJob::class]);

    /** @see \App\Http\Controllers\Frontend\Report\Exports\ExportMoodController */
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            'api/v1/frontend/reports/mood/export?' .
                http_build_query([
                    'type' => $payload['type'],
                    'start_date' => $payload['startDate']->format('Y-m-d'),
                    'end_date' => $payload['endDate']->format('Y-m-d'),
                ])
        )
        ->assertOk();

    assertDatabaseHas('report_tasks', [
        'file_name' => ReportName::Mood->fileName(
            Carbon::parse($payload['startDate'])->format('Y-m-d')
        ),
        'report_id' => Report::byName(ReportName::Mood)->id,
        'created_by_id' => $this->user->id,
        'team_id' => $this->user->team_id,
        'status' => ReportTaskStatus::Pending,
    ]);

    $data = ReportTask::first()->data;

    expect($data->period->start->format('Y-m-d'))->toEqual($payload['startDate']->format('Y-m-d'));
    expect($data->period->end->format('Y-m-d'))->toEqual($payload['endDate']->format('Y-m-d'));

    Queue::assertPushed(GenerateMoodExcelJob::class);
})->with('monthly daily');
