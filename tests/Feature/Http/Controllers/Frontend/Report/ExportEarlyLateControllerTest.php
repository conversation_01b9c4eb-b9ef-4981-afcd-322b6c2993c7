<?php

use App\Enums\ReportName;
use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Report;
use App\Models\Shift;
use App\Models\Tag;
use App\Workflows\GenerateEarlyLateReportWorkflow;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Sassno<PERSON>\Venture\Facades\Workflow;
use function Pest\Laravel\assertDatabaseEmpty;
use function Pest\Laravel\assertDatabaseHas;

test('not found when no attendance records', function ($payload) {
    Workflow::fake();

    // no attendance created
    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl($payload))
        ->assertStatus(422)
        ->assertJson(['message' => __('No employees to export')]);

    assertDatabaseEmpty('report_tasks');

    Workflow::assertNotStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok without filters', function ($payload) {
    Workflow::fake();

    $attendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate'])
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl($payload))
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok when employees', function ($payload) {
    Workflow::fake();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->create();
    $excludedEmployee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $inRangeIncludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $inRangeExcludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl([...$payload, 'employees_ids' => [$includedEmployee->id]]))
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok when filtered by departments', function ($payload) {
    Workflow::fake();

    $includedDepartment = Department::factory()
        ->for($this->tenant)
        ->create();

    $excludedDepartment = Department::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($includedDepartment)
        ->create();

    $excludedEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($excludedDepartment)
        ->create();

    $inRangeIncludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $inRangeExcludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl([...$payload, 'departments_ids' => [$includedDepartment->id]]))
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok when filtered by tags', function ($payload) {
    Workflow::fake();

    $includedTag = Tag::factory()
        ->for($this->tenant)
        ->create();

    $excludedTag = Tag::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->hasAttached(factory: $includedTag, relationship: 'tags')
        ->create();

    $excludedEmployee = Employee::factory()
        ->for($this->tenant)
        ->hasAttached(factory: $excludedTag, relationship: 'tags')
        ->create();

    $inRangeIncludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $inRangeExcludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl([...$payload, 'tags' => [$includedTag->id]]))
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok when filtered by locations', function ($payload) {
    Workflow::fake();

    $includedLocation = Location::factory()
        ->for($this->tenant)
        ->create();

    $excludedLocation = Location::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->hasAttached(
            factory: $includedLocation,
            pivot: ['permanent' => true],
            relationship: 'locations'
        )
        ->create();

    $excludedEmployee = Employee::factory()
        ->for($this->tenant)
        ->hasAttached(
            factory: $excludedLocation,
            pivot: ['permanent' => true],
            relationship: 'locations'
        )
        ->create();

    $inRangeIncludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $inRangeExcludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl([...$payload, 'locations' => [$includedLocation->id]]))
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok when filtered by shifts', function ($payload) {
    Workflow::fake();

    $includedShift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $excludedShift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee->shifts()->sync($includedShift);

    $excludedEmployee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $excludedEmployee->shifts()->sync($excludedShift);

    $inRangeIncludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']), [
            'shift_id' => $includedShift->id,
        ]);

    $inRangeExcludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl([...$payload, 'shifts' => [$includedShift->id]]))
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok when exclude inactive employees by default', function ($payload) {
    Workflow::fake();

    $activeEmployee = Employee::factory()
        ->for($this->tenant)
        ->create(['is_active' => true]);

    $inactiveEmployee = Employee::factory()
        ->for($this->tenant)
        ->create(['is_active' => false]);

    $inRangeIncludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($activeEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $inRangeExcludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($inactiveEmployee)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(
            earlyLateUrl([
                ...$payload,
                'employees_ids' => [$activeEmployee->id],
                // intentionally not sending show_inactive_employees
                // 'show_inactive_employees' => false,
            ])
        )
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

test('ok when include inactive employees', function ($payload) {
    Workflow::fake();

    $activeEmployee = Employee::factory()
        ->for($this->tenant)
        ->create(['is_active' => true]);

    $inactiveEmployee = Employee::factory()
        ->for($this->tenant)
        ->create(['is_active' => false]);

    $inRangeIncludedAttendances = collect([
        ...Attendance::factory()
            ->for($this->tenant)
            ->for($activeEmployee)
            ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate'])),
        ...Attendance::factory()
            ->for($this->tenant)
            ->for($inactiveEmployee)
            ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate'])),
    ]);

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson(earlyLateUrl([...$payload, 'show_inactive_employees' => true]))
        ->assertOk();

    assertReportTaskCreated($payload);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);
})->with('monthly daily');

function earlyLateUrl($payload)
{
    return 'api/v1/frontend/reports/early-late/export' .
        '?' .
        http_build_query([
            'type' => $payload['type'],
            'start_date' => $payload['startDate']->format('Y-m-d'),
            'end_date' => $payload['endDate']->format('Y-m-d'),
            'sheet_mode' => SheetMode::SingleSheet->value,
            'employees_ids' => $payload['employees_ids'] ?? [],
            'departments_ids' => $payload['departments_ids'] ?? [],
            'tags' => $payload['tags'] ?? [],
            'locations' => $payload['locations'] ?? [],
            'shifts' => $payload['shifts'] ?? [],
            'show_inactive_employees' => $payload['show_inactive_employees'] ?? false,
        ]);
}

function assertReportTaskCreated(array $payload)
{
    assertDatabaseHas('report_tasks', [
        'file_name' => ReportName::EarlyLate->fileName(
            Carbon::parse($payload['startDate'])->format('Y-m-d')
        ),
        'report_id' => Report::byName(ReportName::EarlyLate)->id,
        'created_by_id' => test()->user->id,
        'team_id' => test()->user->team_id,
        'status' => ReportTaskStatus::Pending,
    ]);
}
