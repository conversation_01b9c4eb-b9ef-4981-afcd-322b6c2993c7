<?php

use App\Models\Attendance;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

test('fetch employee summary', function () {
    $employee = createDefaultEmployee();

    $period = CarbonPeriod::create('2021-01-01', '2021-01-31');

    $absentPeriod = CarbonPeriod::create('2021-01-16', '2021-01-31');

    // normal shift
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-01 8:00:00'))
        ->date(date: '2021-01-01', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // early in - 15 minutes
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-02 7:45:00'))
        ->date(date: '2021-01-02', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // early in - 10 minutes
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-03 7:50:00'))
        ->date(date: '2021-01-03', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // normal shift
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-04 8:00:00'))
        ->date(date: '2021-01-04', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // late in - 15 minutes
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-05 8:15:00'))
        ->date(date: '2021-01-05', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // late in - 10 minutes
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-06 8:10:00'))
        ->date(date: '2021-01-06', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // absent - 15 days [16-31]
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->absent()
        ->createFromPeriod($absentPeriod, [
            'check_in' => null,
            'in_type' => null,
        ]);

    /** @see \App\Http\Controllers\Frontend\EmployeeSummaryController */
    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get(
            "/api/v1/frontend/employees/$employee->id/summary?" .
                http_build_query([
                    'filter' => [
                        'from' => $period->getStartDate()->format('Y-m-d'),
                        'to' => $period->getEndDate()->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.total_absent_days'))->toBe($absentPeriod->count());
    expect($response->json('data.early_in'))->toBe('00:25:00');
    expect($response->json('data.late_in'))->toBe('00:25:00');
});
