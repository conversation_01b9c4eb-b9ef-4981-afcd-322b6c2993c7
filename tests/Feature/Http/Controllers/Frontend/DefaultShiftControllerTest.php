<?php

use App\Models\Shift;

test('default shift', function () {
    $employee = createDefaultEmployee();

    $nonDefaultShift1 = Shift::factory()
        ->for($this->tenant)
        ->create();

    $defaultShift = Shift::factory()
        ->for($this->tenant)
        ->default()
        ->create();

    $nonDefaultShift2 = Shift::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/shifts/default')
        ->assertOk()
        ->assertJson([
            'data' => [
                'id' => $defaultShift->id,
                'name' => $defaultShift->name,
                'is_default' => true,
            ],
        ]);
});
