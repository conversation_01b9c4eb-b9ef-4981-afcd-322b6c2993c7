<?php

use App\DTOs\EmployeeStatementConfig;
use App\Enums\ApprovalType;
use App\Enums\CheckoutReminderConfig;
use App\Enums\EarlyLateNestingPolicy;
use App\Enums\EarlyLatePeriodPolicy;
use App\Enums\RandomProofOfAttendanceDeadline;
use App\Enums\SheetMode;
use App\Models\Employee;
use App\Models\Tag;
use App\Models\Team;

test('update settings', function () {
    $tenant = Team::factory()->basic()->create();
    $tags = Tag::factory()->for($tenant)->count(2)->create();

    $tenant->update([
        'employees_weekly_summary' => true, // update to false

        'early_late_config' => [
            'enabled' => true,
            'period_policy' => EarlyLatePeriodPolicy::Weekly, // update to monthly
            'nesting_policy' => EarlyLateNestingPolicy::DirectDepartment, // update to nesting
            'sheet_mode' => SheetMode::SingleSheet, // update to multiple sheet
            'excluded_tags' => $tags->pluck('id')->toArray(), // update to empty
        ],
        'remote_work' => 'allowed', // update to not_allowed
        'free_checkout' => 'allowed', // update to not_allowed
        'approval_requests_limit' => fake()->randomNumber(), // update to 10
        'vacation_weekend' => true, // update to false
        'leave_request' => true, // update to false
        'approval_request' => true, // update to false
        'permission_request' => true, // update to false
        'permission_request_monthly_limit_hours' => 12, // update to 13
        'permission_request_daily_limit_hours' => 3, // update to 4

        'remote_work_days_monthly_limit' => 10, // update to 15
        'random_proof_notification_config' => [
            'enabled' => true,
            'count' => 5,
            'deadline' => RandomProofOfAttendanceDeadline::fifteen,
        ],

        'checkout_reminder_config' => CheckoutReminderConfig::ByCheckin,
        // update to CheckoutReminderConfig::ByFlexibleHoursEnd
        'map_report_thresholds' => [
            'yellow' => [
                'min' => 1,
                'max' => 2,
            ],
        ],
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: false, // update to true
            preventRequestsEnabled: false, // update to true
            daysBeforePreventingRequests: 1, // update to 2
            lateCheckinBufferMinutes: 10, // update to 15
            earlyCheckoutBufferMinutes: 10, // update to 15
            absentEnabled: false // update to true
        ),
    ]);

    $employee = Employee::factory()->for($tenant)->create();
    $payload = [
        'employees_weekly_summary' => false,

        'early_late_config' => [
            'enabled' => true,
            'period_policy' => EarlyLatePeriodPolicy::Monthly,
            'nesting_policy' => EarlyLateNestingPolicy::NestedDepartments,
            'sheet_mode' => SheetMode::MultiSheet,
            'excluded_tags' => [],
        ],
        'remote_work' => 'not_allowed',
        'free_checkout' => 'not_allowed',
        'approval_requests_limit' => '10',
        'vacation_weekend' => false,
        'leave_request' => false,
        'approval_request' => false,
        'permission_request' => false,
        'permission_request_monthly_limit_hours' => 13,
        'permission_request_daily_limit_hours' => 4,

        'remote_work_days_monthly_limit' => 15,
        'random_proof_notification_config' => [
            'enabled' => false,
            'count' => 10,
            'deadline' => RandomProofOfAttendanceDeadline::thirty,
        ],
        'checkout_reminder_config' => CheckoutReminderConfig::ByFlexibleHoursEnd,
        'map_report_thresholds' => [
            'yellow' => [
                'min' => 3,
                'max' => 5,
            ],
        ],
        'employee_statement_config' => (new EmployeeStatementConfig(
            enabled: true,
            preventRequestsEnabled: true,
            daysBeforePreventingRequests: 2,
            lateCheckinBufferMinutes: 15,
            earlyCheckoutBufferMinutes: 15,
            absentEnabled: true
        ))->toArray(),
    ];

    /** @see \App\Http\Controllers\Frontend\UpdateSettingController */
    $this->jwtActingAsAttendanceHR($employee)
        ->putJson('api/v1/frontend/settings/update', $payload, [
            'Accept-Language' => 'en',
        ])
        ->assertOk();

    $tenant->refresh();

    expect($tenant->employees_weekly_summary)->toBeFalse();
    expect($tenant->early_late_config->periodPolicy)->toBe(EarlyLatePeriodPolicy::Monthly);
    expect($tenant->early_late_config->nestingPolicy)->toBe(
        EarlyLateNestingPolicy::NestedDepartments
    );
    expect($tenant->early_late_config->sheetMode)->toBe(SheetMode::MultiSheet);
    expect($tenant->early_late_config->excludedTags)->toBeEmpty();
    expect($tenant->remote_work)->toBe('not_allowed');
    expect($tenant->free_checkout)->toBe('not_allowed');
    expect($tenant->approval_requests_limit)->toBe(10);
    expect($tenant->vacation_weekend)->toBeFalse();
    expect($tenant->leave_request)->toBeFalse();
    expect($tenant->approval_request)->toBeFalse();
    expect($tenant->permission_request)->toBeFalse();
    expect($tenant->permission_request_monthly_limit_hours)->toBe(13);
    expect($tenant->permission_request_daily_limit_hours)->toBe(4);
    expect($tenant->remote_work_days_monthly_limit)->toBe(15);
    expect($tenant->checkout_reminder_config)->toBe(CheckoutReminderConfig::ByFlexibleHoursEnd);
    expect($tenant->map_report_thresholds['yellow']['min'])->toBe(3);
    expect($tenant->map_report_thresholds['yellow']['max'])->toBe(5);

    expect($tenant->random_proof_notification_config->enabled)->toBeFalse();
    expect($tenant->random_proof_notification_config->count)->toBe(10);
    expect($tenant->random_proof_notification_config->deadline)->toBe(
        RandomProofOfAttendanceDeadline::thirty
    );
    expect($tenant->employee_statement_config->enabled)->toBeTrue();
    expect($tenant->employee_statement_config->preventRequestsEnabled)->toBeTrue();
    expect($tenant->employee_statement_config->daysBeforePreventingRequests)->toBe(2);
    expect($tenant->employee_statement_config->lateCheckinBufferMinutes)->toBe(15);
    expect($tenant->employee_statement_config->earlyCheckoutBufferMinutes)->toBe(15);
});

test('random proof notification config - count can be null', function () {
    $tenant = Team::factory()->basic()->create();

    $tenant->update([
        'random_proof_notification_config' => [
            'enabled' => true,
            'count' => 5,
            'deadline' => RandomProofOfAttendanceDeadline::fifteen,
        ],
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    $payload = [
        'random_proof_notification_config' => [
            'enabled' => false,
            'count' => null,
            'deadline' => RandomProofOfAttendanceDeadline::thirty,
        ],
    ];

    $this->jwtActingAsAttendanceHR($employee)
        ->putJson('api/v1/frontend/settings/update', $payload, [
            'Accept-Language' => 'en',
        ])
        ->assertOk();

    $tenant->refresh();

    expect($tenant->random_proof_notification_config->count)->toBeNull();
});

test('all fields are optional', function () {
    $tenant = Team::factory()->basic()->create();
    $employee = Employee::factory()->for($tenant)->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->putJson('api/v1/frontend/settings/update')
        ->assertOk();
});

describe('permission request', function () {
    test('value must be boolean', function () {
        $tenant = Team::factory()
            ->basic()
            ->create(['permission_request' => false]);
        $employee = Employee::factory()->for($tenant)->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'permission_request' => [1],
            ])
            ->assertStatus(422);

        $this->assertDatabaseHas(Team::class, ['id' => $tenant->id, 'permission_request' => false]);
    });
    test('option is set to enabled and updated in database', function () {
        $tenant = Team::factory()
            ->basic()
            ->create(['permission_request' => false]);
        $employee = Employee::factory()->for($tenant)->create();

        $newValue = 1;

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'permission_request' => $newValue,
            ])
            ->assertOk();

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'permission_request' => $newValue,
        ]);
    });
});

describe('permission request monthly hours limit', function () {
    test('invalid input', function (string|int $newValue) {
        $expectedDatabaseValue = 12;

        $tenant = Team::factory()
            ->basic()
            ->create(['permission_request_monthly_limit_hours' => $expectedDatabaseValue]);

        $employee = Employee::factory()->for($tenant)->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'permission_request_monthly_limit_hours' => $newValue,
            ])
            ->assertStatus(422);

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'permission_request_monthly_limit_hours' => $expectedDatabaseValue,
        ]);
    })->with([
        'value should be integer' => 'abc',
        'value should be more than or equal 0' => -1,
        'value should be less than or equal total of hours in 31 days' => 31 * 24 + 1,
    ]);
    test('valid input', function (int $newValue) {
        $tenant = Team::factory()
            ->basic()
            ->create(['permission_request_monthly_limit_hours' => 12]);
        $employee = Employee::factory()->for($tenant)->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'permission_request_monthly_limit_hours' => $newValue,
            ])
            ->assertOk();

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'permission_request_monthly_limit_hours' => $newValue,
        ]);
    })->with([
        'limit set to 13 hours and updated in database successfully' => 13,
        'limit set to 0 hours and updated in database successfully' => 0,
        'limit set to total of hours in 31 days and updated in database successfully' => 31 * 24,
    ]);
});

describe('remote work yearly limit', function () {
    test('invalid input', function (string|int $newValue) {
        $expectedDatabaseValue = 12;

        $tenant = Team::factory()
            ->active()
            ->basic()
            ->create(['remote_work_days_yearly_limit' => $expectedDatabaseValue]);

        $employee = Employee::factory()->for($tenant)->active()->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'remote_work_days_yearly_limit' => $newValue,
            ])
            ->assertStatus(422);

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'remote_work_days_yearly_limit' => $expectedDatabaseValue,
        ]);
    })->with([
        'value should be integer' => 'abc',
        'value should be more than or equal 0' => -1,
        'value should be less than or equal 366 days' => 367,
    ]);
    test('valid input', function (int|null $newValue) {
        $tenant = Team::factory()
            ->active()
            ->basic()
            ->create(['remote_work_days_yearly_limit' => 12]);
        $employee = Employee::factory()->for($tenant)->active()->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'remote_work_days_yearly_limit' => $newValue,
            ])
            ->assertOk();

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'remote_work_days_yearly_limit' => $newValue,
        ]);
    })->with([
        'limit set to unlimited and updated in database successfully' => null,
        'limit set to 0 days and updated in database successfully' => 0,
        'limit set to 13 days and updated in database successfully' => 100,
        'limit set to 31 days and updated in database successfully' => 366,
    ]);
});

describe('remote work monthly limit', function () {
    test('invalid input', function (string|int $newValue) {
        $expectedDatabaseValue = 12;

        $tenant = Team::factory()
            ->active()
            ->basic()
            ->create(['remote_work_days_monthly_limit' => $expectedDatabaseValue]);

        $employee = Employee::factory()->for($tenant)->active()->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'remote_work_days_monthly_limit' => $newValue,
            ])
            ->assertStatus(422);

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'remote_work_days_monthly_limit' => $expectedDatabaseValue,
        ]);
    })->with([
        'value should be integer' => 'abc',
        'value should be more than or equal 0' => -1,
        'value should be less than or equal 31 days' => 32,
    ]);
    test('valid input', function (int|null $newValue) {
        $tenant = Team::factory()
            ->active()
            ->basic()
            ->create(['remote_work_days_monthly_limit' => 12]);
        $employee = Employee::factory()->for($tenant)->active()->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'remote_work_days_monthly_limit' => $newValue,
            ])
            ->assertOk();

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'remote_work_days_monthly_limit' => $newValue,
        ]);
    })->with([
        'limit set to unlimited and updated in database successfully' => null,
        'limit set to 0 days and updated in database successfully' => 0,
        'limit set to 13 days and updated in database successfully' => 13,
        'limit set to 31 days and updated in database successfully' => 31,
    ]);
});

describe('remote work weekly limit', function () {
    test('invalid input', function (string|int $newValue) {
        $expectedDatabaseValue = 2;

        $tenant = Team::factory()
            ->active()
            ->basic()
            ->create(['remote_work_days_weekly_limit' => $expectedDatabaseValue]);

        $employee = Employee::factory()->for($tenant)->active()->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'remote_work_days_weekly_limit' => $newValue,
            ])
            ->assertStatus(422);

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'remote_work_days_weekly_limit' => $expectedDatabaseValue,
        ]);
    })->with([
        'value should be integer' => 'abc',
        'value should be more than or equal 0' => -1,
        'value should be less than or equal 7 days' => 8,
    ]);
    test('valid input', function (int|null $newValue) {
        $tenant = Team::factory()
            ->active()
            ->basic()
            ->create(['remote_work_days_weekly_limit' => 2]);
        $employee = Employee::factory()->for($tenant)->active()->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'remote_work_days_weekly_limit' => $newValue,
            ])
            ->assertOk();

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'remote_work_days_weekly_limit' => $newValue,
        ]);
    })->with([
        'limit set to unlimited and updated in database successfully' => null,
        'limit set to 0 days and updated in database successfully' => 0,
        'limit set to 5 days and updated in database successfully' => 5,
        'limit set to 7 days and updated in database successfully' => 7,
    ]);
});

describe('permission request day hours limit', function () {
    test('invalid input', function (string|int $newValue) {
        $tenant = Team::factory()
            ->basic()
            ->create(['permission_request_daily_limit_hours' => 3]);

        $employee = Employee::factory()->for($tenant)->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'permission_request_daily_limit_hours' => $newValue,
            ])
            ->assertStatus(422);

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'permission_request_daily_limit_hours' => 3,
        ]);
    })->with([
        'value should be integer' => 'abc',
        'value should be more than or equal 0' => -1,
        'value should be less than or equal 23 hours' => 24,
    ]);
    test('valid input', function (int $newValue) {
        $tenant = Team::factory()
            ->basic()
            ->create(['permission_request_daily_limit_hours' => 3]);

        $employee = Employee::factory()->for($tenant)->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'permission_request_daily_limit_hours' => $newValue,
            ])
            ->assertOk();

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'permission_request_daily_limit_hours' => $newValue,
        ]);
    })->with([
        'limit set to 4 hours and updated in database successfully' => 4,
        'limit set to 0 hours and updated in database successfully' => 0,
        'limit set to 23 hours and updated in database successfully' => 23,
    ]);
});

describe('report excluded tags', function () {
    test('invalid input - excluded a tag that does not exists', function () {
        $tenant = Team::factory()->basic()->create();

        $employee = Employee::factory()->for($tenant)->create();

        Tag::query()->delete();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'early_late_config' => [
                    'excluded_tags' => [999],
                ],
            ])
            ->assertStatus(422);
    });

    test('invalid input - excluded a tag from another tenant', function () {
        $tenant = Team::factory()->basic()->create();
        $employee = Employee::factory()->for($tenant)->create();
        $tagFromAnotherTenant = Tag::factory()->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'early_late_config' => [
                    'excluded_tags' => [$tagFromAnotherTenant->id],
                ],
            ])
            ->assertStatus(422);
    });

    test('exclude tags successfully', function () {
        $tenant = Team::factory()->basic()->create();
        $employee = Employee::factory()->for($tenant)->create();

        $tag = Tag::factory()->for($tenant)->create();

        /** @see \App\Http\Controllers\Frontend\UpdateSettingController */
        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'early_late_config' => [
                    'excluded_tags' => [$tag->id],
                ],
            ])
            ->assertOk();

        expect($tenant->refresh()->early_late_config->excludedTags)->toBe([$tag->id]);
    });
});

describe('approval type', function () {
    test('value should be a value from the enum', function () {
        $tenant = Team::factory()
            ->basic()
            ->create(['approval_type' => ApprovalType::OneLayer->value]);

        $employee = Employee::factory()->for($tenant)->create();

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'approval_type' => 'abc',
            ])
            ->assertStatus(422);

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'approval_type' => ApprovalType::OneLayer->value,
        ]);
    });
    test('valid input', function () {
        $tenant = Team::factory()
            ->basic()
            ->create(['approval_type' => ApprovalType::OneLayer->value]);
        $employee = Employee::factory()->for($tenant)->create();

        $newValue = ApprovalType::TwoLayer;

        $this->jwtActingAsAttendanceHR($employee)
            ->putJson('api/v1/frontend/settings/update', [
                'approval_type' => $newValue->value,
            ])
            ->assertOk();

        $this->assertDatabaseHas(Team::class, [
            'id' => $tenant->id,
            'approval_type' => $newValue->value,
        ]);
    });
});
