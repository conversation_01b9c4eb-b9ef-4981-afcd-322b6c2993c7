<?php

use App\Models\ReportTask;

test('list report tasks', function () {
    ReportTask::factory()
        ->earlyLate()
        ->for($this->tenant)
        ->createMany(3);

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson('api/v1/frontend/report-tasks')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('show report task', function () {
    $reportTask = ReportTask::factory()
        ->earlyLate()
        ->createdBy($this->user)
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson("api/v1/frontend/report-tasks/$reportTask->id")
        ->assertOk()
        ->assertJson([
            'data' => [
                'id' => $reportTask->id,
                'status' => $reportTask->status->value,
                'display_status' => $reportTask->status->displayName(),
                'file_name' => $reportTask->file_name,
                'file_path' => $reportTask->file_path,
                'start_date' => $reportTask->start_date?->format('Y-m-d H:i:s'),
                'end_date' => $reportTask->end_date?->format('Y-m-d H:i:s'),
                'completed_at' => $reportTask->completed_at?->format('Y-m-d H:i:s'),
                'created_at' => $reportTask->created_at->format('Y-m-d H:i:s'),
                'updated_at' => $reportTask->updated_at->format('Y-m-d H:i:s'),
                'created_by' => [
                    'id' => $reportTask->createdBy->id,
                ],
                'report' => [
                    'id' => $reportTask->report->id,
                    'name' => $reportTask->report->name->value,
                    'display_name' => $reportTask->report->name->displayName(),
                ],
            ],
        ]);
});
