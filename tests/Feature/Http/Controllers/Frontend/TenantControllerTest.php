<?php

use App\Models\Employee;
use App\Models\Team;

test('return current tenant successfully', function () {
    $tenant = Team::factory()->active()->basic()->create();
    $employee = Employee::factory()->for($tenant)->active()->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/tenant')
        ->assertOk()
        ->assertJsonPath('data.id', $tenant->id);
});
