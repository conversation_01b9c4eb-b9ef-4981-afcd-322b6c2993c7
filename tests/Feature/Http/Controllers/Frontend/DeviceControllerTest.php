<?php

use App\Enums\LocationSelection;
use App\Models\Device;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

# list
test('list all devices - get second page', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();
    $anotherTenant = Team::factory()->enterprise()->active()->createOne();

    $employee = Employee::factory()->for($tenant)->active()->create();

    Device::factory()->for($tenant)->deviceLocation()->create();
    Device::factory()->for($tenant)->existingLocation()->create();
    Device::factory($anotherTenant)->create();

    # act
    /** @see \App\Http\Controllers\Frontend\DeviceController::index */
    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/devices?per_page=1&page=2')
        ->assertOk()
        ->assertJsonIsObject('data')
        ->assertJsonPath('data.meta.current_page', 2)
        ->assertJsonPath('data.meta.per_page', 1);
});

test('list all devices', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();
    $anotherTenant = Team::factory()->enterprise()->active()->createOne();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $firstDevice = Device::factory()->for($tenant)->deviceLocation()->create();
    $secondDevice = Device::factory()->for($tenant)->existingLocation()->create();
    Device::factory($anotherTenant)->create();

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/devices')
        ->assertOk()
        ->assertJsonIsArray('data.data')
        ->assertJsonCount(2, 'data.data')
        ->assertJsonPath('data.data.0.id', $firstDevice->id)
        ->assertJsonPath('data.data.1.id', $secondDevice->id)
        ->assertJsonMissingPath('data.data.0.secret_key');
});

test(
    'list all devices with name containing the search term "headquarter" either device name or location name',
    function () {
        # prepare
        $tenant = Team::factory()->enterprise()->active()->create();
        $anotherTenant = Team::factory()->enterprise()->active()->createOne();

        $employee = Employee::factory()->for($tenant)->active()->create();

        $firstIncludedDevice = Device::factory()
            ->for($tenant)
            ->deviceLocation()
            ->create(['name' => 'branch Headquarter']);

        $secondIncludedDevice = Device::factory()
            ->for($tenant)
            ->existingLocation(
                Location::factory()
                    ->for($tenant)
                    ->create(['name' => 'branch Headquarter'])
            )
            ->create(['name' => 'branch outsource 1']);

        Device::factory()
            ->for($tenant)
            ->existingLocation()
            ->create(['name' => 'branch outsource 2']);
        Device::factory($anotherTenant)->create(['name' => 'branch Headquarter']);

        # act
        $this->jwtActingAsAttendanceHR($employee)
            ->getJson('api/v1/frontend/devices?filter[search]=headquarter')
            ->assertOk()
            ->assertJsonIsArray('data.data')
            ->assertJsonCount(2, 'data.data')
            ->assertJsonPath('data.data.0.id', $firstIncludedDevice->id)
            ->assertJsonPath('data.data.1.id', $secondIncludedDevice->id)
            ->assertJsonMissingPath('data.data.0.secret_key');
    }
);

test('search for devices by device username', function () {
    # prepare
    $username = Str::ulid();

    $tenant = Team::factory()->enterprise()->active()->create();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $includedDevice = Device::factory()->for($tenant)->deviceLocation()->create();
    $includedDevice->update(['username' => $username]);

    Device::factory()->for($tenant)->existingLocation()->create();

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->getJson("api/v1/frontend/devices?filter[search]=$username")
        ->assertOk()
        ->assertJsonIsArray('data.data')
        ->assertJsonCount(1, 'data.data')
        ->assertJsonPath('data.data.0.id', $includedDevice->id)
        ->assertJsonMissingPath('data.data.0.secret_key');
});

# create
test(
    'fail to create device with Device Location option - name and location_selection are required',
    function () {
        # prepare
        $tenant = Team::factory()->enterprise()->active()->create();

        $employee = Employee::factory()->for($tenant)->active()->create();

        $payload = [];

        # act
        $this->jwtActingAsAttendanceHR($employee)
            ->postJson('api/v1/frontend/devices', $payload)
            ->assertStatus(422);
    }
);

test('fail to create device with ExistingLocation option - location_id is missing', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $payload = [
        'name' => 'headquarter',
        'location_selection' => LocationSelection::ExistingLocation->value,
    ];

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->postJson('api/v1/frontend/devices', $payload)
        ->assertStatus(422);
    $this->jwtActingAsAttendanceHR($employee)
        ->postJson('api/v1/frontend/devices', [...$payload, 'location_id' => null])
        ->assertStatus(422);

    assertDatabaseMissing('devices', $payload);
});

test('create device with Device Location option successfully - location_id not set', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $payload = [
        'name' => 'headquarter',
        'location_selection' => LocationSelection::DeviceLocation->value,
        'location_id' => null,
    ];

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->postJson('api/v1/frontend/devices', $payload)
        ->assertOk()
        ->assertJsonIsObject('data')
        ->assertJsonPath('data.name', $payload['name'])
        ->assertJsonPath('data.location_selection', $payload['location_selection'])
        ->assertJsonPath('data.location_id', $payload['location_id'])
        ->assertJsonStructure([
            'data' => ['plain_text_secret_key', 'username', 'name', 'location_selection'],
        ]);

    assertDatabaseHas('devices', $payload);
});

test('create device with Device Location option successfully - location_id is set', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $location = Location::factory()->for($tenant)->create();

    $payload = [
        'name' => 'headquarter',
        'location_selection' => LocationSelection::DeviceLocation->value,
        'location_id' => $location,
    ];

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->postJson('api/v1/frontend/devices', $payload)
        ->assertOk()
        ->assertJsonIsObject('data')
        ->assertJsonPath('data.name', $payload['name'])
        ->assertJsonPath('data.location_selection', $payload['location_selection'])
        ->assertJsonPath('data.location_id', null)
        ->assertJsonStructure([
            'data' => ['plain_text_secret_key', 'username', 'name', 'location_selection'],
        ]);

    assertDatabaseHas('devices', array_except($payload, 'location_id'));
});

test('create device with Existing Location option successfully', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $location = Location::factory()->for($tenant)->create();

    $payload = [
        'name' => 'headquarter',
        'location_selection' => LocationSelection::ExistingLocation->value,
        'location_id' => $location->id,
    ];

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->postJson('api/v1/frontend/devices', $payload)
        ->assertOk()
        ->assertJsonIsObject('data')
        ->assertJsonPath('data.name', $payload['name'])
        ->assertJsonPath('data.location_selection', $payload['location_selection'])
        ->assertJsonPath('data.location.id', $payload['location_id'])
        ->assertJsonStructure([
            'data' => [
                'plain_text_secret_key',
                'username',
                'name',
                'location_selection',
                'location',
            ],
        ]);

    assertDatabaseHas('devices', $payload);
});

# update
test('update device with option existing_location to Device_location successfully', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $device = Device::factory()
        ->for($tenant)
        ->existingLocation()
        ->create(['name' => 'headquarter']);

    $payload = [
        'name' => 'outsourcing',
        'location_selection' => LocationSelection::DeviceLocation->value,
        'location_id' => null,
    ];

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->putJson("api/v1/frontend/devices/$device->id", $payload)
        ->assertOk()
        ->assertJsonIsObject('data')
        ->assertJsonPath('data.name', $payload['name'])
        ->assertJsonPath('data.location_selection', $payload['location_selection'])
        ->assertJsonPath('data.location_id', $payload['location_id'])
        ->assertJsonStructure([
            'data' => ['username', 'name', 'location_selection'],
        ])
        ->assertJsonMissingPath('data.plain_text_secret_key');

    assertDatabaseHas('devices', [...$payload, 'id' => $device->id]);
});

# show
test('show device', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();
    $anotherTenant = Team::factory()->enterprise()->active()->createOne();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $device = Device::factory()->for($tenant)->deviceLocation()->create();
    Device::factory()->for($tenant)->existingLocation()->create();
    $deviceFromAnotherTenant = Device::factory($anotherTenant)->create();

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->getJson("api/v1/frontend/devices/$device->id")
        ->assertOk()
        ->assertJsonIsObject('data')
        ->assertJsonPath('data.id', $device->id)
        ->assertJsonMissingPath('data.secret_key');

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson("api/v1/frontend/devices/$deviceFromAnotherTenant->id")
        ->assertNotFound();
});

# delete
test('delete device', function () {
    # prepare
    $tenant = Team::factory()->enterprise()->active()->create();
    $anotherTenant = Team::factory()->enterprise()->active()->createOne();

    $employee = Employee::factory()->for($tenant)->active()->create();

    $device = Device::factory()->for($tenant)->deviceLocation()->create();
    Device::factory()->for($tenant)->existingLocation()->create();
    $deviceFromAnotherTenant = Device::factory($anotherTenant)->create();

    # act
    $this->jwtActingAsAttendanceHR($employee)
        ->deleteJson("api/v1/frontend/devices/$device->id")
        ->assertOk();

    $this->jwtActingAsAttendanceHR($employee)
        ->deleteJson("api/v1/frontend/devices/$deviceFromAnotherTenant->id")
        ->assertNotFound();

    assertDatabaseMissing('devices', ['id' => $device->id, 'deleted_at' => null]);
});
