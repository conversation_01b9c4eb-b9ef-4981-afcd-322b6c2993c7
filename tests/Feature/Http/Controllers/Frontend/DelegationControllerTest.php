<?php

use App\Enums\DelegationType;
use App\Models\Delegation;
use App\Models\Employee;
use function Pest\Laravel\assertDatabaseEmpty;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

function defaultRequestData()
{
    return [
        'shifts' => [
            [
                'id' => createDefaultShift()->id,
                'type' => 'permanent',
            ],
        ],
        'random_proof_notification_config' => [
            'enabled' => true,
            'inherited' => true,
            'count' => 5,
        ],
        'remote_work' => 'inherit',
        'locations' => [
            [
                'id' => createDefaultLocation()->id,
                'type' => 'permanent',
            ],
        ],
    ];
}

it('return delegations of a manager with all request delegated', function () {
    $department = createDefaultDepartment();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->for($department)
        ->create();

    $manager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);

    Delegation::create([
        'type' => DelegationType::EarlyLate,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::LeaveRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::PermissionRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::RegularizationRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::RemoteWorkRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->getJson("api/v1/frontend/employees/$manager->id")
        ->assertOk()
        ->assertJsonPath('data.delegations.remote_work_request_delegated.id', $employee->id)
        ->assertJsonPath('data.delegations.leave_request_delegated.id', $employee->id)
        ->assertJsonPath('data.delegations.regularization_request_delegated.id', $employee->id)
        ->assertJsonPath('data.delegations.permission_request_delegated.id', $employee->id)
        ->assertJsonPath(
            'data.delegations.periodical_early_late_report_delegated.id',
            $employee->id
        );
});

test('self delegation is not allowed', function () {
    # prepare employees
    $department = createDefaultDepartment();

    createDefaultEmployee(['department_id' => $department->id]);

    $manager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);

    # act
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$manager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => $manager->id,
            'leave_request_delegated_id' => $manager->id,
            'regularization_request_delegated_id' => $manager->id,
            'permission_request_delegated_id' => $manager->id,
            'periodical_early_late_report_delegated_id' => $manager->id,
        ])
        ->assertStatus(422);

    assertDatabaseEmpty('delegations');
});

test('no employee can be delegated the same request by multiple managers', function () {
    # prepare employees
    $department = createDefaultDepartment();
    $anotherDepartment = createDefaultDepartment();

    $employee = createDefaultEmployee();

    createDefaultEmployee(['department_id' => $department->id]);

    createDefaultEmployee(['department_id' => $anotherDepartment->id]);

    $manager = createDefaultEmployee();
    $anotherManager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);
    $anotherDepartment->update(['manager_id' => $anotherManager->id]);

    # prepare delegation
    Delegation::create([
        'type' => DelegationType::RemoteWorkRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);

    # act
    /** @see \App\Http\Controllers\Frontend\EmployeeController::update */
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$manager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => $employee->id,
            'leave_request_delegated_id' => null,
            'regularization_request_delegated_id' => null,
            'permission_request_delegated_id' => null,
            'periodical_early_late_report_delegated_id' => null,
        ])
        ->assertOk();

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$anotherManager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => $employee->id,
            'leave_request_delegated_id' => null,
            'regularization_request_delegated_id' => null,
            'permission_request_delegated_id' => null,
            'periodical_early_late_report_delegated_id' => null,
        ])
        ->assertStatus(422);

    # assert
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RemoteWorkRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegatee_id' => $anotherManager->id,
        'delegated_id' => $employee->id,
    ]);
    assertDatabaseMissing('delegations', [
        'delegated_id' => $employee->id,
        'type' => DelegationType::LeaveRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegated_id' => $employee->id,
        'type' => DelegationType::RegularizationRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegated_id' => $employee->id,
        'type' => DelegationType::PermissionRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegated_id' => $employee->id,
        'type' => DelegationType::EarlyLate,
    ]);
});

test('remove delegation of all manager requests successfully', function () {
    # prepare employees
    $department = createDefaultDepartment();

    $anotherDepartment = createDefaultDepartment();

    createDefaultEmployee(['department_id' => $department->id]);

    createDefaultEmployee(['department_id' => $anotherDepartment->id]);

    $employee = createDefaultEmployee();

    $anotherEmployee = createDefaultEmployee();

    $manager = createDefaultEmployee();

    $antherManager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);
    $anotherDepartment->update(['manager_id' => $antherManager->id]);

    # prepare delegations
    Delegation::create([
        'type' => DelegationType::EarlyLate,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::LeaveRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::PermissionRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::RegularizationRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::RemoteWorkRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);

    Delegation::create([
        'type' => DelegationType::EarlyLate,
        'delegatee_id' => $antherManager->id,
        'delegated_id' => $anotherEmployee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::LeaveRequest,
        'delegatee_id' => $antherManager->id,
        'delegated_id' => $anotherEmployee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::PermissionRequest,
        'delegatee_id' => $antherManager->id,
        'delegated_id' => $anotherEmployee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::RegularizationRequest,
        'delegatee_id' => $antherManager->id,
        'delegated_id' => $anotherEmployee->id,
    ]);
    Delegation::create([
        'type' => DelegationType::RemoteWorkRequest,
        'delegatee_id' => $antherManager->id,
        'delegated_id' => $anotherEmployee->id,
    ]);

    # act
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$manager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => null,
            'leave_request_delegated_id' => null,
            'regularization_request_delegated_id' => null,
            'permission_request_delegated_id' => null,
            'periodical_early_late_report_delegated_id' => null,
        ])
        ->assertOk();

    # assert
    assertDatabaseHas('delegations', [
        'delegatee_id' => $antherManager->id,
        'delegated_id' => $anotherEmployee->id,
    ]);

    assertDatabaseMissing('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
    ]);
});

test('delegate all requests of a manager to a single employee successfully', function () {
    # prepare employees
    $department = createDefaultDepartment();

    createDefaultEmployee(['department_id' => $department->id]);
    $employee = createDefaultEmployee();

    $manager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);

    # act
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$manager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => $employee->id,
            'leave_request_delegated_id' => $employee->id,
            'regularization_request_delegated_id' => $employee->id,
            'permission_request_delegated_id' => $employee->id,
            'periodical_early_late_report_delegated_id' => $employee->id,
        ])
        ->assertOk();

    # assert
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RemoteWorkRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::LeaveRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RegularizationRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::PermissionRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::EarlyLate,
    ]);
});

test('delegate some manager requests only successfully', function () {
    # prepare employees
    $department = createDefaultDepartment();

    createDefaultEmployee(['department_id' => $department->id]);

    $employee = createDefaultEmployee();

    $manager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);

    # act
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$manager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => null,
            'leave_request_delegated_id' => $employee->id,
            'regularization_request_delegated_id' => null,
            'permission_request_delegated_id' => $employee->id,
            'periodical_early_late_report_delegated_id' => null,
        ])
        ->assertOk();

    # assert
    assertDatabaseMissing('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RemoteWorkRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::LeaveRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RegularizationRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::PermissionRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::EarlyLate,
    ]);
});

test('delegate a manager requests to multiple employees successfully', function () {
    # prepare employees
    $department = createDefaultDepartment();

    createDefaultEmployee(['department_id' => $department->id]);

    $employee = createDefaultEmployee();

    $anotherEmployee = createDefaultEmployee();

    $manager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);

    # act
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$manager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => $employee->id,
            'leave_request_delegated_id' => $anotherEmployee->id,
            'regularization_request_delegated_id' => $employee->id,
            'permission_request_delegated_id' => $anotherEmployee->id,
            'periodical_early_late_report_delegated_id' => $employee->id,
        ])
        ->assertOk();

    # assert
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RemoteWorkRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $anotherEmployee->id,
        'type' => DelegationType::LeaveRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RegularizationRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $anotherEmployee->id,
        'type' => DelegationType::PermissionRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::EarlyLate,
    ]);
});

test('employee can be delegated different requests by multiple managers successfully', function () {
    # prepare employees
    $department = createDefaultDepartment();
    $anotherDepartment = createDefaultDepartment();

    createDefaultEmployee(['department_id' => $department->id]);

    createDefaultEmployee(['department_id' => $anotherDepartment->id]);

    $lineManager = createDefaultEmployee();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $lineManager->id]);

    $manager = createDefaultEmployee();

    $anotherManager = createDefaultEmployee();

    $department->update(['manager_id' => $manager->id]);
    $anotherDepartment->update(['manager_id' => $anotherManager->id]);

    # act
    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$manager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => $employee->id,
            'leave_request_delegated_id' => null,
            'regularization_request_delegated_id' => null,
            'permission_request_delegated_id' => null,
            'periodical_early_late_report_delegated_id' => null,
        ])
        ->assertOk();

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$anotherManager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => null,
            'leave_request_delegated_id' => $employee->id,
            'regularization_request_delegated_id' => null,
            'permission_request_delegated_id' => null,
            'periodical_early_late_report_delegated_id' => null,
        ])
        ->assertOk();

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/$lineManager->id", [
            ...defaultRequestData(),
            'remote_work_request_delegated_id' => null,
            'leave_request_delegated_id' => null,
            'regularization_request_delegated_id' => $employee->id,
            'permission_request_delegated_id' => null,
            'periodical_early_late_report_delegated_id' => null,
        ])
        ->assertOk();

    # assert
    assertDatabaseHas('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RemoteWorkRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegatee_id' => $anotherManager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RemoteWorkRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $anotherManager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::LeaveRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::LeaveRequest,
    ]);
    assertDatabaseHas('delegations', [
        'delegatee_id' => $lineManager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RegularizationRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegatee_id' => $manager->id,
        'delegated_id' => $employee->id,
        'type' => DelegationType::RegularizationRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegated_id' => $employee->id,
        'type' => DelegationType::PermissionRequest,
    ]);
    assertDatabaseMissing('delegations', [
        'delegated_id' => $employee->id,
        'type' => DelegationType::EarlyLate,
    ]);
});
