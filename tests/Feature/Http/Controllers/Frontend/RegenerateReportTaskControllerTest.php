<?php

use App\Enums\ReportTaskStatus;
use App\Models\Attendance;
use App\Models\ReportTask;
use App\Workflows\GenerateEarlyLateReportWorkflow;
use Carbon\CarbonPeriod;
use <PERSON><PERSON><PERSON><PERSON>\Venture\Facades\Workflow;

it('updates report task status and dispatches job if employees exist', function () {
    Workflow::fake();

    $period = CarbonPeriod::create(now()->subDays(7), now());

    Attendance::factory()
        ->for($this->user)
        ->for($this->tenant)
        ->createFromPeriod($period);

    $reportTask = ReportTask::factory()
        ->earlyLate()
        ->withData([
            'start_date' => $period->getStartDate()->format('Y-m-d'),
            'end_date' => $period->getEndDate()->format('Y-m-d'),
        ])
        ->for($this->tenant)
        ->create([
            'status' => ReportTaskStatus::Success,
            'created_by_id' => $this->user->id,
        ]);

    /** @see \App\Http\Controllers\Frontend\Report\ReportTask\RegenerateReportTaskController */
    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->getJson("api/v1/frontend/report-tasks/$reportTask->id/regenerate")
        ->assertOk();

    $reportTask->refresh();

    expect($reportTask->status)->toBe(ReportTaskStatus::Pending);

    Workflow::assertStarted(GenerateEarlyLateReportWorkflow::class);

    $response->assertJson([
        'data' => [
            'id' => $reportTask->id,
            'status' => ReportTaskStatus::Pending->value,
        ],
    ]);
});
