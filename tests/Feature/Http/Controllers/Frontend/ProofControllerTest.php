<?php

use App\Enums\ProofStatus;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Proof;
use App\Models\Team;
use Carbon\CarbonImmutable;

test('list proofs - no filters', function () {
    $tenant = Team::factory()->create();

    Proof::factory()->for($tenant)->createMany(3);

    $employee = Employee::factory()->for($tenant)->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/proofs')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list proofs - filter by from to', function () {
    $tenant = Team::factory()->create();

    $startDate = CarbonImmutable::now();

    $endDate = $startDate->addDays(3);

    $includedProofs = Proof::factory()
        ->for($tenant)
        ->create(['created_at' => $startDate]);

    $excludedProofs = Proof::factory()
        ->for($tenant)
        ->create(['created_at' => $endDate->addDays(2)]);

    $employee = Employee::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/proofs?' .
                http_build_query([
                    'filter[from]' => $startDate->format('Y-m-d'),
                    'filter[to]' => $endDate->format('Y-m-d'),
                ])
        )
        ->assertOk();

    $responseProofs = $response->json('data.data');

    expect($responseProofs)->toHaveCount(1);

    expect(collect($responseProofs)->pluck('id'))->toContain($includedProofs->id);

    expect(collect($responseProofs)->pluck('id'))
        ->not()
        ->toContain($excludedProofs->id);
});

test('list proofs - filter by status', function () {
    $tenant = Team::factory()->create();

    $includedProofs = Proof::factory()
        ->for($tenant)
        ->create(['status' => ProofStatus::Sent]);

    $excludedProofs = Proof::factory()
        ->for($tenant)
        ->create(['status' => ProofStatus::Missed]);

    $employee = Employee::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/proofs?' .
                http_build_query([
                    'filter[status]' => ProofStatus::Sent->value,
                ])
        )
        ->assertOk();

    $responseProofs = $response->json('data.data');

    expect($responseProofs)->toHaveCount(1);

    expect(collect($responseProofs)->pluck('id'))->toContain($includedProofs->id);

    expect(collect($responseProofs)->pluck('id'))
        ->not()
        ->toContain($excludedProofs->id);
});

test('list proofs - filter by employees', function () {
    $tenant = Team::factory()->create();

    $employee = Employee::factory()->for($tenant)->create();

    $includedProofs = Proof::factory()->for($tenant)->for($employee)->create();

    $excludedProofs = Proof::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/proofs?' .
                http_build_query([
                    'filter[employees]' => [$employee->id],
                ])
        )
        ->assertOk();

    $responseProofs = $response->json('data.data');

    expect($responseProofs)->toHaveCount(1);

    expect(collect($responseProofs)->pluck('id'))->toContain($includedProofs->id);

    expect(collect($responseProofs)->pluck('id'))
        ->not()
        ->toContain($excludedProofs->id);
});

test('list proofs - filter by departments', function () {
    $tenant = Team::factory()->create();

    $department = Department::factory()->for($tenant)->create();

    $employee = Employee::factory()->for($tenant)->for($department)->create();

    $includedProofs = Proof::factory()->for($tenant)->for($employee)->create();

    $excludedProofs = Proof::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/proofs?' .
                http_build_query([
                    'filter[departments]' => [$department->id],
                ])
        )
        ->assertOk();

    $responseProofs = $response->json('data.data');

    expect($responseProofs)->toHaveCount(1);

    expect(collect($responseProofs)->pluck('id'))->toContain($includedProofs->id);

    expect(collect($responseProofs)->pluck('id'))
        ->not()
        ->toContain($excludedProofs->id);
});
