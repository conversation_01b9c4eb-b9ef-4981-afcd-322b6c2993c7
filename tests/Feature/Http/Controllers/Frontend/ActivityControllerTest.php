<?php

use App\Models\Activity;
use App\Models\Employee;
use App\Models\Team;
use Carbon\CarbonImmutable;

test('list activities - no filters', function () {
    $tenant = Team::factory()->create();

    Activity::factory()->for($tenant)->createMany(3);

    $employee = Employee::factory()->for($tenant)->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/activities')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list activities - filter by from to', function () {
    $tenant = Team::factory()->create();

    $startDate = CarbonImmutable::now();

    $endDate = $startDate->addDays(3);

    $includedActivities = Activity::factory()
        ->for($tenant)
        ->create(['created_at' => $startDate]);

    $excludedActivities = Activity::factory()
        ->for($tenant)
        ->create(['created_at' => $endDate->addDays(2)]);

    $employee = Employee::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/activities?' .
                http_build_query([
                    'filter[from]' => $startDate->format('Y-m-d'),
                    'filter[to]' => $endDate->format('Y-m-d'),
                ])
        )
        ->assertOk();

    $responseActivities = $response->json('data.data');

    expect($responseActivities)->toHaveCount(1);

    expect(collect($responseActivities)->pluck('id'))->toContain($includedActivities->id);

    expect(collect($responseActivities)->pluck('id'))
        ->not()
        ->toContain($excludedActivities->id);
});

test('list activities - filter by employees', function () {
    $tenant = Team::factory()->create();

    $employee = Employee::factory()->for($tenant)->create();

    $includedActivities = Activity::factory()->for($tenant)->for($employee)->create();

    $excludedActivities = Activity::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/activities?' .
                http_build_query([
                    'filter[employees]' => [$employee->id],
                ])
        )
        ->assertOk();

    $responseActivities = $response->json('data.data');

    expect($responseActivities)->toHaveCount(1);

    expect(collect($responseActivities)->pluck('id'))->toContain($includedActivities->id);

    expect(collect($responseActivities)->pluck('id'))
        ->not()
        ->toContain($excludedActivities->id);
});
