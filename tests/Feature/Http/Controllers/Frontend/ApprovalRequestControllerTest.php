<?php

use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Tag;
use App\Models\Team;

test('list approval requests - no filters', function () {
    $tenant = Team::factory()->create();

    $approvalRequests = ApprovalRequest::factory()->for($tenant)->createMany(3);

    $approvalRequests
        ->first()
        ->employee->tags()
        ->attach(Tag::factory()->for($tenant)->create());

    $employee = Employee::factory()->for($tenant)->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/approval-requests')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list approval-requests - filter by from to', function () {
    $includedApprovalRequests = ApprovalRequest::factory()
        ->for($this->tenant)
        ->create(['created_at' => '2021-01-02']);

    $excludedApprovalRequests = ApprovalRequest::factory()
        ->for($this->tenant)
        ->create(['created_at' => '2021-01-04']);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/approval-requests?' .
                http_build_query([
                    'filter[from]' => '2021-01-01',
                    'filter[to]' => '2021-01-03',
                ])
        )
        ->assertOk();

    $responseApprovalRequests = $response->json('data.data');

    expect($responseApprovalRequests)->toHaveCount(1);

    expect(collect($responseApprovalRequests)->pluck('id'))->toContain(
        $includedApprovalRequests->id
    );

    expect(collect($responseApprovalRequests)->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequests->id);
});

test('list approval requests - filter by status', function () {
    $tenant = Team::factory()->create();

    $includedApprovalRequests = ApprovalRequest::factory()
        ->for($tenant)
        ->create(['status' => ApprovalRequest::APPROVED]);

    $excludedApprovalRequests = ApprovalRequest::factory()
        ->for($tenant)
        ->create(['status' => ApprovalRequest::PENDING]);

    $employee = Employee::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/approval-requests?' .
                http_build_query([
                    'filter[status]' => [RequestStatus::Approved->value],
                ])
        )
        ->assertOk();

    $responseApprovalRequests = $response->json('data.data');

    expect($responseApprovalRequests)->toHaveCount(1);

    expect(collect($responseApprovalRequests)->pluck('id'))->toContain(
        $includedApprovalRequests->id
    );

    expect(collect($responseApprovalRequests)->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequests->id);
});

test('list approval requests - filter by employees', function () {
    $tenant = Team::factory()->create();

    $employee = Employee::factory()->for($tenant)->create();

    $includedApprovalRequests = ApprovalRequest::factory()->for($tenant)->for($employee)->create();

    $excludedApprovalRequests = ApprovalRequest::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/approval-requests?' .
                http_build_query([
                    'filter[employees]' => [$employee->id],
                ])
        )
        ->assertOk();

    $responseApprovalRequests = $response->json('data.data');

    expect($responseApprovalRequests)->toHaveCount(1);

    expect(collect($responseApprovalRequests)->pluck('id'))->toContain(
        $includedApprovalRequests->id
    );

    expect(collect($responseApprovalRequests)->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequests->id);
});

test('list approval requests - filter by departments', function () {
    $tenant = Team::factory()->create();

    $department = Department::factory()->for($tenant)->create();

    $employee = Employee::factory()->for($tenant)->for($department)->create();

    $includedApprovalRequests = ApprovalRequest::factory()
        ->for($tenant)
        ->for($employee)
        ->for($department)
        ->create();

    $excludedApprovalRequests = ApprovalRequest::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/approval-requests?' .
                http_build_query([
                    'filter[departments]' => [$department->id],
                ])
        )
        ->assertOk();

    $responseApprovalRequests = $response->collect('data.data');

    expect($responseApprovalRequests)->toHaveCount(1);

    expect($responseApprovalRequests->pluck('id'))->toContain($includedApprovalRequests->id);

    expect($responseApprovalRequests->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequests->id);
});

test('list approval requests - filter by type of request', function () {
    $tenant = Team::factory()->create();

    $employee = Employee::factory()->for($tenant)->create();

    $includedApprovalRequests = ApprovalRequest::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['type' => ApprovalRequest::PERMISSION]);

    $excludedApprovalRequests = ApprovalRequest::factory()
        ->for($tenant)
        ->create(['type' => ApprovalRequest::REMOTE_WORK]);

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/approval-requests?' .
                http_build_query([
                    'filter[type]' => [ApprovalRequest::PERMISSION],
                ])
        )
        ->assertOk();

    $responseApprovalRequests = $response->json('data.data');

    expect($responseApprovalRequests)
        ->toHaveCount(1)
        ->and(collect($responseApprovalRequests)->pluck('id'))
        ->toContain($includedApprovalRequests->id)
        ->and(collect($responseApprovalRequests)->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequests->id);
});

test('list approval requests - filter by department of employee', function () {
    $tenant = Team::factory()->create();

    $department = Department::factory()->for($tenant)->create();

    $employee = Employee::factory()->for($tenant)->for($department)->create();

    $includedApprovalRequests = ApprovalRequest::factory()
        ->for($tenant)
        ->for($employee)
        ->for($department)
        ->create();

    $excludedApprovalRequests = ApprovalRequest::factory()->for($tenant)->create();

    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson(
            'api/v1/frontend/approval-requests?' .
                http_build_query([
                    'filter[departments]' => [$department->id],
                ])
        )
        ->assertOk();

    $responseApprovalRequests = $response->json('data.data');

    expect($responseApprovalRequests)
        ->toHaveCount(1)
        ->and(collect($responseApprovalRequests)->pluck('id'))
        ->toContain($includedApprovalRequests->id)
        ->and(collect($responseApprovalRequests)->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequests->id);
});
