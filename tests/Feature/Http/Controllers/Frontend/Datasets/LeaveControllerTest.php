<?php

use App\Enums\RequestStatus;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Leave;
use App\Models\Tag;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;

dataset('filter by', [
    'filter by from to' => function () {
        $tenant = Team::factory()->create();

        $startDate = CarbonImmutable::now();

        $endDate = $startDate->addDays(3);

        $includedLeaves = Leave::factory()
            ->for($tenant)
            ->date(CarbonPeriod::create($startDate, $endDate))
            ->create();

        $excludedLeaves = Leave::factory()
            ->for($tenant)
            ->date(CarbonPeriod::create($endDate->addDays(1), $endDate->addDays(4)))
            ->create();

        $employee = Employee::factory()->for($tenant)->create();

        return [
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[from]' => $startDate->format('Y-m-d'),
                'filter[to]' => $endDate->format('Y-m-d'),
            ],
        ];
    },
    'filter by status' => function () {
        $tenant = Team::factory()->create();

        $includedLeaves = Leave::factory()
            ->for($tenant)
            ->create(['status' => RequestStatus::Approved]);

        $excludedLeaves = Leave::factory()
            ->for($tenant)
            ->create(['status' => RequestStatus::Pending]);

        $employee = Employee::factory()->for($tenant)->create();

        return [
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[status]' => [RequestStatus::Approved->value],
            ],
        ];
    },
    'filter by employees' => function () {
        $tenant = Team::factory()->create();

        $employee = Employee::factory()->for($tenant)->create();

        $includedLeaves = Leave::factory()->for($tenant)->for($employee)->create();

        $excludedLeaves = Leave::factory()->for($tenant)->create();

        return [
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[employees]' => [$employee->id],
            ],
        ];
    },
    'filter by department' => function () {
        $tenant = Team::factory()->create();

        $department = Department::factory()->for($tenant)->create();

        $employee = Employee::factory()->for($tenant)->for($department)->create();

        $includedLeaves = Leave::factory()
            ->for($tenant)
            ->for($employee)
            ->for($department)
            ->create();

        $excludedLeaves = Leave::factory()->for($tenant)->create();

        return [
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[departments]' => [$department->id],
            ],
        ];
    },
    'filter by tags' => function () {
        $tenant = Team::factory()->create();

        $tag = Tag::factory()->for($tenant)->create();

        $employee = Employee::factory()->for($tenant)->create();

        $employee->tags()->attach($tag);

        $includedLeaves = Leave::factory()->for($tenant)->for($employee)->create();

        $excludedLeaves = Leave::factory()->for($tenant)->create();

        return [
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[tags]' => [$tag->id],
            ],
        ];
    },
]);
