<?php

use App\Events\LocationAdded;
use App\Events\LocationUpdated;
use App\Models\Employee;
use App\Models\Location;
use function Pest\Laravel\assertDatabaseHas;

test('create location successfully', function () {
    Event::fake([LocationAdded::class]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $defaultLocation = Location::factory()
        ->for($this->tenant)
        ->create(['is_default' => true]);

    $data = [
        'name' => 'location A',
        'lat' => 24.55,
        'lng' => 46.33,
        'radius' => 30,
        'timezone' => fake()->timezone(),
        'is_default' => true,
        'automatic' => false,
        'check_out_radius' => 0,
    ];

    $this->jwtActingAsAttendanceHR($employee)
        ->postJson('api/v1/frontend/locations', $data)
        ->assertOk();

    assertDatabaseHas('locations', [...$data, 'team_id' => $this->tenant->id]);

    expect($defaultLocation->refresh()->is_default)->toBeFalse();

    Event::assertDispatched(
        LocationAdded::class,
        fn($event) => $event->location->is(Location::firstWhere('name', 'location A'))
    );
});

test('update location successfully', function () {
    Event::fake([LocationUpdated::class]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $defaultLocation = Location::factory()
        ->for($this->tenant)
        ->create(['is_default' => true]);

    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'name' => 'location A',
        'lat' => 24.55,
        'lng' => 46.33,
        'radius' => 30,
        'timezone' => fake()->timezone(),
        'is_default' => true,
        'automatic' => false,
        'check_out_radius' => 0,
    ];

    $this->jwtActingAsAttendanceHR($employee)
        ->putJson("api/v1/frontend/locations/$location->id", $data)
        ->assertOk();

    $data['id'] = $location->id;

    assertDatabaseHas('locations', $data);

    expect($defaultLocation->refresh()->is_default)->toBeFalse();

    Event::assertDispatched(
        LocationUpdated::class,
        fn($event) => $event->location->is(Location::firstWhere('name', 'location A'))
    );
});
