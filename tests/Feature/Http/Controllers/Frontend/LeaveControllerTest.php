<?php

use App\Models\Employee;
use App\Models\Leave;
use App\Models\Team;

test('list leaves - no filters', function () {
    $tenant = Team::factory()->create();

    Leave::factory()->for($tenant)->createMany(3);

    $employee = Employee::factory()->for($tenant)->create();

    $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/leaves')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list leaves with filter', function (
    Employee $employee,
    Leave $includedLeaves,
    Leave $excludedLeaves,
    array $query
) {
    $response = $this->jwtActingAsAttendanceHR($employee)
        ->getJson('api/v1/frontend/leaves?' . http_build_query($query))
        ->assertOk();

    $responseLeaves = $response->json('data.data');

    expect($responseLeaves)
        ->toHaveCount(1)
        ->and(collect($responseLeaves)->pluck('id'))
        ->toContain($includedLeaves->id)
        ->and(collect($responseLeaves)->pluck('id'))
        ->not()
        ->toContain($excludedLeaves->id);
})->with('filter by');
