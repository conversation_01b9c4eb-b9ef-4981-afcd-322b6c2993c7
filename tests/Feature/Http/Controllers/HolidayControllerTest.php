<?php

use App\Models\Holiday;
use App\Models\Team;
use Illuminate\Support\Carbon;
use function Pest\Laravel\assertDatabaseCount;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

test('all form request validation fields are required', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', [])
        ->assertInvalid([
            'name' => __('validation.required', ['attribute' => 'name']),
            'start_date' => __('validation.required', ['attribute' => 'start date']),
            'end_date' => __('validation.required', ['attribute' => 'end date']),
        ]);
});

test('all form request validation fields has correct type', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', [
            'name' => true,
            'start_date' => 'abc',
            'end_date' => 'abc',
        ])
        ->assertInvalid([
            'name' => __('validation.string', ['attribute' => 'name']),
            'start_date' => __('validation.date', ['attribute' => 'start date']),
            'end_date' => [
                __('validation.date', ['attribute' => 'end date']),
                __('validation.date', ['attribute' => 'end date']),
            ],
        ]);
});

test('end date form request validation field must be after or equal "start_date"', function (
    Carbon $start_date,
    Carbon $end_date
) {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', [
            'name' => fake()->name(),
            'start_date' => $start_date->toDateString(),
            'end_date' => $end_date->toDateString(),
        ])
        ->assertOk()
        ->assertValid();
})->with([
    'end date after start date' => [
        'start_date' => today(),
        'end_date' => today()->addDay(),
    ],
    'end date equal start date' => [
        'start_date' => today(),
        'end_date' => today(),
    ],
]);

test('end date form request validation field can not be before "start_date"', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', [
            'name' => fake()->name(),
            'start_date' => today()->toDateString(),
            'end_date' => today()->subDay()->toDateString(),
        ])
        ->assertInvalid([
            'end_date' => __('validation.after_or_equal', [
                'attribute' => 'end date',
                'date' => 'start date',
            ]),
        ]);
});

test('all form request validation fields passes with valid data', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', [
            'name' => fake()->name(),
            'start_date' => now()->toDateString(),
            'end_date' => now()->addDay()->toDateString(),
        ])
        ->assertOk();
});

test('store holiday in the current tenant', function () {
    $data = [
        'name' => fake()->name(),
        'start_date' => now()->toDateString(),
        'end_date' => now()->addDay()->toDateString(),
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', $data)
        ->assertOk();

    $data['team_id'] = $this->tenant->id;

    assertDatabaseHas('holidays', $data);
    assertDatabaseCount('holidays', 1);
});

test(
    "doesn't store team_id passed by user when storing holiday in the current tenant",
    function () {
        $data = [
            'team_id' => $this->tenant->id + 1,
            'name' => fake()->name(),
            'start_date' => now()->toDateString(),
            'end_date' => now()->addDay()->toDateString(),
        ];

        $this->jwtActingAsAttendanceHR($this->user)
            ->post('api/v1/frontend/holidays', $data)
            ->assertOk();

        $data['team_id'] = $this->tenant->id;

        assertDatabaseHas('holidays', $data);
        assertDatabaseCount('holidays', 1);
    }
);

test('store holiday only once', function () {
    $data = [
        'name' => fake()->name(),
        'start_date' => now()->toDateString(),
        'end_date' => now()->addDay()->toDateString(),
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', $data)
        ->assertOk();

    assertDatabaseCount('holidays', 1);
});

test('store holiday has no unique field', function () {
    $data = [
        'name' => fake()->name(),
        'start_date' => now()->toDateString(),
        'end_date' => now()->addDay()->toDateString(),
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', $data)
        ->assertOk();

    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', $data)
        ->assertOk();

    assertDatabaseCount('holidays', 2);
    assertDatabaseHas('holidays', $data);
});

test('start date can be in a past date', function () {
    $data = [
        'name' => fake()->name(),
        'start_date' => now()->subDay()->toDateString(),
        'end_date' => now()->toDateString(),
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/holidays', $data)
        ->assertOk();

    assertDatabaseHas('holidays', $data);
});

test('all form request validation fields of update are required', function () {
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", [])
        ->assertInvalid([
            'name' => __('validation.required', ['attribute' => 'name']),
            'start_date' => __('validation.required', ['attribute' => 'start date']),
            'end_date' => __('validation.required', ['attribute' => 'end date']),
        ]);
});

test('all form request validation fields of update has correct type', function () {
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", [
            'name' => true,
            'start_date' => 'abc',
            'end_date' => 'abc',
        ])
        ->assertInvalid([
            'name' => __('validation.string', ['attribute' => 'name']),
            'start_date' => __('validation.date', ['attribute' => 'start date']),
            'end_date' => [
                __('validation.date', ['attribute' => 'end date']),
                __('validation.date', ['attribute' => 'end date']),
            ],
        ]);
});

test(
    'end date form request validation field of update must be after or equal "start_date"',
    function (Carbon $start_date, Carbon $end_date) {
        $holiday = Holiday::factory()
            ->for($this->tenant)
            ->create([
                'start_date' => now()->addDay()->toDateString(),
                'end_date' => now()->addDays(2)->toDateString(),
            ]);

        $this->jwtActingAsAttendanceHR($this->user)
            ->put("api/v1/frontend/holidays/$holiday->id", [
                'name' => fake()->name(),
                'start_date' => $start_date->toDateString(),
                'end_date' => $end_date->toDateString(),
            ])
            ->assertOk()
            ->assertValid();
    }
)->with([
    'end date after start date' => [
        'start_date' => today(),
        'end_date' => today()->addDay(),
    ],
    'end date equal start date' => [
        'start_date' => today(),
        'end_date' => today(),
    ],
]);

test('end date update form request validation field can not be before "start_date"', function () {
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", [
            'name' => fake()->name(),
            'start_date' => today()->toDateString(),
            'end_date' => today()->subDay()->toDateString(),
        ])
        ->assertInvalid([
            'end_date' => __('validation.after_or_equal', [
                'attribute' => 'end date',
                'date' => 'start date',
            ]),
        ]);
});

test('all form request validation fields of update passes with valid data', function () {
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", [
            'name' => fake()->name(),
            'start_date' => now()->toDateString(),
            'end_date' => now()->addDay()->toDateString(),
        ])
        ->assertOk();
});

test('start date form request validation field of update can be in the past', function () {
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    $data = [
        'name' => fake()->name(),
        'start_date' => now()->subDay()->toDateString(),
        'end_date' => now()->toDateString(),
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", $data)
        ->assertOk();
});

test('form request validation field of update has no unique field', function () {
    $data = [
        'name' => fake()->name(),
        'start_date' => now()->addDay()->toDateString(),
        'end_date' => now()->addDays(2)->toDateString(),
    ];

    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDays(2)->toDateString(),
            'end_date' => now()->addDays(3)->toDateString(),
        ]);

    Holiday::factory()
        ->for($this->tenant)
        ->create($data);

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", $data)
        ->assertOk();
});

test(
    'update holiday when id is found in current tenant and start date in the future only once',
    function () {
        // prepare
        $holiday = Holiday::factory()
            ->for($this->tenant)
            ->create([
                'start_date' => now()->addDay()->toDateString(),
                'end_date' => now()->addDays(2)->toDateString(),
            ]);

        $data = [
            'name' => fake()->name(),
            'start_date' => now()->toDateString(),
            'end_date' => now()->addDay()->toDateString(),
        ];

        // test
        $this->jwtActingAsAttendanceHR($this->user)
            ->put("api/v1/frontend/holidays/$holiday->id", $data)
            ->assertOk();

        $data['team_id'] = $this->tenant->id;

        assertDatabaseHas('holidays', $data);
        assertDatabaseCount('holidays', 1);
    }
);

test(
    'update holiday return response status 422 when id is found in current tenant and start date is today',
    function () {
        // prepare
        $holiday = Holiday::factory()
            ->for($this->tenant)
            ->create([
                'start_date' => now()->toDateString(),
                'end_date' => now()->addDay()->toDateString(),
            ]);

        $updatedData = [
            'name' => fake()->name(),
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ];

        // test
        $this->jwtActingAsAttendanceHR($this->user)
            ->put("api/v1/frontend/holidays/$holiday->id", $updatedData)
            ->assertStatus(422);

        assertDatabaseHas(
            'holidays',
            $holiday->makeHidden(['created_at', 'updated_at'])->toArray()
        );
        assertDatabaseMissing('holidays', $updatedData);
    }
);

test(
    'update holiday return response status 422 when id is found in current tenant and start date is in the past',
    function () {
        // prepare
        $holiday = Holiday::factory()
            ->for($this->tenant)
            ->create([
                'start_date' => now()->subDay()->toDateString(),
                'end_date' => now()->toDateString(),
            ]);

        $updatedData = [
            'name' => fake()->name(),
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ];

        // test
        $this->jwtActingAsAttendanceHR($this->user)
            ->put("api/v1/frontend/holidays/$holiday->id", $updatedData)
            ->assertStatus(422);

        assertDatabaseHas(
            'holidays',
            $holiday->makeHidden(['created_at', 'updated_at'])->toArray()
        );
        assertDatabaseMissing('holidays', $updatedData);
    }
);

test('update holiday return not found response when id is found in another tenant', function () {
    // prepare
    $holiday = Holiday::factory()->create();

    $updatedData = [
        'name' => fake()->name(),
        'start_date' => now()->addDay()->toDateString(),
        'end_date' => now()->addDays(2)->toDateString(),
    ];

    // test
    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", $updatedData)
        ->assertNotFound();

    assertDatabaseHas('holidays', $holiday->makeHidden(['created_at', 'updated_at'])->toArray());
    assertDatabaseMissing('holidays', $updatedData);
});

test('update holiday return not found response when id is not found in any tenant', function () {
    Holiday::truncate();

    // test
    $this->jwtActingAsAttendanceHR($this->user)
        ->put('api/v1/frontend/holidays/1', [
            'name' => fake()->name(),
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ])
        ->assertNotFound();
});

test('update holiday return success response', function () {
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/holidays/$holiday->id", [
            'name' => fake()->name(),
            'start_date' => now()->subDay()->toDateString(),
            'end_date' => now()->toDateString(),
        ])
        ->assertOk();
});

// delete holiday
test('delete holiday when id is found in current tenant and start date in the future', function () {
    // prepare
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    // test
    $this->jwtActingAsAttendanceHR($this->user)
        ->delete("api/v1/frontend/holidays/$holiday->id")
        ->assertOk();

    assertDatabaseMissing('holidays', $holiday->toArray());
});

test(
    'delete holiday return error message when id is found in current tenant and start date is today',
    function () {
        // prepare
        $data = [
            'start_date' => now()->toDateString(),
            'end_date' => now()->addDay()->toDateString(),
        ];
        $holiday = Holiday::factory()
            ->for($this->tenant)
            ->create($data);

        $data['team_id'] = $this->tenant->id;
        $data['id'] = $holiday->id;
        // test
        $this->jwtActingAsAttendanceHR($this->user)
            ->delete("api/v1/frontend/holidays/$holiday->id")
            ->assertStatus(422);

        assertDatabaseHas('holidays', $data);
    }
);

test(
    'delete holiday return error message when id is found in current tenant and start date is in the past',
    function () {
        // prepare
        $data = [
            'start_date' => now()->subDay()->toDateString(),
            'end_date' => now()->toDateString(),
        ];
        $holiday = Holiday::factory()
            ->for($this->tenant)
            ->create($data);

        $data['team_id'] = $this->tenant->id;
        $data['id'] = $holiday->id;

        // test
        $this->jwtActingAsAttendanceHR($this->user)
            ->delete("api/v1/frontend/holidays/$holiday->id")
            ->assertStatus(422);

        assertDatabaseHas('holidays', $data);
    }
);

test('delete holiday return not found response when id is found in another tenant', function () {
    // prepare
    $anotherTeam = Team::factory()->create();
    $holiday = Holiday::factory()->for($anotherTeam)->create();

    // test
    $this->jwtActingAsAttendanceHR($this->user)
        ->delete("api/v1/frontend/holidays/$holiday->id")
        ->assertNotFound();

    assertDatabaseHas('holidays', $holiday->makeHidden(['created_at', 'updated_at'])->toArray());
});

test('delete holiday return not found response when id is not found in any tenant', function () {
    Holiday::truncate();

    // test
    $this->jwtActingAsAttendanceHR($this->user)
        ->delete('api/v1/frontend/holidays/1')
        ->assertNotFound();
});

test('delete holiday return status success', function () {
    $holiday = Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => now()->addDay()->toDateString(),
            'end_date' => now()->addDays(2)->toDateString(),
        ]);

    $this->jwtActingAsAttendanceHR($this->user)
        ->delete("api/v1/frontend/holidays/$holiday->id")
        ->assertOk();

    assertDatabaseMissing(
        'holidays',
        $holiday->makeHidden(['created_at', 'updated_at'])->toArray()
    );
});
