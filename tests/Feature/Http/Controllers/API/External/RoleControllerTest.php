<?php

use App\Enums\SoftwareCode;
use App\Models\Software;

it('return roles by tenant subscription', function () {
    // subscription only to Start software
    $this->createStartSubscription($this->tenant);

    $startRoles = Software::firstWhere('code', SoftwareCode::Start)->roles;

    $response = $this->actingAsTenant()->getJson('api/v1/external/roles')->assertOk();

    expect($response->json('data'))->toBe(
        $startRoles
            ->map(
                fn($role) => [
                    'id' => $role->uuid,
                    'name' => $role->name,
                    'readable_name' => $role->display_name,
                    'description' => $role->description,
                    'application' => $role->software->code->translated(),
                ]
            )
            ->toArray()
    );
});
