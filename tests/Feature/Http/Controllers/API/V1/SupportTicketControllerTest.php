<?php

use App\Enums\Folder;
use App\Enums\SupportTicketSource;
use function Pest\Laravel\assertDatabaseHas;

test('works', function (array $payload) {
    Storage::fake();

    $employee = createDefaultEmployee();

    $hasAttachment = isset($payload['attachment']);

    if ($hasAttachment) {
        Storage::put(
            Folder::SUPPORT_TICKET_ATTACHMENT->tempPath($payload['attachment']),
            'content'
        );
    }

    /** @see \App\Http\Controllers\SupportTicketController */
    $this->actingAsStartAdmin($employee)->postJson('api/v1/support-tickets', $payload)->assertOk();

    $supportTicket = $employee->supportTickets()->first();

    if ($hasAttachment) {
        Storage::assertExists(Folder::SUPPORT_TICKET_ATTACHMENT->path($supportTicket->attachment));

        Storage::assertMissing(Folder::SUPPORT_TICKET_ATTACHMENT->tempPath($payload['attachment']));
    }

    assertDatabaseHas('support_tickets', [
        'title' => $payload['title'],
        'description' => $payload['description'],
        'attachment' => $payload['attachment'],
        'source' => $payload['source'],
        'tenant_id' => $employee->tenant_id,
        'employee_id' => $employee->id,
    ]);
})->with([
    'with attachment' => [
        [
            'title' => fake()->title(),
            'description' => fake()->text(),
            'attachment' => fake()->url(),
            'source' => fake()->randomElement(SupportTicketSource::class),
        ],
    ],
    'without attachment' => [
        [
            'title' => fake()->title(),
            'description' => fake()->text(),
            'attachment' => null,
            'source' => fake()->randomElement(SupportTicketSource::class),
        ],
    ],
]);
