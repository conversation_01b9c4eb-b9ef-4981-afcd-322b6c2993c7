<?php

use App\Enums\Folder;

it('update tenant settings - general info', function () {
    $payload = ['name' => 'new tenant', 'color' => '#ffff66'];

    /** @see \App\Http\Controllers\API\V1\UpdateTenantSettingsController */
    $response = $this->actingAsStartAdmin()
        ->putJson('api/v1/tenant/settings/update', $payload)
        ->assertOk()
        ->assertJson(['message' => __('settings updated successfully')]);

    expect($response->json('data'))
        ->name->toBe($payload['name'])
        ->color->toBe($payload['color']);
});

it('update tenant settings - upload logos when has no logos', function () {
    Storage::fake();

    $payload = [
        'colored_logo' => 'colored_logo.png',
        'white_logo' => 'white_logo.png',
    ];

    Storage::put(Folder::LOGOS->tempPath($payload['colored_logo']), 'content');
    Storage::put(Folder::LOGOS->tempPath($payload['white_logo']), 'content');

    /** @see \App\Http\Controllers\API\V1\UpdateTenantSettingsController */
    $this->actingAsStartAdmin()
        ->putJson('api/v1/tenant/settings/update', $payload)
        ->assertOk()
        ->assertJson(['message' => __('settings updated successfully')]);

    expect($this->tenant->refresh())
        ->colored_logo->toBe($payload['colored_logo'])
        ->white_logo->toBe($payload['white_logo']);

    // New files should be uploaded
    Storage::assertExists(Folder::LOGOS->path($this->tenant->colored_logo));

    Storage::assertExists(Folder::LOGOS->path($this->tenant->white_logo));

    // Temp files should be deleted
    Storage::assertMissing(Folder::LOGOS->tempPath($payload['colored_logo']));

    Storage::assertMissing(Folder::LOGOS->tempPath($payload['white_logo']));
});

it('update tenant settings - leave logos as is if not passed in request', function () {
    Storage::fake();

    $this->tenant->update([
        'colored_logo' => 'colored_logo.png',
        'white_logo' => 'white_logo.png',
    ]);

    Storage::put(Folder::LOGOS->path($this->tenant->colored_logo), 'content');
    Storage::put(Folder::LOGOS->path($this->tenant->white_logo), 'content');

    $payload = ['name' => 'new tenant', 'color' => '#ffff66'];

    /** @see \App\Http\Controllers\API\V1\UpdateTenantSettingsController */
    $this->actingAsStartAdmin()
        ->putJson('api/v1/tenant/settings/update', $payload)
        ->assertOk()
        ->assertJson(['message' => __('settings updated successfully')]);

    expect($this->tenant->refresh())
        ->colored_logo->toBe('colored_logo.png')
        ->white_logo->toBe('white_logo.png');

    // Files should not be deleted
    Storage::assertExists(Folder::LOGOS->path($this->tenant->colored_logo));

    Storage::assertExists(Folder::LOGOS->path($this->tenant->white_logo));
});

it('update tenant settings - replace existing logos when uploading new ones', function () {
    Storage::fake();

    $this->tenant->update([
        'colored_logo' => 'old_colored_logo.png',
        'white_logo' => 'old_white_logo.png',
    ]);

    Storage::put(Folder::LOGOS->path($this->tenant->colored_logo), 'content');
    Storage::put(Folder::LOGOS->path($this->tenant->white_logo), 'content');

    $payload = [
        'colored_logo' => 'new_colored_logo.png',
        'white_logo' => 'new_white_logo.png',
    ];

    Storage::put(Folder::LOGOS->tempPath($payload['colored_logo']), 'content');
    Storage::put(Folder::LOGOS->tempPath($payload['white_logo']), 'content');

    /** @see \App\Http\Controllers\API\V1\UpdateTenantSettingsController */
    $this->actingAsStartAdmin()
        ->putJson('api/v1/tenant/settings/update', $payload)
        ->assertOk()
        ->assertJson(['message' => __('settings updated successfully')]);

    expect($this->tenant->refresh())
        ->colored_logo->toBe($payload['colored_logo'])
        ->white_logo->toBe($payload['white_logo']);

    // New files should be uploaded
    Storage::assertExists(Folder::LOGOS->path($this->tenant->colored_logo));

    Storage::assertExists(Folder::LOGOS->path($this->tenant->white_logo));

    // Old files should be deleted
    Storage::assertMissing(Folder::LOGOS->path('old_colored_logo.png'));

    Storage::assertMissing(Folder::LOGOS->path('old_white_logo.png'));

    // Temp files should be deleted
    Storage::assertMissing(Folder::LOGOS->tempPath($payload['colored_logo']));

    Storage::assertMissing(Folder::LOGOS->tempPath($payload['white_logo']));
});

it(
    'update tenant settings - delete current logos without adding new once\'s when requested',
    function () {
        Storage::fake();

        $this->tenant->update([
            'colored_logo' => 'current_colored_logo.png',
            'white_logo' => 'current_white_logo.png',
        ]);

        Storage::put(Folder::LOGOS->path($this->tenant->colored_logo), 'content');
        Storage::put(Folder::LOGOS->path($this->tenant->white_logo), 'content');

        $payload = [
            'colored_logo' => null,
            'white_logo' => null,
        ];

        /** @see \App\Http\Controllers\API\V1\UpdateTenantSettingsController */
        $this->actingAsStartAdmin()
            ->putJson('api/v1/tenant/settings/update', $payload)
            ->assertOk()
            ->assertJson(['message' => __('settings updated successfully')]);

        expect($this->tenant->refresh())->colored_logo->toBeNull()->white_logo->toBeNull();

        // Current files should be deleted
        Storage::assertMissing(Folder::LOGOS->path('current_colored_logo.png'));

        Storage::assertMissing(Folder::LOGOS->path('current_white_logo.png'));
    }
);
