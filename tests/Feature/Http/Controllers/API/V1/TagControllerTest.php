<?php

use App\Models\Employee;
use App\Models\Tag;
use function Pest\Laravel\assertDatabaseMissing;

test('list of tags', function () {
    Tag::factory()
        ->for($this->tenant)
        ->count(3)
        ->create();

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->getJson('api/v1/tenant/tags')
        ->assertOk()
        ->assertJsonCount(3, 'data.data')
        ->assertJsonStructure([
            'data' => [
                'data' => [
                    '*' => ['id', 'name', 'color', 'employees_count'],
                ],
            ],
        ]);
});

test('list of tags - with search', function () {
    Tag::factory()
        ->for($this->tenant)
        ->count(3)
        ->create();

    Tag::factory()
        ->for($this->tenant)
        ->create(['name' => 'searched tag']);

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->getJson('api/v1/tenant/tags?filter[search]=searched')
        ->assertOk()
        ->assertJsonCount(1, 'data.data')
        ->assertJson([
            'data' => [
                'data' => [['name' => 'searched tag']],
            ],
        ]);
});

test('list of tags - with taggable count', function () {
    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $tag->employees()->attach($employee);

    /** @see \App\Http\Controllers\API\V1\TagController */
    $response = $this->actingAsStartAdmin()->getJson('api/v1/tenant/tags')->assertOk();

    expect($response->json('data.data.0.employees_count'))->toBe(1);
});

test('create a tag', function () {
    $payload = ['name' => 'new tag', 'color' => '#ffff66'];

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->postJson('api/v1/tenant/tags', $payload)
        ->assertOk()
        ->assertJson(['message' => __('Tag created successfully')])
        ->assertJsonFragment($payload);
});

test('create a tag - tag name is unique', function () {
    Tag::factory()
        ->for($this->tenant)
        ->create(['name' => 'new tag']);

    $payload = ['name' => 'new tag', 'color' => '#ffff66'];

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->postJson('api/v1/tenant/tags', $payload)
        ->assertJsonValidationErrors(['name']);
});

test('update a tag', function () {
    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();

    $payload = ['name' => 'updated tag', 'color' => '#ff0000'];

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/tags/$tag->id", $payload)
        ->assertOk()
        ->assertJson(['message' => __('Tag updated successfully')])
        ->assertJsonFragment($payload);
});

test('update a tag - tag name is unique', function () {
    Tag::factory()
        ->for($this->tenant)
        ->create(['name' => 'new tag']);

    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();

    $payload = ['name' => 'new tag', 'color' => '#ffff66'];

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/tags/$tag->id", $payload)
        ->assertJsonValidationErrors(['name']);
});

test('update a tag - tag name is unique ignore current tag', function () {
    $tag = Tag::factory()
        ->for($this->tenant)
        ->create(['name' => 'new tag', 'color' => '#ffff66']);

    $payload = ['name' => 'new tag', 'color' => '#ff0000'];

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/tags/$tag->id", $payload)
        ->assertOk()
        ->assertJson(['message' => __('Tag updated successfully')]);
});

test('delete a tag', function () {
    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $tag->employees()->attach($employee);

    /** @see \App\Http\Controllers\API\V1\TagController */
    $this->actingAsStartAdmin()
        ->deleteJson("api/v1/tenant/tags/$tag->id")
        ->assertOk()
        ->assertJson(['message' => __('Tag deleted successfully')]);

    assertDatabaseMissing('tags', ['id' => $tag->id]);

    expect($employee->refresh()->tags)->toBeEmpty();
});
