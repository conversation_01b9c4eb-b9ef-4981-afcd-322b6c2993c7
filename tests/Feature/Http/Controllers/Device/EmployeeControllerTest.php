<?php

use App\Models\Device;

test('return employee - valid identifier', function (array $payload) {
    $device = Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    $this->actingAs($device)
        ->getJson("api/v1/device/employees/{$payload['identifier']}")
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );
})->with('device check-in-out employee identifier');
