<?php

use App\Models\Device;
use Illuminate\Testing\Fluent\AssertableJson;
use function Pest\Laravel\assertDatabaseHas;

test('login with valid credentials - existing location', function () {
    $payload = [
        'username' => 'device-username',
        'secret_key' => 'device-secret',
    ];

    $device = Device::factory()->existingLocation()->createQuietly($payload);

    /** @see \App\Http\Controllers\Device\Auth\LoginController */
    $this->postJson('api/v1/device/auth/login', $payload)->assertOk()->assertJson(
        fn(AssertableJson $json) => $json
            ->where('data.device.id', $device->id)
            ->has('data.token')
            ->etc()
    );
});

test('login with valid credentials - device location - without sending lat lng', function () {
    $payload = [
        'username' => 'device-username',
        'secret_key' => 'device-secret',
    ];

    $device = Device::factory()->deviceLocation()->createQuietly($payload);

    /** @see \App\Http\Controllers\Device\Auth\LoginController */
    $this->postJson('api/v1/device/auth/login', $payload)->assertInvalid(['lat', 'lng']);
});

test('login with valid credentials - device location - with sending lat lng', function () {
    $payload = [
        'username' => 'device-username',
        'secret_key' => 'device-secret',
    ];

    $latLng = [
        'lat' => fake()->latitude(),
        'lng' => fake()->longitude(),
    ];

    $device = Device::factory()->deviceLocation()->createQuietly($payload);

    /** @see \App\Http\Controllers\Device\Auth\LoginController */
    $this->postJson('api/v1/device/auth/login', [...$payload, ...$latLng])
        ->assertOk()
        ->assertJson(
            fn(AssertableJson $json) => $json
                ->where('data.device.id', $device->id)
                ->has('data.token')
                ->etc()
        );

    assertDatabaseHas('devices', [
        'id' => $device->id,
        'lat' => $latLng['lat'],
        'lng' => $latLng['lng'],
    ]);
});

test('login with invalid username', function () {
    $payload = [
        'username' => 'device-username',
        'secret_key' => 'device-secret',
    ];

    $device = Device::factory()->createQuietly($payload);

    /** @see \App\Http\Controllers\Device\Auth\LoginController */
    $this->postJson('api/v1/device/auth/login', [
        'username' => 'invalid-username',
        'secret_key' => 'invalid-secret',
    ])->assertInvalid([
        'username' => __('This device does not exists'),
    ]);
});

test('login with invalid secret', function () {
    $payload = [
        'username' => 'device-username',
        'secret_key' => 'device-secret',
    ];

    $device = Device::factory()->createQuietly($payload);

    /** @see \App\Http\Controllers\Device\Auth\LoginController */
    $this->postJson('api/v1/device/auth/login', [
        'username' => 'device-username',
        'secret_key' => 'invalid-secret',
    ])->assertInvalid([
        'secret_key' => __('auth.password'),
    ]);
});
