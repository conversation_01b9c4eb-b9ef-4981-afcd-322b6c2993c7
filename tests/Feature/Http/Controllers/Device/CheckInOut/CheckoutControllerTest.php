<?php

use App\Events\EmployeeCheckedOut;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Device;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\travelTo;

test('checkout - not checked in yet', function (array $payload) {
    Event::fake([EmployeeCheckedOut::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    Attendance::factory()
        ->date(today())
        ->yet()
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckoutController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkout', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response
                ->assertJson(['status' => 'NOT_CHECKED_IN_YET'])
                ->assertStatus(422)
        );

    Event::assertNotDispatched(EmployeeCheckedOut::class);
})->with('device check-in-out employee identifier');

test('checkout - normal - existing location', function (array $payload) {
    Event::fake([EmployeeCheckedOut::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    Attendance::factory()
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckoutController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkout', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );

    if ($payload['identifier'] !== 'not-existing') {
        assertDatabaseHas('activities', [
            'employee_id' => $payload['employee']->id,
            'action' => Activity::CHECK_OUT,
            'device_id' => $device->id,
            ...$device->latlng(),
        ]);

        Event::assertDispatched(EmployeeCheckedOut::class);
    }
})->with('device check-in-out employee identifier');

test('checkout - normal - device location', function (array $payload) {
    Event::fake([EmployeeCheckedOut::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->deviceLocation()
        ->create();

    Attendance::factory()
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckoutController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkout', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );

    if ($payload['identifier'] !== 'not-existing') {
        assertDatabaseHas('activities', [
            'employee_id' => $payload['employee']->id,
            'action' => Activity::CHECK_OUT,
            'device_id' => $device->id,
            ...$device->latlng(),
        ]);

        Event::assertDispatched(EmployeeCheckedOut::class);
    }
})->with('device check-in-out employee identifier');

test('check-in - normal - attendance record is updated', function (array $payload) {
    $checkinDate = now();

    $checkoutDate = now()->addHours(8);

    travelTo($checkoutDate);

    $device = Device::factory()
        ->for($this->tenant)
        ->deviceLocation()
        ->create();

    Attendance::factory()
        ->date($checkinDate)
        ->present(checkinDate: $checkinDate, onDuty: true)
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckoutController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkout', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );

    if ($payload['identifier'] !== 'not-existing') {
        assertDatabaseHas('attendances', [
            'check_out' => $checkoutDate,
            'out_type' => Activity::CHECK_OUT,
            'status' => Attendance::PRESENT,
            'on_duty' => 0,
            'check_out_location_id' => $device->location_id,
            'check_out_device_id' => $device->id,
            'net_hours' => '08:00:00',
        ]);
    }
})->with('device check-in-out employee identifier');
