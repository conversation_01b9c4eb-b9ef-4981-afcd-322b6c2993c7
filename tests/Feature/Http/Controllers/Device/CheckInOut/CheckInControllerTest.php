<?php

use App\Events\EmployeeCheckedIn;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Device;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\travelTo;

test('check-in - when no active attendance record, create new one', function (array $payload) {
    Event::fake([EmployeeCheckedIn::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckInController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkin', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );

    if ($payload['identifier'] !== 'not-existing') {
        Event::assertDispatched(EmployeeCheckedIn::class);
    } else {
        Event::assertNotDispatched(EmployeeCheckedIn::class);
    }
})->with('device check-in-out employee identifier');

test('check-in - when shifts end, return no active attendance record', function (array $payload) {
    Event::fake([EmployeeCheckedIn::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    // travel to 20:01
    travelTo(now()->setHour(20)->addMinutes(1));

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckInController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkin', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response
                ->assertJson(['status' => 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD'])
                ->assertStatus(422)
        );

    Event::assertNotDispatched(EmployeeCheckedIn::class);
})->with('device check-in-out employee identifier');

test('check-in - already checkin', function (array $payload) {
    Event::fake([EmployeeCheckedIn::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    Attendance::factory()
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckInController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkin', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response
                ->assertJson(['status' => 'ALREADY_CHECKED_IN'])
                ->assertStatus(422)
        );

    Event::assertNotDispatched(EmployeeCheckedIn::class);
})->with('device check-in-out employee identifier');

test('check-in - normal - existing location', function (array $payload) {
    Event::fake([EmployeeCheckedIn::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    Attendance::factory()
        ->date(today())
        ->yet()
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckInController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkin', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );

    if ($payload['identifier'] !== 'not-existing') {
        assertDatabaseHas('activities', [
            'employee_id' => $payload['employee']->id,
            'action' => Activity::CHECK_IN,
            'device_id' => $device->id,
            'location_id' => $device->location_id,
            ...$device->latlng(),
        ]);
    }

    if ($payload['identifier'] !== 'not-existing') {
        Event::assertDispatched(EmployeeCheckedIn::class);
    } else {
        Event::assertNotDispatched(EmployeeCheckedIn::class);
    }
})->with('device check-in-out employee identifier');

test('check-in - normal - device location', function (array $payload) {
    Event::fake([EmployeeCheckedIn::class]);

    $device = Device::factory()
        ->for($this->tenant)
        ->deviceLocation()
        ->create();

    Attendance::factory()
        ->date(today())
        ->yet()
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckInController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkin', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );

    if ($payload['identifier'] !== 'not-existing') {
        assertDatabaseHas('activities', [
            'employee_id' => $payload['employee']->id,
            'action' => Activity::CHECK_IN,
            'device_id' => $device->id,
            'location_id' => $device->location_id,
            ...$device->latlng(),
        ]);
    }

    if ($payload['identifier'] !== 'not-existing') {
        Event::assertDispatched(EmployeeCheckedIn::class);
    } else {
        Event::assertNotDispatched(EmployeeCheckedIn::class);
    }
})->with('device check-in-out employee identifier');

test('check-in - normal - attendance record is updated', function (array $payload) {
    $device = Device::factory()
        ->for($this->tenant)
        ->deviceLocation()
        ->create();

    $attendance = Attendance::factory()
        ->date(today())
        ->yet()
        ->when($payload['employee'], fn($builder) => $builder->for($payload['employee']))
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Device\CheckInOut\CheckInController */
    $this->actingAs($device)
        ->postJson('api/v1/device/checkin', ['identifier' => $payload['identifier']])
        ->when(
            $payload['identifier'] === 'not-existing',
            fn($response) => $response->assertInvalid(['identifier']),
            fn($response) => $response->assertOk()
        );

    if ($payload['identifier'] !== 'not-existing') {
        $attendance->refresh();

        expect($attendance->check_in)->toBeBetween(now()->subSeconds(5), now()->addSeconds(5));
        expect($attendance->in_type)->toBe(Activity::CHECK_IN);
        expect($attendance->status)->toBe(Attendance::PRESENT);
        expect($attendance->on_duty)->toBeTrue();
        expect($attendance->check_in_location_id)->toBe($device->location_id);
        expect($attendance->check_in_device_id)->toBe($device->id);
    }
})->with('device check-in-out employee identifier');
