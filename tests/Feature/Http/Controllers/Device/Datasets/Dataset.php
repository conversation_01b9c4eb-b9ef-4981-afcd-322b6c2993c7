<?php

use App\Models\Employee;
use App\Models\Shift;
use Database\Factories\ShiftFactory;

dataset('device check-in-out employee identifier', [
    'number' => function () {
        $shift = Shift::factory()
            ->for(test()->tenant)
            ->create([
                'working_hours' => ShiftFactory::workingHoursDefaultTemplate(),
                'force_checkout' => '20:00', // can't checkin after 20:00
            ]);

        $employee = Employee::factory()
            ->for(test()->tenant)
            ->permanentShift($shift)
            ->create();

        return [
            'employee' => $employee,
            'identifier' => $employee->number,
        ];
    },
    'email' => function () {
        $shift = Shift::factory()
            ->for($this->tenant)
            ->create([
                'working_hours' => ShiftFactory::workingHoursDefaultTemplate(),
                'force_checkout' => '20:00', // can't checkin after 20:00
            ]);

        $employee = Employee::factory()
            ->for(test()->tenant)
            ->permanentShift($shift)
            ->create();

        return [
            'employee' => $employee,
            'identifier' => $employee->email,
        ];
    },
    'not-existing' => fn() => [
        'employee' => null,
        'identifier' => 'not-existing',
    ],
]);
