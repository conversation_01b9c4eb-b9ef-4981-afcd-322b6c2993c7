<?php

use Illuminate\Notifications\DatabaseNotification;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

it('require authorization', function () {
    $response = $this->putJson('/api/v2/mobile/notifications/mark-as-read');

    $response->assertUnauthorized();
});

it(
    'marks all notifications as read when there is no notifications and return http code 200 with no content',
    function () {
        $this->jwtActingAsMobile($this->user)
            ->putJson('/api/v2/mobile/notifications/mark-as-read')
            ->assertOk();
    }
);

it(
    'marks all notifications as read when there is notifications and return http code 200 with no content',
    function () {
        foreach (range(1, 6) as $ignored) {
            DatabaseNotification::create([
                'id' => uuid_create(),
                'type' => 'App\Notifications\ProofAttendanceRequested',
                'notifiable_type' => 'App\Models\Employee',
                'notifiable_id' => $this->user->id,
                'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
            ]);
        }

        $this->jwtActingAsMobile($this->user)
            ->put<PERSON><PERSON>('/api/v2/mobile/notifications/mark-as-read')
            ->assertOk()
            ->assertJson(['data' => []]);

        assertDatabaseMissing('notifications', ['read_at' => null]);
    }
);

it('marks all notifications of auth user only as read', function () {
    DatabaseNotification::create([
        'id' => uuid_create(),
        'type' => 'App\Notifications\ProofAttendanceRequested',
        'notifiable_type' => 'App\Models\Employee',
        'notifiable_id' => $this->user->id + 1,
        'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
    ]);

    foreach (range(1, 6) as $ignored) {
        DatabaseNotification::create([
            'id' => uuid_create(),
            'type' => 'App\Notifications\ProofAttendanceRequested',
            'notifiable_type' => 'App\Models\Employee',
            'notifiable_id' => $this->user->id,
            'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
        ]);
    }

    $this->jwtActingAsMobile($this->user)
        ->putJson('/api/v2/mobile/notifications/mark-as-read')
        ->assertOk()
        ->assertJson(['data' => []]);

    assertDatabaseHas('notifications', ['read_at' => null, 'notifiable_id' => $this->user->id + 1]);
});
