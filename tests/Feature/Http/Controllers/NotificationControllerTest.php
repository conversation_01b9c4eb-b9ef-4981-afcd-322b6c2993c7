<?php

use Illuminate\Notifications\DatabaseNotification;

use function Pest\Laravel\assertDatabaseHas;
use function PHPUnit\Framework\assertCount;

// index
it('requires auth', function () {
    $response = $this->getJson('/api/v2/mobile/notifications/');

    $response->assertUnauthorized();
});

it('don\'t mark notifications are read', function () {
    DatabaseNotification::create([
        'id' => uuid_create(),
        'type' => 'App\Notifications\ProofAttendanceRequested',
        'notifiable_type' => 'App\Models\Employee',
        'notifiable_id' => $this->user->id,
        'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
    ]);

    $this->jwtActingAsMobile($this->user)
        ->getJson('/api/v2/mobile/notifications/')
        ->assertOk();

    assertDatabaseHas('notifications', ['read_at' => null, 'notifiable_id' => $this->user->id]);
});

it('return only notifications of authorized user', function () {
    DatabaseNotification::create([
        'id' => uuid_create(),
        'type' => 'App\Notifications\ProofAttendanceRequested',
        'notifiable_type' => 'App\Models\Employee',
        'notifiable_id' => $this->user->id + 1,
        'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
    ]);

    $authUserNotification = DatabaseNotification::create([
        'id' => uuid_create(),
        'type' => 'App\Notifications\ProofAttendanceRequested',
        'notifiable_type' => 'App\Models\Employee',
        'notifiable_id' => $this->user->id,
        'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
    ]);
    $authUserNotification->refresh();

    $this->jwtActingAsMobile($this->user)
        ->getJson('/api/v2/mobile/notifications/')
        ->assertOk()
        ->assertJsonPath('data.data', [$authUserNotification->toArray()]);
});

it('return empty array of notifications when there is no notifications', function () {
    $this->jwtActingAsMobile($this->user)
        ->getJson('/api/v2/mobile/notifications/')
        ->assertOk()
        ->assertJsonPath('data.data', []);
});

it('return all notifications when there are notifications', function () {
    foreach (range(1, 6) as $ignored) {
        DatabaseNotification::create([
            'id' => uuid_create(),
            'type' => 'App\Notifications\ProofAttendanceRequested',
            'notifiable_type' => 'App\Models\Employee',
            'notifiable_id' => $this->user->id,
            'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
        ]);
    }

    $notifications = $this->user->notifications()->unread()->get()->toArray();

    $this->jwtActingAsMobile($this->user)
        ->getJson('/api/v2/mobile/notifications')
        ->assertOk()
        ->assertJsonPath('data.data', $notifications);
});

it('return second page of notifications only', function () {
    foreach (range(1, 6) as $ignored) {
        DatabaseNotification::create([
            'id' => uuid_create(),
            'type' => 'App\Notifications\ProofAttendanceRequested',
            'notifiable_type' => 'App\Models\Employee',
            'notifiable_id' => $this->user->id,
            'data' => '{"type":"PROOF_OF_ATTENDANCE"}',
        ]);
    }

    $response = $this->jwtActingAsMobile($this->user)->getJson(
        'api/v2/mobile/notifications?per_page=4&page=2'
    );

    $response->assertOk();

    assertCount(2, $response->json('data.data'));
});
