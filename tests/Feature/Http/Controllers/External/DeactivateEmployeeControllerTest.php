<?php

it('can deactivate employee', function (array $payload) {
    $this->actingAsTenant()
        ->put<PERSON><PERSON>("api/v1/external/employees/{$payload['identifier']}/deactivate")
        ->assertOk()
        ->assert<PERSON>son(['message' => 'Employee deactivated successfully']);

    expect($payload['employee']->refresh()->is_active)->toBe(0);
})->with([
    'get employee by id' => fn() => [
        'employee' => ($employee = createDefaultEmployee(['is_active' => true])),
        'identifier' => $employee->id,
    ],
    'get employee by email' => fn() => [
        'employee' => ($employee = createDefaultEmployee(['is_active' => true])),
        'identifier' => $employee->email,
    ],
    'get employee by number' => fn() => [
        'employee' => ($employee = createDefaultEmployee(['is_active' => true])),
        'identifier' => $employee->number,
    ],
]);
