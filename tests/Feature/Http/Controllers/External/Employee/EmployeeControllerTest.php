<?php

use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Team;
use function Pest\Laravel\actingAs;

uses()->group('external');

it('fetches all employees', function () {
    $tenant = Team::factory()->create();

    Employee::factory()->for($tenant)->count(3)->create();

    actingAs($tenant)->getJson('api/v1/external/employees')->assertOk()->assertJsonCount(3, 'data');
});

it('only fetches the current team employees', function () {
    $tenant = Team::factory()->create();

    $anotherTenant = Team::factory()->create();

    Employee::factory()->for($tenant)->count(3)->create();

    Employee::factory()->for($anotherTenant)->count(3)->create();

    actingAs($tenant)->getJson('api/v1/external/employees')->assertOk()->assertJsonCount(3, 'data');
});

it('fetch an employee by employee id (number) or email or id', function (?array $data = null) {
    $employee = Employee::factory()
        ->for($this->tenant)
        ->hasAttached(Location::factory()->count(2), [
            'permanent' => fake()->boolean(),
            'start_date' => now()->format('Y-m-d'),
            'end_date' => now()->addMonth()->format('Y-m-d'),
        ])
        ->create([
            'number' => $data['employee_id'] ?? fake()->numberBetween(1, 10),
            'email' => $data['email'] ?? fake()->safeEmail(),
        ]);

    $shift = Shift::factory()
        ->for($this->tenant)
        ->create(['is_default' => true]);

    $employee->shifts()->attach($shift);

    $response = actingAs($this->tenant)
        ->getJson(
            'api/v1/external/employees/get?' . http_build_query($data ?? ['id' => $employee->id])
        )
        ->assertOk();

    expect($response->json('data'))->toBeEmployeeResource($employee);
})->with([
    'by id' => [], // use created employee id
    'by number' => [['employee_id' => (string) fake()->numberBetween(1, 10)]],
    'by email' => [['email' => fake()->safeEmail()]],
]);

expect()->extend('toBeEmployeeResource', function (Employee $employee) {
    $this->id
        ->toBe($employee->id)
        ->name->toBe($employee->name)
        ->email->toBe($employee->email)
        ->first_name->toBe($employee->first_name)
        ->last_name->toBe($employee->last_name)
        ->mobile->toBe($employee->mobile)
        ->position->toBe($employee->position)
        ->number->toBe((string) $employee->number)
        ->is_active->toBe($employee->is_active);

    $this->department->toBe([
        'id' => $employee->department->id,
        'name' => $employee->department->name,
        'remote_work' => $employee->department->remote_work,
        'random_notification' => $employee->department->random_proof_notification_config->oldRandomNotificationValue(),
        'random_proof_notification_config' => [
            'enabled' => $employee->department->random_proof_notification_config->enabled,
            'inherited' => $employee->department->random_proof_notification_config->inherited,
            'count' => $employee->department->random_proof_notification_config->count,
            'deadline' => null,
        ],
    ]);

    $this->shift->toBe([
        'id' => $employee->shift->id,
        'is_default' => $employee->shift->is_default,
        'name' => $employee->shift->name,
        'force_checkout' => $employee->shift->force_checkout->format('Y-m-d H:i:s'),
        'working_hours' => $employee->shift->working_hours,
    ]);

    $this->locations->toBe(
        $employee->locations
            ->map(
                fn(Location $location) => [
                    'id' => $location->id,
                    'name' => $location->name,
                    'permanent' => $location->pivot->permanent,
                    'start_date' => $location->pivot->start_date,
                    'end_date' => $location->pivot->end_date,
                ]
            )
            ->toArray()
    );
});
