<?php

use App\Models\Department;
use App\Models\Team;
use function Pest\Laravel\actingAs;

uses()->group('external');

it('fetches all departments', function () {
    $team = Team::factory()->create();

    Department::factory()->for($team)->count(3)->create();

    /** @see \App\Http\Controllers\External\DepartmentController::index */
    actingAs($team)->getJson('api/v1/external/departments')->assertOk()->assertJsonCount(3, 'data');
});
