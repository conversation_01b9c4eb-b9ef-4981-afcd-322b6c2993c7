<?php

use App\Enums\AttendanceStatus;
use App\Enums\RequestStatus;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Leave;
use Carbon\CarbonImmutable;
use function Pest\Laravel\actingAs;

uses()->group('external');

beforeEach(function () {
    $this->employee = Employee::factory()
        ->for($this->tenant)
        ->create();

    $this->startDate = CarbonImmutable::now();
    $this->endDate = CarbonImmutable::now()->addDays(4);

    $this->attendanceOutside = Attendance::factory()
        ->date(now()->addYear())
        ->for($this->employee)
        ->for($this->tenant)
        ->create(['status' => AttendanceStatus::PRESENT]);
});

it('return leaves of an employee', function (string $inputType) {
    // Given a leave request
    $leave = Leave::factory()
        ->for($this->employee)
        ->for($this->tenant)
        ->create([
            'from_date' => $this->startDate,
            'to_date' => $this->endDate,
            'status' => RequestStatus::Approved,
        ]);

    // Given payload
    $payload = match ($inputType) {
        'email' => ['email' => $this->employee->email],
        'id' => ['employeeId' => $this->employee->nawart_uuid],
    };

    // When the leaves of an employee are fetched
    $response = actingAs($this->tenant)
        ->getJson(
            'api/v1/external/leaves' .
                ($inputType === 'id'
                    ? '/' . $this->employee->nawart_uuid
                    : '?email=' . $this->employee->email),
            $payload
        )
        ->assertOk();

    // Then the response should match the leave data
    expect($response->json('data.data.0'))
        ->id->toBe($leave->id)
        ->from_date->toBe($this->startDate->format('Y-m-d'))
        ->to_date->toBe($this->endDate->format('Y-m-d'))
        ->status->toBe(RequestStatus::Approved->value);
})->with(['id', 'email']);

it('create a new leave request', function () {
    // Given an attendance record
    $attendance = Attendance::factory()
        ->date($this->startDate)
        ->for($this->employee)
        ->for($this->tenant)
        ->create(['status' => AttendanceStatus::ABSENT]);

    // Given payload
    $payload = [
        'email' => $this->employee->email,
        'reason' => 'Vacation',
        'from_date' => $this->startDate->format('Y-m-d'),
        'to_date' => $this->endDate->format('Y-m-d'),
    ];

    // When a leave request is created
    $response = actingAs($this->tenant)
        ->postJson('api/v1/external/leaves', $payload)
        ->assertOk();

    $leave = Leave::first();

    // Then the leave request should be created
    expect($this->employee->leaves()->count())->toBe(1);

    // Then the attendance record should be updated to leave
    expect($attendance->refresh()->status)->toBe(AttendanceStatus::LEAVE->value);

    // Then the attendance record outside the leave period should not be updated
    expect($this->attendanceOutside->refresh()->status)
        ->not()
        ->toBe(AttendanceStatus::LEAVE->value);

    // Then the response should match the leave data
    expect($response->json('data'))
        ->id->toBe($leave->id)
        ->from_date->toBe($payload['from_date'])
        ->to_date->toBe($payload['to_date'])
        ->reason->toBe($payload['reason'])
        ->status->toBe(RequestStatus::Approved->value);
});

it('update a leave request', function (string $inputType) {
    $newStartDate = CarbonImmutable::now()->addMonth();
    $newEndDate = CarbonImmutable::now()->addMonth()->addDays(4);

    // Given a leave request
    $leave = Leave::factory()
        ->for($this->employee)
        ->for($this->tenant)
        ->create([
            'from_date' => $this->startDate,
            'to_date' => $this->endDate,
            'status' => RequestStatus::Approved,
        ]);

    // Given an old attendance record without checkin
    $oldAttendanceWithoutCheckin = Attendance::factory()
        ->date($this->startDate->addDay())
        ->for($this->employee)
        ->for($this->tenant)
        ->create(['status' => AttendanceStatus::LEAVE, 'check_in' => null]);

    // Given a new attendance record (to be updated)
    $newAttendance = Attendance::factory()
        ->date($newStartDate)
        ->for($this->employee)
        ->for($this->tenant)
        ->create(['status' => AttendanceStatus::ABSENT]);

    // Given payload
    $payload = match ($inputType) {
        'email' => [
            'email' => $this->employee->email,
            'old_from_date' => $this->startDate->format('Y-m-d'),
            'old_to_date' => $this->endDate->format('Y-m-d'),
            'new_from_date' => $newStartDate->format('Y-m-d'),
            'new_to_date' => $newEndDate->format('Y-m-d'),
        ],
        'leave' => [
            'new_from_date' => $newStartDate->format('Y-m-d'),
            'new_to_date' => $newEndDate->format('Y-m-d'),
        ],
    };

    // When a leave request is updated
    actingAs($this->tenant)
        ->putJson(
            'api/v1/external/leaves' . ($inputType === 'leave' ? "/$leave->id" : ''),
            $payload
        )
        ->assertOk();

    // Then the leave request should be updated
    expect($leave->refresh()->from_date->format('Y-m-d'))->toBe($payload['new_from_date']);
    expect($leave->refresh()->to_date->format('Y-m-d'))->toBe($payload['new_to_date']);

    // Then the attendance record without checkin should be updated to absent
    expect($oldAttendanceWithoutCheckin->refresh()->status)->toBe(AttendanceStatus::ABSENT->value);

    // Then the attendance record for the new leave period should be updated to leave
    expect($newAttendance->refresh()->status)->toBe(AttendanceStatus::LEAVE->value);

    // Then the attendance record outside the leave period should not be updated
    expect($this->attendanceOutside->refresh()->status)->toBe(AttendanceStatus::PRESENT->value);
})->with(['leave', 'email']);

it('delete a leave request', function (string $inputType) {
    // Given a leave request
    $leave = Leave::factory()
        ->for($this->employee)
        ->for($this->tenant)
        ->create([
            'from_date' => $this->startDate,
            'to_date' => $this->endDate,
            'status' => RequestStatus::Approved,
        ]);

    // Given an attendance record
    $attendance = Attendance::factory()
        ->date($this->startDate)
        ->for($this->employee)
        ->for($this->tenant)
        ->create(['status' => AttendanceStatus::LEAVE]);

    // Given payload
    $payload = match ($inputType) {
        'email' => [
            'email' => $this->employee->email,
            'from_date' => $this->startDate->format('Y-m-d'),
            'to_date' => $this->endDate->format('Y-m-d'),
        ],
        'leave' => [],
    };

    // When a leave request is deleted
    actingAs($this->tenant)
        ->deleteJson(
            'api/v1/external/leaves' . ($inputType === 'leave' ? "/$leave->id" : ''),
            $payload
        )
        ->assertOk();

    // Then the leave request should be deleted
    expect($this->employee->leaves()->count())->toBe(0);

    // Then the attendance record should be updated to absent
    expect($attendance->refresh()->status)->toBe(AttendanceStatus::ABSENT->value);

    // Then the attendance record outside the leave period should not be updated
    expect($this->attendanceOutside->refresh()->status)
        ->not()
        ->toBe(AttendanceStatus::ABSENT->value);
})->with(['leave', 'email']);
