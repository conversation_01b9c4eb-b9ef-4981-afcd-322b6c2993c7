<?php

use App\Models\Employee;
use App\Models\Tenant;

it('can create new employee', function (array $employeePayload, array $departmentPayload) {
    $response = $this->actingAsTenant()->postJson('api/v1/external/employees', [
        ...$employeePayload,
        ...$departmentPayload,
    ]);

    if (
        isset($departmentPayload['department_id']) &&
        $departmentPayload['department_id'] === 'not-existing'
    ) {
        $response->assertStatus(422);
        return;
    }

    $response->assertStatus(200);

    $employee = Employee::find($response->json('data.id'));

    $this->assertModelExists($employee);

    expect(array_column($response->json('data.roles'), 'id'))
        ->toBe($employee->roles->pluck('uuid')->toArray())
        ->and($employee->first_name)
        ->toBe($employeePayload['first_name']);

    if (isset($employeePayload['manager_id'])) {
        expect($employee->manager->id)->toBe($employeePayload['manager_id']);
    }
})
    ->with('employee payloads')
    ->with('employee department payloads');

it('can update an employee', function (
    array $employeePayload,
    string $identifier,
    array $departmentPayload
) {
    $response = $this->actingAsTenant()->putJson("api/v1/external/employees/$identifier", [
        ...$employeePayload,
        ...$departmentPayload,
    ]);

    if (
        isset($departmentPayload['department_id']) &&
        $departmentPayload['department_id'] === 'not-existing'
    ) {
        $response->assertStatus(422);
        return;
    }

    if ($identifier === 'not-existing') {
        $response->assertStatus(404);
        return;
    }

    $response->assertStatus(200);

    if (
        isset($departmentPayload['department_name']) &&
        $departmentPayload['department_name'] === 'not-existing'
    ) {
        // when not existing department name provided, it should be created
        $this->assertDatabaseHas('departments', [
            'name' => $departmentPayload['department_name'],
        ]);
    }

    $employee = Employee::find($response->json('data.id'));

    expect(array_column($response->json('data.roles'), 'id'))
        ->toBe($employee->roles->pluck('uuid')->toArray())
        ->and($employee->first_name)
        ->toBe($employeePayload['first_name']);

    if (isset($employeePayload['manager_id'])) {
        expect($employee->manager->id)->toBe($employeePayload['manager_id']);
    }
})
    ->with('employee payloads')
    ->with('get employee cases')
    ->with('employee department payloads');

it('can update an employee - send same identifier', function () {
    $employee = createDefaultEmployee([
        'email' => fake()->email(),
        'phone' => fake()->numerify('050#######'),
        'number' => fake()->numerify('#######'),
    ]);

    // send the same identifier, if `ignoring` the unique rule not working
    // it will return 422 (identifier is already taken), so we assert ok
    $this->actingAsTenant()
        ->putJson("api/v1/external/employees/$employee->id", ['id' => $employee->id])
        ->assertOk();

    $this->actingAsTenant()
        ->putJson("api/v1/external/employees/$employee->email", ['email' => $employee->email])
        ->assertOk();

    $this->actingAsTenant()
        ->putJson("api/v1/external/employees/$employee->number", ['number' => $employee->number])
        ->assertOk();
});

it('can list employees', function () {
    $tenant = Tenant::factory()->create();

    // 3 employees for this tenant
    Employee::factory()->count(3)->for($tenant)->create();

    // 1 employee for another tenant
    Employee::factory()->for(Tenant::factory())->create();

    $this->actingAsTenant($tenant)
        ->getJson('api/v1/external/employees')
        ->assertOk()
        ->assertJsonCount(3, 'data');
});

it('can fetch a single employee', function (array $identifier) {
    $url = match (true) {
        isset($identifier['route']) => "api/v1/external/employees/{$identifier['route']}",
        isset($identifier['id']) => "api/v1/external/employees/get?id={$identifier['id']}",
        isset($identifier['email']) => "api/v1/external/employees/get?email={$identifier['email']}",
        isset($identifier['number'])
            => "api/v1/external/employees/get?number={$identifier['number']}",
    };

    $response = $this->actingAsTenant()->getJson($url);

    if (str_contains(Arr::first($identifier), 'not_existing')) {
        $response->assertStatus(404);
        return;
    }

    $response->assertOk();

    $employee = Employee::find($response->json('data.id'));

    expect($response->json('data'))
        ->email->toBe($employee->email)
        ->first_name->toBe($employee->first_name)
        ->last_name->toBe($employee->last_name);
})->with([
    'by route' => fn() => ['route' => createDefaultEmployee()->id],
    'by id' => fn() => ['id' => createDefaultEmployee()->id],
    'by email' => fn() => ['email' => createDefaultEmployee()->email],
    'by number' => fn() => ['number' => createDefaultEmployee()->number],
    'by invalid route' => fn() => ['route' => 'not_existing'],
    'by invalid id' => fn() => ['id' => 'not_existing'],
    'by invalid email' => fn() => ['email' => '<EMAIL>'],
    'by invalid number' => fn() => ['number' => 'not_existing'],
]);
