<?php

use App\Models\Attendance;
use Carbon\CarbonPeriod;
use function Pest\Laravel\actingAs;

$invalidKeys = ['invalid-id', 'invalid-nawart-uuid', 'invalid-email', 'invalid-number'];

it('can get employee attendance - without date filter', function (string $identifier) use (
    $invalidKeys
) {
    $startDate = now()->subDays(7);
    $endDate = now();

    $employee = createDefaultEmployee();

    $attendances = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->createFromPeriod(CarbonPeriod::create($startDate, $endDate));

    $identifier = match ($identifier) {
        'id' => $employee->id,
        'nawart-uuid' => $employee->nawart_uuid,
        'email' => $employee->email,
        'number' => $employee->number,
        default => $identifier,
    };

    $response = actingAs($this->tenant)->getJson(
        "api/v1/external/attendances/employees/$identifier"
    );

    if (in_array($identifier, $invalidKeys)) {
        $response->assertNotFound();
        return;
    } else {
        $response->assertOk();
    }

    $attendancesResponse = collect($response->json('data.data'))->pluck('id');

    expect(
        $attendances->pluck('id')->every(fn($id) => $attendancesResponse->contains($id))
    )->toBeTrue();
})->with([
    'id',
    'nawart-uuid',
    'email',
    'number',
    'invalid-id',
    'invalid-nawart-uuid',
    'invalid-email',
    'invalid-number',
]);

it('can get employee attendance - with date filter', function (string $identifier) use (
    $invalidKeys
) {
    $startDate = now()->subDays(7);
    $endDate = now();

    $employee = createDefaultEmployee();

    $inRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->createFromPeriod(CarbonPeriod::create($startDate, $endDate));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->create([
            'date' => now()->subYear(),
        ]);

    $identifier = match ($identifier) {
        'id' => $employee->id,
        'nawart-uuid' => $employee->nawart_uuid,
        'email' => $employee->email,
        'number' => $employee->number,
        default => $identifier,
    };

    $response = actingAs($this->tenant)->getJson(
        "api/v1/external/attendances/employees/$identifier?" .
            http_build_query([
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
            ])
    );

    if (in_array($identifier, $invalidKeys)) {
        $response->assertNotFound();
        return;
    } else {
        $response->assertOk();
    }

    $attendancesResponse = collect($response->json('data.data'))->pluck('id');

    expect(
        $inRangeAttendance->pluck('id')->every(fn($id) => $attendancesResponse->contains($id))
    )->toBeTrue();

    expect($attendancesResponse->contains($outOfRangeAttendance->id))->toBeFalse();
})->with([
    'id',
    'nawart-uuid',
    'email',
    'number',
    'invalid-id',
    'invalid-nawart-uuid',
    'invalid-email',
    'invalid-number',
]);
