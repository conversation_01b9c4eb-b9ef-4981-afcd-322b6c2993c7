<?php

use App\Models\Attendance;
use App\Models\Device;
use App\Models\Location;
use App\Models\Shift;

test('monitor check in', function () {
    $testEmployee = createDefaultEmployee(['email' => '<EMAIL>']);

    Attendance::factory()
        ->date(today())
        ->for($testEmployee)
        ->for($this->tenant)
        ->create();

    Location::factory()
        ->for($this->tenant)
        ->default()
        ->create();

    Shift::factory()
        ->for($this->tenant)
        ->default()
        ->create();

    Device::factory()
        ->for($this->tenant)
        ->existingLocation()
        ->create();

    $this->getJson('checkin/up')->assertOk();
});
