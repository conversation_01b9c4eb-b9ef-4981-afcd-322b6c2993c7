<?php

use App\Events\DepartmentUpdated;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;

test('update department', function () {
    Event::fake([DepartmentUpdated::class]);

    $team = Team::factory()
        ->enterprise()
        ->randomProofNotificationConfig(enabled: true, count: 5)
        ->create();

    $employee = Employee::factory()->for($team)->create();

    $department = Department::factory()
        ->for($team)
        ->randomProofNotificationConfig(enabled: true, inherited: true)
        ->create([
            'remote_work' => 'inherited',
        ]);

    $payload = [
        'remote_work' => 'allowed',
        'random_proof_notification_config' => [
            'enabled' => false,
            'inherited' => false,
            'count' => 10,
        ],
    ];

    /** @see \App\Http\Controllers\Frontend\DepartmentController::update */
    $this->jwtActingAsAttendanceHR($employee)
        ->put<PERSON><PERSON>("api/v1/frontend/departments/$department->id", $payload)
        ->assertOk();

    $department->refresh();

    expect($department->remote_work)->toBe($payload['remote_work']);
    expect($department->random_proof_notification_config->enabled)->toBe(
        $payload['random_proof_notification_config']['enabled']
    );
    expect($department->random_proof_notification_config->inherited)->toBe(
        $payload['random_proof_notification_config']['inherited']
    );
    expect($department->random_proof_notification_config->count)->toBe(
        $payload['random_proof_notification_config']['count']
    );

    Event::assertDispatched(
        DepartmentUpdated::class,
        fn($event) => $event->department->is($department)
    );
});
