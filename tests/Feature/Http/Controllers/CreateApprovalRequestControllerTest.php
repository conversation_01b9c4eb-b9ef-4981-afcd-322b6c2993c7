<?php

use App\DTOs\EmployeeStatementConfig;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\EmployeeStatement;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;
use Illuminate\Support\Carbon;
use function Pest\Laravel\travelTo;

describe('permission request', function () {
    beforeEach(function () {
        Carbon::setTestNow(Carbon::today()->startOfMonth()->addDays(15));
    });

    test(
        'returns bad request response with message "permission requests are disabled" when permission request is disabled',
        function (null|int $monthlyLimit, null|int $dailyLimit) {
            # assert
            $this->assertDatabaseMissing(ApprovalRequest::class, [
                'type' => ApprovalRequest::PERMISSION,
            ]);

            # arrange
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create([
                    'permission_request' => false,
                    'permission_request_monthly_limit_hours' => $monthlyLimit,
                    'permission_request_daily_limit_hours' => $dailyLimit,
                ]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $date->setTime(8, 0),
                    'to' => $date->setTime(10, 0),
                    'reason' => 'test',
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertBadRequest()
                ->assertJsonPath('status', 'PERMISSION_REQUESTS_ARE_DISABLED');

            $this->assertDatabaseMissing(ApprovalRequest::class, [
                'type' => ApprovalRequest::PERMISSION,
            ]);
        }
    )->with([
        'limits are not set' => [
            'monthlyLimit' => null,
            'dailyLimit' => null,
        ],
        'limits are set but not exceeded' => [
            'monthlyLimit' => 12,
            'dailyLimit' => 4,
        ],
        'limits are set and exceeded' => [
            'monthlyLimit' => 1,
            'dailyLimit' => 1,
        ],
    ]);

    it(
        'return Ok response and create pending permission request when permission request is enabled - reason is nullable',
        function () {
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create(['permission_request' => true]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();
            $from = $date->setTime(8, 0);
            $to = $date->setTime(10, 0);
            $reason = null;

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $from,
                    'to' => $to,
                    'reason' => $reason,
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertOk();

            $this->assertDatabaseHas(ApprovalRequest::class, [
                'team_id' => $tenant->id,
                'employee_id' => $employee->id,
                'type' => ApprovalRequest::PERMISSION,
                'status' => ApprovalRequest::PENDING,
                'from_datetime' => $from->setTimezone('UTC')->toDateTimeString(),
                'to_datetime' => $to->setTimezone('UTC')->toDateTimeString(),
                'reason' => $reason,
            ]);
        }
    );

    it(
        'return Ok response and create pending permission request when permission request is enabled',
        function (null|int $monthlyLimit, null|int $dailyLimit) {
            # arrange
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create([
                    'permission_request' => true,
                    'permission_request_monthly_limit_hours' => $monthlyLimit,
                    'permission_request_daily_limit_hours' => $dailyLimit,
                ]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();
            $from = $date->setTime(8, 0);
            $to = $date->setTime(10, 0);
            $reason = 'test';

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->pending()
                ->date(CarbonPeriodImmutable::dates($date->setTime(7, 0), $date->setTime(8, 0)))
                ->regularization()
                ->create();

            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->subDay()->setTime(7, 0),
                        $date->subDay()->setTime(8, 0)
                    )
                )
                ->permission()
                ->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $from,
                    'to' => $to,
                    'reason' => $reason,
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertOk();

            $this->assertDatabaseHas(ApprovalRequest::class, [
                'team_id' => $tenant->id,
                'employee_id' => $employee->id,
                'type' => ApprovalRequest::PERMISSION,
                'status' => ApprovalRequest::PENDING,
                'from_datetime' => $from->setTimezone('UTC')->toDateTimeString(),
                'to_datetime' => $to->setTimezone('UTC')->toDateTimeString(),
                'reason' => $reason,
            ]);
        }
    )->with([
        'only daily limit is set and limit not exceeded' => [
            'monthlyLimit' => null,
            'dailyLimit' => 3,
        ],
        'only monthly limit is set and limit not exceeded' => [
            'monthlyLimit' => 3,
            'dailyLimit' => null,
        ],
        'limits are not set' => [
            'monthlyLimit' => null,
            'dailyLimit' => null,
        ],
        'limits are set to zero is the same as no limit' => [
            'monthlyLimit' => 0,
            'dailyLimit' => 0,
        ],
    ]);

    it(
        'return bad request with message "limit exceeded" when permission request is enabled and only daily limit is set and limit exceeded',
        function () {
            # arrange
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create([
                    'permission_request' => true,
                    'permission_request_daily_limit_hours' => 2,
                    'permission_request_monthly_limit_hours' => null,
                ]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();
            $from = $date->setTime(8, 0);
            $to = $date->setTime(11, 0);
            $reason = 'test';

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            # permission requests
            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(CarbonPeriodImmutable::dates($date->setTime(6, 59), $date->setTime(7, 59)))
                ->permission()
                ->create();
            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->subDay()->setTime(6, 59),
                        $date->subDay()->setTime(7, 59)
                    )
                )
                ->permission()
                ->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $from,
                    'to' => $to,
                    'reason' => $reason,
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertBadRequest()
                ->assertJsonPath('status', 'DAILY_OR_MONTHLY_LIMIT_EXCEEDED');

            $this->assertDatabaseMissing(ApprovalRequest::class, [
                'employee_id' => $employee->id,
                'type' => ApprovalRequest::PERMISSION,
                'from_datetime' => $from->setTimezone('UTC')->toDateTimeString(),
                'to_datetime' => $to->setTimezone('UTC')->toDateTimeString(),
            ]);
        }
    );

    it(
        'return bad request with message "limit exceeded" when permission request is enabled and only monthly limit is set and limit exceeded',
        function () {
            # arrange
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create([
                    'permission_request' => true,
                    'permission_request_daily_limit_hours' => null,
                    'permission_request_monthly_limit_hours' => 2,
                ]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();
            $from = $date->setTime(8, 0);
            $to = $date->setTime(10, 0);
            $reason = 'test';

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->subDay()->setTime(7, 0),
                        $date->subDay()->setTime(8, 0)
                    )
                )
                ->permission()
                ->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $from,
                    'to' => $to,
                    'reason' => $reason,
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertBadRequest()
                ->assertJsonPath('status', 'DAILY_OR_MONTHLY_LIMIT_EXCEEDED');

            $this->assertDatabaseMissing(ApprovalRequest::class, [
                'employee_id' => $employee->id,
                'type' => ApprovalRequest::PERMISSION,
                'from_datetime' => $from->setTimezone('UTC')->toDateTimeString(),
                'to_datetime' => $to->setTimezone('UTC')->toDateTimeString(),
            ]);
        }
    );

    it(
        'return bad request with message "limit exceeded" when permission request is enabled and both monthly and daily limits are set and only daily limit exceeded',
        function () {
            # arrange
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create([
                    'permission_request' => true,
                    'permission_request_daily_limit_hours' => 3,
                    'permission_request_monthly_limit_hours' => 12,
                ]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();
            $from = $date->setTime(8, 0);
            $to = $date->setTime(11, 1);
            $reason = 'test';

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->subDay()->setTime(7, 0),
                        $date->subDay()->setTime(8, 0)
                    )
                )
                ->permission()
                ->create();
            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->addDay()->setTime(7, 0),
                        $date->addDay()->setTime(8, 0)
                    )
                )
                ->permission()
                ->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $from,
                    'to' => $to,
                    'reason' => $reason,
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertBadRequest()
                ->assertJsonPath('status', 'DAILY_OR_MONTHLY_LIMIT_EXCEEDED');

            $this->assertDatabaseMissing(ApprovalRequest::class, [
                'employee_id' => $employee->id,
                'type' => ApprovalRequest::PERMISSION,
                'from_datetime' => $from->setTimezone('UTC')->toDateTimeString(),
                'to_datetime' => $to->setTimezone('UTC')->toDateTimeString(),
            ]);
        }
    );

    it(
        'return bad request with message "limit exceeded" when permission request is enabled and both monthly and daily limits are set and only monthly limit exceeded',
        function () {
            # arrange
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create([
                    'permission_request' => true,
                    'permission_request_daily_limit_hours' => 5,
                    'permission_request_monthly_limit_hours' => 6,
                ]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();
            $from = $date->setTime(8, 0);
            $to = $date->setTime(10, 0);
            $reason = 'test';

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->subDay()->setTime(7, 0),
                        $date->subDay()->setTime(10, 0)
                    )
                )
                ->permission()
                ->create();
            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->addDay()->setTime(7, 0),
                        $date->addDay()->setTime(10, 0)
                    )
                )
                ->permission()
                ->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $from,
                    'to' => $to,
                    'reason' => $reason,
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertBadRequest()
                ->assertJsonPath('status', 'DAILY_OR_MONTHLY_LIMIT_EXCEEDED');

            $this->assertDatabaseMissing(ApprovalRequest::class, [
                'employee_id' => $employee->id,
                'type' => ApprovalRequest::PERMISSION,
                'from_datetime' => $from->setTimezone('UTC')->toDateTimeString(),
                'to_datetime' => $to->setTimezone('UTC')->toDateTimeString(),
            ]);
        }
    );

    it(
        'return bad request with message "limit exceeded" when permission request is enabled and both monthly and daily limits are set and both daily and monthly limits are exceeded',
        function () {
            # arrange
            $tenant = Team::factory()
                ->active()
                ->basic()
                ->create([
                    'permission_request' => true,
                    'permission_request_daily_limit_hours' => 4,
                    'permission_request_monthly_limit_hours' => 5,
                ]);

            $employee = Employee::factory()->active()->for($tenant)->create();

            $date = CarbonImmutable::today();
            $from = $date->setTime(8, 0);
            $to = $date->setTime(10, 0);
            $reason = 'test';

            Attendance::factory()->for($tenant)->for($employee)->date($date)->create();

            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(
                    CarbonPeriodImmutable::dates(
                        $date->subDay()->setTime(7, 0),
                        $date->subDay()->setTime(9, 0)
                    )
                )
                ->permission()
                ->create();
            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date(CarbonPeriodImmutable::dates($date->setTime(11, 0), $date->setTime(14, 0)))
                ->permission()
                ->create();

            # act & assert
            /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
            $this->jwtActingAsMobile($employee)
                ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                    'from' => $from,
                    'to' => $to,
                    'reason' => $reason,
                    'type' => ApprovalRequest::PERMISSION,
                ])
                ->assertBadRequest()
                ->assertJsonPath('status', 'DAILY_OR_MONTHLY_LIMIT_EXCEEDED');

            $this->assertDatabaseMissing(ApprovalRequest::class, [
                'employee_id' => $employee->id,
                'type' => ApprovalRequest::PERMISSION,
                'from_datetime' => $from->setTimezone('UTC')->toDateTimeString(),
                'to_datetime' => $to->setTimezone('UTC')->toDateTimeString(),
            ]);
        }
    );
});

# regularization requests

test('regularization request when no attendance record exists', function () {
    $this->jwtActingAsMobile($this->user)
        ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
            'from' => today()->subDay()->setTime(8, 0),
            'to' => today()->subDay()->setTime(16, 0),
            'reason' => 'test',
            'type' => ApprovalRequest::REGULARIZATION,
        ])
        ->assertBadRequest()
        ->assertJsonPath('message', __('no attendance record found in request date'));
});

test(
    'regularization request when attendance record exists but there is already a pending request for the same day',
    function () {
        Attendance::factory()
            ->for($this->user)
            ->for($this->tenant)
            ->date(today()->subDay())
            ->create();

        $fromDate = today()->subDay()->setTime(8, 0);
        $toDate = today()->subDay()->setTime(16, 0);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($this->user)
            ->for($this->user->department)
            ->create([
                'from_datetime' => $fromDate,
                'to_datetime' => $toDate,
                'type' => ApprovalRequest::REGULARIZATION,
                'status' => ApprovalRequest::PENDING,
            ]);

        $this->jwtActingAsMobile($this->user)
            ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                'from' => $fromDate,
                'to' => $toDate,
                'reason' => 'test',
                'type' => ApprovalRequest::REGULARIZATION,
            ])
            ->assertBadRequest()
            ->assertJsonPath(
                'message',
                __(
                    'pending or approved regularization request in the regulated date already exists'
                )
            );
    }
);

test(
    'regularization request when attendance record exists but there is already an approved request for the same day',
    function () {
        Attendance::factory()
            ->for($this->user)
            ->for($this->tenant)
            ->date(today()->subDay())
            ->create();

        $fromDate = today()->subDay()->setTime(8, 0);
        $toDate = today()->subDay()->setTime(16, 0);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($this->user)
            ->for($this->user->department)
            ->create([
                'from_datetime' => $fromDate,
                'to_datetime' => $toDate,
                'type' => ApprovalRequest::REGULARIZATION,
                'status' => ApprovalRequest::APPROVED,
            ]);

        $this->jwtActingAsMobile($this->user)
            ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                'from' => $fromDate,
                'to' => $toDate,
                'reason' => 'test',
                'type' => ApprovalRequest::REGULARIZATION,
            ])
            ->assertBadRequest()
            ->assertJsonPath(
                'message',
                __(
                    'pending or approved regularization request in the regulated date already exists'
                )
            );
    }
);

test(
    'regularization request when attendance record exists and there is a rejected request for the same day',
    function () {
        Attendance::factory()
            ->for($this->user)
            ->for($this->tenant)
            ->date(today()->subDay())
            ->create();

        $anotherEmployee = Employee::factory()
            ->for($this->tenant)
            ->create();

        $fromDate = today()->subDay()->setTime(8, 0);
        $toDate = today()->subDay()->setTime(16, 0);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($this->user)
            ->for($this->user->department)
            ->create([
                'from_datetime' => $fromDate,
                'to_datetime' => $toDate,
                'type' => ApprovalRequest::REGULARIZATION,
                'status' => ApprovalRequest::REJECTED,
            ]);
        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($anotherEmployee)
            ->for($this->user->department)
            ->create([
                'from_datetime' => $fromDate,
                'to_datetime' => $toDate,
                'type' => ApprovalRequest::REGULARIZATION,
                'status' => ApprovalRequest::PENDING,
            ]);
        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($anotherEmployee)
            ->for($this->user->department)
            ->create([
                'from_datetime' => $fromDate,
                'to_datetime' => $toDate,
                'type' => ApprovalRequest::REGULARIZATION,
                'status' => ApprovalRequest::APPROVED,
            ]);

        $this->jwtActingAsMobile($this->user)
            ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                'from' => $fromDate,
                'to' => $toDate,
                'reason' => 'test',
                'type' => ApprovalRequest::REGULARIZATION,
            ])
            ->assertOk();
    }
);

test('regularization request when limit is reached', function () {
    $this->tenant->update(['approval_requests_limit' => 5]);

    Attendance::factory()
        ->for($this->user)
        ->for($this->tenant)
        ->date(today()->subDay())
        ->create();

    ApprovalRequest::factory(4)
        ->for($this->tenant)
        ->for($this->user)
        ->for($this->user->department)
        ->approved()
        ->regularization()
        ->sequence(
            [
                'from_datetime' => today()->subDays(2)->setTime(8, 0),
                'to_datetime' => today()->subDays(2)->setTime(16, 0),
            ],
            [
                'from_datetime' => today()->subDays(3)->setTime(8, 0),
                'to_datetime' => today()->subDays(3)->setTime(16, 0),
            ],
            [
                'from_datetime' => today()->subDays(4)->setTime(8, 0),
                'to_datetime' => today()->subDays(4)->setTime(16, 0),
            ],
            [
                'from_datetime' => today()->subDays(5)->setTime(8, 0),
                'to_datetime' => today()->subDays(5)->setTime(16, 0),
            ]
        )
        ->create();

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($this->user)
        ->for($this->user->department)
        ->regularization()
        ->create([
            'from_datetime' => today()->subDays(6)->setTime(8, 0),
            'to_datetime' => today()->subDays(6)->setTime(16, 0),
            'status' => ApprovalRequest::PENDING,
        ]);

    $this->jwtActingAsMobile($this->user)
        ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
            'from' => today()->subDay()->setTime(8, 0),
            'to' => today()->subDay()->setTime(16, 0),
            'reason' => 'test',
            'type' => ApprovalRequest::REGULARIZATION,
        ])
        ->assertBadRequest()
        ->assertJsonPath('status', 'REACHED_MAXIMUM_NUMBER_OF_APPROVAL_REQUESTS');
});

test(
    'regularization request when attendance record exists and there is no requests for the same day',
    function () {
        Attendance::factory()
            ->for($this->user)
            ->for($this->tenant)
            ->date(today()->subDay())
            ->create();

        $fromDate = today()->subDay()->setTime(8, 0);
        $toDate = today()->subDay()->setTime(16, 0);

        $this->jwtActingAsMobile($this->user)
            ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                'from' => $fromDate,
                'to' => $toDate,
                'reason' => 'test',
                'type' => ApprovalRequest::REGULARIZATION,
            ])
            ->assertOk();
    }
);

test(
    'employee statement - when employee requests regularization for recent days (allow)',
    function () {
        travelTo('2022-01-05');

        $team = Team::factory()
            ->enterprise()
            ->create([
                'employee_statement_config' => new EmployeeStatementConfig(
                    enabled: true,
                    preventRequestsEnabled: true,
                    daysBeforePreventingRequests: 3 // oldest day to allow is 2022-01-02
                ),
            ]);

        $employee = Employee::factory()->for($team)->create();

        // Create attendance records with employee statements
        $attendance = Attendance::factory()
            ->for($employee)
            ->for($team)
            ->present()
            ->date(date: '2022-01-03')
            ->create([
                'employee_statement_enabled' => true,
            ]);

        EmployeeStatement::factory()->for($employee)->for($attendance)->for($team)->create();

        /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
        $this->jwtActingAsMobile($employee)
            ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                'from' => Carbon::parse('2022-01-03')->setTime(8, 0),
                'to' => Carbon::parse('2022-01-03')->setTime(16, 0),
                'reason' => 'test',
                'type' => ApprovalRequest::REGULARIZATION,
            ])
            ->assertOk();

        $newApprovalRequest = $employee->approvalRequests()->first();

        expect($attendance->employeeStatement->requestable->id)->toEqual($newApprovalRequest->id);
    }
);

test(
    'employee statement - when employee requests regularization for older days (reject)',
    function () {
        travelTo('2022-01-05');

        $team = Team::factory()
            ->enterprise()
            ->create([
                'employee_statement_config' => new EmployeeStatementConfig(
                    enabled: true,
                    preventRequestsEnabled: true,
                    daysBeforePreventingRequests: 3 // oldest day to allow is 2022-01-02
                ),
            ]);

        $employee = Employee::factory()->for($team)->create();

        // Create attendance records with employee statements
        Attendance::factory()
            ->for($employee)
            ->for($team)
            ->present()
            ->date(date: '2022-01-01')
            ->create([
                'employee_statement_enabled' => true,
            ]);

        /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
        $this->jwtActingAsMobile($employee)
            ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
                'from' => Carbon::parse('2022-01-01')->setTime(8, 0),
                'to' => Carbon::parse('2022-01-01')->setTime(16, 0),
                'reason' => 'test',
                'type' => ApprovalRequest::REGULARIZATION,
            ])
            ->assertInvalid([
                'date' => __(
                    'You can not request leave or permission or regularization older than :days days',
                    ['days' => 3]
                ),
            ]);
    }
);

test('employee statement - when feature is disabled', function () {
    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: false,
                preventRequestsEnabled: false,
                daysBeforePreventingRequests: 3
            ),
        ]);

    $employee = Employee::factory()->for($team)->create();

    // Create old attendance records with employee statements
    Attendance::factory()
        ->for($employee)
        ->for($team)
        ->present()
        ->date(date: '2022-01-01')
        ->create([
            'employee_statement_enabled' => true,
        ]);

    /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
    $this->jwtActingAsMobile($employee)
        ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
            'from' => Carbon::parse('2022-01-01')->setTime(8, 0),
            'to' => Carbon::parse('2022-01-01')->setTime(16, 0),
            'reason' => 'test',
            'type' => ApprovalRequest::REGULARIZATION,
        ])
        ->assertOk();
});

test('employee statement - when prevention is disabled but feature is enabled', function () {
    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: false,
                daysBeforePreventingRequests: 3
            ),
        ]);

    $employee = Employee::factory()->for($team)->create();

    // Create old attendance records with employee statements
    Attendance::factory()
        ->for($employee)
        ->for($team)
        ->present()
        ->date(date: '2022-01-01')
        ->create([
            'employee_statement_enabled' => true,
        ]);

    /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
    $this->jwtActingAsMobile($employee)
        ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
            'from' => Carbon::parse('2022-01-01')->setTime(8, 0),
            'to' => Carbon::parse('2022-01-01')->setTime(16, 0),
            'reason' => 'test',
            'type' => ApprovalRequest::REGULARIZATION,
        ])
        ->assertOk();
});

test('employee statement - when no employee statements exist for the period', function () {
    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: true,
                daysBeforePreventingRequests: 3
            ),
        ]);

    $employee = Employee::factory()->for($team)->create();

    // Create attendance records without employee statements
    Attendance::factory()
        ->for($employee)
        ->for($team)
        ->present()
        ->date(date: '2022-01-01')
        ->create([
            'employee_statement_enabled' => false,
        ]);

    /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
    $this->jwtActingAsMobile($employee)
        ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
            'from' => Carbon::parse('2022-01-01')->setTime(8, 0),
            'to' => Carbon::parse('2022-01-01')->setTime(16, 0),
            'reason' => 'test',
            'type' => ApprovalRequest::REGULARIZATION,
        ])
        ->assertOk();
});

test('employee statement - when requesting permission for older days with statements', function () {
    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: true,
                daysBeforePreventingRequests: 3
            ),
            'permission_request' => true,
        ]);

    $employee = Employee::factory()->for($team)->create();

    // Create old attendance records with employee statements
    Attendance::factory()
        ->for($employee)
        ->for($team)
        ->present()
        ->date(date: '2022-01-01')
        ->create([
            'employee_statement_enabled' => true,
        ]);

    /** @see \App\Http\Controllers\Mobile\V2\ApprovalRequests\CreateApprovalRequestController */
    $this->jwtActingAsMobile($employee)
        ->postJson('/api/v2/mobile/approval_requests/create_approval_request', [
            'from' => Carbon::parse('2022-01-01')->setTime(8, 0),
            'to' => Carbon::parse('2022-01-01')->setTime(10, 0),
            'reason' => 'test',
            'type' => ApprovalRequest::PERMISSION,
        ])
        ->assertInvalid([
            'date' => __(
                'You can not request leave or permission or regularization older than :days days',
                ['days' => 3]
            ),
        ]);
});
