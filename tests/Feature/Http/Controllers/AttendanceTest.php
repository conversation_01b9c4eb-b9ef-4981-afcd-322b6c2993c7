<?php

use App\Enums\AttendanceStatus;
use App\Models\Activity;
use App\Models\Attendance;
use Carbon\CarbonInterval;
use Illuminate\Support\Carbon;

test('attendance early/late in/out are calculated correctly', function (
    Carbon $check_in,
    ?Carbon $check_out,
    int $flexible_hours,
    Carbon $shift_from,
    Carbon $shift_to,
    array $expected
) {
    // prepare
    $attendance = Attendance::factory()
        ->date(Carbon::today())
        ->create([
            'status' => AttendanceStatus::PRESENT,
            'shift_from' => $shift_from,
            'shift_to' => $shift_to,
            'check_in' => $check_in,
            'check_out' => $check_out,
            'in_type' => Activity::CHECK_IN,
            'out_type' => $check_out ? Activity::CHECK_OUT : null,
            'flexible_hours' => $flexible_hours,
        ]);

    // test
    expect($attendance->early_in)
        ->toEqual($expected['early_in'], 'early in value should be ' . $expected['early_in'])
        ->and($attendance->late_in)
        ->toEqual($expected['late_in'], 'late in value should be ' . $expected['late_in'])
        ->and($attendance->early_out)
        ->toEqual($expected['early_out'], 'early out value should be ' . $expected['early_out'])
        ->and($attendance->late_out)
        ->toEqual($expected['late_out'], 'late out value should be ' . $expected['late_out']);
})->with('attendance early late provider');

test('attendance within shift time', function (
    Carbon $checkIn,
    ?Carbon $checkOut,
    int $flexibleHours,
    Carbon $shiftFrom,
    Carbon $shiftTo,
    CarbonInterval $expectedShiftHoursAttendance
) {
    $attendance = Attendance::factory()
        ->date(Carbon::today())
        ->create([
            'status' => AttendanceStatus::PRESENT,
            'shift_from' => $shiftFrom,
            'shift_to' => $shiftTo,
            'check_in' => $checkIn,
            'check_out' => $checkOut,
            'in_type' => Activity::CHECK_IN,
            'out_type' => $checkOut ? Activity::CHECK_OUT : null,
            'flexible_hours' => $flexibleHours,
        ]);

    expect($attendance->actualDurationWithinShift()->totalSeconds)->toEqual(
        $expectedShiftHoursAttendance->totalSeconds
    );
})->with('attendance within shift time provider provider');
