<?php

use Carbon\CarbonInterval;
use Illuminate\Support\Carbon;

dataset('attendance early late provider', function () {
    // flexible hours: (7-9) - (15-17)
    //  - early in: < 7
    //  - late in: > 9
    //  - early out: < 15
    //  - late out: > 17

    // no flexible hours: (8) - (16)
    //  - early in: < 8
    //  - late in: > 8
    //  - early out: < 16
    //  - late out: > 16

    $shiftFrom = Carbon::today()->setTime(8, 0);
    $shift_to = Carbon::today()->setTime(16, 0);

    return [
        'case 1: early-in - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(7, 59),
            'check_out' => null,
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:01',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 2: early-in - flexible hours' => [
            'check_in' => Carbon::today()->setTime(6, 59),
            'check_out' => null,
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:01',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 3: normal-in - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => null,
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 4: normal-in - flexible hours' => [
            'check_in' => Carbon::today()->setTime(7, 00),
            'check_out' => null,
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 5: normal-in - flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => null,
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 6: normal-in - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 00),
            'check_out' => null,
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 7: late-in - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 01),
            'check_out' => null,
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 8: late-in - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 01),
            'check_out' => null,
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 9: early-in - early-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(6, 59),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 01:01',
                'late_in' => '- 00:00',
                'early_out' => '- 01:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 10: normal-in - early-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 01:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 11: late-in - early-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 01),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 01:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 12: early-in - early-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(6, 59),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:01',
                'late_in' => '- 00:00',
                'early_out' => '- 00:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 13: normal-in - early-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(7, 00),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 14: normal-in - early-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 15: normal-in - early-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 00),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 16: late-in - early-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 01),
            'check_out' => Carbon::today()->setTime(14, 59),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 00:01',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 17: early-in - normal-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(7, 59),
            'check_out' => Carbon::today()->setTime(16, 00),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:01',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 18: normal-in - normal-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => Carbon::today()->setTime(16, 00),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 19: late-in - normal-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 01),
            'check_out' => Carbon::today()->setTime(16, 01),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:01',
            ],
        ],
        'case 20: early-in - normal-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(6, 59),
            'check_out' => Carbon::today()->setTime(15, 00),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:01',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 21: normal-in - normal-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(7, 00),
            'check_out' => Carbon::today()->setTime(15, 00),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 22: normal-in - normal-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => Carbon::today()->setTime(16, 00),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 23: normal-in - normal-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 00),
            'check_out' => Carbon::today()->setTime(17, 00),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 24: late-in - normal-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 01),
            'check_out' => Carbon::today()->setTime(17, 00),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 25: early-in - late-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(7, 59),
            'check_out' => Carbon::today()->setTime(16, 01),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:01',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:01',
            ],
        ],
        'case 26: normal-in - late-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => Carbon::today()->setTime(16, 01),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:01',
            ],
        ],
        'case 27: late-in - late-out - no flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 01),
            'check_out' => Carbon::today()->setTime(16, 02),
            'flexible_hours' => 0,
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:02',
            ],
        ],
        'case 28: early-in - late-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(6, 59),
            'check_out' => Carbon::today()->setTime(17, 01),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:01',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:01',
            ],
        ],
        'case 29: normal-in - late-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(7, 00),
            'check_out' => Carbon::today()->setTime(17, 01),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:01',
            ],
        ],
        'case 30: normal-in - late-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(8, 00),
            'check_out' => Carbon::today()->setTime(17, 01),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:01',
            ],
        ],
        'case 31: normal-in - late-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 00),
            'check_out' => Carbon::today()->setTime(17, 01),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:01',
            ],
        ],
        'case 32: late-in - late-out - flexible hours' => [
            'check_in' => Carbon::today()->setTime(9, 01),
            'check_out' => Carbon::today()->setTime(17, 02),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:01',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:02',
            ],
        ],
        'case 33: late-in - late-out - flexible hours - difference less than minute' => [
            'check_in' => Carbon::today()->setTime(9, 0, 1),
            'check_out' => Carbon::today()->setTime(17, 0, 1),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 34: early-in - early-out - flexible hours - difference less than minute' => [
            'check_in' => Carbon::today()->setTime(6, 69, 01),
            'check_out' => Carbon::today()->setTime(14, 59, 01),
            'flexible_hours' => 60, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 35: late-in - late-out - no flexible hours - difference less than minute' => [
            'check_in' => Carbon::today()->setTime(8, 0, 59),
            'check_out' => Carbon::today()->setTime(16, 0, 59),
            'flexible_hours' => 0, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
        'case 36: early-in - early-out - no flexible hours - difference less than minute' => [
            'check_in' => Carbon::today()->setTime(7, 59, 01),
            'check_out' => Carbon::today()->setTime(15, 59, 01),
            'flexible_hours' => 0, // flexible hours in minutes
            'shift_from' => $shiftFrom,
            'shift_to' => $shift_to,
            'expected' => [
                'early_in' => '+ 00:00',
                'late_in' => '- 00:00',
                'early_out' => '- 00:00',
                'late_out' => '+ 00:00',
            ],
        ],
    ];
});

dataset('attendance within shift time provider provider', function () {
    // flexible hours: 7 - 17
    // no flexible hours: 8 - 16
    $shiftFrom = Carbon::today()->setTime(8, 0);
    $shiftTo = Carbon::today()->setTime(16, 0);

    return [
        'case 1: check-in before shift start and no check-out with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(7, 0),
            'checkOut' => null,
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::seconds(0),
        ],
        'case 2: check-in before shift start and check-out before shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(7, 0),
            'checkOut' => Carbon::today()->setTime(15, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hour(7),
        ],
        'case 3: check-in before shift start and check-out before shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(6, 0),
            'checkOut' => Carbon::today()->setTime(16, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hour(9),
        ],
        'case 4: check-in before shift start and check-out exactly at shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(7, 0),
            'checkOut' => Carbon::today()->setTime(16, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hour(8),
        ],
        'case 5: check-in before shift start and check-out exactly at shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(6, 0),
            'checkOut' => Carbon::today()->setTime(17, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(10),
        ],
        'case 6: check-in before shift start and check-out after shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(7, 0),
            'checkOut' => Carbon::today()->setTime(17, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(8),
        ],
        'case 7: check-in before shift start and check-out after shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(6, 0),
            'checkOut' => Carbon::today()->setTime(18, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(10),
        ],
        'case 8: check-in exactly at shift start and check-out before shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(8, 0),
            'checkOut' => Carbon::today()->setTime(15, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(7),
        ],
        'case 9: check-in exactly at shift start and check-out before shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(7, 0),
            'checkOut' => Carbon::today()->setTime(16, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(9),
        ],
        'case 10: check-in exactly at shift start and check-out exactly at shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(8, 0),
            'checkOut' => Carbon::today()->setTime(16, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hour(8),
        ],
        'case 11: check-in exactly at shift start and check-out exactly at shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(7, 0),
            'checkOut' => Carbon::today()->setTime(17, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(10),
        ],
        'case 12: check-in exactly at shift start and check-out after shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(8, 0),
            'checkOut' => Carbon::today()->setTime(17, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(8),
        ],
        'case 13: check-in exactly at shift start and check-out after shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(8, 0),
            'checkOut' => Carbon::today()->setTime(17, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(8),
        ],
        'case 14: check-in exactly at shift start and check-out after shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(7, 0),
            'checkOut' => Carbon::today()->setTime(18, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(10),
        ],
        'case 15: check-in after shift start and check-out before shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(9, 0),
            'checkOut' => Carbon::today()->setTime(15, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(6),
        ],
        'case 16: check-in after shift start and check-out before shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(8, 0),
            'checkOut' => Carbon::today()->setTime(16, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(8),
        ],
        'case 17: check-in after shift start and check-out exactly at shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(9, 0),
            'checkOut' => Carbon::today()->setTime(16, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(7),
        ],
        'case 18: check-in after shift start and check-out exactly at shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(8, 0),
            'checkOut' => Carbon::today()->setTime(17, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(9),
        ],
        'case 19: check-in after shift start and check-out after shift end with no flexible hours' => [
            'checkIn' => Carbon::today()->setTime(9, 0),
            'checkOut' => Carbon::today()->setTime(17, 0),
            'flexibleHours' => 0, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(7),
        ],
        'case 20: check-in after shift start and check-out after shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(8, 0),
            'checkOut' => Carbon::today()->setTime(18, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(9),
        ],
        'case 21: check-in after shift end and check-out after shift end with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(17, 30),
            'checkOut' => Carbon::today()->setTime(18, 0),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(0),
        ],
        'case 22: check-in before shift start and check-out before shift start with flexible hours' => [
            'checkIn' => Carbon::today()->setTime(6, 0),
            'checkOut' => Carbon::today()->setTime(6, 30),
            'flexibleHours' => 60, # in minutes
            'shiftFrom' => $shiftFrom,
            'shiftTo' => $shiftTo,
            'expectedShiftHoursAttendance' => CarbonInterval::hours(0),
        ],
    ];
});
