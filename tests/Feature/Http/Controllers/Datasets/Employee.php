<?php

dataset('get employee cases', [
    'get employee by id' => fn() => createDefaultEmployee()->id,
    'get employee by email' => fn() => createDefaultEmployee()->email,
    'get employee by number' => fn() => createDefaultEmployee()->number,
    'get employee by invalid id' => fn() => 'not-existing',
    'get employee by invalid email' => fn() => 'not-existing',
    'get employee by invalid number' => fn() => 'not-existing',
]);
