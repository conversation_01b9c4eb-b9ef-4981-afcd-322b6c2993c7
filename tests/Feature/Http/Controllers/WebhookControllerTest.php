<?php

use App\Models\Webhook;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

test('create webhook', function () {
    $payload = [
        'name' => fake()->name(),
        'url' => fake()->url(),
        'active' => true,
        'event_name' => fake()->word(),
    ];

    $this->actioningAsAttendanceDeveloper($this->user)
        ->postJson('api/v1/frontend/webhooks', $payload)
        ->assertOk();

    assertDatabaseHas('webhooks', $payload);
});

test('update webhook', function () {
    $webhook = Webhook::factory()
        ->for($this->tenant)
        ->create();

    $payload = [
        'name' => fake()->name(),
        'url' => fake()->url(),
        'active' => true,
        'event_name' => fake()->word(),
    ];

    $this->actioningAsAttendanceDeveloper($this->user)
        ->put<PERSON><PERSON>("api/v1/frontend/webhooks/$webhook->id", $payload)
        ->assertOk();

    assertDatabaseHas('webhooks', $payload);
});

test('destroy webhook', function () {
    $webhook = Webhook::factory()
        ->for($this->tenant)
        ->create();

    $this->actioningAsAttendanceDeveloper($this->user)
        ->deleteJson("api/v1/frontend/webhooks/$webhook->id")
        ->assertOk();

    assertDatabaseMissing('webhooks', ['id' => $webhook->id, 'deleted_at' => null]);
});
