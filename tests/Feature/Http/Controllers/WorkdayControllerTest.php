<?php

use App\Models\Team;
use App\Models\Workday;
use function Pest\Laravel\assertDatabaseCount;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

test('all form request validation fields are required', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/workdays', [])
        ->assertInvalid([
            'name' => __('validation.required', ['attribute' => 'name']),
            'start_time' => __('validation.required', ['attribute' => 'start time']),
            'end_time' => __('validation.required', ['attribute' => 'end time']),
            'color' => __('validation.required', ['attribute' => 'color']),
            'flexible_time_before' => __('validation.required', [
                'attribute' => 'flexible time before',
            ]),
            'flexible_time_after' => __('validation.required', [
                'attribute' => 'flexible time after',
            ]),
        ]);
});

test('all form request validation fields have correct type', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/workdays', [
            'name' => true,
            'start_time' => 'not-a-time',
            'end_time' => 'not-a-time',
            'color' => 'not-a-hex-color',
            'flexible_time_before' => 'not-a-time',
            'flexible_time_after' => 'not-a-time',
            'prevent_checkout_after' => 'not-a-time',
        ])
        ->assertInvalid([
            'start_time' => __('validation.date_format', [
                'attribute' => 'start time',
                'format' => 'H:i',
            ]),
            'end_time' => __('validation.date_format', [
                'attribute' => 'end time',
                'format' => 'H:i',
            ]),
            'color' => __('validation.hex_color', ['attribute' => 'color']),
            'flexible_time_before' => __('validation.date_format', [
                'attribute' => 'flexible time before',
                'format' => 'H:i',
            ]),
            'flexible_time_after' => __('validation.date_format', [
                'attribute' => 'flexible time after',
                'format' => 'H:i',
            ]),
            'prevent_checkout_after' => __('validation.date_format', [
                'attribute' => 'prevent checkout after',
                'format' => 'H:i',
            ]),
        ]);
});

test('all form request validation fields pass with valid data', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/workdays', [
            'name' => 'Morning Shift',
            'start_time' => '08:00',
            'end_time' => '16:00',
            'color' => '#FF5733',
            'flexible_time_before' => '00:00',
            'flexible_time_after' => '00:30',
            'prevent_checkout_after' => '19:30',
        ])
        ->assertOk();
});

test('store workday in the current tenant', function () {
    $data = [
        'name' => 'Morning Shift',
        'start_time' => '08:00',
        'end_time' => '16:00',
        'color' => '#FF5733',
        'flexible_time_before' => '00:00',
        'flexible_time_after' => '00:30',
        'prevent_checkout_after' => '19:30',
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->post('api/v1/frontend/workdays', $data)
        ->assertOk();

    $data['team_id'] = $this->tenant->id;

    assertDatabaseHas('workdays', $data);
    assertDatabaseCount('workdays', 1);
});

test('index returns paginated workdays', function () {
    $workdays = Workday::factory()
        ->count(3)
        ->for($this->tenant)
        ->create();

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get('api/v1/frontend/workdays')
        ->assertOk();

    $response->assertJsonCount(3, 'data.data');
    $response->assertJsonStructure([
        'data' => [
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'start_time',
                    'end_time',
                    'color',
                    'flexible_time_before',
                    'flexible_time_after',
                    'prevent_checkout_after',
                ],
            ],
            'links',
            'meta',
        ],
    ]);
});

test('index filters workdays by search term', function () {
    Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Morning Shift']);

    Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Evening Shift']);

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get('api/v1/frontend/workdays?filter[search]=Morning')
        ->assertOk();

    $response->assertJsonCount(1, 'data.data');
    $response->assertJsonPath('data.data.0.name', 'Morning Shift');
});

test('show returns a specific workday', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create([
            'start_time' => '08:00:00',
            'end_time' => '16:00:00',
            'flexible_time_before' => '00:30:00', // 30 minutes
            'flexible_time_after' => '00:45:00', // 45 minutes
        ]);

    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get("api/v1/frontend/workdays/{$workday->id}")
        ->assertOk();

    $response->assertJsonStructure([
        'data' => [
            'id',
            'name',
            'start_time',
            'end_time',
            'start_time_including_flexible_time',
            'end_time_including_flexible_time',
            'color',
            'flexible_time_before',
            'flexible_time_after',
            'prevent_checkout_after',
        ],
    ]);
    $response->assertJsonPath('data.id', $workday->id);

    $response->assertJsonPath('data.start_time_including_flexible_time', '07:30:00');

    $response->assertJsonPath('data.end_time_including_flexible_time', '16:45:00');
});

test('update workday with valid data', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'name' => 'Updated Shift',
        'start_time' => '09:00',
        'end_time' => '17:00',
        'color' => '#00FF00',
        'flexible_time_before' => '08:30',
        'flexible_time_after' => '17:30',
        'prevent_checkout_after' => '19:00',
    ];

    $this->jwtActingAsAttendanceHR($this->user)
        ->put("api/v1/frontend/workdays/{$workday->id}", $data)
        ->assertOk();

    $data['team_id'] = $this->tenant->id;

    assertDatabaseHas('workdays', $data);
});

test('destroy deletes a workday', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->delete("api/v1/frontend/workdays/{$workday->id}")
        ->assertOk();

    assertDatabaseMissing('workdays', ['id' => $workday->id]);
});

test('destroy returns not found for non-existent workday', function () {
    $this->jwtActingAsAttendanceHR($this->user)
        ->delete('api/v1/frontend/workdays/999')
        ->assertNotFound();
});

test('destroy returns not found for workday from another tenant', function () {
    $anotherTeam = Team::factory()->create();
    $workday = Workday::factory()->for($anotherTeam)->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->delete("api/v1/frontend/workdays/{$workday->id}")
        ->assertNotFound();
});

test('dashboard viewer can access index endpoint', function () {
    Workday::factory()
        ->count(3)
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->get('api/v1/frontend/workdays')
        ->assertOk()
        ->assertJsonCount(3, 'data');
});

test('dashboard viewer cannot access other endpoints', function () {
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create();

    $data = [
        'name' => 'New Shift',
        'start_time' => '09:00',
        'end_time' => '17:00',
        'color' => '#00FF00',
        'flexible_time_before' => '08:30',
        'flexible_time_after' => '17:30',
        'prevent_checkout_after' => '19:00',
    ];

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->post('api/v1/frontend/workdays', $data)
        ->assertForbidden();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->get("api/v1/frontend/workdays/{$workday->id}")
        ->assertForbidden();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->put("api/v1/frontend/workdays/{$workday->id}", $data)
        ->assertForbidden();

    $this->jwtActingAsAttendanceDashboardViewer($this->user)
        ->delete("api/v1/frontend/workdays/{$workday->id}")
        ->assertForbidden();
});

test('workday resource returns properly formatted time fields', function () {
    // Create a workday with specific time values for testing
    $workday = Workday::factory()
        ->for($this->tenant)
        ->create([
            'start_time' => '08:00:00',
            'end_time' => '16:00:00',
            'flexible_time_before' => '00:30:00',
            'flexible_time_after' => '00:45:00',
            'prevent_checkout_after' => '19:00:00',
        ]);

    // Get the workday through the API to trigger the resource transformation
    $response = $this->jwtActingAsAttendanceHR($this->user)
        ->get("api/v1/frontend/workdays/{$workday->id}")
        ->assertOk();

    // Assert that all time fields are in the H:i:s format
    $response->assertJsonPath('data.start_time', '08:00:00');
    $response->assertJsonPath('data.end_time', '16:00:00');
    $response->assertJsonPath('data.start_time_including_flexible_time', '07:30:00');
    $response->assertJsonPath('data.end_time_including_flexible_time', '16:45:00');
    $response->assertJsonPath('data.flexible_time_before', '00:30:00');
    $response->assertJsonPath('data.flexible_time_after', '00:45:00');
    $response->assertJsonPath('data.prevent_checkout_after', '19:00:00');
});
