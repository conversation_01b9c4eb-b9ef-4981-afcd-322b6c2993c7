<?php

use App\Enums\RemoteWorkPolicy;
use App\Events\EmployeeCheckedIn;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Database\Factories\ShiftFactory;
use Illuminate\Http\Response;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\travelTo;

test('check-in - when no active attendance record, create new one', function () {
    Event::fake([EmployeeCheckedIn::class]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentShift(
            Shift::factory()
                ->for($this->tenant)
                ->create()
        )
        ->create();

    // did not create attendance record for today...

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    /** @see \App\Http\Controllers\Mobile\V2\CheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkin', [
            'lat' => $location->lat,
            'lng' => $location->lng,
        ])
        ->assertOk()
        ->assertJson([
            'data' => [
                'status' => 'CHECK_IN',
            ],
        ]);

    Event::assertDispatched(EmployeeCheckedIn::class);
});

test('check-in - when shifts end, return no active attendance record', function () {
    Event::fake([EmployeeCheckedIn::class]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentShift(
            Shift::factory()
                ->for($this->tenant)
                ->create([
                    'working_hours' => ShiftFactory::workingHoursDefaultTemplate(),
                    'force_checkout' => '20:00', // can't checkin after 20:00
                ])
        )
        ->create();

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    // travel to 20:01
    travelTo(now()->setHour(20)->addMinutes(1));

    /** @see \App\Http\Controllers\Mobile\V2\CheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkin', [
            'lat' => $location->lat,
            'lng' => $location->lng,
        ])
        ->assertStatus(417)
        ->assertJson([
            'status' => 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD',
        ]);

    Event::assertNotDispatched(EmployeeCheckedIn::class);
});

test('check-in - already checkin', function () {
    Event::fake([EmployeeCheckedIn::class]);

    $employee = createDefaultEmployee();

    Attendance::factory()
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->for($employee)
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkin', [
            'lat' => 0,
            'lng' => 0,
        ])
        ->assertJson([
            'status' => 'ALREADY_CHECKED_IN',
        ]);

    Event::assertNotDispatched(EmployeeCheckedIn::class);
});

test('check-in - inside location', function () {
    Event::fake([EmployeeCheckedIn::class]);

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentShift(
            Shift::factory()
                ->for($this->tenant)
                ->create()
        )
        ->permanentLocation($location)
        ->create();

    Attendance::factory()
        ->date(today())
        ->yet()
        ->for($employee)
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkin', [
            'lat' => $location->lat,
            'lng' => $location->lng,
        ])
        ->assertOk()
        ->assertJson([
            'data' => [
                'status' => 'CHECK_IN',
            ],
        ]);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::CHECK_IN,
        'lat' => $location->lat,
        'lng' => $location->lng,
        'location_id' => $location->id,
    ]);

    Event::assertDispatched(EmployeeCheckedIn::class);
});

test('check-in - outside location - remote work allowed', function () {
    Event::fake([EmployeeCheckedIn::class]);

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentShift(
            Shift::factory()
                ->for($this->tenant)
                ->create()
        )
        ->permanentLocation($location)
        ->create(['remote_work' => 'allowed']);

    Attendance::factory()
        ->date(today())
        ->for($employee)
        ->for($this->tenant)
        ->yet()
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkin', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertOk()
        ->assertJson([
            'data' => [
                'status' => 'REMOTE_CHECK_IN_APPROVED',
            ],
        ]);

    Event::assertDispatched(EmployeeCheckedIn::class);
});

test(
    'check-in - outside location - remote work allowed with approval - limit not reached',
    /**
     * @param  array<CarbonImmutable>  $neutralRemoteWorkDates
     * @param  array<CarbonImmutable>  $neutralApprovalRemoteWorkDates
     */
    function (
        CarbonImmutable $travelTo,
        int|null $yearlyLimit,
        int|null $monthlyLimit,
        int|null $weeklyLimit,
        array $neutralRemoteWorkDates,
        array $neutralApprovalRemoteWorkDates
    ) {
        $this->travelTo($travelTo);

        Event::fake([EmployeeCheckedIn::class]);

        $tenant = Team::factory()->create([
            'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
            'remote_work_days_yearly_limit' => $yearlyLimit,
            'remote_work_days_monthly_limit' => $monthlyLimit,
            'remote_work_days_weekly_limit' => $weeklyLimit,
        ]);

        $location = Location::factory()
            ->for($tenant)
            ->create(['radius' => 100]);

        $employee = Employee::factory()
            ->for($tenant)
            ->permanentShift(Shift::factory()->for($tenant)->create())
            ->permanentLocation($location)
            ->create(['remote_work' => 'allowed_with_approval']);

        Attendance::factory()->date(today())->for($employee)->for($tenant)->yet()->create();

        # create remote work request that doesn't reach the limit
        foreach ($neutralRemoteWorkDates as $remoteWorkDate) {
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkIn()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkOut()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        # add neutral regularization request that shouldn't be counted in remote work limit
        foreach ($neutralApprovalRemoteWorkDates as $approvalRequestDate) {
            ApprovalRequest::factory()
                ->approved()
                ->regularization()
                ->date(CarbonPeriod::create($approvalRequestDate, $approvalRequestDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController */
        $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/checkin', [
                'lat' => $location->lat + 1,
                'lng' => $location->lng + 1,
            ])
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => 'REMOTE_CHECK_IN',
                ],
            ]);

        assertDatabaseHas('approval_requests', [
            'team_id' => $employee->team_id,
            'employee_id' => $employee->id,
            'department_id' => $employee->department_id,
            'reason' => 'checkin and out of the zone',
            'type' => ApprovalRequest::REMOTE_WORK,
            'status' => ApprovalRequest::PENDING,
            'attendance_type' => ApprovalRequest::CHECK_IN,
        ]);

        assertDatabaseHas('activities', [
            'employee_id' => $employee->id,
            'action' => Activity::REMOTE_CHECK_IN,
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
            'location_id' => null,
        ]);

        Event::assertDispatched(EmployeeCheckedIn::class);
    }
)->with('limit not reached');

test(
    'check-in - outside location - remote work allowed with approval - limit not reached - already has request',
    /**
     * @param  array<CarbonImmutable>  $neutralRemoteWorkDates
     * @param  array<CarbonImmutable>  $neutralApprovalRemoteWorkDates
     */
    function (
        CarbonImmutable $travelTo,
        int|null $yearlyLimit,
        int|null $monthlyLimit,
        int|null $weeklyLimit,
        array $neutralRemoteWorkDates,
        array $neutralApprovalRemoteWorkDates
    ) {
        $this->travelTo($travelTo);

        Event::fake([EmployeeCheckedIn::class]);

        $tenant = Team::factory()->create([
            'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
            'remote_work_days_yearly_limit' => $yearlyLimit,
            'remote_work_days_monthly_limit' => $monthlyLimit,
            'remote_work_days_weekly_limit' => $weeklyLimit,
        ]);

        $location = Location::factory()
            ->for($tenant)
            ->create(['radius' => 100]);

        $employee = Employee::factory()
            ->for($tenant)
            ->permanentShift(Shift::factory()->for($tenant)->create())
            ->permanentLocation($location)
            ->create(['remote_work' => 'allowed_with_approval']);

        Attendance::factory()->date(today())->for($employee)->for($tenant)->yet()->create();

        # create remote work request that doesn't reach the limit of remote work
        foreach ($neutralRemoteWorkDates as $remoteWorkDate) {
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkIn()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkOut()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        # add neutral regularization request that shouldn't be counted in remote work limit
        foreach ($neutralApprovalRemoteWorkDates as $approvalRequestDate) {
            ApprovalRequest::factory()
                ->approved()
                ->regularization()
                ->date(CarbonPeriod::create($approvalRequestDate, $approvalRequestDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        // already has request
        ApprovalRequest::factory()
            ->pending()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth()))
            ->for($employee)
            ->for($tenant)
            ->create();

        /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController */
        $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/checkin', [
                'lat' => $location->lat + 1,
                'lng' => $location->lng + 1,
            ])
            ->assertJson([
                'status' => 'ALREADY_HAS_REMOTE_WORK_APPROVAL_REQUEST',
            ]);

        Event::assertNotDispatched(EmployeeCheckedIn::class);
    }
)->with('limit not reached');

test(
    'check-in - outside location - remote work allowed with approval - limit reached',
    /**
     * @param  array<CarbonImmutable>  $remoteWorkDates
     */
    function (
        CarbonImmutable $travelTo,
        int|null $yearlyLimit,
        int|null $monthlyLimit,
        int|null $weeklyLimit,
        array $remoteWorkDates
    ) {
        $this->travelTo($travelTo);

        Event::fake([EmployeeCheckedIn::class]);

        $tenant = Team::factory()->create([
            'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
            'remote_work_days_yearly_limit' => $yearlyLimit,
            'remote_work_days_monthly_limit' => $monthlyLimit,
            'remote_work_days_weekly_limit' => $weeklyLimit,
        ]);

        $location = Location::factory()
            ->for($tenant)
            ->create(['radius' => 100]);

        $employee = Employee::factory()
            ->for($tenant)
            ->permanentLocation($location)
            ->create(['remote_work' => 'allowed_with_approval']);

        Attendance::factory()->date(today())->for($employee)->for($tenant)->yet()->create();

        # create remote work request that does reach the limit
        foreach ($remoteWorkDates as $remoteWorkDate) {
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkIn()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkOut()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController */
        $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/checkin', [
                'lat' => $location->lat + 1,
                'lng' => $location->lng + 1,
            ])
            ->assertStatus(Response::HTTP_EXPECTATION_FAILED)
            ->assertJson([
                'status' => 'REACHED_REMOTE_WORK_DAYS_MONTHLY_LIMITS',
            ]);

        Event::assertNotDispatched(EmployeeCheckedIn::class);
    }
)->with('limit reached');

test('check-in - outside location - remote work not allowed', function () {
    Event::fake([EmployeeCheckedIn::class]);

    $tenant = Team::factory()->create(['remote_work' => 'not_allowed']);

    $location = Location::factory()
        ->for($tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()->for($tenant)->permanentLocation($location)->create();

    Attendance::factory()->date(today())->for($employee)->for($tenant)->yet()->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkin', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertJson([
            'status' => 'OUT_OF_ZONE',
        ]);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::OUT_OF_ZONE_CHECK_IN_LOG,
        'lat' => $location->lat + 1,
        'lng' => $location->lng + 1,
        'location_id' => null,
    ]);

    Event::assertNotDispatched(EmployeeCheckedIn::class);
});

test('checkin - inside location - automatic checkin', function () {
    Event::fake([EmployeeCheckedIn::class]);

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date(today())
        ->yet()
        ->create();

    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkin', [
            'lat' => $location->lat,
            'lng' => $location->lng,
            'automatic' => true,
        ])
        ->assertOk()
        ->assertJson([
            'data' => [
                'status' => 'AUTOMATIC_CHECK_IN',
            ],
        ]);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::AUTOMATIC_CHECK_IN,
        'lat' => $location->lat,
        'lng' => $location->lng,
        'location_id' => $location->id,
    ]);

    Event::assertDispatched(EmployeeCheckedIn::class);
});
