<?php

use App\Enums\RemoteWorkPolicy;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Illuminate\Testing\Fluent\AssertableJson;
use function Pest\Laravel\assertDatabaseHas;

test('can check-in - no active record', function () {
    $employee = createDefaultEmployee();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController */

    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_in', [
            'lat' => 0,
            'lng' => 0,
        ])
        ->assertJson([
            'status' => 'EMPLOYEE_DOES_NOT_HAVE_ACTIVE_RECORD',
        ])
        ->assertStatus(417);
});

test('can check-in - inside location', function () {
    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create();

    Attendance::factory()
        ->date(today())
        ->for($employee)
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_in', [
            'lat' => $location->lat,
            'lng' => $location->lng,
        ])
        ->assertOk()
        ->assertJson([
            'status' => 'ALLOWED',
        ]);
});

test('can check-in - outside location', function () {
    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create();

    Attendance::factory()
        ->date(today())
        ->for($employee)
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_in', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertJson(fn(AssertableJson $json) => $json->whereNot('status', 'ALLOWED')->etc());
});

test('can check-in - outside location - remote work allowed', function () {
    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create(['remote_work' => 'allowed']);

    Attendance::factory()
        ->date(today())
        ->for($employee)
        ->for($this->tenant)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_in', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertOk()
        ->assertJson([
            'status' => 'APPROVED',
        ]);
});

test(
    'can check-in - outside location - remote work allowed with approval - limit not reached',
    /**
     * @param  array<CarbonImmutable>  $neutralRemoteWorkDates
     * @param  array<CarbonImmutable>  $neutralApprovalRemoteWorkDates
     */
    function (
        CarbonImmutable $travelTo,
        int|null $yearlyLimit,
        int|null $monthlyLimit,
        int|null $weeklyLimit,
        array $neutralRemoteWorkDates,
        array $neutralApprovalRemoteWorkDates
    ) {
        $this->travelTo($travelTo);

        $tenant = Team::factory()->create([
            'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
            'remote_work_days_yearly_limit' => $yearlyLimit,
            'remote_work_days_monthly_limit' => $monthlyLimit,
            'remote_work_days_weekly_limit' => $weeklyLimit,
        ]);

        $location = Location::factory()
            ->for($tenant)
            ->create(['radius' => 100]);

        $employee = Employee::factory()
            ->for($tenant)
            ->permanentLocation($location)
            ->create(['remote_work' => 'allowed_with_approval']);

        Attendance::factory()->date(today())->for($employee)->for($tenant)->create();

        foreach ($neutralRemoteWorkDates as $remoteWorkDate) {
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkIn()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkOut()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        foreach ($neutralApprovalRemoteWorkDates as $approvalRequestDate) {
            ApprovalRequest::factory()
                ->approved()
                ->regularization()
                ->date(CarbonPeriod::create($approvalRequestDate, $approvalRequestDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController */
        $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/can_check_in', [
                'lat' => $location->lat + 1,
                'lng' => $location->lng + 1,
            ])
            ->assertJson([
                'status' => 'ATTENDANCE_REQUEST_CREATED',
            ]);
    }
)->with('limit not reached');

test(
    'can check-in - outside location - remote work allowed with approval - limit reached',
    /**
     * @param  array<CarbonImmutable>  $remoteWorkDates
     */
    function (
        CarbonImmutable $travelTo,
        int|null $yearlyLimit,
        int|null $monthlyLimit,
        int|null $weeklyLimit,
        array $remoteWorkDates
    ) {
        $this->travelTo($travelTo);

        $tenant = Team::factory()->create([
            'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
            'remote_work_days_yearly_limit' => $yearlyLimit,
            'remote_work_days_monthly_limit' => $monthlyLimit,
            'remote_work_days_weekly_limit' => $weeklyLimit,
        ]);

        $location = Location::factory()
            ->for($tenant)
            ->create(['radius' => 100]);

        $employee = Employee::factory()
            ->for($tenant)
            ->permanentLocation($location)
            ->create(['remote_work' => 'allowed_with_approval']);

        Attendance::factory()->date(today())->for($employee)->for($tenant)->create();

        foreach ($remoteWorkDates as $remoteWorkDate) {
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkIn()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
            ApprovalRequest::factory()
                ->approved()
                ->remoteWork()
                ->checkOut()
                ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
                ->for($employee)
                ->for($tenant)
                ->create();
        }

        /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController */
        $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/can_check_in', [
                'lat' => $location->lat + 1,
                'lng' => $location->lng + 1,
            ])
            ->assertJson([
                'status' => 'REACHED_REMOTE_WORK_DAYS_MONTHLY_LIMITS',
            ]);
    }
)->with('limit reached');

test('can check-in - outside location - remote work not allowed', function () {
    $tenant = Team::factory()->create(['remote_work' => 'not_allowed']);

    $location = Location::factory()
        ->for($tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()->for($tenant)->permanentLocation($location)->create();

    Attendance::factory()->date(today())->for($employee)->for($tenant)->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckInController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_in', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertJson([
            'status' => 'NOT_ALLOWED',
        ]);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::OUT_OF_ZONE_CHECK_IN_LOG,
        'lat' => $location->lat + 1,
        'lng' => $location->lng + 1,
        'location_id' => null,
    ]);
});
