<?php

use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use Carbon\CarbonPeriod;
use function Pest\Laravel\assertDatabaseHas;

test('can checkout - not checked in yet', function () {
    $employee = createDefaultEmployee(); // helper to create an employee
    // Employee does not have a checked-in attendance record

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckOutController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_out', ['lat' => 0, 'lng' => 0])
        ->assertJson([
            'status' => 'NOT_CHECKED_IN_YET',
        ])
        ->assertStatus(417);
});

test('can checkout - inside location', function () {
    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckOutController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_out', [
            'lat' => $location->lat,
            'lng' => $location->lng,
        ])
        ->assertOk()
        ->assertJson([
            'status' => 'ALLOWED',
        ]);
});

test('can checkout - outside location - remote work allowed', function () {
    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create(['remote_work' => 'allowed']);

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckOutController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_out', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertOk()
        ->assertJson([
            'status' => 'APPROVED',
        ]);
});

test('can checkout - outside location - remote work allowed with approval', function () {
    $this->travelTo(today()->startOfYear()->addMonths(6));

    $tenant = Team::factory()->create([
        'remote_work' => 'allowed_with_approval',
        'remote_work_days_yearly_limit' => 1,
        'remote_work_days_monthly_limit' => 1,
        'remote_work_days_weekly_limit' => 1,
    ]);

    $location = Location::factory()
        ->for($tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($tenant)
        ->permanentLocation($location)
        ->create(['remote_work' => 'allowed_with_approval']);

    Attendance::factory()
        ->for($employee)
        ->for($tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    # remote work limit should not prevent checkout
    ApprovalRequest::factory()
        ->approved()
        ->remoteWork()
        ->checkIn()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckOutController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_out', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertJson([
            'status' => 'ATTENDANCE_REQUEST_CREATED',
        ])
        ->assertStatus(417);
});

test('can checkout - outside location - remote work not allowed', function () {
    $tenant = Team::factory()->create(['remote_work' => 'not_allowed']);

    $location = Location::factory()
        ->for($tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()->for($tenant)->permanentLocation($location)->create();

    Attendance::factory()
        ->for($employee)
        ->for($tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\CheckInOut\CanCheckOutController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/can_check_out', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertJson([
            'status' => 'NOT_ALLOWED',
        ])
        ->assertStatus(417);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::OUT_OF_ZONE_CHECK_OUT_LOG,
        'lat' => $location->lat + 1,
        'lng' => $location->lng + 1,
        'location_id' => null,
    ]);
});
