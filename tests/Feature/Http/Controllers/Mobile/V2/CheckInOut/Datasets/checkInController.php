<?php

use App\Macro\CarbonMacro;

CarbonMacro::defineMacro();

dataset('limit not reached', function () {
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $neutralApprovalRemoteWorkDates = [$travelTo, $travelTo->subDay()];

    return [
        'no limit is set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => null,
            'weeklyLimit' => null,
            'neutralRemoteWorkDates' => [$travelTo->subDay()],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'all limits are set to zero' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 0,
            'monthlyLimit' => 0,
            'weeklyLimit' => 0,
            'neutralRemoteWorkDates' => [$travelTo->subDay()],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'only yearly limit is set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 2,
            'monthlyLimit' => null,
            'weeklyLimit' => null,
            'neutralRemoteWorkDates' => [
                $travelTo->subYear(),
                $travelTo->subYear()->subDay(),
                $travelTo->startOfYear(),
            ],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'only monthly limit is set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => 2,
            'weeklyLimit' => null,
            'neutralRemoteWorkDates' => [
                $travelTo->subMonth(),
                $travelTo->subMonth()->subDay(),
                $travelTo->startOfMonth(),
            ],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'only weekly limit is set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => null,
            'weeklyLimit' => 2,
            'neutralRemoteWorkDates' => [
                $travelTo->subWeek(),
                $travelTo->subWeek()->subDay(),
                $travelTo->localStartOfWeek(),
            ],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'yearly and monthly limits are set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 5,
            'monthlyLimit' => 2,
            'weeklyLimit' => null,
            'neutralRemoteWorkDates' => [
                $travelTo->subYear(),
                $travelTo->subYear()->subDay(),
                $travelTo->subYear()->subDays(2),
                $travelTo->subYear()->subDays(3),
                $travelTo->subYear()->subDays(5),
                $travelTo->startOfYear(),
                $travelTo->subMonth(),
                $travelTo->subMonth()->subday(),
                $travelTo->startOfMonth(),
            ],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'yearly and weekly limits are set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 5,
            'monthlyLimit' => null,
            'weeklyLimit' => 2,
            'neutralRemoteWorkDates' => [
                $travelTo->subYear(),
                $travelTo->subYear()->subDay(),
                $travelTo->subYear()->subDays(2),
                $travelTo->subYear()->subDays(3),
                $travelTo->subYear()->subDays(4),
                $travelTo->startOfYear(),
                $travelTo->subWeek(),
                $travelTo->subWeek()->subday(),
                $travelTo->localStartOfWeek(),
            ],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'monthly and weekly limits are set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => 5,
            'weeklyLimit' => 2,
            'neutralRemoteWorkDates' => [
                $travelTo->subMonth(),
                $travelTo->subMonth()->subDay(),
                $travelTo->subMonth()->subDays(2),
                $travelTo->subMonth()->subDays(3),
                $travelTo->subMonth()->subDays(4),
                $travelTo->startOfMonth(),
                $travelTo->subWeek(),
                $travelTo->subWeek()->subday(),
                $travelTo->localStartOfWeek(),
            ],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
        'all limits are set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 10,
            'monthlyLimit' => 6,
            'weeklyLimit' => 2,
            'neutralRemoteWorkDates' => [
                $travelTo->subYear(),
                $travelTo->subYear()->subDay(),
                $travelTo->subYear()->subDays(2),
                $travelTo->subYear()->subDays(3),
                $travelTo->subYear()->subDays(4),
                $travelTo->subYear()->subDays(5),
                $travelTo->subYear()->subDays(6),
                $travelTo->subYear()->subDays(7),
                $travelTo->subYear()->subDays(8),
                $travelTo->startOfYear(),
                $travelTo->subMonth(),
                $travelTo->subMonth()->subday(),
                $travelTo->subMonth()->subdays(2),
                $travelTo->startOfMonth(),
                $travelTo->startOfMonth()->addDay(),
                $travelTo->subWeek(),
                $travelTo->subWeek()->subday(),
                $travelTo->localStartOfWeek(),
            ],
            'neutralApprovalRemoteWorkDates' => $neutralApprovalRemoteWorkDates,
        ],
    ];
});

dataset('limit reached', function () {
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();

    return [
        'only yearly limit is set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 2,
            'monthlyLimit' => null,
            'weeklyLimit' => null,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->startOfYear()],
        ],
        'only monthly limit is set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => 2,
            'weeklyLimit' => null,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->startOfMonth()],
        ],
        'only weekly limit is set' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => null,
            'weeklyLimit' => 2,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->localStartOfWeek()],
        ],
        'yearly and monthly limits are set and monthly limit reached' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 3,
            'monthlyLimit' => 2,
            'weeklyLimit' => null,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->startOfMonth()],
        ],
        'yearly and monthly limits are set and yearly limit reached' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 2,
            'monthlyLimit' => 2,
            'weeklyLimit' => null,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->subMonth()],
        ],
        'yearly and weekly limits are set and yearly limit reached' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 2,
            'monthlyLimit' => null,
            'weeklyLimit' => 2,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->subWeek()],
        ],
        'yearly and weekly limits are set and weekly limit reached' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 3,
            'monthlyLimit' => null,
            'weeklyLimit' => 2,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->localStartOfWeek()],
        ],
        'monthly and weekly limits are set and weekly limit reached' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => 3,
            'weeklyLimit' => 2,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->localStartOfWeek()],
        ],
        'monthly and weekly limits are set and monthly limit reached' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => null,
            'monthlyLimit' => 2,
            'weeklyLimit' => 2,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->subWeek()],
        ],
        'all limits are set and reached' => [
            'travelTo' => $travelTo,
            'yearlyLimit' => 2,
            'monthlyLimit' => 2,
            'weeklyLimit' => 2,
            'remoteWorkDates' => [$travelTo->subDay(), $travelTo->subDays(2)],
        ],
    ];
});
