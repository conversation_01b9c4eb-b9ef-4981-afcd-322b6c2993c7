<?php

use App\Events\EmployeeCheckedOut;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Event;
use function Pest\Laravel\assertDatabaseHas;

test('checkout - not checked in yet', function () {
    Event::fake([EmployeeCheckedOut::class]);

    $employee = createDefaultEmployee();

    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkout', ['lat' => 0, 'lng' => 0])
        ->assertJson([
            'status' => 'NOT_CHECKED_IN_YET',
        ])
        ->assertStatus(417);

    Event::assertNotDispatched(EmployeeCheckedOut::class);
});

test('checkout - inside location', function () {
    Event::fake([EmployeeCheckedOut::class]);

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkout', [
            'lat' => $location->lat,
            'lng' => $location->lng,
        ])
        ->assertOk()
        ->assertJson([
            'data' => [
                'status' => 'CHECK_OUT',
            ],
        ]);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::CHECK_OUT,
        'lat' => $location->lat,
        'lng' => $location->lng,
        'location_id' => $location->id,
    ]);

    Event::assertDispatched(EmployeeCheckedOut::class);
});

test('checkout - outside location - remote work allowed', function () {
    Event::fake([EmployeeCheckedOut::class]);

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create(['remote_work' => 'allowed']);

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkout', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertOk()
        ->assertJson([
            'data' => [
                'status' => 'REMOTE_CHECK_OUT_APPROVED',
            ],
        ]);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::REMOTE_CHECK_OUT_APPROVED,
        'lat' => $location->lat + 1,
        'lng' => $location->lng + 1,
        'location_id' => null,
    ]);

    Event::assertDispatched(EmployeeCheckedOut::class);
});

test(
    'checkout - outside location - remote work allowed with approval - no pending request',
    function () {
        $this->travelTo(Carbon::createFromDate(2024, 6, 15)->localEndOfWeek());

        Event::fake([EmployeeCheckedOut::class]);

        $tenant = Team::factory()->create([
            'remote_work' => 'allowed_with_approval',
            'remote_work_days_yearly_limit' => 1,
            'remote_work_days_monthly_limit' => 1,
            'remote_work_days_weekly_limit' => 1,
        ]);

        $location = Location::factory()
            ->for($tenant)
            ->create(['radius' => 100]);

        $employee = Employee::factory()
            ->for($tenant)
            ->permanentLocation($location)
            ->create(['remote_work' => 'allowed_with_approval']);

        Attendance::factory()
            ->for($employee)
            ->for($tenant)
            ->date(today())
            ->present(checkinDate: now(), onDuty: true)
            ->create();

        # remote work limit should not prevent checkout
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
            ->for($employee)
            ->for($tenant)
            ->create();

        $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/checkout', [
                'lat' => $location->lat + 1,
                'lng' => $location->lng + 1,
            ])
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => 'REMOTE_CHECK_OUT',
                ],
            ]);

        assertDatabaseHas('approval_requests', [
            'team_id' => $employee->team_id,
            'employee_id' => $employee->id,
            'department_id' => $employee->department_id,
            'reason' => 'checkout and out of the zone',
            'type' => ApprovalRequest::REMOTE_WORK,
            'status' => ApprovalRequest::PENDING,
            'attendance_type' => ApprovalRequest::CHECK_OUT,
        ]);

        assertDatabaseHas('activities', [
            'employee_id' => $employee->id,
            'action' => Activity::REMOTE_CHECK_OUT,
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
            'location_id' => null,
        ]);

        Event::assertDispatched(EmployeeCheckedOut::class);
    }
);

test(
    'checkout - outside location - remote work allowed with approval - pending request exists',
    function () {
        Event::fake([EmployeeCheckedOut::class]);

        $tenant = Team::factory()->create(['remote_work' => 'allowed_with_approval']);

        $location = Location::factory()
            ->for($tenant)
            ->create(['radius' => 100]);

        $employee = Employee::factory()
            ->for($tenant)
            ->permanentLocation($location)
            ->create(['remote_work' => 'allowed_with_approval']);

        Attendance::factory()
            ->for($employee)
            ->for($tenant)
            ->date(today())
            ->present(checkinDate: now(), onDuty: true)
            ->create();

        ApprovalRequest::factory()
            ->pending()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth()))
            ->for($employee)
            ->for($tenant)
            ->create();

        $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/checkout', [
                'lat' => $location->lat + 1,
                'lng' => $location->lng + 1,
            ])
            ->assertJson([
                'status' => 'ALREADY_HAS_REMOTE_WORK_APPROVAL_REQUEST',
            ])
            ->assertStatus(417);

        Event::assertNotDispatched(EmployeeCheckedOut::class);
    }
);

test('checkout - outside location - remote work not allowed', function () {
    Event::fake([EmployeeCheckedOut::class]);

    $tenant = Team::factory()->create(['remote_work' => 'not_allowed']);

    $location = Location::factory()
        ->for($tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()->for($tenant)->permanentLocation($location)->create();

    Attendance::factory()
        ->for($employee)
        ->for($tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkout', [
            'lat' => $location->lat + 1,
            'lng' => $location->lng + 1,
        ])
        ->assertJson([
            'status' => 'OUT_OF_ZONE',
        ])
        ->assertStatus(406);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::OUT_OF_ZONE_CHECK_OUT_LOG,
        'lat' => $location->lat + 1,
        'lng' => $location->lng + 1,
        'location_id' => null,
    ]);

    Event::assertNotDispatched(EmployeeCheckedOut::class);
});

test('checkout - inside location - automatic checkout', function () {
    Event::fake([EmployeeCheckedOut::class]);

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['radius' => 100]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($location)
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date(today())
        ->present(checkinDate: now(), onDuty: true)
        ->create();

    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/checkout', [
            'lat' => $location->lat,
            'lng' => $location->lng,
            'automatic' => true,
        ])
        ->assertOk()
        ->assertJson([
            'data' => [
                'status' => 'AUTOMATIC_CHECK_OUT',
            ],
        ]);

    assertDatabaseHas('activities', [
        'employee_id' => $employee->id,
        'action' => Activity::AUTOMATIC_CHECK_OUT,
        'lat' => $location->lat,
        'lng' => $location->lng,
        'location_id' => $location->id,
    ]);

    Event::assertDispatched(EmployeeCheckedOut::class);
});
