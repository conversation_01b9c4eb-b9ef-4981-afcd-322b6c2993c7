<?php

use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Decision;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;

test('list approval requests - no filters', function () {
    $tenant = Team::factory()->create();

    $employee = Employee::factory()->for($tenant)->create();

    $anotherEmployee = Employee::factory()->for($tenant)->create();

    ApprovalRequest::factory()->for($tenant)->for($employee)->createMany(3);

    ApprovalRequest::factory()->for($tenant)->for($anotherEmployee)->createMany(3);

    $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/approval-requests/current')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list approval requests with filter', function (
    Employee $employee,
    ApprovalRequest $includedApprovalRequest,
    ApprovalRequest $excludedApprovalRequest,
    array $query
) {
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/approval-requests/current?' . http_build_query($query))
        ->assertOk();

    $responseApprovalRequest = $response->json('data.data');

    expect($responseApprovalRequest)
        ->toHaveCount(1)
        ->and(collect($responseApprovalRequest)->pluck('id'))
        ->toContain($includedApprovalRequest->id)
        ->and(collect($responseApprovalRequest)->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequest->id);
})->with([
    'filter by from to' => function () {
        $tenant = Team::factory()->create();

        $employee = Employee::factory()->for($tenant)->create();

        $startDate = CarbonImmutable::now();

        $endDate = $startDate->addDays(3);

        $includedApprovalRequest = ApprovalRequest::factory()
            ->for($tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($startDate, $endDate))
            ->create();

        $excludedApprovalRequest = ApprovalRequest::factory()
            ->for($tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($endDate->addDays(2), $endDate->addDays(4)))
            ->create();

        return [
            'employee' => $employee,
            'includedApprovalRequest' => $includedApprovalRequest,
            'excludedApprovalRequest' => $excludedApprovalRequest,
            'query' => [
                'filter[from]' => $startDate->format('Y-m-d'),
                'filter[to]' => $endDate->format('Y-m-d'),
            ],
        ];
    },
    'filter by status' => function () {
        $tenant = Team::factory()->create();
        $employee = Employee::factory()->for($tenant)->create();

        $includedApprovalRequest = ApprovalRequest::factory()
            ->for($tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Approved]);

        $excludedApprovalRequest = ApprovalRequest::factory()
            ->for($tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Pending]);

        return [
            'employee' => $employee,
            'includedApprovalRequest' => $includedApprovalRequest,
            'excludedApprovalRequest' => $excludedApprovalRequest,
            'query' => [
                'filter[status]' => [RequestStatus::Approved->value],
            ],
        ];
    },
]);

test('list approval requests with decisions included', function () {
    $tenant = Team::factory()->create();
    $department = Department::factory()->for($tenant)->create();

    // Create employee and manager
    $manager = Employee::factory()->for($tenant)->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create approval requests with decisions
    $approvalRequest = ApprovalRequest::factory()
        ->for($tenant)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Approved,
        ]);

    // Create a decision for the approval request
    $decision = Decision::create([
        'team_id' => $tenant->id,
        'employee_id' => $employee->id,
        'decider_id' => $manager->id,
        'decidable_id' => $approvalRequest->id,
        'decidable_type' => get_class($approvalRequest),
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::First,
    ]);

    // Make the API request with decisions included
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/approval-requests/current?include[]=decisions')
        ->assertOk();

    // Assert the response contains the decisions
    $responseData = $response->json('data.data.0');

    expect($responseData)
        ->toHaveKey('decisions')
        ->and($responseData['decisions'])
        ->toBeArray()
        ->toHaveCount(1)
        ->and($responseData['decisions'][0]['id'])
        ->toBe($decision->id)
        ->and($responseData['decisions'][0]['status'])
        ->toBe(RequestStatus::Approved->value)
        ->and($responseData['decisions'][0]['layer'])
        ->toBe(DecisionLayer::First->value);
});
