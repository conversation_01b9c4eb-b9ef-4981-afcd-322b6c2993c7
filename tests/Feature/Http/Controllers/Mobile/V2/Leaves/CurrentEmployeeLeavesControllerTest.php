<?php

use App\Enums\DecisionLayer;
use App\Enums\RequestStatus;
use App\Models\Decision;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Leave;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;

test('list leaves - no filters', function () {
    $tenant = Team::factory()->create();

    $employee = Employee::factory()->for($tenant)->create();

    $anotherEmployee = Employee::factory()->for($tenant)->create();

    Leave::factory()->for($tenant)->for($employee)->createMany(3);

    Leave::factory()->for($tenant)->for($anotherEmployee)->createMany(3);

    $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/leaves/current')
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list leaves with filter', function (
    Employee $employee,
    Leave $includedLeaves,
    Leave $excludedLeaves,
    array $query
) {
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/leaves/current?' . http_build_query($query))
        ->assertOk();

    $responseLeaves = $response->json('data.data');

    expect($responseLeaves)
        ->toHaveCount(1)
        ->and(collect($responseLeaves)->pluck('id'))
        ->toContain($includedLeaves->id)
        ->and(collect($responseLeaves)->pluck('id'))
        ->not()
        ->toContain($excludedLeaves->id);
})->with([
    'filter by from to' => function () {
        $tenant = Team::factory()->create();

        $employee = Employee::factory()->for($tenant)->create();

        $startDate = CarbonImmutable::now();

        $endDate = $startDate->addDays(3);

        $includedLeaves = Leave::factory()
            ->for($tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($startDate, $endDate))
            ->create();

        $excludedLeaves = Leave::factory()
            ->for($tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($endDate->addDays(1), $endDate->addDays(4)))
            ->create();

        return [
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[from]' => $startDate->format('Y-m-d'),
                'filter[to]' => $endDate->format('Y-m-d'),
            ],
        ];
    },
    'filter by status' => function () {
        $tenant = Team::factory()->create();
        $employee = Employee::factory()->for($tenant)->create();

        $includedLeaves = Leave::factory()
            ->for($tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Approved]);

        $excludedLeaves = Leave::factory()
            ->for($tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Pending]);

        return [
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[status]' => [RequestStatus::Approved->value],
            ],
        ];
    },
]);

test('list leaves with decisions included', function () {
    $tenant = Team::factory()->create();
    $department = Department::factory()->for($tenant)->create();

    // Create employee and manager
    $manager = Employee::factory()->for($tenant)->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->for($department)
        ->create(['manager_id' => $manager->id]);

    // Create leave with decision
    $leave = Leave::factory()
        ->for($tenant)
        ->for($employee)
        ->for($department)
        ->create([
            'status' => RequestStatus::Approved,
            'from_date' => now(),
            'to_date' => now()->addDays(3),
        ]);

    // Create a decision for the leave
    $decision = Decision::create([
        'team_id' => $tenant->id,
        'employee_id' => $employee->id,
        'decider_id' => $manager->id,
        'decidable_id' => $leave->id,
        'decidable_type' => get_class($leave),
        'status' => RequestStatus::Approved,
        'layer' => DecisionLayer::First,
    ]);

    // Make the API request with decisions included
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/leaves/current?include[]=decisions')
        ->assertOk();

    // Assert the response contains the decisions
    $responseData = $response->json('data.data.0');

    expect($responseData)
        ->toHaveKey('decisions')
        ->and($responseData['decisions'])
        ->toBeArray()
        ->toHaveCount(1)
        ->and($responseData['decisions'][0]['id'])
        ->toBe($decision->id)
        ->and($responseData['decisions'][0]['status'])
        ->toBe(RequestStatus::Approved->value)
        ->and($responseData['decisions'][0]['layer'])
        ->toBe(DecisionLayer::First->value);
});
