<?php

use App\DTOs\EmployeeStatementConfig;
use App\Enums\AttendanceStatus;
use App\Enums\Folder;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\EmployeeStatement;
use App\Models\Team;
use App\Notifications\NewLeaveRequestNotification;
use Carbon\CarbonPeriod;
use function Pest\Laravel\travelTo;

test('create a leave', function () {
    Notification::fake();

    Storage::fake();

    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $payload = [
        'from' => '2022-01-01',
        'to' => '2022-01-02',
        'reason' => 'Sick',
        'attachment' => 'attachment.pdf',
    ];

    Storage::put(Folder::LEAVE_ATTACHMENTS->tempPath($payload['attachment']), 'content');

    /** @see \App\Http\Controllers\Mobile\V2\Leaves\LeaveController */
    $response = $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/leaves', $payload)
        ->assertOk();

    $leave = $employee->leaves()->first();

    Storage::assertExists(Folder::LEAVE_ATTACHMENTS->path($leave->attachment));

    Storage::assertMissing(Folder::LEAVE_ATTACHMENTS->tempPath($payload['attachment']));

    Notification::assertSentTo(
        $manager,
        NewLeaveRequestNotification::class,
        fn($notification) => $notification->leave->is($leave)
    );

    expect($response->json('data'))
        ->id->toBe($leave->id)
        ->reason->toBe($leave->reason)
        ->from_date->toBe($leave->from_date->format('Y-m-d H:i:s'))
        ->to_date->toBe($leave->to_date->format('Y-m-d H:i:s'))
        ->attachment_url->not->toBeNull();
});

test('employee statement - when employee request leave for recent days (allow)', function () {
    Notification::fake();

    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: true,
                daysBeforePreventingRequests: 3 // oldest day to allow is 2022-01-02
            ),
        ]);

    $manager = Employee::factory()->for($team)->create();

    $employee = Employee::factory()
        ->for($team)
        ->create(['manager_id' => $manager->id]);

    $payload = [
        'from' => '2022-01-03',
        'to' => '2022-01-05',
        'reason' => 'Sick',
    ];

    Attendance::factory()
        ->for($employee)
        ->for($team)
        ->present() // no weekends
        ->createFromPeriod(
            period: CarbonPeriod::create('2022-01-01', '2022-01-05'),
            attributes: [
                'employee_statement_enabled' => true,
            ]
        );

    /** @see \App\Http\Controllers\Mobile\V2\Leaves\LeaveController */
    $response = $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/leaves', $payload)
        ->assertOk();

    $leave = $employee->leaves()->first();

    Notification::assertSentTo(
        $manager,
        NewLeaveRequestNotification::class,
        fn($notification) => $notification->leave->is($leave)
    );

    expect($response->json('data'))
        ->id->toBe($leave->id)
        ->reason->toBe($leave->reason)
        ->from_date->toBe($leave->from_date->format('Y-m-d H:i:s'))
        ->to_date->toBe($leave->to_date->format('Y-m-d H:i:s'));
});

test('employee statement - when employee request leave for older days (reject)', function () {
    Notification::fake();

    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: true,
                daysBeforePreventingRequests: 3 // oldest day to allow is 2022-01-02
            ),
        ]);

    $manager = Employee::factory()->for($team)->create();

    $employee = Employee::factory()
        ->for($team)
        ->create(['manager_id' => $manager->id]);

    $payload = [
        'from' => '2022-01-01',
        'to' => '2022-01-02',
        'reason' => 'Sick',
    ];

    Attendance::factory()
        ->for($employee)
        ->for($team)
        ->present() // no weekends
        ->createFromPeriod(
            period: CarbonPeriod::create('2022-01-01', '2022-01-05'),
            attributes: [
                'employee_statement_enabled' => true,
            ]
        );

    /** @see \App\Http\Controllers\Mobile\V2\Leaves\LeaveController */
    $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/leaves', $payload)
        ->assertInvalid([
            'date' => __(
                'You can not request leave or permission or regularization older than :days days',
                ['days' => 3]
            ),
        ]);
});

test(
    'employee statement - when employee request leave for older days but counting weekends (allow)',
    function () {
        Notification::fake();

        travelTo('2022-01-05');

        $team = Team::factory()
            ->enterprise()
            ->create([
                'employee_statement_config' => new EmployeeStatementConfig(
                    enabled: true,
                    preventRequestsEnabled: true,
                    daysBeforePreventingRequests: 3 // oldest day to allow is 2022-01-02
                ),
            ]);

        $manager = Employee::factory()->for($team)->create();

        $employee = Employee::factory()
            ->for($team)
            ->create(['manager_id' => $manager->id]);

        $payload = [
            'from' => '2022-01-01',
            'to' => '2022-01-02',
            'reason' => 'Sick',
        ];

        $attendances = Attendance::factory()
            ->for($employee)
            ->for($team)
            ->present() // no weekends
            ->createFromPeriod(
                period: CarbonPeriod::create('2022-01-01', '2022-01-05'),
                attributes: [
                    'employee_statement_enabled' => true,
                    'is_weekend' => true,
                ],
                statuses: [
                    '2022-01-01' => AttendanceStatus::WEEKEND,
                    '2022-01-02' => AttendanceStatus::WEEKEND,
                    '2022-01-03' => AttendanceStatus::PRESENT,
                    '2022-01-04' => AttendanceStatus::PRESENT,
                    '2022-01-05' => AttendanceStatus::PRESENT,
                ]
            );

        $employeeStatements = $attendances->map(
            fn($attendance) => EmployeeStatement::factory()
                ->for($employee)
                ->for($team)
                ->for($attendance)
                ->create()
        );

        /** @see \App\Http\Controllers\Mobile\V2\Leaves\LeaveController */
        $response = $this->jwtActingAsMobile($employee)
            ->postJson('api/v2/mobile/leaves', $payload)
            ->assertOk();

        $leave = $employee->leaves()->first();

        Notification::assertSentTo(
            $manager,
            NewLeaveRequestNotification::class,
            fn($notification) => $notification->leave->is($leave)
        );

        expect($response->json('data'))
            ->id->toBe($leave->id)
            ->reason->toBe($leave->reason)
            ->from_date->toBe($leave->from_date->format('Y-m-d H:i:s'))
            ->to_date->toBe($leave->to_date->format('Y-m-d H:i:s'));

        foreach ($leave->attendances->load('employeeStatement.requestable') as $attendance) {
            expect($attendance->employeeStatement->requestable->id)->toBe($leave->id);
        }
    }
);

test('employee statement - when feature is disabled', function () {
    Notification::fake();

    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: false,
                preventRequestsEnabled: false,
                daysBeforePreventingRequests: 3
            ),
        ]);

    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->create(['manager_id' => $manager->id]);

    $payload = [
        'from' => '2022-01-01',
        'to' => '2022-01-02',
        'reason' => 'Sick',
    ];

    Attendance::factory()->for($employee)->for($team)->present()->createFromPeriod(
        period: CarbonPeriod::create('2022-01-01', '2022-01-05'),
        attributes: [
            'employee_statement_enabled' => true,
        ]
    );

    $response = $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/leaves', $payload)
        ->assertOk();

    $leave = $employee->leaves()->first();

    expect($response->json('data'))
        ->id->toBe($leave->id)
        ->reason->toBe($leave->reason)
        ->from_date->toBe($leave->from_date->format('Y-m-d H:i:s'))
        ->to_date->toBe($leave->to_date->format('Y-m-d H:i:s'));
});

test('employee statement - when prevention is disabled but feature is enabled', function () {
    Notification::fake();

    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: false,
                daysBeforePreventingRequests: 3
            ),
        ]);

    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->create(['manager_id' => $manager->id]);

    $payload = [
        'from' => '2022-01-01',
        'to' => '2022-01-02',
        'reason' => 'Sick',
    ];

    Attendance::factory()->for($employee)->for($team)->present()->createFromPeriod(
        period: CarbonPeriod::create('2022-01-01', '2022-01-05'),
        attributes: [
            'employee_statement_enabled' => true,
        ]
    );

    $response = $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/leaves', $payload)
        ->assertOk();

    $leave = $employee->leaves()->first();

    expect($response->json('data'))
        ->id->toBe($leave->id)
        ->reason->toBe($leave->reason)
        ->from_date->toBe($leave->from_date->format('Y-m-d H:i:s'))
        ->to_date->toBe($leave->to_date->format('Y-m-d H:i:s'));
});

test('employee statement - when no employee statements exist for the period', function () {
    Notification::fake();

    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: true,
                daysBeforePreventingRequests: 3
            ),
        ]);

    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->create(['manager_id' => $manager->id]);

    $payload = [
        'from' => '2022-01-01',
        'to' => '2022-01-02',
        'reason' => 'Sick',
    ];

    Attendance::factory()->for($employee)->for($team)->present()->createFromPeriod(
        period: CarbonPeriod::create('2022-01-01', '2022-01-05'),
        attributes: [
            'employee_statement_enabled' => false,
        ]
    );

    $response = $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/leaves', $payload)
        ->assertOk();

    $leave = $employee->leaves()->first();

    expect($response->json('data'))
        ->id->toBe($leave->id)
        ->reason->toBe($leave->reason)
        ->from_date->toBe($leave->from_date->format('Y-m-d H:i:s'))
        ->to_date->toBe($leave->to_date->format('Y-m-d H:i:s'));
});

test('employee statement - with mixed statement and non-statement days', function () {
    Notification::fake();

    travelTo('2022-01-05');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: true,
                preventRequestsEnabled: true,
                daysBeforePreventingRequests: 3
            ),
        ]);

    $manager = Employee::factory()->for($team)->create();
    $employee = Employee::factory()
        ->for($team)
        ->create(['manager_id' => $manager->id]);

    $payload = [
        'from' => '2022-01-03',
        'to' => '2022-01-05',
        'reason' => 'Sick',
    ];

    Attendance::factory()->for($employee)->for($team)->present()->createFromPeriod(
        period: CarbonPeriod::create('2022-01-01', '2022-01-05'),
        attributes: [
            'employee_statement_enabled' => false,
        ],
        statuses: [
            '2022-01-01' => AttendanceStatus::PRESENT,
            '2022-01-02' => AttendanceStatus::PRESENT,
            '2022-01-03' => AttendanceStatus::PRESENT,
            '2022-01-04' => AttendanceStatus::PRESENT,
            '2022-01-05' => AttendanceStatus::PRESENT,
        ]
    );

    // Update specific days to have employee statements
    $employee
        ->attendances()
        ->whereDate('date', '2022-01-03')
        ->update(['employee_statement_enabled' => true]);

    $response = $this->jwtActingAsMobile($employee)
        ->postJson('api/v2/mobile/leaves', $payload)
        ->assertOk();

    $leave = $employee->leaves()->first();

    expect($response->json('data'))
        ->id->toBe($leave->id)
        ->reason->toBe($leave->reason)
        ->from_date->toBe($leave->from_date->format('Y-m-d H:i:s'))
        ->to_date->toBe($leave->to_date->format('Y-m-d H:i:s'));
});
