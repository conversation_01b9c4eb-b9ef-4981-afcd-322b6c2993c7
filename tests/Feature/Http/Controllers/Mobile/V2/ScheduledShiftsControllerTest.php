<?php

use App\Enums\AttendanceDayType;
use App\Enums\PreferredLanguage;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\Leave;
use App\Models\Shift;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Carbon\CarbonPeriodImmutable;

test('endpoint accessible authorized employees only', function () {
    $this->getJson('/api/v2/mobile/employees/scheduled-shifts')->assertUnauthorized();

    $this->actingAs($this->user)
        ->getJson('/api/v2/mobile/employees/scheduled-shifts')
        ->assertUnauthorized();
});

test('from and to parameters are required', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/scheduled-shifts')
        ->assertStatus(422)
        ->assertJsonValidationErrors(['from' => 'required', 'to' => 'required']);
});

test('from and to parameters are expected to be dates', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query(['from' => 'abc', 'to' => 1])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['from' => 'not a valid date', 'to' => 'not a valid date']);
});

test('from parameter should be in the present of the future', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query(['from' => today()->subDay()->toDateString()])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['from' => 'must be a date after or equal to today']);

    // happy path - in the present
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => today()->toDateString(),
                ])
        )
        ->assertJsonMissingValidationErrors('from');

    // happy path - in the future
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => today()->addDay()->toDateString(),
                ])
        )
        ->assertJsonMissingValidationErrors(['from']);
});

test('to parameter should be later than from parameter', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => today()->toDateString(),
                    'to' => today()->toDateString(),
                ])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['to' => 'must be a date after from']);
});

test('the maximum period should not exceed 31 days', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    $startDate = CarbonImmutable::today()->addMonth()->setDay(16);
    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $startDate->toDateString(),
                    'to' => $startDate->addDays(35)->toDateString(),
                ])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['to' => 'date range exceed limit']);

    // happy path - period is exactly 31 days
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $startDate->toDateString(),
                    'to' => $startDate->addDays(31)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJsonMissingValidationErrors(['from', 'to']);
});

test('returns shifts scheduled in the passed period - with permanent shift', function () {
    # arrange
    $tenant = Team::factory()
        ->basic()
        ->active()
        ->create(['vacation_weekend' => true]);

    $period = CarbonPeriodImmutable::dates(
        today()->addWeek()->localStartOfWeek(),
        today()->addWeek()->localStartOfWeek()->addDays(31)
    );

    $permanentShift = Shift::factory()->for($tenant)->workingHoursWithWeekend(70)->create();
    $temporaryShiftWithin = Shift::factory()->for($tenant)->workingHoursWithWeekend(0)->create();
    $temporaryShiftStartedBefore = Shift::factory()
        ->for($tenant)
        ->workingHoursWithWeekend(5)
        ->create();
    $temporaryShiftEndAfter = Shift::factory()->for($tenant)->workingHoursWithWeekend(10)->create();

    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->permanentShift($permanentShift)
        ->temporaryShift(
            $temporaryShiftWithin,
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(4),
                $period->getEndDate()->subDays(11)
            )
        )
        ->temporaryShift(
            $temporaryShiftStartedBefore,
            CarbonPeriod::dates(
                $period->getStartDate()->subDay(),
                $period->getStartDate()->addDays(3)
            )
        )
        ->temporaryShift(
            $temporaryShiftEndAfter,
            CarbonPeriod::dates($period->getEndDate()->subDays(6), $period->getEndDate()->addDay())
        )
        ->create();

    # holiday
    Holiday::factory()
        ->for($tenant)
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(4),
                $period->getStartDate()->addDays(5)
            )
        )
        ->create();
    Holiday::factory()
        ->for($tenant)
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(21),
                $period->getStartDate()->addDays(21)
            )
        )
        ->create();

    # leave
    Leave::factory()
        ->for($tenant)
        ->for($employee)
        ->approved()
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(11),
                $period->getStartDate()->addDays(12)
            )
        )
        ->create();
    Leave::factory()
        ->for($tenant)
        ->for($employee)
        ->approved()
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(18),
                $period->getStartDate()->addDays(18)
            )
        )
        ->create();
    Leave::factory()
        ->for($tenant)
        ->for($employee)
        ->approved()
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(20),
                $period->getStartDate()->addDays(20)
            )
        )
        ->create();

    // assert whole period
    $expectedWholePeriod = [
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftStartedBefore->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 5,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDay()->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftStartedBefore->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 5,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(2)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftStartedBefore->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 5,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(3)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftStartedBefore->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 5,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(4)->toDateString(),
            'shift' => [
                // weekday and holiday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::HOLIDAY->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(5)->toDateString(),
            'shift' => [
                // weekend and holiday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::HOLIDAY->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(6)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => 'WEEKEND',
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(7)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(8)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(9)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(10)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(11)->toDateString(),
            'shift' => [
                // leave in weekday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(12)->toDateString(),
            'shift' => [
                // leave in weekend - $vacationWeekendPolicy is true(weekend included in vacation)
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(13)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(14)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(15)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(16)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(17)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(18)->toDateString(),
            'shift' => [
                // leave in holiday and weekday - leave takes priority
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(19)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(20)->toDateString(),
            'shift' => [
                // leave in holiday weekend with
                // $vacationWeekendPolicy = true (vacation includes weekend)
                // - leaves takes priority
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // permanent shift only - permanent shift
        [
            'date' => $period->getStartDate()->addDays(21)->toDateString(),
            'shift' => [
                // weekday and holiday - holiday takes priority
                'shift' => ['id' => $permanentShift->id],
                'status' => AttendanceDayType::HOLIDAY->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // permanent shift only - permanent shift
        [
            'date' => $period->getStartDate()->addDays(22)->toDateString(),
            'shift' => [
                'shift' => ['id' => $permanentShift->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 70,
            ],
        ],
        // permanent shift only - permanent shift
        [
            'date' => $period->getStartDate()->addDays(23)->toDateString(),
            'shift' => [
                'shift' => ['id' => $permanentShift->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 70,
            ],
        ],
        // permanent shift only - permanent shift
        [
            'date' => $period->getStartDate()->addDays(24)->toDateString(),
            'shift' => [
                'shift' => ['id' => $permanentShift->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 70,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(25)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftEndAfter->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 10,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(26)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftEndAfter->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(27)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftEndAfter->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(28)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftEndAfter->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 10,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(29)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftEndAfter->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 10,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(30)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftEndAfter->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 10,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getEndDate()->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftEndAfter->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 10,
            ],
        ],
    ];

    /* @see \App\Http\Controllers\Mobile\V2\ScheduledShiftsController */
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $period->getStartDate()->toDateString(),
                    'to' => $period->getEndDate()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedWholePeriod]);

    // assert shift surrounding period
    $expectedSurroundingPeriod = [
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(5)->toDateString(),
            'shift' => [
                // weekend and holiday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::HOLIDAY->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(6)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(7)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(8)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(9)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(10)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(11)->toDateString(),
            'shift' => [
                // leave in weekday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(12)->toDateString(),
            'shift' => [
                // leave in weekend - $vacationWeekendPolicy is true(weekend included in vacation)
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(13)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(14)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(15)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(16)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(17)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(18)->toDateString(),
            'shift' => [
                // leave in holiday and weekday - leave takes priority
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(19)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $period->getStartDate()->addDays(5)->toDateString(),
                    'to' => $period->getStartDate()->addDays(19)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedSurroundingPeriod]);

    // assert shift exact period
    $expectedExactPeriod = [
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(4)->toDateString(),
            'shift' => [
                // weekday and holiday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::HOLIDAY->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(5)->toDateString(),
            'shift' => [
                // weekend and holiday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::HOLIDAY->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(6)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(7)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(8)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(9)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(10)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(11)->toDateString(),
            'shift' => [
                // leave in weekday
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(12)->toDateString(),
            'shift' => [
                // leave in weekend - $vacationWeekendPolicy is true(weekend included in vacation)
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(13)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(14)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(15)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(16)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(17)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(18)->toDateString(),
            'shift' => [
                // leave in holiday and weekday - leave takes priority
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(19)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::WEEKEND->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
        // both permanent and temporary shifts assigned - temporary shift
        [
            'date' => $period->getStartDate()->addDays(20)->toDateString(),
            'shift' => [
                // leave in holiday weekend with
                // $vacationWeekendPolicy = true (vacation includes weekend)
                // - leaves takes priority
                'shift' => ['id' => $temporaryShiftWithin->id],
                'status' => AttendanceDayType::LEAVE->value,
                'from' => null,
                'to' => null,
                'flexible_hours' => null,
            ],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $period->getStartDate()->addDays(4)->toDateString(),
                    'to' => $period->getStartDate()->addDays(20)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedExactPeriod]);
});

test('returns shifts scheduled in the passed period - weekend vacation is false', function () {
    # arrange
    $tenant = Team::factory()
        ->basic()
        ->active()
        ->create(['vacation_weekend' => false]);

    $period = CarbonPeriodImmutable::dates(
        today()->addWeek()->localStartOfWeek(),
        today()->addWeek()->localStartOfWeek()->addDays(31)
    );

    $permanentShift = Shift::factory()->for($tenant)->workingHoursWithWeekend(70)->create();
    $temporaryShift = Shift::factory()->for($tenant)->workingHoursWithWeekend(0)->create();

    $employee = Employee::factory()
        ->for($tenant)
        ->permanentShift($permanentShift)
        ->temporaryShift(
            $temporaryShift,
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(4),
                $period->getEndDate()->subDays(11)
            )
        )
        ->active()
        ->create();

    # holiday
    Holiday::factory()
        ->for($tenant)
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(20),
                $period->getStartDate()->addDays(20)
            )
        )
        ->create();

    # leave
    Leave::factory()
        ->for($tenant)
        ->for($employee)
        ->approved()
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(11),
                $period->getStartDate()->addDays(12)
            )
        )
        ->create();
    Leave::factory()
        ->for($tenant)
        ->for($employee)
        ->approved()
        ->date(
            CarbonPeriod::dates(
                $period->getStartDate()->addDays(20),
                $period->getStartDate()->addDays(20)
            )
        )
        ->create();

    // assert leave and weekend with $vacationWeekendPolicy set to false (vacation doesn't include weekend)
    $expectedLeaveInWeekend = [
        'date' => $period->getStartDate()->addDays(12)->toDateString(),
        'shift' => [
            // leave in weekend - $vacationWeekendPolicy is false (weekend isn't included in vacation)
            'shift' => ['id' => $temporaryShift->id],
            'status' => AttendanceDayType::WEEKEND->value,
            'from' => null,
            'to' => null,
            'flexible_hours' => null,
        ],
    ];
    $expectedLeaveInHolidayWeekend = [
        'date' => $period->getStartDate()->addDays(20)->toDateString(),
        'shift' => [
            // leave in holiday weekend with
            // $vacationWeekendPolicy = false (vacation isn't includes weekend)
            // - leaves takes priority
            'shift' => ['id' => $temporaryShift->id],
            'status' => AttendanceDayType::WEEKEND->value,
            'from' => null,
            'to' => null,
            'flexible_hours' => null,
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $period->getStartDate()->addDays(12)->toDateString(),
                    'to' => $period->getStartDate()->addDays(20)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.0.date', $expectedLeaveInWeekend['date'])
        ->assertJsonPath('data.0.shift.shift.id', $expectedLeaveInWeekend['shift']['shift']['id'])
        ->assertJsonPath('data.0.shift.status', $expectedLeaveInWeekend['shift']['status'])
        ->assertJsonPath('data.0.shift.from', $expectedLeaveInWeekend['shift']['from'])
        ->assertJsonPath('data.0.shift.to', $expectedLeaveInWeekend['shift']['to'])
        ->assertJsonPath(
            'data.0.shift.flexible_hours',
            $expectedLeaveInWeekend['shift']['flexible_hours']
        )
        ->assertJsonPath('data.8.date', $expectedLeaveInHolidayWeekend['date'])
        ->assertJsonPath(
            'data.8.shift.shift.id',
            $expectedLeaveInHolidayWeekend['shift']['shift']['id']
        )
        ->assertJsonPath('data.8.shift.status', $expectedLeaveInHolidayWeekend['shift']['status'])
        ->assertJsonPath('data.8.shift.from', $expectedLeaveInHolidayWeekend['shift']['from'])
        ->assertJsonPath('data.8.shift.to', $expectedLeaveInHolidayWeekend['shift']['to'])
        ->assertJsonPath(
            'data.8.shift.flexible_hours',
            $expectedLeaveInHolidayWeekend['shift']['flexible_hours']
        );
});

test('returns shifts scheduled in the passed period - no permanent shift', function () {
    # arrange
    $tenant = Team::factory()
        ->basic()
        ->active()
        ->create(['vacation_weekend' => true]);

    $period = CarbonPeriodImmutable::dates(
        today()->addWeek()->localStartOfWeek(),
        today()->addWeek()->localStartOfWeek()->addDays(3)
    );

    $temporaryShift = Shift::factory()->for($tenant)->workingHoursWithWeekend(0)->create();

    $employee = Employee::factory()
        ->for($tenant)
        ->temporaryShift(
            $temporaryShift,
            CarbonPeriod::dates($period->getStartDate(), $period->getStartDate()->addDays(2))
        )
        ->active()
        ->create();

    // act & assert
    $expected = [
        // only temporary shifts assigned
        [
            'date' => $period->getStartDate()->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShift->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // only temporary shifts assigned
        [
            'date' => $period->getStartDate()->addDay()->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShift->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // only temporary shifts assigned
        [
            'date' => $period->getStartDate()->addDays(2)->toDateString(),
            'shift' => [
                'shift' => ['id' => $temporaryShift->id],
                'status' => AttendanceDayType::WEEKDAY->value,
                'from' => $period->getStartDate()->setTime(8, 0)->toTimeString(),
                'to' => $period->getStartDate()->setTime(16, 0)->toTimeString(),
                'flexible_hours' => 0,
            ],
        ],
        // no shifts assigned - return null shift
        [
            'date' => $period->getStartDate()->addDays(3)->toDateString(),
            'shift' => null,
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $period->getStartDate()->toDateString(),
                    'to' => $period->getEndDate()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expected]);
});

test('returns shifts scheduled in the passed period - no shift assigned', function () {
    # arrange
    $tenant = Team::factory()
        ->basic()
        ->active()
        ->create(['vacation_weekend' => true]);
    $employee = Employee::factory()->for($tenant)->active()->create();

    $period = CarbonPeriodImmutable::dates(
        today()->addWeek()->localStartOfWeek(),
        today()->addWeek()->localStartOfWeek()->addDay()
    );

    # un-assigned shift
    Shift::factory()->for($tenant)->create();

    // act & assert
    $expected = [
        // no shift assigned
        [
            'date' => $period->getStartDate()->toDateString(),
            'shift' => null,
        ],
        // no shift assigned
        [
            'date' => $period->getStartDate()->addDay()->toDateString(),
            'shift' => null,
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-shifts?' .
                http_build_query([
                    'from' => $period->getStartDate()->toDateString(),
                    'to' => $period->getEndDate()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expected]);
});

test('next day checkout')->todo();
