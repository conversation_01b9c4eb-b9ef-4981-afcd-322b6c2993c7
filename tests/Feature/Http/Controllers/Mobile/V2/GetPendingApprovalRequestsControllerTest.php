<?php

use App\Enums\DelegationType;
use App\Models\ApprovalRequest;
use App\Models\Delegation;
use App\Models\Department;
use App\Models\Employee;

it(
    'returns approval requests for a manager of employees with no regularization delegation',
    function () {
        $manager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $manager->id]);

        $employeeForAnotherManager = Employee::factory()
            ->for($this->tenant)
            ->create();

        Notification::fake();

        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        $this->jwtActingAsMobile($manager)
            ->getJson('/api/v2/mobile/approval_requests/pending')
            ->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'id' => $approvalRequest->id,
                        'employee_name' => $employee->name,
                    ],
                ],
            ]);
    }
);

it(
    'returns no approval requests for a manager with only regularization requests delegated',
    function () {
        # prepare
        $manager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $manager->id]);
        $delegatedEmployee = Employee::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $manager->id]);

        $employeeForAnotherManager = Employee::factory()
            ->for($this->tenant)
            ->create();

        Notification::fake();

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        Delegation::factory()->create([
            'type' => DelegationType::RegularizationRequest,
            'delegated_id' => $delegatedEmployee->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        $this->jwtActingAsMobile($manager)
            ->getJson('/api/v2/mobile/approval_requests/pending')
            ->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'id' => $approvalRequest->id,
                        'employee_name' => $employee->name,
                    ],
                ],
            ]);
    }
);

it('returns no approval requests for a manager with all requests delegation', function () {
    # prepare
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $department = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);
    $delegatedEmployee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    $employeeForAnotherManager = Employee::factory()
        ->for($this->tenant)
        ->create();

    Notification::fake();

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($department)
        ->for($employee)
        ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::REGULARIZATION]);

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($department)
        ->for($employee)
        ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($department)
        ->for($employeeForAnotherManager)
        ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::REGULARIZATION]);

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($department)
        ->for($employeeForAnotherManager)
        ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

    Delegation::factory()->create([
        'type' => DelegationType::RegularizationRequest,
        'delegated_id' => $delegatedEmployee->id,
        'delegatee_id' => $manager->id,
    ]);
    Delegation::factory()->create([
        'type' => DelegationType::PermissionRequest,
        'delegated_id' => $delegatedEmployee->id,
        'delegatee_id' => $manager->id,
    ]);

    # act
    $this->jwtActingAsMobile($manager)
        ->getJson('/api/v2/mobile/approval_requests/pending')
        ->assertOk()
        ->assertJsonCount(0, 'data');
});

it(
    'returns approval requests for a delegated employee with regularization requests only delegation',
    function () {
        # prepare
        $manager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $manager->id]);
        $delegatedEmployee = Employee::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $manager->id]);

        $employeeForAnotherManager = Employee::factory()
            ->for($this->tenant)
            ->create();

        Notification::fake();

        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        Delegation::factory()->create([
            'type' => DelegationType::RegularizationRequest,
            'delegated_id' => $delegatedEmployee->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        $this->jwtActingAsMobile($delegatedEmployee)
            ->getJson('/api/v2/mobile/approval_requests/pending')
            ->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJson([
                'data' => [
                    [
                        'id' => $approvalRequest->id,
                        'employee_name' => $employee->name,
                    ],
                ],
            ]);
    }
);
