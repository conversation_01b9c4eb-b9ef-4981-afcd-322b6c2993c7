<?php

use App\Enums\ApprovalType;
use App\Enums\DelegationType;
use App\Models\Delegation;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;

test('when team has one-layer approval type - has direct manager', function () {
    $team = Team::factory()
        ->enterprise()
        ->create(['approval_type' => ApprovalType::OneLayer]);

    $directManager = Employee::factory()->for($team)->create();

    $employee = Employee::factory()->for($team)->directManager($directManager)->create();

    /** @see \App\Http\Controllers\Mobile\V2\RequestApproversController */
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/request-approvers')
        ->assertOk();

    expect($response->json('data.first_layer.leave_approver.id'))->toBe($directManager->id);
    expect($response->json('data.first_layer.permission_approver.id'))->toBe($directManager->id);
    expect($response->json('data.first_layer.regularization_approver.id'))->toBe(
        $directManager->id
    );
    expect($response->json('data.first_layer.remote_work_approver.id'))->toBe($directManager->id);

    expect($response->json('data.second_layer.leave_approver'))->toBeNull();
    expect($response->json('data.second_layer.permission_approver'))->toBeNull();
    expect($response->json('data.second_layer.regularization_approver'))->toBeNull();
    expect($response->json('data.second_layer.remote_work_approver'))->toBeNull();
});

test('when team has one-layer approval type - has department manager', function () {
    $team = Team::factory()
        ->enterprise()
        ->create(['approval_type' => ApprovalType::OneLayer]);

    $departmentManager = Employee::factory()->for($team)->create();

    $department = Department::factory()
        ->for($team)
        ->create([
            'manager_id' => $departmentManager->id,
        ]);

    $employee = Employee::factory()->for($team)->for($department)->create();

    /** @see \App\Http\Controllers\Mobile\V2\RequestApproversController */
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/request-approvers')
        ->assertOk();

    expect($response->json('data.first_layer.leave_approver.id'))->toBe($departmentManager->id);
    expect($response->json('data.first_layer.permission_approver.id'))->toBe(
        $departmentManager->id
    );
    expect($response->json('data.first_layer.regularization_approver.id'))->toBe(
        $departmentManager->id
    );
    expect($response->json('data.first_layer.remote_work_approver.id'))->toBe(
        $departmentManager->id
    );

    expect($response->json('data.second_layer.leave_approver'))->toBeNull();
    expect($response->json('data.second_layer.permission_approver'))->toBeNull();
    expect($response->json('data.second_layer.regularization_approver'))->toBeNull();
    expect($response->json('data.second_layer.remote_work_approver'))->toBeNull();
});

test('when team has one-layer approval type - manager delegated some requests', function () {
    $team = Team::factory()
        ->enterprise()
        ->create(['approval_type' => ApprovalType::OneLayer]);

    $directManager = Employee::factory()->for($team)->create();

    $delegatedManager = Employee::factory()->for($team)->create();

    Delegation::create([
        'type' => DelegationType::LeaveRequest,
        'delegatee_id' => $directManager->id,
        'delegated_id' => $delegatedManager->id,
    ]);

    Delegation::create([
        'type' => DelegationType::PermissionRequest,
        'delegatee_id' => $directManager->id,
        'delegated_id' => $delegatedManager->id,
    ]);

    $employee = Employee::factory()->for($team)->directManager($directManager)->create();

    /** @see \App\Http\Controllers\Mobile\V2\RequestApproversController */
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/request-approvers')
        ->assertOk();

    expect($response->json('data.first_layer.leave_approver.id'))->toBe($delegatedManager->id);
    expect($response->json('data.first_layer.permission_approver.id'))->toBe($delegatedManager->id);
    expect($response->json('data.first_layer.regularization_approver.id'))->toBe(
        $directManager->id
    );
    expect($response->json('data.first_layer.remote_work_approver.id'))->toBe($directManager->id);

    expect($response->json('data.second_layer.leave_approver'))->toBeNull();
    expect($response->json('data.second_layer.permission_approver'))->toBeNull();
    expect($response->json('data.second_layer.regularization_approver'))->toBeNull();
    expect($response->json('data.second_layer.remote_work_approver'))->toBeNull();
});

test('when team has two-layer approval type', function () {
    $team = Team::factory()
        ->enterprise()
        ->create(['approval_type' => ApprovalType::TwoLayer]);

    $directManager = Employee::factory()->for($team)->create();

    $departmentManager = Employee::factory()->for($team)->create();

    $department = Department::factory()
        ->for($team)
        ->create([
            'manager_id' => $departmentManager->id,
        ]);

    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->directManager($directManager)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\RequestApproversController */
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/request-approvers')
        ->assertOk();

    expect($response->json('data.first_layer.leave_approver.id'))->toBe($directManager->id);
    expect($response->json('data.first_layer.permission_approver.id'))->toBe($directManager->id);
    expect($response->json('data.first_layer.regularization_approver.id'))->toBe(
        $directManager->id
    );
    expect($response->json('data.first_layer.remote_work_approver.id'))->toBe($directManager->id);

    expect($response->json('data.second_layer.leave_approver.id'))
        ->toBe($departmentManager->id)
        ->and($response->json('data.second_layer.permission_approver.id'))
        ->toBe($departmentManager->id)
        ->and($response->json('data.second_layer.regularization_approver.id'))
        ->toBe($departmentManager->id)
        ->and($response->json('data.second_layer.remote_work_approver.id'))
        ->toBe($departmentManager->id);
});

test('when team has two-layer approval type - manager delegated some requests', function () {
    $team = Team::factory()
        ->enterprise()
        ->create(['approval_type' => ApprovalType::TwoLayer]);

    $directManager = Employee::factory()->for($team)->create();

    $delegatedManagerByDirect = Employee::factory()->for($team)->create();

    Delegation::create([
        'type' => DelegationType::LeaveRequest,
        'delegatee_id' => $directManager->id,
        'delegated_id' => $delegatedManagerByDirect->id,
    ]);

    Delegation::create([
        'type' => DelegationType::PermissionRequest,
        'delegatee_id' => $directManager->id,
        'delegated_id' => $delegatedManagerByDirect->id,
    ]);

    $departmentManager = Employee::factory()->for($team)->create();

    $department = Department::factory()
        ->for($team)
        ->create([
            'manager_id' => $departmentManager->id,
        ]);

    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->directManager($directManager)
        ->create();

    $delegatedManagerByDepartment = Employee::factory()->for($team)->create();

    Delegation::create([
        'type' => DelegationType::LeaveRequest,
        'delegatee_id' => $departmentManager->id,
        'delegated_id' => $delegatedManagerByDepartment->id,
    ]);

    Delegation::create([
        'type' => DelegationType::PermissionRequest,
        'delegatee_id' => $departmentManager->id,
        'delegated_id' => $delegatedManagerByDepartment->id,
    ]);

    /** @see \App\Http\Controllers\Mobile\V2\RequestApproversController */
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/request-approvers')
        ->assertOk();

    expect($response->json('data.first_layer.leave_approver.id'))->toBe(
        $delegatedManagerByDirect->id
    );
    expect($response->json('data.first_layer.permission_approver.id'))->toBe(
        $delegatedManagerByDirect->id
    );
    expect($response->json('data.first_layer.regularization_approver.id'))->toBe(
        $directManager->id
    );
    expect($response->json('data.first_layer.remote_work_approver.id'))->toBe($directManager->id);

    expect($response->json('data.second_layer.leave_approver.id'))->toBe(
        $delegatedManagerByDepartment->id
    );
    expect($response->json('data.second_layer.permission_approver.id'))->toBe(
        $delegatedManagerByDepartment->id
    );
    expect($response->json('data.second_layer.regularization_approver.id'))->toBe(
        $departmentManager->id
    );
    expect($response->json('data.second_layer.remote_work_approver.id'))->toBe(
        $departmentManager->id
    );
});

test('edge case: when employee has no manager', function () {
    $team = Team::factory()
        ->enterprise()
        ->create(['approval_type' => ApprovalType::TwoLayer]);

    $employee = Employee::factory()->for($team)->create();

    /** @see \App\Http\Controllers\Mobile\V2\RequestApproversController */
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/request-approvers')
        ->assertOk();

    expect($response->json('data.first_layer.leave_approver'))->toBeNull();
    expect($response->json('data.first_layer.permission_approver'))->toBeNull();
    expect($response->json('data.first_layer.regularization_approver'))->toBeNull();
    expect($response->json('data.first_layer.remote_work_approver'))->toBeNull();

    expect($response->json('data.second_layer.leave_approver'))->toBeNull();
    expect($response->json('data.second_layer.permission_approver'))->toBeNull();
    expect($response->json('data.second_layer.regularization_approver'))->toBeNull();
    expect($response->json('data.second_layer.remote_work_approver'))->toBeNull();
});
