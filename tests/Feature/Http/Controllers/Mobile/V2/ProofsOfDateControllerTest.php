<?php

use App\Enums\ProofStatus;
use App\Models\Employee;
use App\Models\Proof;
use App\Models\Team;

test('list second page of all proofs for today using pagination', function () {
    # prepare
    $tenant = Team::factory()
        ->enterprise()
        ->create(['active' => true]);

    $employee = Employee::factory()
        ->for($tenant)
        ->create(['is_active' => true]);

    $date = today();

    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Sent, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Missed, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Accepted, 'created_at' => $date]);

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/proofs-of-date?' .
                http_build_query([
                    'per_page' => 2,
                    'page' => 2,
                    'filter' => [
                        'from' => $date->format('Y-m-d'),
                        'to' => $date->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonCount(1, 'data.data')
        ->assertJsonPath('data.meta.current_page', 2);
});

test('list all proofs for today for current employee', function () {
    # prepare
    $tenant = Team::factory()
        ->enterprise()
        ->create(['active' => true]);

    $employee = Employee::factory()
        ->for($tenant)
        ->create(['is_active' => true]);
    $anotherEmployee = Employee::factory()
        ->for($tenant)
        ->create(['is_active' => true]);

    $date = today();
    $anotherDate = today()->subDay();

    Proof::factory()
        ->for($tenant)
        ->for($anotherEmployee)
        ->create(['status' => ProofStatus::Sent, 'created_at' => $date]);

    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Sent, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Missed, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Accepted, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Rejected, 'created_at' => $date]);

    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Sent, 'created_at' => $anotherDate]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Missed, 'created_at' => $anotherDate]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Accepted, 'created_at' => $anotherDate]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Rejected, 'created_at' => $anotherDate]);

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/proofs-of-date?' .
                http_build_query([
                    'filter' => [
                        'from' => $date->format('Y-m-d'),
                        'to' => $date->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonCount(4, 'data.data');
});

test('list only pending proofs of today for current employee', function () {
    # prepare
    $tenant = Team::factory()
        ->enterprise()
        ->create(['active' => true]);

    $employee = Employee::factory()
        ->for($tenant)
        ->create(['is_active' => true]);
    $anotherEmployee = Employee::factory()
        ->for($tenant)
        ->create(['is_active' => true]);

    $date = today();
    $anotherDate = today()->subDay();

    Proof::factory()
        ->for($tenant)
        ->for($anotherEmployee)
        ->create(['status' => ProofStatus::Sent, 'created_at' => $date]);

    $proof = Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Sent, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Missed, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Accepted, 'created_at' => $date]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Rejected, 'created_at' => $date]);

    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Sent, 'created_at' => $anotherDate]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Missed, 'created_at' => $anotherDate]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Accepted, 'created_at' => $anotherDate]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Rejected, 'created_at' => $anotherDate]);

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/proofs-of-date?' .
                http_build_query([
                    'filter' => [
                        'from' => $date->format('Y-m-d'),
                        'to' => $date->format('Y-m-d'),
                        'status' => [ProofStatus::Sent->value],
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonCount(1, 'data.data')
        ->assertJsonPath('data.data.0.id', $proof->id);
});
