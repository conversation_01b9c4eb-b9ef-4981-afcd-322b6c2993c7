<?php

use App\Enums\ProofMethod;
use App\Models\Employee;
use App\Notifications\CheckoutReminderNotification;
use App\Notifications\ProofAttendanceRequested;

test('endpoint requires auth', function () {
    # act & assert
    /* @see \App\Http\Controllers\Mobile\V2\DeleteAllNotificationsController */
    $this->deleteJson('/api/v2/mobile/notifications')->assertUnauthorized();

    /* @see \App\Http\Controllers\Mobile\V2\DeleteAllNotificationsController */
    $this->jwtActingAsMobile($this->user)
        ->deleteJson('/api/v2/mobile/notifications')
        ->assertOk();
});

test('delete all notification of employee when there are notifications', function () {
    # arrange
    /* @type Employee $employee */
    $employee = $this->user;

    // create notifications for employee
    $employee->notify(new CheckoutReminderNotification());
    $employee->notify(new ProofAttendanceRequested(ProofMethod::Manual));

    // mark one notification as read
    $employee->notifications()->first()->markAsRead();

    # act & assert
    /* @see \App\Http\Controllers\Mobile\V2\DeleteAllNotificationsController */
    $this->jwtActingAsMobile($employee)->deleteJson('/api/v2/mobile/notifications')->assertOk();

    expect($employee->notifications()->exists())->toBeFalse(
        'All notifications of employee should be deleted'
    );
});

test(
    'delete all notification of employee only and no notifications of another employee',
    function () {
        # arrange
        /* @type Employee $employee */
        $employee = $this->user;
        /* @type Employee $anotherEmployee */
        $anotherEmployee = Employee::factory()
            ->for($this->tenant)
            ->active()
            ->create();

        // create notifications for employee
        $employee->notify(new CheckoutReminderNotification());
        $employee->notify(new ProofAttendanceRequested(ProofMethod::Manual));

        // create notifications for another employee
        $anotherEmployee->notify(new CheckoutReminderNotification());
        $anotherEmployee->notify(new ProofAttendanceRequested(ProofMethod::Manual));

        # act & assert
        /* @see \App\Http\Controllers\Mobile\V2\DeleteAllNotificationsController */
        $this->jwtActingAsMobile($employee)->deleteJson('/api/v2/mobile/notifications')->assertOk();

        expect($employee->notifications()->exists())->toBeFalse(
            'All notifications of employee should be deleted'
        );
        expect($anotherEmployee->notifications()->exists())->toBeTrue(
            'Notifications of another employee should not be deleted'
        );
    }
);

test('delete all notification of employee when there are no notifications', function () {
    # arrange
    /* @type Employee $employee */
    $employee = $this->user;

    # act & assert
    /* @see \App\Http\Controllers\Mobile\V2\DeleteAllNotificationsController */
    $this->jwtActingAsMobile($employee)->deleteJson('/api/v2/mobile/notifications')->assertOk();

    expect($employee->notifications()->exists())->toBeFalse(
        'All notifications of employee should be deleted'
    );
});
