<?php

use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Models\Delegation;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Leave;

it('returns leave requests for a manager of employees', function () {
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $department = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    $employeeForAnotherManager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $leave = Leave::factory()
        ->for($this->tenant)
        ->for($department)
        ->for($employee)
        ->create(['status' => RequestStatus::Pending]);

    Leave::factory()
        ->for($this->tenant)
        ->for($employeeForAnotherManager)
        ->create(['status' => RequestStatus::Pending]);

    $this->jwtActingAsMobile($manager)
        ->getJson('/api/v2/mobile/leaves/pending')
        ->assertOk()
        ->assertJsonCount(1, 'data')
        ->assertJson([
            'data' => [
                [
                    'id' => $leave->id,
                    'employee_name' => $employee->name,
                ],
            ],
        ]);
});

it('returns no leave requests for a manager with leave delegation', function () {
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $department = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    $delegated = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    $employeeForAnotherManager = Employee::factory()
        ->for($this->tenant)
        ->create();

    Leave::factory()
        ->for($this->tenant)
        ->for($department)
        ->for($employee)
        ->create(['status' => RequestStatus::Pending]);

    Leave::factory()
        ->for($this->tenant)
        ->for($employeeForAnotherManager)
        ->create(['status' => RequestStatus::Pending]);

    Delegation::factory()->create([
        'type' => DelegationType::LeaveRequest,
        'delegated_id' => $delegated->id,
        'delegatee_id' => $manager->id,
    ]);

    $this->jwtActingAsMobile($manager)
        ->getJson('/api/v2/mobile/leaves/pending')
        ->assertOk()
        ->assertJsonCount(0, 'data');
});

it('returns leave requests for a delegated employee', function () {
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $department = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    $delegated = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    $employeeForAnotherManager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $leave = Leave::factory()
        ->for($this->tenant)
        ->for($department)
        ->for($employee)
        ->create(['status' => RequestStatus::Pending]);

    Leave::factory()
        ->for($this->tenant)
        ->for($employeeForAnotherManager)
        ->create(['status' => RequestStatus::Pending]);

    Delegation::factory()->create([
        'type' => DelegationType::LeaveRequest,
        'delegated_id' => $delegated->id,
        'delegatee_id' => $manager->id,
    ]);

    $this->jwtActingAsMobile($delegated)
        ->getJson('/api/v2/mobile/leaves/pending')
        ->assertOk()
        ->assertJsonCount(1, 'data')
        ->assertJson([
            'data' => [
                [
                    'id' => $leave->id,
                    'employee_name' => $employee->name,
                ],
            ],
        ]);
});
