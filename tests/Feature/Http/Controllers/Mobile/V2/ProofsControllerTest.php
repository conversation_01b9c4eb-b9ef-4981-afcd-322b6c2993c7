<?php

use App\Enums\ProofStatus;
use App\Models\Employee;
use App\Models\Proof;
use App\Models\Team;

test('list only pending proofs', function () {
    # prepare
    $tenant = Team::factory()->create(['active' => true]);
    $employee = Employee::factory()
        ->for($tenant)
        ->create(['is_active' => true]);
    $proof = Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Sent]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Missed]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Accepted]);
    Proof::factory()
        ->for($tenant)
        ->for($employee)
        ->create(['status' => ProofStatus::Rejected]);

    $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/proofs')
        ->assertOk()
        ->assertJsonCount(1, 'data')
        ->assertJsonPath('data.0.id', $proof->id);
});
