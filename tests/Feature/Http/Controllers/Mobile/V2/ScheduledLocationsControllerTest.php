<?php

use App\Enums\PreferredLanguage;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use Carbon\CarbonPeriodImmutable;

test('endpoint accessible authorized employees only', function () {
    $this->getJson('/api/v2/mobile/employees/scheduled-locations')->assertUnauthorized();

    $this->actingAs($this->user)
        ->getJson('/api/v2/mobile/employees/scheduled-locations')
        ->assertUnauthorized();
});

test('from and to parameters are required', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson('/api/v2/mobile/employees/scheduled-locations')
        ->assertStatus(422)
        ->assertJsonValidationErrors(['from' => 'required', 'to' => 'required']);
});

test('from and to parameters are expected to be dates', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query(['from' => 'abc', 'to' => 1])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['from' => 'not a valid date', 'to' => 'not a valid date']);
});

test('from parameter should be in the present of the future', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query(['from' => today()->subDay()->toDateString()])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['from' => 'must be a date after or equal to today']);

    // happy path - in the present
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => today()->toDateString(),
                ])
        )
        ->assertJsonMissingValidationErrors('from');

    // happy path - in the future
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => today()->addDay()->toDateString(),
                ])
        )
        ->assertJsonMissingValidationErrors(['from']);
});

test('to parameter should be later than from parameter', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => today()->toDateString(),
                    'to' => today()->toDateString(),
                ])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['to' => 'must be a date after from']);
});

test('the maximum period should not exceed 31 days', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()
        ->for($tenant)
        ->active()
        ->create(['preferred_language' => PreferredLanguage::English->value]);

    # act & assert
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => today()->toDateString(),
                    'to' => today()->addDays(35)->toDateString(),
                ])
        )
        ->assertStatus(422)
        ->assertJsonValidationErrors(['to' => 'date range exceed limit']);

    // happy path - period is exactly 31 days
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => today()->toDateString(),
                    'to' => today()->addDays(31)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJsonMissingValidationErrors(['from', 'to']);
});

test('returns locations scheduled in the passed period - with permanent location', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()->for($tenant)->active()->create();

    $period = CarbonPeriodImmutable::dates(today()->addDays(2), today()->addDays(33));

    $permanentLocation = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($permanentLocation, ['permanent' => true]);

    $temporaryLocationWithin = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationWithin, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->addDay()->format('Y-m-d'),
        'end_date' => $period->getEndDate()->subDay()->format('Y-m-d'),
    ]);

    $temporaryLocationStartedBefore = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationStartedBefore, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->subDay()->format('Y-m-d'),
        'end_date' => $period->getStartDate()->addDays(5)->format('Y-m-d'),
    ]);

    $temporaryLocationEndAfter = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationEndAfter, [
        'permanent' => false,
        'start_date' => $period->getEndDate()->subDays(5)->format('Y-m-d'),
        'end_date' => $period->getEndDate()->addDays(2)->format('Y-m-d'),
    ]);

    $temporaryLocationSurrounding = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationSurrounding, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->subDay()->format('Y-m-d'),
        'end_date' => $period->getEndDate()->addDay()->format('Y-m-d'),
    ]);

    $temporaryLocationExact = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationExact, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->format('Y-m-d'),
        'end_date' => $period->getEndDate()->format('Y-m-d'),
    ]);

    $overlappingTemporaryLocation = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($overlappingTemporaryLocation, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->format('Y-m-d'),
        'end_date' => $period->getStartDate()->addDays(5)->format('Y-m-d'),
    ]);
    $employee->locations()->attach($overlappingTemporaryLocation, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->addDays(3)->format('Y-m-d'),
        'end_date' => $period->getStartDate()->addDays(6)->format('Y-m-d'),
    ]);

    # act & assert

    // assert whole period
    $expectedWholePeriod = [
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDay()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(2)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // both permanent and temporary locations assigned - same location overlapping
        [
            'date' => $period->getStartDate()->addDays(3)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id], // same location overlapping
            ],
        ],
        // both permanent and temporary locations assigned - same location overlapping
        [
            'date' => $period->getStartDate()->addDays(4)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id], // same location overlapping
            ],
        ],
        // both permanent and temporary locations assigned - same location overlapping
        [
            'date' => $period->getStartDate()->addDays(5)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id], // same location overlapping
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(6)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(7)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(8)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(9)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(10)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(11)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(12)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(13)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(14)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(15)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(16)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(17)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(18)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(19)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(20)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(21)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(22)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(23)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(24)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(25)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(26)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(27)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(28)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(29)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(30)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getEndDate()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getStartDate()->toDateString(),
                    'to' => $period->getEndDate()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedWholePeriod]);

    // assert start of period
    $expectedStartOfPeriod = [
        // permanent location assigned only
        [
            'date' => $period->getStartDate()->subDays(2)->toDateString(),
            'locations' => [['id' => $permanentLocation->id]],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->subDay()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDay()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(2)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // both permanent and temporary locations assigned - same location overlapping
        [
            'date' => $period->getStartDate()->addDays(3)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id], // same location overlapping
            ],
        ],
        // both permanent and temporary locations assigned - same location overlapping
        [
            'date' => $period->getStartDate()->addDays(4)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id], // same location overlapping
            ],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getStartDate()->subDays(2)->toDateString(),
                    'to' => $period->getStartDate()->addDays(4)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedStartOfPeriod]);

    // assert end of period
    $expectedEndOfPeriod = [
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(27)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(28)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(29)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getStartDate()->addDays(30)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getEndDate()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getEndDate()->addDay()->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
            ],
        ],
        // both permanent and temporary locations assigned
        [
            'date' => $period->getEndDate()->addDays(2)->toDateString(),
            'locations' => [
                ['id' => $permanentLocation->id],
                ['id' => $temporaryLocationEndAfter->id],
            ],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getStartDate()->addDays(27)->toDateString(),
                    'to' => $period->getEndDate()->addDays(2)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedEndOfPeriod]);
});

test('returns locations scheduled in the passed period - without permanent location', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()->for($tenant)->active()->create();

    $period = CarbonPeriodImmutable::dates(today()->addDays(2), today()->addDays(33));

    $defaultLocation = Location::factory()->for($tenant)->default()->create();

    $notDefaultLocation = Location::factory()->for($tenant)->default()->create();

    $temporaryLocationWithin = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationWithin, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->addDay()->format('Y-m-d'),
        'end_date' => $period->getEndDate()->subDay()->format('Y-m-d'),
    ]);

    $temporaryLocationStartedBefore = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationStartedBefore, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->subDay()->format('Y-m-d'),
        'end_date' => $period->getStartDate()->addDays(5)->format('Y-m-d'),
    ]);

    $temporaryLocationEndAfter = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationEndAfter, [
        'permanent' => false,
        'start_date' => $period->getEndDate()->subDays(5)->format('Y-m-d'),
        'end_date' => $period->getEndDate()->addDays(2)->format('Y-m-d'),
    ]);

    $temporaryLocationSurrounding = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationSurrounding, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->subDay()->format('Y-m-d'),
        'end_date' => $period->getEndDate()->addDay()->format('Y-m-d'),
    ]);

    $temporaryLocationExact = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($temporaryLocationExact, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->format('Y-m-d'),
        'end_date' => $period->getEndDate()->format('Y-m-d'),
    ]);

    $overlappingTemporaryLocation = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($overlappingTemporaryLocation, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->format('Y-m-d'),
        'end_date' => $period->getStartDate()->addDays(5)->format('Y-m-d'),
    ]);
    $employee->locations()->attach($overlappingTemporaryLocation, [
        'permanent' => false,
        'start_date' => $period->getStartDate()->addDays(3)->format('Y-m-d'),
        'end_date' => $period->getStartDate()->addDays(6)->format('Y-m-d'),
    ]);

    # act & assert
    // temporary location assigned only
    $expectedTemporaryLocationsOnly = [
        // temporary locations assigned only
        [
            'date' => $period->getEndDate()->addDay()->toDateString(),
            'locations' => [
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
            ],
        ],
        // temporary location assigned only
        [
            'date' => $period->getEndDate()->addDays(2)->toDateString(),
            'locations' => [['id' => $temporaryLocationEndAfter->id]],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getEndDate()->addDay()->toDateString(),
                    'to' => $period->getEndDate()->addDays(2)->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedTemporaryLocationsOnly]);

    // no permanent location assigned - default location of tenant is set
    $expectedNoLocationAssigned = [
        // no location assigned - fall back to all locations of the tenant regardless of default location
        [
            'date' => $period->getStartDate()->subDays(2)->toDateString(),
            'locations' => [
                ['id' => $defaultLocation->id],
                ['id' => $notDefaultLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // only temporary locations assigned
        [
            'date' => $period->getStartDate()->subDay()->toDateString(),
            'locations' => [
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
            ],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getStartDate()->subDays(2)->toDateString(),
                    'to' => $period->getStartDate()->subDay()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedNoLocationAssigned]);

    // no permanent location assigned - default location of tenant is not set
    $defaultLocation->update(['is_default' => false]);

    $expectedNoLocationAssigned = [
        // no location assigned - fall back to all locations of the tenant regardless of default location
        [
            'date' => $period->getStartDate()->subDays(2)->toDateString(),
            'locations' => [
                ['id' => $defaultLocation->id],
                ['id' => $notDefaultLocation->id],
                ['id' => $temporaryLocationWithin->id],
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationEndAfter->id],
                ['id' => $temporaryLocationSurrounding->id],
                ['id' => $temporaryLocationExact->id],
                ['id' => $overlappingTemporaryLocation->id],
            ],
        ],
        // only temporary locations assigned
        [
            'date' => $period->getStartDate()->subDay()->toDateString(),
            'locations' => [
                ['id' => $temporaryLocationStartedBefore->id],
                ['id' => $temporaryLocationSurrounding->id],
            ],
        ],
    ];

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getStartDate()->subDays(2)->toDateString(),
                    'to' => $period->getStartDate()->subDay()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedNoLocationAssigned]);
});

test('returns locations scheduled in the passed period - no location assigned', function () {
    # arrange
    $tenant = Team::factory()->basic()->active()->create();
    $employee = Employee::factory()->for($tenant)->active()->create();

    $period = CarbonPeriodImmutable::dates(today()->addDays(2), today()->addDays(33));

    $defaultLocation = Location::factory()->for($tenant)->default()->create();
    $location = Location::factory()->for($tenant)->default()->create();

    # act & assert
    $expectedNoLocationAssigned = [
        // no location assigned - fall back to all locations of the tenant regardless of default location
        [
            'date' => $period->getStartDate()->subDays(2)->toDateString(),
            'locations' => [['id' => $defaultLocation->id], ['id' => $location->id]],
        ],
        // no location assigned - fall back to all locations of the tenant regardless of default location
        [
            'date' => $period->getStartDate()->subDay()->toDateString(),
            'locations' => [['id' => $defaultLocation->id], ['id' => $location->id]],
        ],
    ];

    // no permanent location assigned - default location of tenant is set
    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getStartDate()->subDays(2)->toDateString(),
                    'to' => $period->getStartDate()->subDay()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedNoLocationAssigned]);

    // no location assigned - default location of tenant is not set
    $defaultLocation->update(['is_default' => false]);

    $this->jwtActingAsMobile($employee)
        ->getJson(
            '/api/v2/mobile/employees/scheduled-locations?' .
                http_build_query([
                    'from' => $period->getStartDate()->subDays(2)->toDateString(),
                    'to' => $period->getStartDate()->subDay()->toDateString(),
                ])
        )
        ->assertOk()
        ->assertJson(['data' => $expectedNoLocationAssigned]);
});
