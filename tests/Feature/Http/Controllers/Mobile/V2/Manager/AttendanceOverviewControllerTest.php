<?php

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;

test('attendance overview - today stats', function () {
    $date = CarbonImmutable::now();

    $manager = createDefaultEmployee();

    $anotherManager = createDefaultEmployee();

    // test for another manager, it should not be included in the result
    Attendance::factory()
        ->present()
        ->for($this->tenant)
        ->for(createDefaultEmployee(['manager_id' => $anotherManager->id]))
        ->date($date)
        ->create(['active_until' => now()->addDay()]);

    // 5 employees, 1 is present, 1 is absent, 1 is on leave, 1 is yet, 1 is upcoming shift
    $attributes = ['manager_id' => $manager->id];

    Attendance::factory()
        ->present()
        ->for($this->tenant)
        ->for(createDefaultEmployee($attributes))
        ->date($date, shiftFrom: now()->subMinute())
        ->create(['active_until' => now()->addDay()]);

    Attendance::factory()
        ->absent()
        ->for($this->tenant)
        ->for(createDefaultEmployee($attributes))
        ->date($date, shiftFrom: now()->subMinute())
        ->create(['active_until' => now()->addDay()]);

    Attendance::factory()
        ->leave()
        ->for($this->tenant)
        ->for(createDefaultEmployee($attributes))
        ->date($date, shiftFrom: now()->subMinute())
        ->create(['active_until' => now()->addDay()]);

    // yet
    Attendance::factory()
        ->for($this->tenant)
        ->for(createDefaultEmployee($attributes))
        ->date($date, shiftFrom: now()->subMinute())
        ->create(['active_until' => now()->addDay()]);

    // upcoming shift
    Attendance::factory()
        ->for($this->tenant)
        ->for(createDefaultEmployee($attributes))
        ->date($date, shiftFrom: now()->addMinute())
        ->create(['active_until' => now()->addMinute()->addDay()]);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\AttendanceOverviewController */
    $response = $this->jwtActingAsMobile($manager)
        ->getJson('api/v2/mobile/manager/attendance-overview')
        ->assertOk();

    expect($response->json('data.today.total'))->toBe(5);
    expect($response->json('data.today.present'))->toBe(1);
    expect($response->json('data.today.absent'))->toBe(1);
    expect($response->json('data.today.leave'))->toBe(1);
    expect($response->json('data.today.yet'))->toBe(2); // 1 is yet, 1 is upcoming shift
    expect($response->json('data.today.upcoming_shift'))->toBe(1);
});

test('attendance overview - average working hours', function () {
    $period = CarbonPeriod::create('2021-08-01', '2021-08-03');
    $outsidePeriod = CarbonPeriod::create('2021-08-04', '2021-08-06');

    $manager = createDefaultEmployee();

    $anotherManager = createDefaultEmployee();

    $employeeAttributes = ['manager_id' => $manager->id];

    // case 1
    Attendance::factory()
        ->for($this->tenant)
        ->for(createDefaultEmployee($employeeAttributes))
        ->createFromPeriod(
            period: $period,
            statuses: [
                '2021-08-01' => '08:00-16:00', // Sunday - 8 hours
                '2021-08-02' => '08:00-15:00', // Monday - 7 hours,
                '2021-08-03' => '08:00-18:00', // Tuesday - 10 hours,
            ]
        );

    // case 2
    Attendance::factory()
        ->for($this->tenant)
        ->for(createDefaultEmployee($employeeAttributes))
        ->createFromPeriod(
            period: $period,
            statuses: [
                '2021-08-01' => '08:00-16:00', // Sunday - 8 hours
                '2021-08-02' => '08:00-15:00', // Monday - 7 hours,
                '2021-08-03' => '08:00-15:00', // Tuesday - 7 hours,
            ]
        );

    // case 3 - outside period should not be included
    Attendance::factory()
        ->for($this->tenant)
        ->for(createDefaultEmployee($employeeAttributes))
        ->createFromPeriod(
            period: $outsidePeriod,
            statuses: [
                '2021-08-04' => '08:00-16:00', // Sunday - 8 hours
                '2021-08-05' => '08:00-15:00', // Monday - 7 hours,
                '2021-08-06' => '08:00-15:00', // Tuesday - 7 hours,
            ]
        );

    // case 4 - absent should not be included
    Attendance::factory()
        ->for($this->tenant)
        ->for(createDefaultEmployee($employeeAttributes))
        ->createFromPeriod(
            period: $period,
            statuses: [
                '2021-08-01' => AttendanceStatus::ABSENT,
                '2021-08-02' => AttendanceStatus::ABSENT,
                '2021-08-03' => AttendanceStatus::ABSENT,
            ]
        );

    // case 5 - another manager's employee should not be included
    Attendance::factory()
        ->for($this->tenant)
        ->for(createDefaultEmployee(['manager_id' => $anotherManager->id]))
        ->createFromPeriod(
            period: $period,
            statuses: [
                '2021-08-01' => '08:00-16:00', // Sunday - 8 hours
                '2021-08-02' => '08:00-15:00', // Monday - 7 hours,
                '2021-08-03' => '08:00-15:00', // Tuesday - 7 hours,
            ]
        );

    /** @see \App\Http\Controllers\Mobile\V2\Manager\AttendanceOverviewController */
    $response = $this->jwtActingAsMobile($manager)
        ->getJson(
            'api/v2/mobile/manager/attendance-overview?' .
                http_build_query([
                    'filter' => [
                        'from' => $period->start->format('Y-m-d'),
                        'to' => $period->end->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk();

    expect($response->json('data.average_working_hours.sunday'))->toEqual(8);
    expect($response->json('data.average_working_hours.monday'))->toBe(7);
    expect($response->json('data.average_working_hours.tuesday'))->toBe(8.5);
    expect($response->json('data.average_working_hours.wednesday'))->toBe(0);
    expect($response->json('data.average_working_hours.thursday'))->toBe(0);
    expect($response->json('data.average_working_hours.friday'))->toBe(0);
    expect($response->json('data.average_working_hours.saturday'))->toBe(0);
});
