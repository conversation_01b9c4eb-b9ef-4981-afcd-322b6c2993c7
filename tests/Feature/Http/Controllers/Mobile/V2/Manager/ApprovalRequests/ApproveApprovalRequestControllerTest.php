<?php

use App\Enums\AttendanceStatus;
use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Events\ApprovalRequestApproved;
use App\Models\Activity;
use App\Models\ApprovalRequest;
use App\Models\Delegation;
use App\Models\Leave;
use App\Notifications\ApprovalRequestApprovedNotification;
use App\Notifications\GenericMobileNotification;
use App\Notifications\NewApprovalRequestNotification;
use App\Notifications\NewLeaveRequestNotification;

test('approve approval request - all', function (string $type) {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    $anotherManagerApprovalRequests = ApprovalRequest::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $alreadyApprovedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create(['updated_at' => null]);

    $rejectedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApproveApprovalRequestController */
    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/approval-requests/approve', ['all' => true])
        ->assertOk();

    $approvalRequests->each->refresh();

    $anotherManagerApprovalRequests->each->refresh();

    $rejectedApprovalRequest->refresh();

    $alreadyApprovedApprovalRequest->refresh();

    $approvalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Approved->value);
    });

    $anotherManagerApprovalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Pending->value);
    });

    expect($rejectedApprovalRequest->status)->toEqual(RequestStatus::Rejected->value);

    expect($alreadyApprovedApprovalRequest->updated_at)->toBeNull();
})->with('approval requests types');

test('approve approval request - ids', function (string $type) {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    $anotherManagerApprovalRequests = ApprovalRequest::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $alreadyApprovedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create(['updated_at' => null]);

    $rejectedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create();

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/approval-requests/approve', [
            'approval_requests' => $approvalRequests->pluck('id')->toArray(),
        ])
        ->assertOk();

    $approvalRequests->each->refresh();

    $anotherManagerApprovalRequests->each->refresh();

    $rejectedApprovalRequest->refresh();

    $alreadyApprovedApprovalRequest->refresh();

    $approvalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Approved->value);
    });

    $anotherManagerApprovalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Pending->value);
    });

    expect($rejectedApprovalRequest->status)->toEqual(RequestStatus::Rejected->value);

    expect($alreadyApprovedApprovalRequest->updated_at)->toBeNull();
})->with('approval requests types');

test(
    'approve approval request - accepting permission request should not update attendance record or have side-effect',
    function () {
        Event::fake([ApprovalRequestApproved::class]);
        Notification::fake();

        $manager = createDefaultEmployee();

        $employee = createDefaultEmployee(['manager_id' => $manager->id]);

        $permissionApprovalRequests = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employee)
            ->permission()
            ->count(3)
            ->create();

        $this->jwtActingAsMobile($manager)
            ->putJson('api/v2/mobile/manager/approval-requests/approve', [
                'approval_requests' => $permissionApprovalRequests->pluck('id')->toArray(),
            ])
            ->assertOk();

        $permissionApprovalRequests->each->refresh();

        $permissionApprovalRequests->each(function ($approvalRequest) {
            expect($approvalRequest->status)->toEqual(RequestStatus::Approved->value);
        });

        $employee->attendances->each(function ($attendance) {
            expect($attendance->status)->toEqual(AttendanceStatus::YET->value);
            expect($attendance->net_hours)->toBe('00:00');
            expect($attendance->check_in)->toBeNull();
            expect($attendance->check_out)->toBeNull();
            expect($attendance->in_type)->toBeNull();
            expect($attendance->out_type)->toBeNull();
        });

        Event::assertNotDispatched(ApprovalRequestApproved::class);
        Notification::assertNotSentTo($employee, ApprovalRequestApprovedNotification::class);
    }
);

test(
    'approve approval request - accepting regularization or remote work request should update attendance record and have side-effect',
    function (string $type, string $attendanceType) {
        Event::fake([ApprovalRequestApproved::class]);
        Notification::fake();

        $manager = createDefaultEmployee();
        $employee = createDefaultEmployee(['manager_id' => $manager->id]);

        $approvalRequests = createApprovalRequests(
            employee: $employee,
            status: RequestStatus::DeprecatedPending,
            type: $type,
            attendanceType: $attendanceType
        );

        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApproveApprovalRequestController */
        $this->jwtActingAsMobile($manager)
            ->putJson('api/v2/mobile/manager/approval-requests/approve', [
                'approval_requests' => $approvalRequests->pluck('id')->toArray(),
            ])
            ->assertOk();

        $approvalRequests->each->refresh();
        $employee->refresh();

        $approvalRequests->each(function ($approvalRequest) {
            expect($approvalRequest->status)->toEqual(RequestStatus::Approved->value);
        });

        $employee->attendances->each(function ($attendance) use ($attendanceType) {
            expect($attendance->status)->toEqual(AttendanceStatus::PRESENT->value);
            expect($attendance->net_hours->toTimeString())->toBe('08:00:00');

            $expectedCheckin = now()->setTime(8, 0, 0)->toTimeString();
            $expectedCheckout = now()->setTime(16, 0, 0)->toTimeString();

            if ($attendanceType === ApprovalRequest::CHECK_IN) {
                expect($attendance->check_in->toTimeString())->toEqual($expectedCheckin);
                expect($attendance->check_out)->toBeNull();
                expect($attendance->on_duty)->toBeTrue();
            }

            if ($attendanceType === ApprovalRequest::CHECK_OUT) {
                expect($attendance->check_in)->toBeNull();
                expect($attendance->check_out->toTimeString())->toEqual($expectedCheckout);
            }

            if ($attendanceType === ApprovalRequest::CHECK_IN_OUT) {
                expect($attendance->check_in->toTimeString())->toEqual($expectedCheckin);
                expect($attendance->check_out->toTimeString())->toEqual($expectedCheckout);
            }

            if ($attendanceType === ApprovalRequest::REGULARIZATION) {
                expect($attendance->in_type)->toBe(Activity::REGULATED_CHECK_IN);
                expect($attendance->out_type)->toBe(Activity::REGULATED_CHECK_OUT);
                expect($attendance->is_adjusted)->toBeTrue();
            }

            if ($attendanceType === ApprovalRequest::REMOTE_WORK) {
                expect($attendance->in_type)->toBe(Activity::REMOTE_CHECK_IN);
                expect($attendance->out_type)->toBe(Activity::REMOTE_CHECK_OUT);
            }
        });

        Event::assertDispatched(ApprovalRequestApproved::class);
        Notification::assertSentTo($employee, ApprovalRequestApprovedNotification::class);
    }
)
    ->with([ApprovalRequest::REGULARIZATION, ApprovalRequest::REMOTE_WORK])
    ->with([ApprovalRequest::CHECK_IN, ApprovalRequest::CHECK_OUT, ApprovalRequest::CHECK_IN_OUT]);

test('approve approval request - delegate can approve', function (string $type) {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequestDelegateEmployee = createDefaultEmployee();

    Delegation::create([
        'delegated_id' => $approvalRequestDelegateEmployee->id,
        'delegatee_id' => $manager->id,
        'type' => match ($type) {
            ApprovalRequest::REGULARIZATION => DelegationType::RegularizationRequest,
            ApprovalRequest::PERMISSION => DelegationType::PermissionRequest,
            ApprovalRequest::REMOTE_WORK => DelegationType::RemoteWorkRequest,
        },
    ]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    $alreadyApprovedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $rejectedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($approvalRequestDelegateEmployee)
        ->putJson('api/v2/mobile/manager/approval-requests/approve', [
            'approval_requests' => $approvalRequests->pluck('id')->toArray(),
        ])
        ->assertOk();

    $approvalRequests->each->refresh();

    $approvalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Approved->value);
    });

    expect($alreadyApprovedApprovalRequest->status)->toEqual(RequestStatus::Approved);

    expect($rejectedApprovalRequest->updated_at)->toBeNull();
})->with('approval requests types');

test('approve approval request - pending request notification should be deleted', function (
    string $type
) {
    # arrange
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    // send approval request pending notifications to manager
    $approvalRequests->each(function ($approvalRequest) use ($manager) {
        $manager->notify(new NewApprovalRequestNotification($approvalRequest));
    });

    // send unrelated notification
    $manager->notify(new GenericMobileNotification('subject', 'body'));
    // send notification where only notification type match
    $employee->notify(
        new NewApprovalRequestNotification(ApprovalRequest::factory()->create(['type' => $type]))
    );
    // send notification where only data.payload.id match
    $manager->notify(
        new NewLeaveRequestNotification(
            Leave::factory()->create(['id' => $approvalRequests->firstOrFail()->id])
        )
    );

    # act
    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/approval-requests/approve', ['all' => true])
        ->assertOk();

    // assert approval request pending notifications for manager are deleted
    $approvalRequests->each(function ($approvalRequest) use ($manager) {
        expect(
            $manager
                ->notifications()
                ->where('type', NewApprovalRequestNotification::class)
                ->where('data->payload->id', $approvalRequest->id)
                ->doesntExist()
        )->toBeTrue(
            'pending request notification for approval request ' .
                $approvalRequest->id .
                ' should be deleted'
        );
    });

    // assert unrelated notification is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', GenericMobileNotification::class)
            ->exists()
    )->toBeTrue('unrelated notifications should not be deleted');

    // assert only notification type match is not deleted
    expect(
        $employee
            ->notifications()
            ->where('type', NewApprovalRequestNotification::class)
            ->exists()
    )->toBeTrue('only notification type match should not be deleted');

    // assert where only data.payload.id match is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', NewLeaveRequestNotification::class)
            ->where('data->payload->id', $approvalRequests->firstOrFail()->id)
            ->exists()
    )->toBeTrue('only data.payload.id match should not be deleted');
})->with('approval requests types');
