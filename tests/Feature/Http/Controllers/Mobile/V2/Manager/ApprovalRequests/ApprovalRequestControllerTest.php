<?php

use App\Enums\ApprovalType;
use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Delegation;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Services\ApproveApprovalRequestService;
use App\Services\RejectApprovalRequestService;

it(
    'one-layer system - returns approval requests for a direct manager of employees with no regularization delegation',
    function () {
        $manager = createDefaultEmployee();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = createDefaultEmployee(['manager_id' => $manager->id]);

        $employeeForAnotherManager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $response = $this->jwtActingAsMobile($manager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');

        expect($response->json('data.data.*.id'))->toEqual([$approvalRequest->id]);
    }
);

it(
    'one-layer system - returns approval requests for a department manager of employees with no regularization delegation',
    function () {
        $manager = createDefaultEmployee();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = createDefaultEmployee(['department_id' => $department->id]);

        $anotherDepartment = Department::factory()
            ->for($this->tenant)
            ->create();

        $employeeForAnotherDepartment = Employee::factory()
            ->for($this->tenant)
            ->for($anotherDepartment)
            ->create();

        // 1. a regularization approval request for the employee (should be included in the response)
        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        // 2. a regularization approval request for another employee (should not be included in the response)
        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($anotherDepartment)
            ->for($employeeForAnotherDepartment)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $response = $this->jwtActingAsMobile($manager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');

        expect($response->json('data.data.*.id'))->toEqual([$approvalRequest->id]);
    }
);

it(
    'one-layer system - returns no approval requests for a manager with only regularization requests delegated',
    function () {
        # prepare
        $manager = createDefaultEmployee();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = createDefaultEmployee(['manager_id' => $manager->id]);

        $delegatedEmployee = createDefaultEmployee(['manager_id' => $manager->id]);

        $employeeForAnotherManager = createDefaultEmployee();

        // 1. a regularization approval request for the employee (should not be included in the response)
        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        // 2. a permission approval request for the employee (should be included in the response)
        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        // 3. a  regularization approval request for another employee (should not be included in the response)
        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        // 4. a permission approval request for another employee (should be included in the response)
        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        Delegation::factory()->create([
            'type' => DelegationType::RegularizationRequest,
            'delegated_id' => $delegatedEmployee->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $response = $this->jwtActingAsMobile($manager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');

        expect($response->json('data.data.*.id'))->toEqual([$approvalRequest->id]);
    }
);

it(
    'one-layer system - returns no approval requests for a manager with all requests delegation',
    function () {
        # prepare
        $manager = createDefaultEmployee();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = createDefaultEmployee(['manager_id' => $manager->id]);

        $delegatedEmployee = createDefaultEmployee(['manager_id' => $manager->id]);

        $employeeForAnotherManager = createDefaultEmployee();

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employeeForAnotherManager)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        Delegation::factory()->create([
            'type' => DelegationType::RegularizationRequest,
            'delegated_id' => $delegatedEmployee->id,
            'delegatee_id' => $manager->id,
        ]);
        Delegation::factory()->create([
            'type' => DelegationType::PermissionRequest,
            'delegated_id' => $delegatedEmployee->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $this->jwtActingAsMobile($manager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(0, 'data.data');
    }
);

it(
    'one-layer system - returns approval requests for a delegated employee with regularization requests only delegation',
    function () {
        # prepare
        $manager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $department = Department::factory()
            ->for($this->tenant)
            ->for($manager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $manager->id]);

        $delegatedEmployee = Employee::factory()
            ->for($this->tenant)
            ->create(['manager_id' => $manager->id]);

        $employeeForAnotherManager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $approvalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($department)
            ->for($employee)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create(['status' => ApprovalRequest::PENDING, 'type' => ApprovalRequest::PERMISSION]);

        Delegation::factory()->create([
            'type' => DelegationType::RegularizationRequest,
            'delegated_id' => $delegatedEmployee->id,
            'delegatee_id' => $manager->id,
        ]);

        # act
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $response = $this->jwtActingAsMobile($delegatedEmployee)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');

        expect($response->json('data.data.*.id'))->toEqual([$approvalRequest->id]);
    }
);

it(
    'one-layer system - returns approval requests for a delegated employee who is also a manager',
    function () {
        // 1. a manager of is delegated
        $delegatedManager = createDefaultEmployee();

        $departmentOfDelegatedManager = createDefaultDepartment([
            'manager_id' => $delegatedManager->id,
        ]);

        // 2. a manager of the manager is the delegatee
        $delegateeManager = createDefaultEmployee();

        $departmentOfDelegateeManager = createDefaultDepartment([
            'manager_id' => $delegateeManager->id,
        ]);

        // 3. an employee who belongs to the delegatee manager
        $employeeForDelegateeManager = createDefaultEmployee([
            'manager_id' => $delegateeManager->id,
        ]);

        // 4. an employee who belongs to the delegatee manager
        $employeeForDelegatedManager = createDefaultEmployee([
            'manager_id' => $delegatedManager->id,
        ]);

        // 5. another employee who should not be included in the response
        $employeeForAnotherManager = createDefaultEmployee();

        // 6. an approval request for the employee who belongs to the delegated manager - included in response
        $approvalRequest1 = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($departmentOfDelegatedManager)
            ->for($employeeForDelegatedManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        // 7. an approval request for the employee who belongs to the delegatee manager - included in the response
        $approvalRequest2 = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($departmentOfDelegatedManager)
            ->for($employeeForDelegateeManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        // 8. an approval request for the employee who belongs to another manager - not included in the response
        ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employeeForAnotherManager)
            ->create([
                'status' => ApprovalRequest::PENDING,
                'type' => ApprovalRequest::REGULARIZATION,
            ]);

        Delegation::factory()->create([
            'type' => DelegationType::RegularizationRequest,
            'delegated_id' => $delegatedManager->id,
            'delegatee_id' => $delegateeManager->id,
        ]);

        # act
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $response = $this->jwtActingAsMobile($delegatedManager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(2, 'data.data');

        expect($response->json('data.data.*.id'))->toEqual([
            $approvalRequest1->id,
            $approvalRequest2->id,
        ]);
    }
);

it(
    'two-layer system - direct manager can see initial approval request but can not see it after decided',
    function (RequestStatus $requestStatus) {
        $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);

        $directManager = Employee::factory()->for($team)->create();

        $departmentManager = Employee::factory()->for($team)->create();

        $department = Department::factory()
            ->for($team)
            ->for($departmentManager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($team)
            ->for($department)
            ->create(['manager_id' => $directManager->id]);

        $approvalRequest = ApprovalRequest::factory()
            ->for($team)
            ->for($department)
            ->for($employee)
            ->pending()
            ->create();

        // initial approval request - can see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $this->jwtActingAsMobile($directManager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');

        match ($requestStatus) {
            RequestStatus::Approved => (new ApproveApprovalRequestService(
                approvalRequest: $approvalRequest,
                decider: $directManager
            ))->handle(),
            RequestStatus::Rejected => (new RejectApprovalRequestService(
                approvalRequest: $approvalRequest,
                rejectionReason: 'Rejected',
                decider: $directManager
            ))->handle(),
            default => '',
        };

        // approved approval request - can not see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $this->jwtActingAsMobile($directManager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(0, 'data.data');
    }
)->with([RequestStatus::Approved, RequestStatus::Rejected]);

it(
    'two-layer system - department manager can not see initial approval request but can see it after decided',
    function (RequestStatus $requestStatus) {
        $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);

        $directManager = Employee::factory()->for($team)->create();

        $departmentManager = Employee::factory()->for($team)->create();

        $department = Department::factory()
            ->for($team)
            ->for($departmentManager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($team)
            ->for($department)
            ->create(['manager_id' => $directManager->id]);

        $approvalRequest = ApprovalRequest::factory()
            ->for($team)
            ->for($department)
            ->for($employee)
            ->pending()
            ->create();

        // initial approval request - can see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $this->jwtActingAsMobile($departmentManager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(0, 'data.data');

        match ($requestStatus) {
            RequestStatus::Approved => (new ApproveApprovalRequestService(
                approvalRequest: $approvalRequest,
                decider: $directManager
            ))->handle(),
            RequestStatus::Rejected => (new RejectApprovalRequestService(
                approvalRequest: $approvalRequest,
                rejectionReason: 'Rejected',
                decider: $directManager
            ))->handle(),
            default => '',
        };

        // approved approval request - can not see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\ApprovalRequests\ApprovalRequestController */
        $this->jwtActingAsMobile($departmentManager)
            ->getJson('/api/v2/mobile/manager/approval-requests')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');
    }
)->with([RequestStatus::Approved, RequestStatus::Rejected]);
