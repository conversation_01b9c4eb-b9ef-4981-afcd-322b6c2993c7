<?php

use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Events\ApprovalRequestRejected;
use App\Models\ApprovalRequest;
use App\Models\Delegation;
use App\Models\Leave;
use App\Notifications\ApprovalRequestRejectedNotification;
use App\Notifications\GenericMobileNotification;
use App\Notifications\NewApprovalRequestNotification;
use App\Notifications\NewLeaveRequestNotification;

test('reject approval request - all', function (string $type) {
    Event::fake([ApprovalRequestRejected::class]);
    Notification::fake();

    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    $anotherManagerApprovalRequests = ApprovalRequest::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $approvedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $alreadyRejectedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/approval-requests/reject', [
            'all' => true,
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    $approvalRequests->each->refresh();

    $anotherManagerApprovalRequests->each->refresh();

    $alreadyRejectedApprovalRequest->refresh();

    $approvedApprovalRequest->refresh();

    $approvalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Rejected->value);
        expect($approvalRequest->rejection_reason)->toBe('test');
    });

    $anotherManagerApprovalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Pending->value);
    });

    expect($alreadyRejectedApprovalRequest->updated_at)->toBeNull();

    expect($approvedApprovalRequest->status)->toBe(RequestStatus::Approved->value);

    Event::assertDispatched(ApprovalRequestRejected::class);
    Notification::assertSentTo($employee, ApprovalRequestRejectedNotification::class);
})->with('approval requests types');

test('reject approval request - ids', function (string $type) {
    Event::fake([ApprovalRequestRejected::class]);
    Notification::fake();

    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    $anotherManagerApprovalRequests = ApprovalRequest::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $approvedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $alreadyRejectedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/approval-requests/reject', [
            'approval_requests' => $approvalRequests->pluck('id')->toArray(),
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    $approvalRequests->each->refresh();

    $anotherManagerApprovalRequests->each->refresh();

    $alreadyRejectedApprovalRequest->refresh();

    $approvedApprovalRequest->refresh();

    $approvalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Rejected->value);
        expect($approvalRequest->rejection_reason)->toBe('test');
    });

    $anotherManagerApprovalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Pending->value);
    });

    expect($alreadyRejectedApprovalRequest->updated_at)->toBeNull();

    expect($approvedApprovalRequest->status)->toBe(RequestStatus::Approved->value);

    Event::assertDispatched(ApprovalRequestRejected::class);
    Notification::assertSentTo($employee, ApprovalRequestRejectedNotification::class);
})->with('approval requests types');

test('reject approval request - delegate can approve', function (string $type) {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequestDelegateEmployee = createDefaultEmployee();

    Delegation::create([
        'delegated_id' => $approvalRequestDelegateEmployee->id,
        'delegatee_id' => $manager->id,
        'type' => match ($type) {
            ApprovalRequest::REGULARIZATION => DelegationType::RegularizationRequest,
            ApprovalRequest::PERMISSION => DelegationType::PermissionRequest,
            ApprovalRequest::REMOTE_WORK => DelegationType::RemoteWorkRequest,
        },
    ]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    $approvedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $alreadyRejectedApprovalRequest = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($approvalRequestDelegateEmployee)
        ->putJson('api/v2/mobile/manager/approval-requests/reject', [
            'approval_requests' => $approvalRequests->pluck('id')->toArray(),
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    $approvalRequests->each->refresh();

    $approvedApprovalRequest->refresh();

    $alreadyRejectedApprovalRequest->refresh();

    $approvalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)->toEqual(RequestStatus::Rejected->value);
        expect($approvalRequest->rejection_reason)->toBe('test');
    });

    expect($approvedApprovalRequest->status)->toEqual(RequestStatus::Approved->value);

    expect($alreadyRejectedApprovalRequest->updated_at)->toBeNull();
})->with('approval requests types');

test('reject approval request - not pending approval requests can not be approved', function (
    string $type
) {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::Approved, $type);

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/approval-requests/reject', [
            'approval_requests' => $approvalRequests->pluck('id')->toArray(),
            'rejection_reason' => 'test',
        ])
        ->assertBadRequest();

    $approvalRequests->each->refresh();

    $approvalRequests->each(function ($approvalRequest) {
        expect($approvalRequest->status)
            ->not()
            ->toEqual(RequestStatus::Rejected->value);
    });
})->with('approval requests types');

test('reject approval request - pending request notification should be deleted', function (
    string $type
) {
    # arrange
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $approvalRequests = createApprovalRequests($employee, RequestStatus::DeprecatedPending, $type);

    // send approval request pending notifications to manager
    $approvalRequests->each(function ($approvalRequest) use ($manager) {
        $manager->notify(new NewApprovalRequestNotification($approvalRequest));
    });

    // send unrelated notification
    $manager->notify(new GenericMobileNotification('subject', 'body'));
    // send notification where only notification type match
    $employee->notify(
        new NewApprovalRequestNotification(ApprovalRequest::factory()->create(['type' => $type]))
    );
    // send notification where only data.payload.id match
    $manager->notify(
        new NewLeaveRequestNotification(
            Leave::factory()->create(['id' => $approvalRequests->firstOrFail()->id])
        )
    );

    # act
    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/approval-requests/reject', [
            'all' => true,
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    // assert approval request pending notifications for manager are deleted
    $approvalRequests->each(function ($approvalRequest) use ($manager) {
        expect(
            $manager
                ->notifications()
                ->where('type', NewApprovalRequestNotification::class)
                ->where('data->payload->id', $approvalRequest->id)
                ->doesntExist()
        )->toBeTrue(
            'pending request notification for approval request ' .
                $approvalRequest->id .
                ' should be deleted'
        );
    });

    // assert unrelated notification is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', GenericMobileNotification::class)
            ->exists()
    )->toBeTrue('unrelated notifications should not be deleted');

    // assert only notification type match is not deleted
    expect(
        $employee
            ->notifications()
            ->where('type', NewApprovalRequestNotification::class)
            ->exists()
    )->toBeTrue('only notification type match notifications should not be deleted');

    // assert where only data.payload.id match is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', NewLeaveRequestNotification::class)
            ->where('data->payload->id', $approvalRequests->firstOrFail()->id)
            ->exists()
    )->toBeTrue('only data.payload.id match notifications should not be deleted');
})->with('approval requests types');
