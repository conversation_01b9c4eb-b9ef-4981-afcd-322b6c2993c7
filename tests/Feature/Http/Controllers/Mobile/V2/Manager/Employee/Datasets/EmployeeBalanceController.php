<?php

use Carbon\CarbonPeriod;

dataset('permission request', function () {
    $travelTo = today()->startOfMonth()->addDays(15)->toImmutable();

    return [
        'no limit is set - no permission requests in database - limit not reached' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => null,
            'dailyLimit' => null,
            'pendingPermissionDates' => [],
            'approvedPermissionDates' => [],
            'expectedMonthlyBalance' => null,
            'expectedDailyBalance' => null,
            'expectedCurrentBalance' => null,
        ],
        'limits are set to zero is the same as no limit - limit not reached' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => 0,
            'dailyLimit' => 0,
            'pendingPermissionDates' => [],
            'approvedPermissionDates' => [],
            'expectedMonthlyBalance' => null,
            'expectedDailyBalance' => null,
            'expectedCurrentBalance' => null,
        ],
        'no limit is set - with permission requests in database - limit not reached' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => null,
            'dailyLimit' => null,
            'pendingPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(6, 59), $travelTo->setTime(7, 59)),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates(
                    $travelTo->subDays(3)->setTime(6, 59),
                    $travelTo->subDays(3)->setTime(7, 59)
                ),
            ],
            'expectedMonthlyBalance' => null,
            'expectedDailyBalance' => null,
            'expectedCurrentBalance' => null,
        ],
        'only daily limit is set - limit not exceeded' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => null,
            'dailyLimit' => 3,
            'pendingPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(7, 0), $travelTo->setTime(8, 0)),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(9, 0), $travelTo->setTime(9, 59, 58)),
                CarbonPeriod::dates(
                    $travelTo->subDay()->setTime(6, 00),
                    $travelTo->subDay()->setTime(10, 0)
                ),
            ],
            'expectedMonthlyBalance' => null,
            'expectedDailyBalance' => '01:00:02',
            'expectedCurrentBalance' => '01:00:02',
        ],
        'only monthly limit is set - limit not exceeded' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => 3,
            'dailyLimit' => null,
            'pendingPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(6, 59), $travelTo->setTime(7, 59)),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates(
                    $travelTo->startOfMonth()->setTime(8, 59),
                    $travelTo->startOfMonth()->setTime(9, 58, 58)
                ),
                CarbonPeriod::dates(
                    $travelTo->subMonth()->setTime(6, 00),
                    $travelTo->subMonth()->setTime(10, 0)
                ),
            ],
            'expectedMonthlyBalance' => '01:00:02',
            'expectedDailyBalance' => null,
            'expectedCurrentBalance' => '01:00:02',
        ],

        'only daily limit is set - limit exceeded' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => null,
            'dailyLimit' => 3,
            'pendingPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(7, 0), $travelTo->setTime(8, 0)),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(9, 0), $travelTo->setTime(11, 0)),
            ],
            'expectedMonthlyBalance' => null,
            'expectedDailyBalance' => '00:00:00',
            'expectedCurrentBalance' => '00:00:00',
        ],
        'only monthly limit is set - limit exceeded' => [
            'travelTo' => $travelTo->endOfMonth(),
            'monthlyLimit' => 2,
            'dailyLimit' => null,
            'pendingPermissionDates' => [
                CarbonPeriod::dates(
                    $travelTo->endOfMonth()->setTime(7, 0),
                    $travelTo->endOfMonth()->setTime(8, 0)
                ),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates(
                    $travelTo->startOfMonth()->setTime(9, 0),
                    $travelTo->startOfMonth()->setTime(10, 0)
                ),
            ],
            'expectedMonthlyBalance' => '00:00:00',
            'expectedDailyBalance' => null,
            'expectedCurrentBalance' => '00:00:00',
        ],
        'both monthly and daily limits are set - only daily limit exceeded' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => 12,
            'dailyLimit' => 3,
            'pendingPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(7, 0), $travelTo->setTime(8, 0)),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(9, 0), $travelTo->setTime(11, 0)),
                CarbonPeriod::dates(
                    $travelTo->subDay()->setTime(9, 0),
                    $travelTo->subDay()->setTime(10, 30)
                ),
            ],
            'expectedMonthlyBalance' => '07:30:00',
            'expectedDailyBalance' => '00:00:00',
            'expectedCurrentBalance' => '00:00:00',
        ],
        'both monthly and daily limits are set - only monthly limit exceeded' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => 6,
            'dailyLimit' => 5,
            'pendingPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(7, 0), $travelTo->setTime(8, 30, 29)),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(9, 0), $travelTo->setTime(11, 0)),
                CarbonPeriod::dates(
                    $travelTo->startOfMonth()->setTime(7, 0),
                    $travelTo->startOfMonth()->setTime(9, 29, 31)
                ),
            ],
            'expectedMonthlyBalance' => '00:00:00',
            'expectedDailyBalance' => '01:29:31',
            'expectedCurrentBalance' => '00:00:00',
        ],
        'both monthly and daily limits are set - both daily and monthly limits are exceeded' => [
            'travelTo' => $travelTo,
            'monthlyLimit' => 5,
            'dailyLimit' => 4,
            'pendingPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(7, 0), $travelTo->setTime(9, 0)),
            ],
            'approvedPermissionDates' => [
                CarbonPeriod::dates($travelTo->setTime(11, 0), $travelTo->setTime(13, 0)),
                CarbonPeriod::dates(
                    $travelTo->startOfMonth()->setTime(7, 0),
                    $travelTo->startOfMonth()->setTime(8, 0)
                ),
            ],
            'expectedMonthlyBalance' => '00:00:00',
            'expectedDailyBalance' => '00:00:00',
            'expectedCurrentBalance' => '00:00:00',
        ],
    ];
});
