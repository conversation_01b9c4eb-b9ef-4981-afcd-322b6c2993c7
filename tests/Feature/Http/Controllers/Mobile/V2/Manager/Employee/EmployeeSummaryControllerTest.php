<?php

use App\Models\Attendance;
use Carbon\CarbonPeriod;

it('return employee summary - no date', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $startDate = now()->startOfMonth();
    $endDate = now()->endOfMonth();

    Attendance::factory()
        ->for($employee)
        ->createFromPeriod(CarbonPeriod::create($startDate, $endDate));

    $response = $this->jwtActingAsMobile($manager)
        ->getJson("api/v2/mobile/manager/employee-summary/$employee->id")
        ->assertOk();

    // todo: assert response data
});

it('return employee summary - date', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $startDate = now()->startOfMonth();
    $endDate = now()->endOfMonth();

    Attendance::factory()
        ->for($employee)
        ->createFromPeriod(CarbonPeriod::create($startDate, $endDate));

    $response = $this->jwtActingAsMobile($manager)
        ->getJson(
            "api/v2/mobile/manager/employee-summary/$employee->id?" .
                http_build_query(['date' => $startDate->format('Y-m')])
        )
        ->assertOk();

    // todo: assert response data
});

it('return employee summary - employee for another manager return forbidden', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee();

    $this->jwtActingAsMobile($manager)
        ->getJson("api/v2/mobile/manager/employee-summary/$employee->id")
        ->assertForbidden();
});
