<?php

use App\Models\Employee;
use App\Models\Shift;
use App\Models\Team;
use Carbon\CarbonPeriod;

it('detach a permanent shift from employee by his direct manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentShift = createDefaultShift();
    $temporaryShift = createDefaultShift();

    $managedEmployee = Employee::factory()
        ->directManager($manager)
        ->for($this->tenant)
        ->temporaryShift($temporaryShift, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentShift($permanentShift)
        ->create();

    $assignedPermanentShiftID = $managedEmployee
        ->shifts()
        ->wherePivot('permanent', true)
        ->firstOrFail()->pivot->id;

    assert(!is_null($assignedPermanentShiftID), 'this variable must be a valid ID');

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/shifts/$assignedPermanentShiftID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->shifts()->count())->toBe(1);
    expect($managedEmployee->shifts()->first()->id)->toBe($temporaryShift->id);
});

it('detach a permanent shift from employee by his department manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentShift = createDefaultShift();
    $temporaryShift = createDefaultShift();

    $managedEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for($this->tenant)
        ->temporaryShift($temporaryShift, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentShift($permanentShift)
        ->create();

    $assignedPermanentShiftID = $managedEmployee
        ->shifts()
        ->wherePivot('permanent', true)
        ->firstOrFail()->pivot->id;

    assert(!is_null($assignedPermanentShiftID), 'this variable must be a valid ID');

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/shifts/$assignedPermanentShiftID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->shifts()->count())->toBe(1);
    expect($managedEmployee->shifts()->first()->id)->toBe($temporaryShift->id);
});

it('detach a temporary shift from employee by his direct manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentShift = createDefaultShift();
    $temporaryShift = createDefaultShift();

    $managedEmployee = Employee::factory()
        ->directManager($manager)
        ->for($this->tenant)
        ->temporaryShift($temporaryShift, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentShift($permanentShift)
        ->create();

    $assignedTemporaryShiftID = $managedEmployee
        ->shifts()
        ->wherePivot('permanent', false)
        ->firstOrFail()->pivot->id;

    assert(!is_null($assignedTemporaryShiftID), 'this variable must be a valid ID');

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/shifts/$assignedTemporaryShiftID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->shifts()->count())->toBe(1);
    expect($managedEmployee->shifts()->first()->id)->toBe($permanentShift->id);
});

it('detach a temporary shift from employee by his department manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentShift = createDefaultShift();
    $temporaryShift = createDefaultShift();

    $managedEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for($this->tenant)
        ->temporaryShift($temporaryShift, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentShift($permanentShift)
        ->create();

    $assignedTemporaryShiftID = $managedEmployee
        ->shifts()
        ->wherePivot('permanent', false)
        ->firstOrFail()->pivot->id;

    assert(!is_null($assignedTemporaryShiftID), 'this variable must be a valid ID');

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/shifts/$assignedTemporaryShiftID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->shifts()->count())->toBe(1);
    expect($managedEmployee->shifts()->first()->id)->toBe($permanentShift->id);
});

it('detach a shift from employee by direct manager when department manager exists', function () {
    // arrange
    $directManager = createDefaultEmployee();
    $departmentManager = createDefaultEmployee();

    $managedEmployee = Employee::factory()
        ->directManager($directManager)
        ->managerByDepartment($departmentManager)
        ->for($this->tenant)
        ->permanentShift(createDefaultShift())
        ->create();

    $assignedShiftID = $managedEmployee->shifts()->firstOrFail()->pivot->id;

    assert(!is_null($assignedShiftID), 'this variable must be a valid ID');

    // act
    $this->jwtActingAsMobile($directManager)
        ->deleteJson("api/v2/mobile/manager/employees/$managedEmployee->id/shifts/$assignedShiftID")
        ->assertOk();

    // assert
    expect($managedEmployee->shifts()->count())->toBe(0);
});

it('detach a shift from employee by manager - shift assignment should exists', function () {
    // arrange
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->managerByDepartment($manager)
        ->for($this->tenant)
        ->create();

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson("api/v2/mobile/manager/employees/$employee->id/shifts/99999")
        ->assertNotFound();

    // assert
    expect($employee->shifts()->count())->toBe(0);
});

it(
    "detach a shift from employee by manager - manager cannot detach a shift from other managers' employees",
    function () {
        // arrange
        $manager = createDefaultEmployee();
        $anotherManager = createDefaultEmployee();

        $permanentShift = createDefaultShift();

        $employee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentShift($permanentShift)
            ->create();
        $anotherManagerEmployee = Employee::factory()
            ->managerByDepartment($anotherManager)
            ->for($this->tenant)
            ->permanentShift($permanentShift)
            ->create();

        $assignedShiftID = $anotherManagerEmployee->shifts()->firstOrFail()->pivot->id;

        assert(!is_null($assignedShiftID), 'this variable must be a valid ID');

        // act
        $this->jwtActingAsMobile($manager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$anotherManagerEmployee->id/shifts/$assignedShiftID"
            )
            ->assertForbidden();

        // assert
        expect($employee->shifts()->count())->toBe(1);
        expect($anotherManagerEmployee->shifts()->count())->toBe(1);
    }
);

it(
    'detach a shift from employee by manager - manager cannot detach a shift from an employee in another tenant',
    function () {
        // arrange
        $anotherTenant = Team::factory()->active()->basic()->create();

        $manager = createDefaultEmployee();

        $employee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentShift(createDefaultShift())
            ->create();

        $anotherTenantEmployee = Employee::factory()
            ->for($anotherTenant)
            ->permanentShift(Shift::factory()->for($anotherTenant)->create())
            ->create();

        $assignedShiftID = $anotherTenantEmployee->shifts()->firstOrFail()->pivot->id;

        assert(!is_null($assignedShiftID), 'this variable must be a valid ID');

        // act
        $this->jwtActingAsMobile($manager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$anotherTenantEmployee->id/shifts/$assignedShiftID"
            )
            ->assertNotFound();

        // assert
        expect($employee->shifts()->count())->toBe(1);
    }
);

it(
    'detach a shift from employee by manager - shift assignment should belong to the employee',
    function () {
        // arrange
        $manager = createDefaultEmployee();

        $permanentShift = createDefaultShift();

        $employee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentShift($permanentShift)
            ->create();

        $anotherEmployee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentShift($permanentShift)
            ->create();

        $assignedShiftOfAnotherEmployeeID = $anotherEmployee->shifts()->firstOrFail()->pivot->id;

        assert(!is_null($assignedShiftOfAnotherEmployeeID), 'this variable must be a valid ID');

        // act
        $this->jwtActingAsMobile($manager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$employee->id/shifts/$assignedShiftOfAnotherEmployeeID"
            )
            ->assertNotFound();

        // assert
        expect($employee->shifts()->count())->toBe(1);
        expect($anotherEmployee->shifts()->count())->toBe(1);
    }
);

it(
    'detach a shift from employee by manager - department manager cannot detach a shift from his employee that has a direct manager',
    function () {
        // arrange
        $directManager = createDefaultEmployee();
        $departmentManager = createDefaultEmployee();

        $managedEmployee = Employee::factory()
            ->directManager($directManager)
            ->managerByDepartment($departmentManager)
            ->for($this->tenant)
            ->permanentShift(createDefaultShift())
            ->create();

        $assignedShiftID = $managedEmployee->shifts()->firstOrFail()->pivot->id;

        assert(!is_null($assignedShiftID), 'this variable must be a valid ID');

        // act
        $this->jwtActingAsMobile($departmentManager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$managedEmployee->id/shifts/$assignedShiftID"
            )
            ->assertForbidden();

        // assert
        expect($managedEmployee->shifts()->count())->toBe(1);
    }
);
