<?php

use App\Models\Employee;

it('list employees', function () {
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->create();

    $anotherManager = createDefaultEmployee();

    $anotherEmployee = Employee::factory()
        ->for($this->tenant)
        ->directManager($anotherManager)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeController */
    $response = $this->jwtActingAsMobile($manager)
        ->getJson('api/v2/mobile/manager/employees')
        ->assertOk();

    expect($response->json('data.data'))->toHaveCount(1);

    expect($response->json('data.data.0.id'))->toBe($employee->id);

    expect($response->json('data.data.*.id'))->not->toContain($anotherEmployee->id);
});

it('show an employee', function () {
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeController */
    $this->jwtActingAsMobile($manager)
        ->getJson("api/v2/mobile/manager/employees/$employee->id")
        ->assertOk()
        ->assertJsonPath('data.id', $employee->id);
});

it('show an employee - manager can not see other managers employees', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee();

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeController */
    $this->jwtActingAsMobile($manager)
        ->getJson("api/v2/mobile/manager/employees/$employee->id")
        ->assertForbidden();
});
