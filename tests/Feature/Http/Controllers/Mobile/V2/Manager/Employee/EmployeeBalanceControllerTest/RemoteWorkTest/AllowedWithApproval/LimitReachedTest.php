<?php

use App\Enums\RemoteWorkPolicy;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Models\Team;
use Carbon\CarbonPeriod;

test('only monthly limit is set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => 2,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->startOfMonth()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = 0;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('only weekly limit is set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->localStartOfWeek()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = 0;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('yearly and monthly limits are set and monthly limit reached', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 3,
        'remote_work_days_monthly_limit' => 2,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->startOfMonth()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 1;
    $expectedMonthlyBalance = 0;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('yearly and monthly limits are set and yearly limit reached', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 2,
        'remote_work_days_monthly_limit' => 2,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->subMonth()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 0;
    $expectedMonthlyBalance = 1;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('yearly and weekly limits are set and yearly limit reached', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 2,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->subWeek()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 0;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = 1;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('yearly and weekly limits are set and weekly limit reached', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 3,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->localStartOfWeek()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 1;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = 0;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('monthly and weekly limits are set and weekly limit reached', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => 3,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->localStartOfWeek()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = 1;
    $expectedWeeklyBalance = 0;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('monthly and weekly limits are set and monthly limit reached', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => 2,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo, $travelTo->subWeek()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = 0;
    $expectedWeeklyBalance = 1;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('all limits are set and reached', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 2,
        'remote_work_days_monthly_limit' => 2,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->subDays(2)] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 0;
    $expectedMonthlyBalance = 0;
    $expectedWeeklyBalance = 0;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('only yearly limit is set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 2,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach ([$travelTo->subDay(), $travelTo->startOfYear()] as $remoteWorkDate) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 0;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = 0;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});
