<?php

use App\Enums\RemoteWorkPolicy;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;

test('return correct balance for regularization requests - default to current month', function () {
    $outsidePeriod = CarbonPeriod::create('2021-02-01', '2021-02-28');

    $period = CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth());

    $team = Team::factory()
        ->enterprise()
        ->create([
            'remote_work_days_monthly_limit' => 10,
            'approval_requests_limit' => 10,
            'permission_request_monthly_limit_hours' => 10,
        ]);

    $employee = Employee::factory()->for($team)->create();

    $excludedRegularizationRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->approved()
        ->date($outsidePeriod)
        ->regularization()
        ->create(['created_at' => $outsidePeriod->start]);

    $includedRegularizationRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->approved()
        ->date($period)
        ->regularization()
        ->create(['created_at' => $period->start]);

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.regularization', 9) // we have 1 regularization request in the period
        ->assertJsonPath('data.leave', null); // we dont have limit for leave
});

test('return correct balance for regularization requests - filtered by from to', function () {
    $outsidePeriod = CarbonPeriod::create('2021-02-01', '2021-02-28');

    $period = CarbonPeriod::create('2021-01-01', '2021-01-31');

    $team = Team::factory()
        ->enterprise()
        ->create([
            'remote_work_days_monthly_limit' => 10,
            'approval_requests_limit' => 10,
            'permission_request_monthly_limit_hours' => 10,
        ]);

    $employee = Employee::factory()->for($team)->create();

    $excludedRegularizationRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->approved()
        ->date($outsidePeriod)
        ->regularization()
        ->create(['created_at' => $outsidePeriod->start]);

    $includedRegularizationRequest = ApprovalRequest::factory()
        ->for($team)
        ->for($employee)
        ->approved()
        ->date($period)
        ->regularization()
        ->create(['created_at' => $period->start]);

    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => $period->start->format('Y-m-d'),
                        'to' => $period->end->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.regularization', 9) // we have 1 regularization request in the period
        ->assertJsonPath('data.leave', null); // we dont have limit for leave
});

test(
    'return correct permission request balance - ENABLED',
    /**
     * @param array<CarbonPeriod> $pendingPermissionDates
     * @param array<CarbonPeriod> $approvedPermissionDates
     */
    function (
        CarbonImmutable $travelTo,
        int|null $monthlyLimit,
        int|null $dailyLimit,
        array $pendingPermissionDates,
        array $approvedPermissionDates,
        string|null $expectedMonthlyBalance,
        string|null $expectedDailyBalance,
        string|null $expectedCurrentBalance
    ) {
        // arrange
        $this->travelTo($travelTo);

        $tenant = Team::factory()->create([
            'permission_request' => true,
            'permission_request_monthly_limit_hours' => $monthlyLimit,
            'permission_request_daily_limit_hours' => $dailyLimit,
        ]);

        $employee = Employee::factory()->for($tenant)->create();

        # create pending permission requests
        foreach ($pendingPermissionDates as $pendingPermissionDate) {
            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->pending()
                ->date($pendingPermissionDate)
                ->permission()
                ->create();
        }

        # create approved permission requests
        foreach ($approvedPermissionDates as $approvedPermissionDate) {
            ApprovalRequest::factory()
                ->for($tenant)
                ->for($employee)
                ->approved()
                ->date($approvedPermissionDate)
                ->permission()
                ->create();
        }

        # add neutral regularization requests that shouldn't be counted in permission requests limit
        ApprovalRequest::factory()
            ->approved()
            ->regularization()
            ->date(CarbonPeriod::create(today(), today()))
            ->for($employee)
            ->for($tenant)
            ->create();

        # add neutral remote work requests that shouldn't be counted in permission requests limit
        ApprovalRequest::factory()
            ->approved()
            ->regularization()
            ->date(CarbonPeriod::create(today(), today()))
            ->for($employee)
            ->for($tenant)
            ->create();

        // act & assert
        $this->jwtActingAsMobile($employee)
            ->get('api/v2/mobile/employees/balance')
            ->assertOk()
            ->assertJsonPath('data.permission.monthly_balance', $expectedMonthlyBalance)
            ->assertJsonPath('data.permission.daily_balance', $expectedDailyBalance)
            ->assertJsonPath('data.permission.current_balance', $expectedCurrentBalance);

        // permission ignores from to filter
        $this->jwtActingAsMobile($employee)
            ->get(
                'api/v2/mobile/employees/balance?' .
                    http_build_query([
                        'filter' => [
                            'from' => today()->subDay()->format('Y-m-d'),
                            'to' => today()->format('Y-m-d'),
                        ],
                    ])
            )
            ->assertOk()
            ->assertJsonPath('data.permission.monthly_balance', $expectedMonthlyBalance)
            ->assertJsonPath('data.permission.daily_balance', $expectedDailyBalance)
            ->assertJsonPath('data.permission.current_balance', $expectedCurrentBalance);
    }
)->with('permission request');

test('return correct permission request balance - DISABLED', function () {
    $tenant = Team::factory()->create([
        'permission_request' => false,
        'permission_request_monthly_limit_hours' => 9,
        'permission_request_daily_limit_hours' => 8,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    // act & assert
    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.permission', null);

    // permission ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->subDay()->format('Y-m-d'),
                        'to' => today()->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.permission', null);
});
