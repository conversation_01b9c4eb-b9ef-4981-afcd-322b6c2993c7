<?php

use App\Enums\RemoteWorkPolicy;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Models\Team;
use Carbon\CarbonPeriod;

test('no limit is set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();

    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    $remoteWorkDate = $travelTo->subDay();

    ApprovalRequest::factory()
        ->approved()
        ->remoteWork()
        ->checkIn()
        ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->remoteWork()
        ->checkOut()
        ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
        ->for($employee)
        ->for($tenant)
        ->create();

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = null;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('all limits are set to zero', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();

    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 0,
        'remote_work_days_monthly_limit' => 0,
        'remote_work_days_weekly_limit' => 0,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    $remoteWorkDate = $travelTo->subDay();

    ApprovalRequest::factory()
        ->approved()
        ->remoteWork()
        ->checkIn()
        ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->remoteWork()
        ->checkOut()
        ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
        ->for($employee)
        ->for($tenant)
        ->create();

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = null;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('only yearly limit is set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 2,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach (
        [$travelTo->subYear(), $travelTo->subYear()->subDay(), $travelTo->startOfYear()]
        as $remoteWorkDate
    ) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 1;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = 1;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('only monthly limit is set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => 2,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach (
        [$travelTo->subMonth(), $travelTo->subMonth()->subDay(), $travelTo->startOfMonth()]
        as $remoteWorkDate
    ) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = 1;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = 1;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('only weekly limit is set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach (
        [$travelTo->subWeek(), $travelTo->subWeek()->subDay(), $travelTo->localStartOfWeek()]
        as $remoteWorkDate
    ) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = 1;
    $expectedCurrentBalance = 1;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('yearly and monthly limits are set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 5,
        'remote_work_days_monthly_limit' => 2,
        'remote_work_days_weekly_limit' => null,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach (
        [
            $travelTo->subYear(),
            $travelTo->subYear()->subDay(),
            $travelTo->subYear()->subDays(2),
            $travelTo->subYear()->subDays(3),
            $travelTo->subYear()->subDays(5),
            $travelTo->startOfYear(),
            $travelTo->subMonth(),
            $travelTo->subMonth()->subday(),
            $travelTo->startOfMonth(),
        ]
        as $remoteWorkDate
    ) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 1;
    $expectedMonthlyBalance = 1;
    $expectedWeeklyBalance = null;
    $expectedCurrentBalance = 1;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('yearly and weekly limits are set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 5,
        'remote_work_days_monthly_limit' => null,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach (
        [
            $travelTo->subYear(),
            $travelTo->subYear()->subDay(),
            $travelTo->subYear()->subDays(2),
            $travelTo->subYear()->subDays(3),
            $travelTo->subYear()->subDays(4),
            $travelTo->startOfYear(),
            $travelTo->subWeek(),
            $travelTo->subWeek()->subday(),
            $travelTo->localStartOfWeek(),
        ]
        as $remoteWorkDate
    ) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 1;
    $expectedMonthlyBalance = null;
    $expectedWeeklyBalance = 1;
    $expectedCurrentBalance = 1;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('monthly and weekly limits are set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => null,
        'remote_work_days_monthly_limit' => 5,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach (
        [
            $travelTo->subMonth(),
            $travelTo->subMonth()->subDay(),
            $travelTo->subMonth()->subDays(2),
            $travelTo->subMonth()->subDays(3),
            $travelTo->subMonth()->subDays(4),
            $travelTo->startOfMonth(),
            $travelTo->subWeek(),
            $travelTo->subWeek()->subday(),
            $travelTo->localStartOfWeek(),
        ]
        as $remoteWorkDate
    ) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = null;
    $expectedMonthlyBalance = 1;
    $expectedWeeklyBalance = 1;
    $expectedCurrentBalance = 1;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});

test('all limits are set', function () {
    // arrange
    $travelTo = today()->startOfYear()->addMonths(6)->addDays(15)->localEndOfWeek()->toImmutable();
    $this->travelTo($travelTo);

    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED_WITH_APPROVAL->value,
        'remote_work_days_yearly_limit' => 10,
        'remote_work_days_monthly_limit' => 6,
        'remote_work_days_weekly_limit' => 2,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    # create remote work requests
    foreach (
        [
            $travelTo->subYear(),
            $travelTo->subYear()->subDay(),
            $travelTo->subYear()->subDays(2),
            $travelTo->subYear()->subDays(3),
            $travelTo->subYear()->subDays(4),
            $travelTo->subYear()->subDays(5),
            $travelTo->subYear()->subDays(6),
            $travelTo->subYear()->subDays(7),
            $travelTo->subYear()->subDays(8),
            $travelTo->startOfYear(),
            $travelTo->subMonth(),
            $travelTo->subMonth()->subday(),
            $travelTo->subMonth()->subdays(2),
            $travelTo->startOfMonth(),
            $travelTo->startOfMonth()->addDay(),
            $travelTo->subWeek(),
            $travelTo->subWeek()->subday(),
            $travelTo->localStartOfWeek(),
        ]
        as $remoteWorkDate
    ) {
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkIn()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
        ApprovalRequest::factory()
            ->approved()
            ->remoteWork()
            ->checkOut()
            ->date(CarbonPeriod::create($remoteWorkDate, $remoteWorkDate))
            ->for($employee)
            ->for($tenant)
            ->create();
    }

    # add neutral regularization requests that shouldn't be counted in remote work limit
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today()->subDay(), today()->subDay()))
        ->for($employee)
        ->for($tenant)
        ->create();
    ApprovalRequest::factory()
        ->approved()
        ->regularization()
        ->date(CarbonPeriod::create(today(), today()))
        ->for($employee)
        ->for($tenant)
        ->create();

    // act & assert
    $expectedYearlyBalance = 1;
    $expectedMonthlyBalance = 1;
    $expectedWeeklyBalance = 1;
    $expectedCurrentBalance = 1;

    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', $expectedYearlyBalance)
        ->assertJsonPath('data.remote_work.monthly_balance', $expectedMonthlyBalance)
        ->assertJsonPath('data.remote_work.weekly_balance', $expectedWeeklyBalance)
        ->assertJsonPath('data.remote_work.current_balance', $expectedCurrentBalance);
});
