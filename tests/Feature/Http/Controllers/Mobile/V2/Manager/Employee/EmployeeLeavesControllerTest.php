<?php

use App\Enums\RequestStatus;
use App\Models\Employee;
use App\Models\Leave;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;

test('list leaves - no filters', function () {
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->create();

    $anotherEmployee = createDefaultEmployee();

    Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->createMany(3);

    Leave::factory()
        ->for($this->tenant)
        ->for($anotherEmployee)
        ->createMany(3);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeLeaveController */
    $this->jwtActingAsMobile($manager)
        ->getJson("api/v2/mobile/manager/employees/$employee->id/leaves")
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list leaves with filter', function (
    Employee $manager,
    Employee $employee,
    Leave $includedLeaves,
    Leave $excludedLeaves,
    array $query
) {
    $response = $this->jwtActingAsMobile($manager)
        ->getJson(
            "api/v2/mobile/manager/employees/$employee->id/leaves?" . http_build_query($query)
        )
        ->assertOk();

    $responseLeaves = $response->json('data.data');

    expect($responseLeaves)
        ->toHaveCount(1)
        ->and(collect($responseLeaves)->pluck('id'))
        ->toContain($includedLeaves->id)
        ->and(collect($responseLeaves)->pluck('id'))
        ->not()
        ->toContain($excludedLeaves->id);
})->with([
    'filter by from to' => function () {
        $manager = createDefaultEmployee();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->directManager($manager)
            ->create();

        $startDate = CarbonImmutable::now();

        $endDate = $startDate->addDays(3);

        $includedLeaves = Leave::factory()
            ->for($this->tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($startDate, $endDate))
            ->create();

        $excludedLeaves = Leave::factory()
            ->for($this->tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($endDate->addDays(1), $endDate->addDays(4)))
            ->create();

        return [
            'manager' => $manager,
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[from]' => $startDate->format('Y-m-d'),
                'filter[to]' => $endDate->format('Y-m-d'),
            ],
        ];
    },
    'filter by status' => function () {
        $manager = createDefaultEmployee();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->directManager($manager)
            ->create();

        $includedLeaves = Leave::factory()
            ->for($this->tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Approved]);

        $excludedLeaves = Leave::factory()
            ->for($this->tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Pending]);

        return [
            'manager' => $manager,
            'employee' => $employee,
            'includedLeaves' => $includedLeaves,
            'excludedLeaves' => $excludedLeaves,
            'query' => [
                'filter[status]' => [RequestStatus::Approved->value],
            ],
        ];
    },
]);
