<?php

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Employee;

it('returns attendances - from to', function () {
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date('2023-01-01')
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date('2023-01-02')
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date('2023-01-03')
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeAttendanceController */
    $this->jwtActingAsMobile($manager)
        ->getJson(
            uri: "api/v2/mobile/manager/employees/$employee->id/attendances?" .
                http_build_query([
                    'filter' => [
                        'from' => '2023-01-01',
                        'to' => '2023-01-02',
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonCount(2, 'data.data');
});

it('returns attendances - status', function () {
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date('2023-01-01')
        ->create(['status' => AttendanceStatus::PRESENT]);

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->date('2023-01-02')
        ->create(['status' => AttendanceStatus::ABSENT]);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeAttendanceController */
    $this->jwtActingAsMobile($manager)
        ->getJson(
            uri: "api/v2/mobile/manager/employees/$employee->id/attendances?" .
                http_build_query([
                    'filter' => [
                        'from' => '2023-01-01',
                        'to' => '2023-01-02',
                        'status' => AttendanceStatus::PRESENT->value,
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonCount(1, 'data.data')
        ->assertJsonFragment(['status' => AttendanceStatus::PRESENT]);
});
