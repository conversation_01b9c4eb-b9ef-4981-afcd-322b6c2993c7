<?php

use App\Enums\RemoteWorkPolicy;
use App\Models\Employee;
use App\Models\Team;

test('return correct remote work balance - ALLOWED_WITHOUT_APPROVAL', function () {
    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::ALLOWED->value,
        'remote_work_days_yearly_limit' => 9,
        'remote_work_days_monthly_limit' => 8,
        'remote_work_days_weekly_limit' => 7,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    // act & assert
    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', null)
        ->assertJsonPath('data.remote_work.monthly_balance', null)
        ->assertJsonPath('data.remote_work.weekly_balance', null)
        ->assertJsonPath('data.remote_work.current_balance', null);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work.yearly_balance', null)
        ->assertJsonPath('data.remote_work.monthly_balance', null)
        ->assertJsonPath('data.remote_work.weekly_balance', null)
        ->assertJsonPath('data.remote_work.current_balance', null);
});

test('return correct remote work balance - NOT_ALLOWED', function () {
    $tenant = Team::factory()->create([
        'remote_work' => RemoteWorkPolicy::NOT_ALLOWED->value,
        'remote_work_days_yearly_limit' => 9,
        'remote_work_days_monthly_limit' => 8,
        'remote_work_days_weekly_limit' => 7,
    ]);

    $employee = Employee::factory()->for($tenant)->create();

    // act & assert
    $this->jwtActingAsMobile($employee)
        ->get('api/v2/mobile/employees/balance')
        ->assertOk()
        ->assertJsonPath('data.remote_work', null);

    // remote work ignores from to filter
    $this->jwtActingAsMobile($employee)
        ->get(
            'api/v2/mobile/employees/balance?' .
                http_build_query([
                    'filter' => [
                        'from' => today()->startOfMonth()->addDays(3)->format('Y-m-d'),
                        'to' => today()->endOfMonth()->subDays(3)->format('Y-m-d'),
                    ],
                ])
        )
        ->assertOk()
        ->assertJsonPath('data.remote_work', null);
});
