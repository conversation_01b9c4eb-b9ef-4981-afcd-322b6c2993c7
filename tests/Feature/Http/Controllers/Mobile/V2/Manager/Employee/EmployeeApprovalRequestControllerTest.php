<?php

use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;

test('list approval requests - no filters', function () {
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->directManager($manager)
        ->create();

    $anotherEmployee = createDefaultEmployee();

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($employee)
        ->createMany(3);

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($anotherEmployee)
        ->createMany(3);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeApprovalRequestController */
    $this->jwtActingAsMobile($manager)
        ->getJson("api/v2/mobile/manager/employees/$employee->id/approval-requests")
        ->assertOk()
        ->assertJsonCount(3, 'data.data');
});

test('list approval requests with filter', function (
    Employee $manager,
    Employee $employee,
    ApprovalRequest $includedApprovalRequest,
    ApprovalRequest $excludedApprovalRequest,
    array $query
) {
    /** @see \App\Http\Controllers\Mobile\V2\Manager\Employee\EmployeeApprovalRequestController */
    $response = $this->jwtActingAsMobile($manager)
        ->getJson(
            "api/v2/mobile/manager/employees/$employee->id/approval-requests?" .
                http_build_query($query)
        )
        ->assertOk();

    $responseApprovalRequest = $response->json('data.data');

    expect($responseApprovalRequest)
        ->toHaveCount(1)
        ->and(collect($responseApprovalRequest)->pluck('id'))
        ->toContain($includedApprovalRequest->id)
        ->and(collect($responseApprovalRequest)->pluck('id'))
        ->not()
        ->toContain($excludedApprovalRequest->id);
})->with([
    'filter by from to' => function () {
        $manager = createDefaultEmployee();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->directManager($manager)
            ->create();

        $startDate = CarbonImmutable::now();

        $endDate = $startDate->addDays(3);

        $includedApprovalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($startDate, $endDate))
            ->create();

        $excludedApprovalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employee)
            ->date(CarbonPeriod::create($endDate->addDays(2), $endDate->addDays(4)))
            ->create();

        return [
            'manager' => $manager,
            'employee' => $employee,
            'includedApprovalRequest' => $includedApprovalRequest,
            'excludedApprovalRequest' => $excludedApprovalRequest,
            'query' => [
                'filter[from]' => $startDate->format('Y-m-d'),
                'filter[to]' => $endDate->format('Y-m-d'),
            ],
        ];
    },
    'filter by status' => function () {
        $manager = createDefaultEmployee();

        $employee = Employee::factory()
            ->for($this->tenant)
            ->directManager($manager)
            ->create();

        $includedApprovalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Approved]);

        $excludedApprovalRequest = ApprovalRequest::factory()
            ->for($this->tenant)
            ->for($employee)
            ->create(['status' => RequestStatus::Pending]);

        return [
            'manager' => $manager,
            'employee' => $employee,
            'includedApprovalRequest' => $includedApprovalRequest,
            'excludedApprovalRequest' => $excludedApprovalRequest,
            'query' => [
                'filter[status]' => [RequestStatus::Approved->value],
            ],
        ];
    },
]);
