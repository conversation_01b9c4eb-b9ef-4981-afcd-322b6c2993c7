<?php

use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use Carbon\CarbonPeriod;

it('detach a location from employee by manager - location assignment should exists', function () {
    // arrange
    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->managerByDepartment($manager)
        ->for($this->tenant)
        ->create();

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson("api/v2/mobile/manager/employees/$employee->id/locations/99999")
        ->assertNotFound();

    // assert
    expect($employee->locations()->count())->toBe(0);
});

it(
    "detach a location from employee by manager - manager cannot detach a location from other managers' employees",
    function () {
        // arrange
        $manager = createDefaultEmployee();
        $anotherManager = createDefaultEmployee();

        $permanentLocation = createDefaultLocation();

        $employee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentLocation($permanentLocation)
            ->create();
        $anotherManagerEmployee = Employee::factory()
            ->managerByDepartment($anotherManager)
            ->for($this->tenant)
            ->permanentLocation($permanentLocation)
            ->create();

        $assignedLocationID = $anotherManagerEmployee->locations()->firstOrFail()->pivot->id;

        // act
        $this->jwtActingAsMobile($manager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$anotherManagerEmployee->id/locations/$assignedLocationID"
            )
            ->assertForbidden();

        // assert
        expect($employee->locations()->count())->toBe(1);
        expect($anotherManagerEmployee->locations()->count())->toBe(1);
    }
);

it(
    'detach a location from employee by manager - manager cannot detach a location from an employee in another tenant',
    function () {
        // arrange
        $anotherTenant = Team::factory()->active()->basic()->create();

        $manager = createDefaultEmployee();

        $employee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentLocation(createDefaultLocation())
            ->create();

        $anotherTenantEmployee = Employee::factory()
            ->for($anotherTenant)
            ->permanentLocation(Location::factory()->for($anotherTenant)->create())
            ->create();

        $assignedLocationID = $anotherTenantEmployee->locations()->firstOrFail()->pivot->id;

        // act
        $this->jwtActingAsMobile($manager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$anotherTenantEmployee->id/locations/$assignedLocationID"
            )
            ->assertNotFound();

        // assert
        expect($employee->locations()->count())->toBe(1);
    }
);

it(
    'detach a location from employee by manager - location assignment should belong to the employee',
    function () {
        // arrange
        $manager = createDefaultEmployee();

        $permanentLocation = createDefaultLocation();

        $employee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentLocation($permanentLocation)
            ->create();

        $anotherEmployee = Employee::factory()
            ->managerByDepartment($manager)
            ->for($this->tenant)
            ->permanentLocation($permanentLocation)
            ->create();

        $assignedLocationOfAnotherEmployeeID = $anotherEmployee->locations()->firstOrFail()->pivot
            ->id;

        // act
        $this->jwtActingAsMobile($manager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$employee->id/locations/$assignedLocationOfAnotherEmployeeID"
            )
            ->assertNotFound();

        // assert
        expect($employee->locations()->count())->toBe(1);
        expect($anotherEmployee->locations()->count())->toBe(1);
    }
);

it(
    'detach a location from employee by manager - department manager cannot detach a location from his employee that has a direct manager',
    function () {
        // arrange
        $directManager = createDefaultEmployee();
        $departmentManager = createDefaultEmployee();

        $managedEmployee = Employee::factory()
            ->directManager($directManager)
            ->managerByDepartment($departmentManager)
            ->for($this->tenant)
            ->permanentLocation(createDefaultLocation())
            ->create();

        $assignedLocationID = $managedEmployee->locations()->firstOrFail()->pivot->id;

        // act
        $this->jwtActingAsMobile($departmentManager)
            ->deleteJson(
                "api/v2/mobile/manager/employees/$managedEmployee->id/locations/$assignedLocationID"
            )
            ->assertForbidden();

        // assert
        expect($managedEmployee->locations()->count())->toBe(1);
    }
);

it('detach a permanent location from employee by his direct manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentLocation = createDefaultLocation();
    $temporaryLocation = createDefaultLocation();

    $managedEmployee = Employee::factory()
        ->directManager($manager)
        ->for($this->tenant)
        ->temporaryLocation($temporaryLocation, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentLocation($permanentLocation)
        ->create();

    $assignedPermanentLocationID = $managedEmployee
        ->locations()
        ->where('permanent', true)
        ->firstOrFail()->pivot->id;

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/locations/$assignedPermanentLocationID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->locations()->count())->toBe(1);
    expect($managedEmployee->locations()->first()->id)->toBe($temporaryLocation->id);
});

it('detach a permanent location from employee by his department manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentLocation = createDefaultLocation();
    $temporaryLocation = createDefaultLocation();

    $managedEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for($this->tenant)
        ->temporaryLocation($temporaryLocation, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentLocation($permanentLocation)
        ->create();

    $assignedPermanentLocationID = $managedEmployee
        ->locations()
        ->where('permanent', true)
        ->firstOrFail()->pivot->id;

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/locations/$assignedPermanentLocationID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->locations()->count())->toBe(1);
    expect($managedEmployee->locations()->first()->id)->toBe($temporaryLocation->id);
});

it('detach a temporary location from employee by his direct manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentLocation = createDefaultLocation();
    $temporaryLocation = createDefaultLocation();

    $managedEmployee = Employee::factory()
        ->directManager($manager)
        ->for($this->tenant)
        ->temporaryLocation($temporaryLocation, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentLocation($permanentLocation)
        ->create();

    $assignedTemporaryLocationID = $managedEmployee
        ->locations()
        ->where('permanent', false)
        ->firstOrFail()->pivot->id;

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/locations/$assignedTemporaryLocationID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->locations()->count())->toBe(1);
    expect($managedEmployee->locations()->first()->id)->toBe($permanentLocation->id);
});

it('detach a temporary location from employee by his department manager', function () {
    // arrange
    $manager = createDefaultEmployee();

    $permanentLocation = createDefaultLocation();
    $temporaryLocation = createDefaultLocation();

    $managedEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for($this->tenant)
        ->temporaryLocation($temporaryLocation, CarbonPeriod::dates(today(), today()->addMonth()))
        ->permanentLocation($permanentLocation)
        ->create();

    $assignedTemporaryLocationID = $managedEmployee
        ->locations()
        ->where('permanent', false)
        ->firstOrFail()->pivot->id;

    // act
    $this->jwtActingAsMobile($manager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/locations/$assignedTemporaryLocationID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->locations()->count())->toBe(1);
    expect($managedEmployee->locations()->first()->id)->toBe($permanentLocation->id);
});

it('detach a location from employee by direct manager when department manager exists', function () {
    // arrange
    $directManager = createDefaultEmployee();
    $departmentManager = createDefaultEmployee();

    $managedEmployee = Employee::factory()
        ->directManager($directManager)
        ->managerByDepartment($departmentManager)
        ->for($this->tenant)
        ->permanentLocation(createDefaultLocation())
        ->create();

    $assignedLocationID = $managedEmployee->locations()->firstOrFail()->pivot->id;

    // act
    $this->jwtActingAsMobile($directManager)
        ->deleteJson(
            "api/v2/mobile/manager/employees/$managedEmployee->id/locations/$assignedLocationID"
        )
        ->assertOk();

    // assert
    expect($managedEmployee->locations()->count())->toBe(0);
});
