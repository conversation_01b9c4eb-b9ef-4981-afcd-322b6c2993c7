<?php

use App\Enums\DurationStatus;
use App\Jobs\EmployeeShiftJob;
use App\Models\Employee;
use App\Models\Shift;
use App\Notifications\NewPermanentShiftAssignedNotification;
use App\Notifications\NewTemporaryShiftAssignedNotification;
use App\Notifications\PermanentShiftAssignedChangedNotification;
use Carbon\CarbonPeriod;

beforeEach(function () {
    // by default, before doing the tests, the tenants will always have at least one default shift.
    Shift::factory()
        ->for($this->tenant)
        ->create(['is_default' => true]);
});

it('attach employees to a shift by manager - employee ids', function (DurationStatus $type) {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $manager = createDefaultEmployee();

    $employeeManagedByDepartment = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create();
    $employeeManagedByDirectManager = Employee::factory()
        ->directManager($manager)
        ->for(test()->tenant)
        ->create();

    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-shift/$shift->id", [
            'employees' => [$employeeManagedByDepartment->id, $employeeManagedByDirectManager->id],
            ...durationStatusPayload($type),
        ])
        ->assertOk();

    if ($type === DurationStatus::PERMANENT) {
        $shift = $employeeManagedByDepartment->shifts->first();

        expect($shift)->not->toBeNull();

        expect($shift->pivot->start_at)->toBeNull();
        expect($shift->pivot->end_at)->toBeNull();
        expect($shift->pivot->permanent)->toBeTruthy();
    } else {
        $shift = $employeeManagedByDepartment->shifts->first(
            fn($shift) => !$shift->pivot->permanent
        );

        expect($shift)->not->toBeNull();

        expect($shift->pivot->start_at)
            ->not()
            ->toBeNull();
        expect($shift->pivot->end_at)
            ->not()
            ->toBeNull();
        expect($shift->pivot->permanent)->toBeFalsy();
    }

    expect($employeeManagedByDepartment->shifts->pluck('id'))->toContain($shift->id);

    expect($employeeManagedByDirectManager->shifts->pluck('id'))->toContain($shift->id);
})->with('duration-status');

it('attach employees to a shift by manager - search', function (DurationStatus $type) {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $search = 'employee 1';

    $manager = createDefaultEmployee();

    $includedEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create(['first_name' => $search]);

    $excludedEmployee = Employee::factory()
        ->directManager($manager)
        ->for(test()->tenant)
        ->create(['first_name' => 'employee 2']);

    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-shift/$shift->id", [
            'all' => true,
            'filter' => ['search' => $search],
            ...durationStatusPayload($type),
        ])
        ->assertOk();

    if ($type === DurationStatus::PERMANENT) {
        $shift = $includedEmployee->shifts->first();

        expect($shift)->not->toBeNull();

        expect($shift->pivot->start_at)->toBeNull();
        expect($shift->pivot->end_at)->toBeNull();
        expect($shift->pivot->permanent)->toBeTruthy();
    } else {
        $shift = $includedEmployee->shifts->first(fn($shift) => !$shift->pivot->permanent);

        expect($shift)->not->toBeNull();

        expect($shift->pivot->start_at)
            ->not()
            ->toBeNull();
        expect($shift->pivot->end_at)
            ->not()
            ->toBeNull();
        expect($shift->pivot->permanent)->toBeFalsy();
    }

    expect($includedEmployee->shifts->pluck('id'))->toContain($shift->id);

    expect($excludedEmployee->shifts->pluck('id'))->not->toContain($shift->id);
})->with('duration-status');

it('attach employees to a shift by manager - only have a single permanent shift', function () {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();
    $anotherShift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create();

    $employee->shifts()->attach($shift);

    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-shift/$anotherShift->id", [
            'employees' => [$employee->id],
            'type' => DurationStatus::PERMANENT->value,
        ])
        ->assertOk();

    expect($employee->shifts->count())->toBe(1);

    expect($employee->shifts[0]->id)->toBe($anotherShift->id);
});

it('attach employees to a shift by manager - cannot have two overlapping temp shifts', function () {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create();

    $employee->shifts()->attach($shift, [
        'start_at' => now(),
        'end_at' => now()->addDays(1),
    ]);

    $response = $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-shift/$shift->id", [
            'employees' => [$employee->id],
            'type' => DurationStatus::TEMPORARY->value,
            'start_date' => now()->addDays(1)->format('Y-m-d'),
            'end_date' => now()->addDays(2)->format('Y-m-d'),
        ])
        ->assertStatus(422);

    expect($response->json('data')[0]['id'])->toBe($employee->id);
});

it(
    'attach employees to a shift by manager - manager cannot assign to other managers\' employees',
    function () {
        $shift = Shift::factory()
            ->for($this->tenant)
            ->create();

        $manager = createDefaultEmployee();

        $anotherManager = createDefaultEmployee();

        $employee = Employee::factory()
            ->directManager($anotherManager)
            ->for(test()->tenant)
            ->create();

        $this->jwtActingAsMobile($manager)
            ->putJson("api/v2/mobile/employees/attach-shift/$shift->id", [
                'employees' => [$employee->id],
                'type' => DurationStatus::PERMANENT->value,
            ])
            ->assertOk();

        expect($employee->shifts->pluck('id'))->not->toContain($shift->id);
    }
);

it('attach employees to a shift by manager - queue if employees > 100', function (
    DurationStatus $type
) {
    Queue::fake([EmployeeShiftJob::class]);

    $manager = createDefaultEmployee();

    $employees = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->count(101)
        ->create();

    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-shift/$shift->id", [
            'employees' => $employees->pluck('id'),
            ...durationStatusPayload($type),
        ])
        ->assertOk()
        ->assertJsonFragment([
            'message' => __('The shift was added to employees successfully, it may take a while'),
        ]);

    expect($employees[0]->shifts->pluck('id'))->not->toContain($shift->id);

    Queue::assertPushed(EmployeeShiftJob::class);
})->with('duration-status');

# notifications

it('notify employees about changes in their permanent shift', function () {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();
    $anotherShift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->permanentShift($shift)
        ->create();
    $anotherEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create();

    # act
    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-shift/$anotherShift->id", [
            'employees' => [$employee->id, $anotherEmployee->id],
            'type' => DurationStatus::PERMANENT->value,
        ])
        ->assertOk();

    # assert
    expect(
        $employee
            ->notifications()
            ->where('type', PermanentShiftAssignedChangedNotification::class)
            ->where('data->shift->id', $anotherShift->id)
            ->exists()
    )->toBeTrue('a notification should be sent for the changed permanent shift');

    expect(
        $anotherEmployee
            ->notifications()
            ->where('type', NewPermanentShiftAssignedNotification::class)
            ->where('data->shift->id', $anotherShift->id)
            ->exists()
    )->toBeTrue('a notification should be sent for the newly assigned permanent shift');

    expect($employee->notifications()->count())->toBe(
        1,
        'there should be exactly 1 notification sent to the first employee'
    );

    expect($anotherEmployee->notifications()->count())->toBe(
        1,
        'there should be exactly 1 notification sent to the second employee'
    );
});

it('notify employees about changes in their temporary shift', function () {
    $shift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $manager = createDefaultEmployee();

    $employee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create();
    $anotherEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create();

    $temporaryShiftPeriod = CarbonPeriod::dates(
        now()->setTime(0, 0),
        now()->addDays(5)->setTime(0, 0)
    );

    # act
    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-shift/$shift->id", [
            'employees' => [$employee->id, $anotherEmployee->id],
            'type' => DurationStatus::TEMPORARY->value,
            'start_date' => $temporaryShiftPeriod->getStartDate()->format('Y-m-d'),
            'end_date' => $temporaryShiftPeriod->getEndDate()->format('Y-m-d'),
        ])
        ->assertOk();

    # assert
    expect(
        $employee
            ->notifications()
            ->where('type', NewTemporaryShiftAssignedNotification::class)
            ->where('data->shift->id', $shift->id)
            ->where('data->startDate', $temporaryShiftPeriod->getStartDate()->toISOString())
            ->where('data->endDate', $temporaryShiftPeriod->getEndDate()->toISOString())
            ->exists()
    )->toBeTrue(
        'a notification should be sent to the first employee about the newly assigned temporary shift'
    );

    expect(
        $anotherEmployee
            ->notifications()
            ->where('type', NewTemporaryShiftAssignedNotification::class)
            ->where('data->shift->id', $shift->id)
            ->where('data->startDate', $temporaryShiftPeriod->getStartDate()->toISOString())
            ->where('data->endDate', $temporaryShiftPeriod->getEndDate()->toISOString())
            ->exists()
    )->toBeTrue(
        'a notification should be sent to the first employee about the newly assigned temporary shift'
    );

    expect($employee->notifications()->count())->toBe(
        1,
        'there should be exactly 1 notification sent to the first employee'
    );

    expect($anotherEmployee->notifications()->count())->toBe(
        1,
        'there should be exactly 1 notification sent to the second employee'
    );
});
