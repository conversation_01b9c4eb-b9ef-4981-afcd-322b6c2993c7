<?php

use App\Enums\DurationStatus;
use App\Jobs\EmployeeLocationJob;
use App\Models\Employee;
use App\Models\Location;
use Carbon\Carbon;

it('attach employees to a location by manager', function (DurationStatus $durationStatus) {
    $manager = createDefaultEmployee();

    $employeeManagedByDepartment = Employee::factory()
        ->managerByDepartment($manager)
        ->for($this->tenant)
        ->create();

    $employeeManagedByDirectManager = Employee::factory()
        ->directManager($manager)
        ->for($this->tenant)
        ->create();

    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-location/$location->id", [
            'employees' => [$employeeManagedByDepartment->id, $employeeManagedByDirectManager->id],
            ...durationStatusPayload($durationStatus),
        ])
        ->assertOk();

    expect($employeeManagedByDepartment->locations->first()->id)->toBe($location->id);

    expect($employeeManagedByDirectManager->locations->first()->id)->toBe($location->id);
})->with('duration-status');

it('attach employees to locations', function () {
    $manager = createDefaultEmployee();

    $employees = Employee::factory()
        ->directManager($manager)
        ->for($this->tenant)
        ->count(2)
        ->create();

    $location = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'Location 1']);

    $location2 = Location::factory()
        ->for($this->tenant)
        ->create(['name' => 'Location 2']);

    $start = now()->subDay()->format('Y-m-d');
    $end = now()->addDay()->format('Y-m-d');

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/employees/attach-location', [
            'employees' => $employees->pluck('id')->toArray(),
            'locations' => [
                [
                    'id' => $location->id,
                    'type' => DurationStatus::PERMANENT->value,
                ],
                [
                    'id' => $location2->id,
                    'type' => DurationStatus::TEMPORARY->value,
                    'start_date' => $start,
                    'end_date' => $end,
                ],
            ],
        ])
        ->assertOk();

    $employees->each(function ($employee) use ($end, $start) {
        $permanentLocation = $employee->locations->firstWhere('name', 'Location 1');
        $temporaryLocation = $employee->locations->firstWhere('name', 'Location 2');

        expect($permanentLocation->pivot->permanent)->toBe(1);
        expect($permanentLocation->pivot->start_date)->toBeNull();
        expect($permanentLocation->pivot->end_date)->toBeNull();

        expect($temporaryLocation->pivot->permanent)->toBe(0);
        expect(Carbon::parse($temporaryLocation->pivot->start_date)->format('Y-m-d'))->toBe($start);
        expect(Carbon::parse($temporaryLocation->pivot->end_date)->format('Y-m-d'))->toBe($end);
    });
});

it('attach employees to a location by manager - search', function (DurationStatus $type) {
    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    $search = 'employee 1';

    $manager = createDefaultEmployee();

    $includedEmployee = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->create(['first_name' => $search]);

    $excludedEmployee = Employee::factory()
        ->directManager($manager)
        ->for(test()->tenant)
        ->create(['first_name' => 'employee 2']);

    $this->jwtActingAsMobile($manager)
        ->putJson("api/v2/mobile/employees/attach-location/$location->id", [
            'all' => true,
            'filter' => ['search' => $search],
            ...durationStatusPayload($type),
        ])
        ->assertOk();

    if ($type === DurationStatus::PERMANENT) {
        $location = $includedEmployee->locations->first();

        expect($location)->not->toBeNull();

        expect($location->pivot->start_date)->toBeNull();
        expect($location->pivot->end_date)->toBeNull();
        expect($location->pivot->permanent)->toBeTruthy();
    } else {
        $location = $includedEmployee->locations->first(
            fn($location) => !$location->pivot->permanent
        );

        expect($location)->not->toBeNull();

        expect($location->pivot->start_date)
            ->not()
            ->toBeNull();
        expect($location->pivot->end_date)
            ->not()
            ->toBeNull();
        expect($location->pivot->permanent)->toBeFalsy();
    }

    expect($includedEmployee->locations->pluck('id'))->toContain($location->id);

    expect($excludedEmployee->locations->pluck('id'))->not->toContain($location->id);
})->with('duration-status');

it(
    'attach employees to a location by manager - manager cannot assign to other managers\' employees',
    function (DurationStatus $durationStatus) {
        $manager = createDefaultEmployee();

        $anotherManager = createDefaultEmployee();

        $employee = Employee::factory()
            ->directManager($anotherManager)
            ->for($this->tenant)
            ->create();

        $location = Location::factory()
            ->for($this->tenant)
            ->create();

        $this->jwtActingAsMobile($manager)
            ->putJson("api/v2/mobile/employees/attach-location/$location->id", [
                'employees' => [$employee->id],
                ...durationStatusPayload($durationStatus),
            ])
            ->assertOk();

        expect($employee->locations->pluck('id'))->not->toContain($location->id);
    }
)->with('duration-status');

it('attach employees to a location - queue if employees > 100', function (DurationStatus $type) {
    Queue::fake([EmployeeLocationJob::class]);

    $manager = createDefaultEmployee();

    $employees = Employee::factory()
        ->managerByDepartment($manager)
        ->for(test()->tenant)
        ->count(101)
        ->create();

    $location = Location::factory()
        ->for($this->tenant)
        ->create();

    $this->jwtActingAsAttendanceHR($this->user)
        ->putJson("api/v1/frontend/employees/attach-location/$location->id", [
            'employees' => $employees->pluck('id'),
            ...durationStatusPayload($type),
        ])
        ->assertOk()
        ->assertJsonFragment([
            'message' => __(
                'The location was added to employees successfully, it may take a while'
            ),
        ]);

    expect($employees[0]->locations->pluck('id'))->not->toContain($location->id);

    Queue::assertPushed(EmployeeLocationJob::class);
})->with('duration-status');
