<?php

use App\Enums\AttendanceStatus;
use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Delegation;
use App\Models\Leave;
use App\Notifications\GenericMobileNotification;
use App\Notifications\NewApprovalRequestNotification;
use App\Notifications\NewLeaveRequestNotification;
use Carbon\CarbonPeriod;

test('approve leave - all', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $anotherManagerLeaves = Leave::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $alreadyApprovedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create(['updated_at' => null]);

    $rejectedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create();

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\ApproveLeaveController */
    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/approve', ['all' => true])
        ->assertOk();

    $leaves->each->refresh();

    $anotherManagerLeaves->each->refresh();

    $rejectedLeave->refresh();
    $alreadyApprovedLeave->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Approved);
    });

    $anotherManagerLeaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Pending);
    });

    expect($rejectedLeave->status)->toEqual(RequestStatus::Rejected);

    expect($alreadyApprovedLeave->updated_at)->toBeNull();
});

test('approve leave - ids', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $leavesNotIncluded = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $anotherManagerLeaves = Leave::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $alreadyApprovedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create(['updated_at' => null]);

    $rejectedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create();

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/approve', [
            'leaves' => $leaves->pluck('id')->toArray(),
        ])
        ->assertOk();

    $leaves->each->refresh();

    $leavesNotIncluded->each->refresh();

    $anotherManagerLeaves->each->refresh();

    $rejectedLeave->refresh();

    $alreadyApprovedLeave->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Approved);
    });

    $leavesNotIncluded->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Pending);
    });

    $anotherManagerLeaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Pending);
    });

    expect($rejectedLeave->status)->toEqual(RequestStatus::Rejected);

    expect($alreadyApprovedLeave->updated_at)->toBeNull();
});

test('approve leave - delegate can approve', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaveDelegateEmployee = createDefaultEmployee();

    Delegation::create([
        'delegated_id' => $leaveDelegateEmployee->id,
        'delegatee_id' => $manager->id,
        'type' => DelegationType::LeaveRequest,
    ]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $alreadyApprovedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $rejectedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($leaveDelegateEmployee)
        ->putJson('api/v2/mobile/manager/leaves/approve', [
            'leaves' => $leaves->pluck('id')->toArray(),
        ])
        ->assertOk();

    $leaves->each->refresh();

    $rejectedLeave->refresh();

    $alreadyApprovedLeave->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Approved);
    });

    expect($alreadyApprovedLeave->status)->toEqual(RequestStatus::Approved);

    expect($rejectedLeave->updated_at)->toBeNull();
});

test('approve leave - attendance records should be leaves', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $period = CarbonPeriod::create('2021-08-01', '2021-08-07');

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->date($period)
        ->count(3)
        ->create();

    $attendances = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->yet()
        ->createFromPeriod(period: $period);

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/approve', ['all' => true])
        ->assertOk();

    $leaves->each->refresh();

    $attendances->each->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Approved);
    });

    $attendances->each(function ($attendance) {
        expect($attendance->status)->toEqual(AttendanceStatus::LEAVE->value);
    });
});

test('approve leave - pending request notification should be deleted', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    // send leave request pending notifications to manager
    $leaves->each(function ($leave) use ($manager) {
        $manager->notify(new NewLeaveRequestNotification($leave));
    });

    // send unrelated notifications
    $manager->notify(new GenericMobileNotification('subject', 'body'));
    // send notification where only notification type match
    $employee->notify(new NewLeaveRequestNotification(Leave::factory()->create()));
    // send notification where only data.payload.id match
    $manager->notify(
        new NewApprovalRequestNotification(
            ApprovalRequest::factory()->create(['id' => $leaves->firstOrFail()->id])
        )
    );

    # act
    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/approve', ['all' => true])
        ->assertOk();

    // assert leave request pending notifications for manager are deleted
    $leaves->each(function ($leave) use ($manager) {
        expect(
            $manager
                ->notifications()
                ->where('type', NewLeaveRequestNotification::class)
                ->where('data->payload->id', $leave->id)
                ->doesntExist()
        )->toBeTrue('pending request notification for leave ' . $leave->id . ' should be deleted');
    });

    // assert unrelated notification is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', GenericMobileNotification::class)
            ->exists()
    )->toBeTrue('unrelated notifications should not be deleted');

    // assert only notification type match is not deleted
    expect(
        $employee
            ->notifications()
            ->where('type', NewLeaveRequestNotification::class)
            ->exists()
    )->toBeTrue('only notification type match should not be deleted');

    // assert only data.payload.id match is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', NewApprovalRequestNotification::class)
            ->where('data->payload->id', $leaves->firstOrFail()->id)
            ->exists()
    )->toBeTrue('only data.payload.id match should not be deleted');
});
