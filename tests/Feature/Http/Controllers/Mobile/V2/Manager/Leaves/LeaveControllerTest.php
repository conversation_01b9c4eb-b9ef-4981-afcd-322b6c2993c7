<?php

use App\Enums\ApprovalType;
use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Models\Delegation;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Leave;
use App\Models\Team;
use App\Services\ApproveLeaveService;
use App\Services\RejectLeaveService;

it('one-layer system - returns leave requests for a direct manager of employees', function () {
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);

    $manager = Employee::factory()->for($team)->create();

    $department = Department::factory()->for($team)->for($manager, 'manager')->create();

    $employee = Employee::factory()
        ->for($team)
        ->directManager($manager)
        ->for($department)
        ->create();

    $employeeForAnotherManager = Employee::factory()->for($team)->create();

    $leave = Leave::factory()
        ->for($team)
        ->for($department)
        ->for($employee)
        ->create(['status' => RequestStatus::Pending]);

    Leave::factory()
        ->for($team)
        ->for($employeeForAnotherManager)
        ->create(['status' => RequestStatus::Pending]);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
    $response = $this->jwtActingAsMobile($manager)
        ->getJson('/api/v2/mobile/manager/leaves')
        ->assertOk()
        ->assertJsonCount(1, 'data.data');

    expect($response->json('data.data.0.id'))->toBe($leave->id);
});

it('one-layer system - returns leave requests for a department manager of employees', function () {
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);

    $manager = Employee::factory()->for($team)->create();

    $department = Department::factory()->for($team)->for($manager, 'manager')->create();

    $employee = Employee::factory()
        ->for($team)
        ->directManager($manager)
        ->for($department)
        ->create();

    $anotherDepartment = Department::factory()->for($team)->create();

    $employeeForAnotherDepartment = Employee::factory()
        ->for($team)
        ->for($anotherDepartment)
        ->create();

    $leave = Leave::factory()
        ->for($team)
        ->for($department)
        ->for($employee)
        ->create(['status' => RequestStatus::Pending]);

    Leave::factory()
        ->for($team)
        ->for($anotherDepartment)
        ->for($employeeForAnotherDepartment)
        ->create(['status' => RequestStatus::Pending]);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
    $response = $this->jwtActingAsMobile($manager)
        ->getJson('/api/v2/mobile/manager/leaves')
        ->assertOk()
        ->assertJsonCount(1, 'data.data');

    expect($response->json('data.data.0.id'))->toBe($leave->id);
});

it('one-layer system - returns no leave requests for a manager with leave delegation', function () {
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);

    $manager = Employee::factory()->for($team)->create();

    $department = Department::factory()->for($team)->for($manager, 'manager')->create();

    $employee = Employee::factory()->for($team)->directManager($manager)->create();

    $delegated = Employee::factory()->for($team)->directManager($manager)->create();

    $employeeForAnotherManager = Employee::factory()->for($team)->create();

    Leave::factory()
        ->for($team)
        ->for($department)
        ->for($employee)
        ->create(['status' => RequestStatus::Pending]);

    Leave::factory()
        ->for($team)
        ->for($department)
        ->for($employeeForAnotherManager)
        ->create(['status' => RequestStatus::Pending]);

    Delegation::factory()->create([
        'type' => DelegationType::LeaveRequest,
        'delegated_id' => $delegated->id,
        'delegatee_id' => $manager->id,
    ]);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
    $this->jwtActingAsMobile($manager)
        ->getJson('/api/v2/mobile/manager/leaves')
        ->assertOk()
        ->assertJsonCount(0, 'data.data');
});

it('one-layer system - returns leave requests for a delegated employee', function () {
    $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);

    $manager = Employee::factory()->for($team)->create();

    $department = Department::factory()->for($team)->for($manager, 'manager')->create();

    $employee = Employee::factory()->for($team)->directManager($manager)->create();

    $delegated = Employee::factory()->for($team)->directManager($manager)->create();

    $employeeForAnotherManager = Employee::factory()->for($team)->create();

    $leave = Leave::factory()->for($team)->for($department)->for($employee)->pending()->create();

    Leave::factory()->for($team)->for($employeeForAnotherManager)->pending()->create();

    Delegation::factory()->create([
        'type' => DelegationType::LeaveRequest,
        'delegated_id' => $delegated->id,
        'delegatee_id' => $manager->id,
    ]);

    /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
    $response = $this->jwtActingAsMobile($delegated)
        ->getJson('/api/v2/mobile/manager/leaves')
        ->assertOk()
        ->assertJsonCount(1, 'data.data');

    expect($response->json('data.data.*.id'))->toEqual([$leave->id]);
});

it(
    'one-layer system - returns approval requests for a delegated employee who is also a manager',
    function () {
        $team = Team::factory()->create(['approval_type' => ApprovalType::OneLayer]);

        // 1. a manager of is delegated
        $delegatedManager = Employee::factory()->for($team)->create();

        $departmentOfDelegatedManager = Department::factory()
            ->for($team)
            ->for($delegatedManager, 'manager')
            ->create();

        // 2. a manager of the manager is the delegatee
        $delegateeManager = Employee::factory()->for($team)->create();

        $departmentOfDelegateeManager = Department::factory()
            ->for($team)
            ->for($delegateeManager, 'manager')
            ->create();

        // 3. an employee who belongs to the delegatee manager
        $employeeForDelegateeManager = Employee::factory()
            ->for($team)
            ->directManager($delegateeManager)
            ->create();

        // 4. an employee who belongs to the delegatee manager
        $employeeForDelegatedManager = Employee::factory()
            ->for($team)
            ->directManager($delegatedManager)
            ->create();

        // 5. another employee who should not be included in the response
        $employeeForAnotherManager = Employee::factory()->for($team)->create();

        // 6. a leave request for the employee who belongs to the delegated manager - included in response
        $leave1 = Leave::factory()
            ->for($team)
            ->for($departmentOfDelegatedManager)
            ->for($employeeForDelegatedManager)
            ->pending()
            ->create();

        // 7. a leave request for the employee who belongs to the delegatee manager - included in the response
        $leave2 = Leave::factory()
            ->for($team)
            ->for($departmentOfDelegatedManager)
            ->for($employeeForDelegateeManager)
            ->pending()
            ->create();

        // 8. a leave request for the employee who belongs to another manager - not included in the response
        Leave::factory()->for($team)->for($employeeForAnotherManager)->pending()->create();

        Delegation::factory()->create([
            'type' => DelegationType::LeaveRequest,
            'delegated_id' => $delegatedManager->id,
            'delegatee_id' => $delegateeManager->id,
        ]);

        # act
        /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
        $response = $this->jwtActingAsMobile($delegatedManager)
            ->getJson('/api/v2/mobile/manager/leaves')
            ->assertOk()
            ->assertJsonCount(2, 'data.data');

        expect($response->json('data.data.*.id'))->toEqual([$leave1->id, $leave2->id]);
    }
);

it(
    'two-layer system - direct manager can see initial leave request but can not see it after decided',
    function (RequestStatus $requestStatus) {
        $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);

        $directManager = Employee::factory()->for($team)->create();

        $departmentManager = Employee::factory()->for($team)->create();

        $department = Department::factory()
            ->for($team)
            ->for($departmentManager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($team)
            ->for($department)
            ->create(['manager_id' => $directManager->id]);

        $leave = Leave::factory()
            ->for($team)
            ->for($department)
            ->for($employee)
            ->pending()
            ->create();

        // initial leave request - can see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
        $this->jwtActingAsMobile($directManager)
            ->getJson('/api/v2/mobile/manager/leaves')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');

        match ($requestStatus) {
            RequestStatus::Approved => (new ApproveLeaveService(
                leave: $leave,
                decider: $directManager
            ))->handle(),
            RequestStatus::Rejected => (new RejectLeaveService(
                leave: $leave,
                rejectionReason: 'Rejected',
                decider: $directManager
            ))->handle(),
            default => '',
        };

        // approved leave request - can not see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
        $this->jwtActingAsMobile($directManager)
            ->getJson('/api/v2/mobile/manager/leaves')
            ->assertOk()
            ->assertJsonCount(0, 'data.data');
    }
)->with([RequestStatus::Approved, RequestStatus::Rejected]);

it(
    'two-layer system - department manager can not see initial leave request but can see it after decided',
    function (RequestStatus $requestStatus) {
        $team = Team::factory()->create(['approval_type' => ApprovalType::TwoLayer]);

        $directManager = Employee::factory()->for($team)->create();

        $departmentManager = Employee::factory()->for($team)->create();

        $department = Department::factory()
            ->for($team)
            ->for($departmentManager, 'manager')
            ->create();

        $employee = Employee::factory()
            ->for($team)
            ->for($department)
            ->create(['manager_id' => $directManager->id]);

        $leave = Leave::factory()
            ->for($team)
            ->for($department)
            ->for($employee)
            ->pending()
            ->create();

        // initial leave request - can see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
        $this->jwtActingAsMobile($departmentManager)
            ->getJson('/api/v2/mobile/manager/leaves')
            ->assertOk()
            ->assertJsonCount(0, 'data.data');

        match ($requestStatus) {
            RequestStatus::Approved => (new ApproveLeaveService(
                leave: $leave,
                decider: $directManager
            ))->handle(),
            RequestStatus::Rejected => (new RejectLeaveService(
                leave: $leave,
                rejectionReason: 'Rejected',
                decider: $directManager
            ))->handle(),
            default => '',
        };

        // approved leave request - can not see it
        /** @see \App\Http\Controllers\Mobile\V2\Manager\Leaves\LeaveController */
        $this->jwtActingAsMobile($departmentManager)
            ->getJson('/api/v2/mobile/manager/leaves')
            ->assertOk()
            ->assertJsonCount(1, 'data.data');
    }
)->with([RequestStatus::Approved, RequestStatus::Rejected]);
