<?php

use App\Enums\AttendanceStatus;
use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Delegation;
use App\Models\Leave;
use App\Notifications\GenericMobileNotification;
use App\Notifications\NewApprovalRequestNotification;
use App\Notifications\NewLeaveRequestNotification;
use Carbon\CarbonPeriod;

test('reject leave - all', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $anotherManagerLeaves = Leave::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $approvedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $alreadyRejectedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/reject', [
            'all' => true,
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    $leaves->each->refresh();

    $anotherManagerLeaves->each->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Rejected);
        expect($leave->rejection_reason)->toBe('test');
    });

    $anotherManagerLeaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Pending);
    });

    expect($approvedLeave->status)->toEqual(RequestStatus::Approved);

    expect($alreadyRejectedLeave->updated_at)->toBeNull();
});

test('reject leave - ids', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $leavesNotIncluded = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $anotherManagerLeaves = Leave::factory()
        ->for($this->tenant)
        ->pending()
        ->count(3)
        ->create();

    $approvedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $alreadyRejectedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/reject', [
            'leaves' => $leaves->pluck('id')->toArray(),
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    $leaves->each->refresh();

    $leavesNotIncluded->each->refresh();

    $anotherManagerLeaves->each->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Rejected);
        expect($leave->rejection_reason)->toBe('test');
    });

    $leavesNotIncluded->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Pending);
    });

    $anotherManagerLeaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Pending);
    });

    expect($approvedLeave->status)->toEqual(RequestStatus::Approved);

    expect($alreadyRejectedLeave->updated_at)->toBeNull();
});

test('reject leave - delegate can reject', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaveDelegateEmployee = createDefaultEmployee();

    Delegation::create([
        'delegated_id' => $leaveDelegateEmployee->id,
        'delegatee_id' => $manager->id,
        'type' => DelegationType::LeaveRequest,
    ]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    $approvedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->create();

    $alreadyRejectedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->rejected()
        ->create(['updated_at' => null]);

    $this->jwtActingAsMobile($leaveDelegateEmployee)
        ->putJson('api/v2/mobile/manager/leaves/reject', [
            'leaves' => $leaves->pluck('id')->toArray(),
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    $leaves->each->refresh();

    $approvedLeave->refresh();
    $alreadyRejectedLeave->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Rejected);
        expect($leave->rejection_reason)->toBe('test');
    });

    expect($approvedLeave->status)->toEqual(RequestStatus::Approved);

    expect($alreadyRejectedLeave->updated_at)->toBeNull();
});

test('reject leave - not pending leaves can not be rejected', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->approved()
        ->count(3)
        ->create();

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/reject', [
            'leaves' => $leaves->pluck('id')->toArray(),
            'rejection_reason' => 'test',
        ])
        ->assertBadRequest();

    $leaves->each->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)
            ->not()
            ->toEqual(RequestStatus::Rejected->value);
    });
});

test('reject leave - attendance records is not reflected to be leaves', function () {
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $period = CarbonPeriod::create('2021-08-01', '2021-08-07');

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->date($period)
        ->count(3)
        ->create();

    $attendances = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->yet()
        ->createFromPeriod(period: $period);

    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/reject', [
            'all' => true,
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    $leaves->each->refresh();

    $attendances->each->refresh();

    $leaves->each(function ($leave) {
        expect($leave->status)->toEqual(RequestStatus::Rejected);
        expect($leave->rejection_reason)->toBe('test');
    });

    $attendances->each(function ($attendance) {
        expect($attendance->status)
            ->not()
            ->toEqual(AttendanceStatus::LEAVE->value);
    });
});

test('reject leave - pending request notification should be deleted', function () {
    # arrange
    $manager = createDefaultEmployee();

    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $leaves = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->pending()
        ->count(3)
        ->create();

    // send leave request pending notifications to manager
    $leaves->each(function ($leave) use ($manager) {
        $manager->notify(new NewLeaveRequestNotification($leave));
    });

    // send unrelated notifications
    $manager->notify(new GenericMobileNotification('subject', 'body'));
    // send notification where only notification type match
    $employee->notify(new NewLeaveRequestNotification(Leave::factory()->create()));
    // send notification where only data.payload.id match
    $manager->notify(
        new NewApprovalRequestNotification(
            ApprovalRequest::factory()->create(['id' => $leaves->firstOrFail()->id])
        )
    );

    # act
    $this->jwtActingAsMobile($manager)
        ->putJson('api/v2/mobile/manager/leaves/reject', [
            'all' => true,
            'rejection_reason' => 'test',
        ])
        ->assertOk();

    // assert leave request pending notifications for manager are deleted
    $leaves->each(function ($leave) use ($manager) {
        expect(
            $manager
                ->notifications()
                ->where('type', NewLeaveRequestNotification::class)
                ->where('data->payload->id', $leave->id)
                ->doesntExist()
        )->toBeTrue('pending request notification for leave ' . $leave->id . ' should be deleted');
    });

    // assert unrelated notification is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', GenericMobileNotification::class)
            ->exists()
    )->toBeTrue('unrelated notifications should not be deleted');

    // assert only notification type match is not deleted
    expect(
        $employee
            ->notifications()
            ->where('type', NewLeaveRequestNotification::class)
            ->exists()
    )->toBeTrue('only notification type match should not be deleted');

    // assert only data.payload.id match is not deleted
    expect(
        $manager
            ->notifications()
            ->where('type', NewApprovalRequestNotification::class)
            ->where('data->payload->id', $leaves->firstOrFail()->id)
            ->exists()
    )->toBeTrue('only data.payload.id match should not be deleted');
});
