<?php

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Employee;

it('returns attendances - from to', function () {
    $employee = Employee::factory()->create();

    Attendance::factory()
        ->for($employee)
        ->for($employee->team)
        ->date('2023-01-01')
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($employee->team)
        ->date('2023-01-02')
        ->create();

    Attendance::factory()
        ->for($employee)
        ->for($employee->team)
        ->date('2023-01-03')
        ->create();

    $this->jwtActingAsMobile($employee)
        ->getJson(
            uri: '/api/v2/mobile/attendances/current?filter[from]=2023-01-01&filter[to]=2023-01-02'
        )
        ->assertStatus(200)
        ->assertJsonCount(2, 'data.data');
});

it('returns attendances - status', function () {
    $employee = Employee::factory()->create();

    Attendance::factory()
        ->for($employee)
        ->for($employee->team)
        ->date('2023-01-01')
        ->create(['status' => AttendanceStatus::PRESENT]);

    Attendance::factory()
        ->for($employee)
        ->for($employee->team)
        ->date('2023-01-02')
        ->create(['status' => AttendanceStatus::ABSENT]);

    $this->jwtActingAsMobile($employee)
        ->getJson(
            uri: '/api/v2/mobile/attendances/current?filter[from]=2023-01-01&filter[to]=2023-01-02&filter[status]=PRESENT'
        )
        ->assertStatus(200)
        ->assertJsonCount(1, 'data.data')
        ->assertJsonFragment(['status' => AttendanceStatus::PRESENT]);
});
