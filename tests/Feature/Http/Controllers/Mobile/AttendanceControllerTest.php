<?php

use App\Models\Employee;

test('employee can view his average weekly summary', function () {
    $employee = Employee::factory()->create(['remote_work' => 'allowed']);

    $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/summary/weekly_average')
        ->assertSuccessful();
});

test('employee can view his daily attendance summary', function () {
    $employee = Employee::factory()->create(['remote_work' => 'allowed']);

    $year = today()->year;
    $month = today()->format('m');
    $day = today()->format('d');

    $this->jwtActingAsMobile($employee)
        ->getJson("api/v2/mobile/summary/days?from=$year-$month-$day&to=$year-$month-$day")
        ->assertSuccessful();
});
