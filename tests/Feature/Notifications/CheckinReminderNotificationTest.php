<?php

use App\Models\Attendance;
use App\Notifications\CheckinReminderNotification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

test('it works - send when not checked in', function () {
    $notifiable = createDefaultEmployee();

    $todayAttendance = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->date(today())
        ->yet()
        ->create();

    $notification = new CheckinReminderNotification();

    expect($notification->via($notifiable))->toBe([OneSignalChannel::class, 'database']);

    expect($notification->toArray($notifiable))->toBe([
        'type' => 'CHECKIN_REMINDER',
    ]);

    expect($notification->toOneSignal($notifiable))->toBeInstanceOf(OneSignalMessage::class);

    expect($notification->shouldSend($notifiable))->toBeTrue();
});

test('it works - dont send when check in', function () {
    $notifiable = createDefaultEmployee();

    $todayAttendance = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->date(today())
        ->present(onDuty: true)
        ->create();

    $notification = new CheckinReminderNotification();

    expect($notification->via($notifiable))->toBe([
        'NotificationChannels\OneSignal\OneSignalChannel',
        'database',
    ]);

    expect($notification->toArray($notifiable))->toBe([
        'type' => 'CHECKIN_REMINDER',
    ]);

    expect($notification->toOneSignal($notifiable))->toBeObject();

    expect($notification->shouldSend($notifiable))->toBeFalse();
});
