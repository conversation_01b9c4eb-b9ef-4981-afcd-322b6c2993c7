<?php

use App\Enums\ProofMethod;
use App\Models\Attendance;
use App\Notifications\ProofAttendanceRequested;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

test('it works - send when checked in', function (ProofMethod $proofMethod) {
    $notifiable = createDefaultEmployee();

    $todayAttendance = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->date(today())
        ->present(onDuty: true)
        ->create();

    $notification = new ProofAttendanceRequested($proofMethod);

    expect($notification->via($notifiable))->toBe([OneSignalChannel::class, 'database']);

    expect($notification->toArray($notifiable))->toBe([
        'type' => 'PROOF_OF_ATTENDANCE',
    ]);

    expect($notification->toOneSignal($notifiable))->toBeInstanceOf(OneSignalMessage::class);

    expect($notification->shouldSend($notifiable))->toBeTrue();
})->with(ProofMethod::cases());

test('it works - dont send when not check in', function (ProofMethod $proofMethod) {
    $notifiable = createDefaultEmployee();

    $todayAttendance = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->date(today())
        ->yet()
        ->create();

    $notification = new ProofAttendanceRequested($proofMethod);

    expect($notification->via($notifiable))->toBe([OneSignalChannel::class, 'database']);

    expect($notification->toArray($notifiable))->toBe([
        'type' => 'PROOF_OF_ATTENDANCE',
    ]);

    expect($notification->toOneSignal($notifiable))->toBeInstanceOf(OneSignalMessage::class);

    expect($notification->shouldSend($notifiable))->toBeFalse();
})->with(ProofMethod::cases());
