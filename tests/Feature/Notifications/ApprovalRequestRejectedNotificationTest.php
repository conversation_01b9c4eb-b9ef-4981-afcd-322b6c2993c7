<?php

use App\Models\ApprovalRequest;
use App\Notifications\ApprovalRequestRejectedNotification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

test('it works', function () {
    $notifiable = createDefaultEmployee();

    $approvalRequest = ApprovalRequest::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->pending()
        ->create();

    $notification = new ApprovalRequestRejectedNotification($approvalRequest);

    expect($notification->via($notifiable))->toBe([OneSignalChannel::class, 'database']);

    $payload = $notification->toArray($notifiable);

    expect($payload['type'])->toBe('APPROVAL_REQUEST_REJECTED');
    expect($payload['payload']['id'])->toBe($approvalRequest->id);

    expect($notification->toOneSignal($notifiable))->toBeInstanceOf(OneSignalMessage::class);
});
