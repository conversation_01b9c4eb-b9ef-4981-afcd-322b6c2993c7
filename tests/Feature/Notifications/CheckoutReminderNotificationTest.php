<?php

use App\Models\Attendance;
use App\Notifications\CheckoutReminderNotification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

test('it works - send when checked in', function () {
    $notifiable = createDefaultEmployee();

    $todayAttendance = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->date(today())
        ->present(onDuty: true)
        ->create();

    $notification = new CheckoutReminderNotification();

    expect($notification->via($notifiable))->toBe([OneSignalChannel::class, 'database']);

    expect($notification->toArray($notifiable))->toBe([
        'type' => 'CHECKOUT_REMINDER',
    ]);

    expect($notification->toOneSignal($notifiable))->toBeInstanceOf(OneSignalMessage::class);

    expect($notification->shouldSend($notifiable))->toBeTrue();
});

test('it works - dont send when not check in', function () {
    $notifiable = createDefaultEmployee();

    $todayAttendance = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->date(today())
        ->yet()
        ->create();

    $notification = new CheckoutReminderNotification();

    expect($notification->via($notifiable))->toBe([OneSignalChannel::class, 'database']);

    expect($notification->toArray($notifiable))->toBe([
        'type' => 'CHECKOUT_REMINDER',
    ]);

    expect($notification->toOneSignal($notifiable))->toBeInstanceOf(OneSignalMessage::class);

    expect($notification->shouldSend($notifiable))->toBeFalse();
});
