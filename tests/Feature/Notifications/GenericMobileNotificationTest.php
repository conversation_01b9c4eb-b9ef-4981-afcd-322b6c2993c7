<?php

use App\Notifications\GenericMobileNotification;
use NotificationChannels\OneSignal\OneSignalChannel;
use NotificationChannels\OneSignal\OneSignalMessage;

test('it works', function () {
    $notifiable = createDefaultEmployee();

    $notification = new GenericMobileNotification('subject', 'body');

    expect($notification->subject)->toBe('subject');

    expect($notification->body)->toBe('body');

    expect($notification->via($notifiable))->toBe([OneSignalChannel::class, 'database']);

    expect($notification->toOneSignal($notifiable))->toBeInstanceOf(OneSignalMessage::class);

    expect($notification->toArray($notifiable))->toBe([
        'subject' => 'subject',
        'body' => 'body',
    ]);
});
