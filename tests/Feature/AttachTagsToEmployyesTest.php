<?php

use App\Models\Employee;
use App\Models\Role;
use Database\Seeders\RolesAndPermissions;
use Laravel\Passport\Passport;

beforeEach(function () {
    $this->employee = Employee::factory()->create();

    Passport::actingAs($this->employee);

    $this->seed(RolesAndPermissions::class);

    $this->startRole = Role::first();

    $this->employee->assignRole($this->startRole);
});
it('can attach tags to all employees', function () {
    $employees = Employee::factory()
        ->count(5)
        ->create(['tenant_id' => $this->employee->tenant->id]);

    $tags = [['name' => 'tag1', 'color' => '#9289bd'], ['name' => 'tag2', 'color' => '#674EA7']];

    $this->putJson('api/v1/tenant/employees/attach-tags', [
        'tags' => $tags,
        'all' => true,
    ])
        ->assertOk()
        ->assertJson(['message' => __('tags was added to employees successfully')]);

    $employees->fresh()->each(function ($employee) use ($tags) {
        $employeeTagIds = $employee->tags->pluck('name')->toArray();
        expect($employeeTagIds)
            ->toContain($tags[0]['name'])
            ->and($employeeTagIds)
            ->toContain($tags[1]['name']);
    });
});

it('can attach tags to specific employees', function () {
    $employees = Employee::factory()
        ->count(3)
        ->create(['tenant_id' => $this->employee->tenant->id]);

    $employeeIds = $employees->pluck('id')->take(2)->toArray();

    $tags = [['name' => 'tag1', 'color' => '#9289bd'], ['name' => 'tag2', 'color' => '#674EA7']];

    $this->putJson('api/v1/tenant/employees/attach-tags', [
        'tags' => $tags,
        'all' => false,
        'employees' => $employeeIds,
    ])
        ->assertOk()
        ->assertJson(['message' => __('tags was added to employees successfully')]);

    $employees->fresh()->each(function ($employee) use ($tags, $employeeIds) {
        $employeeTagIds = $employee->tags->pluck('name')->toArray();
        if (in_array($employee->id, $employeeIds)) {
            expect($employeeTagIds)
                ->toContain($tags[0]['name'])
                ->and($employeeTagIds)
                ->toContain($tags[1]['name']);
        } else {
            expect($employeeTagIds)
                ->not()
                ->toContain($tags[0]['name'])
                ->and($employeeTagIds)
                ->not()
                ->toContain($tags[1]['name']);
        }
    });
});

it('can not set all to false without send employeeIds', function () {
    $tags = [['name' => 'tag1', 'color' => '#9289bd'], ['name' => 'tag2', 'color' => '#674EA7']];
    $this->putJson('api/v1/tenant/employees/attach-tags', [
        'tags' => $tags,
        'all' => false,
        'employees' => [],
    ])
        ->assertJsonValidationErrors('employees')
        ->assertStatus(422);
});
