<?php

use App\DTOs\ReportData;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Tag;
use App\Scopes\EarlyLateFilters;
use Carbon\CarbonPeriod;

it('throws exception when applied to non-attendance model', function () {
    $filters = new EarlyLateFilters(new ReportData());

    expect(fn() => $filters(Employee::query()))->toThrow(InvalidArgumentException::class);
});

it('filters by date range', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $inRangeAttendances = Attendance::factory()
        ->for($this->tenant)
        ->createFromPeriod($period);

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($period->start->copy()->subDay())
        ->create();

    $filters = new EarlyLateFilters(
        new ReportData([
            'start_date' => $period->start,
            'end_date' => $period->end,
        ])
    );

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())
        ->toEqual($inRangeAttendances->pluck('id')->sort()->values())
        ->not->toContain($outOfRangeAttendance->id);
});

it('filters by employees', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $includedEmployee = createDefaultEmployee();
    $excludedEmployee = createDefaultEmployee();

    $includedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod($period);

    $excludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod($period);

    $reportData = new ReportData([
        'start_date' => $period->start,
        'end_date' => $period->end,
        'employees_ids' => [$includedEmployee->id],
    ]);

    $filters = new EarlyLateFilters($reportData);

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())->toEqual(
        $includedAttendances->pluck('id')->sort()->values()
    );

    foreach ($excludedAttendances as $attendance) {
        expect($filteredAttendances->pluck('id'))->not->toContain($attendance->id);
    }
});

it('filters by departments', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $includedDepartment = Department::factory()
        ->for($this->tenant)
        ->create();
    $excludedDepartment = Department::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($includedDepartment)
        ->create();

    $excludedEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($excludedDepartment)
        ->create();

    $includedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod($period);

    $excludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod($period);

    $reportData = new ReportData([
        'start_date' => $period->start,
        'end_date' => $period->end,
        'departments_ids' => [$includedDepartment->id],
    ]);

    $filters = new EarlyLateFilters($reportData);

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())->toEqual(
        $includedAttendances->pluck('id')->sort()->values()
    );

    foreach ($excludedAttendances as $attendance) {
        expect($filteredAttendances->pluck('id'))->not->toContain($attendance->id);
    }
});

it('filters by tags', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $includedTag = Tag::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->hasAttached($includedTag)
        ->create();

    $excludedEmployee = createDefaultEmployee();

    $includedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod($period);

    $excludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod($period);

    $reportData = new ReportData([
        'start_date' => $period->start,
        'end_date' => $period->end,
        'tags' => [$includedTag->id],
    ]);

    $filters = new EarlyLateFilters($reportData);

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())->toEqual(
        $includedAttendances->pluck('id')->sort()->values()
    );

    foreach ($excludedAttendances as $attendance) {
        expect($filteredAttendances->pluck('id'))->not->toContain($attendance->id);
    }
});

it('filters by locations', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $includedLocation = Location::factory()
        ->for($this->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for($this->tenant)
        ->hasAttached(
            factory: $includedLocation,
            pivot: ['permanent' => true],
            relationship: 'locations'
        )
        ->create();

    $excludedEmployee = createDefaultEmployee();

    $includedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod($period);

    $excludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod($period);

    $reportData = new ReportData([
        'start_date' => $period->start,
        'end_date' => $period->end,
        'locations' => [$includedLocation->id],
    ]);

    $filters = new EarlyLateFilters($reportData);

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())->toEqual(
        $includedAttendances->pluck('id')->sort()->values()
    );

    foreach ($excludedAttendances as $attendance) {
        expect($filteredAttendances->pluck('id'))->not->toContain($attendance->id);
    }
});

it('filters by shifts', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $includedShift = createDefaultShift();
    $excludedShift = createDefaultShift();

    $includedEmployee = createDefaultEmployee();
    $includedEmployee->shifts()->sync($includedShift);

    $excludedEmployee = createDefaultEmployee();
    $excludedEmployee->shifts()->sync($excludedShift);

    $includedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod($period);

    $excludedAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod($period);

    $reportData = new ReportData([
        'start_date' => $period->start,
        'end_date' => $period->end,
        'shifts' => [$includedShift->id],
    ]);

    $filters = new EarlyLateFilters($reportData);

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())->toEqual(
        $includedAttendances->pluck('id')->sort()->values()
    );

    foreach ($excludedAttendances as $attendance) {
        expect($filteredAttendances->pluck('id'))->not->toContain($attendance->id);
    }
});

it('filters inactive employees by default', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $activeEmployee = Employee::factory()
        ->for($this->tenant)
        ->active()
        ->create();

    $inactiveEmployee = Employee::factory()
        ->for($this->tenant)
        ->inactive()
        ->create();

    $activeAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($activeEmployee)
        ->createFromPeriod($period);

    $inactiveAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($inactiveEmployee)
        ->createFromPeriod($period);

    $reportData = new ReportData([
        'start_date' => $period->start,
        'end_date' => $period->end,
        'showInactiveEmployees' => false,
    ]);

    $filters = new EarlyLateFilters($reportData);

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())->toEqual(
        $activeAttendances->pluck('id')->sort()->values()
    );

    foreach ($inactiveAttendances as $attendance) {
        expect($filteredAttendances->pluck('id'))->not->toContain($attendance->id);
    }
});

it('includes inactive employees when specified', function () {
    $period = CarbonPeriod::create('2024-01-01', '2024-01-31');

    $activeEmployee = Employee::factory()
        ->for($this->tenant)
        ->active()
        ->create();

    $inactiveEmployee = Employee::factory()
        ->for($this->tenant)
        ->inactive()
        ->create();

    $activeAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($activeEmployee)
        ->createFromPeriod($period);

    $inactiveAttendances = Attendance::factory()
        ->for($this->tenant)
        ->for($inactiveEmployee)
        ->createFromPeriod($period);

    $reportData = new ReportData([
        'start_date' => $period->start,
        'end_date' => $period->end,
        'show_inactive_employees' => true,
    ]);

    $filters = new EarlyLateFilters($reportData);

    $filteredAttendances = Attendance::query()->tap($filters)->get();

    expect($filteredAttendances->pluck('id')->sort()->values())->toEqual(
        collect([...$activeAttendances, ...$inactiveAttendances])
            ->pluck('id')
            ->sort()
            ->values()
    );
});
