<?php

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Support\EmployeeAttendanceSummary;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

test('count absent/present days', function () {
    $employee = createDefaultEmployee();

    $period = CarbonPeriod::create('2021-01-01', '2021-01-31');

    $presentPeriod = CarbonPeriod::create('2021-01-01', '2021-01-15');

    $absentPeriod = CarbonPeriod::create('2021-01-16', '2021-01-31');

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->createFromPeriod($presentPeriod, [
            'status' => AttendanceStatus::PRESENT,
        ]);

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->createFromPeriod($absentPeriod, [
            'status' => AttendanceStatus::ABSENT,
        ]);

    $employeeSummary = new EmployeeAttendanceSummary(
        $employee,
        $period->getStartDate(),
        $period->getEndDate()
    );

    expect($employeeSummary->totalAbsentDays())->toBe($absentPeriod->count());
    expect($employeeSummary->totalPresentDays())->toBe($presentPeriod->count());
});

test('calculate early in', function () {
    $employee = createDefaultEmployee();

    $period = CarbonPeriod::create('2021-01-01', '2021-01-31');

    // normal shift
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-01 8:00:00'))
        ->date(date: '2021-01-01', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // early in - 15 minutes
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-02 7:45:00'))
        ->date(date: '2021-01-02', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    // early in - 10 minutes
    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present(checkinDate: Carbon::parse('2021-01-03 7:50:00'))
        ->date(date: '2021-01-03', shiftFrom: '08:00:00', shiftTo: '16:00:00', flexibleHours: 0)
        ->create();

    $employeeSummary = new EmployeeAttendanceSummary(
        $employee,
        $period->getStartDate(),
        $period->getEndDate()
    );

    expect($employeeSummary->calculateEarlyIn())->toBe('00:25:00');
});

test('calculate late in', function () {
    $employee = createDefaultEmployee();

    $period = CarbonPeriod::create('2021-08-01', '2021-08-31');

    Attendance::factory()
        ->for($employee)
        ->for($this->tenant)
        ->present()
        ->createFromPeriod(
            period: CarbonPeriod::create('2021-08-01', '2021-08-03'),
            statuses: [
                '2021-08-01' => '08:00-16:00',
                '2021-08-02' => '08:15-16:00',
                '2021-08-03' => '08:10-16:00',
            ],
            flexibleHours: 0
        );

    $employeeSummary = new EmployeeAttendanceSummary($employee, $period->start, $period->end);

    expect($employeeSummary->calculateLateIn())->toBe('00:25:00');
});
