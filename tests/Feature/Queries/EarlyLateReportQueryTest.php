<?php

use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Queries\EarlyLateReportQuery;
use Carbon\CarbonPeriod;

test('attendance query returns records ordered by employee name and department', function () {
    // Create test data
    $reportTask = ReportTask::factory()
        ->earlyLate()
        ->withData([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
        ])
        ->create();

    $department1 = Department::factory()
        ->for($reportTask->team)
        ->create(['name' => 'A Department']);

    $department2 = Department::factory()
        ->for($reportTask->team)
        ->create(['name' => 'B Department']);

    $employee1 = Employee::factory()
        ->for($reportTask->team)
        ->for($department2)
        ->create(['first_name' => 'Bob']);

    $employee2 = Employee::factory()
        ->for($reportTask->team)
        ->for($department1)
        ->create(['first_name' => 'Alice']);

    // Create attendances for different dates
    $period = CarbonPeriod::create(
        $reportTask->data->period->start,
        $reportTask->data->period->end
    );

    foreach ($period as $date) {
        Attendance::factory()
            ->for($reportTask->team)
            ->for($employee1)
            ->date($date)
            ->create();

        Attendance::factory()
            ->for($reportTask->team)
            ->for($employee2)
            ->date($date)
            ->create();
    }

    // Execute query
    $results = (new EarlyLateReportQuery($reportTask))->attendanceQuery()->get();

    // Assert results are ordered correctly
    expect($results->pluck('employee_id')->first())
        ->toBe($employee2->id)
        ->and($results->pluck('employee_id')->last())
        ->toBe($employee1->id);
});

test('employee query returns only employees with attendance records', function () {
    $reportTask = ReportTask::factory()
        ->earlyLate()
        ->withData([
            'start_date' => now()->subDays(7)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
        ])
        ->create();

    // Employee with attendance
    $employeeWithAttendance = Employee::factory()
        ->for($reportTask->team)
        ->create();

    Attendance::factory()
        ->for($reportTask->team)
        ->for($employeeWithAttendance)
        ->date($reportTask->data->period->start)
        ->create();

    // Employee without attendance
    $employeeWithoutAttendance = Employee::factory()
        ->for($reportTask->team)
        ->create();

    $employees = (new EarlyLateReportQuery($reportTask))->employeeQuery()->get();

    expect($employees)
        ->toHaveCount(1)
        ->and($employees->first()->id)
        ->toBe($employeeWithAttendance->id);
});
