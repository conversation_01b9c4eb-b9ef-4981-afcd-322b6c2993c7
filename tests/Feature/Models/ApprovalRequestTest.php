<?php

use App\Models\ApprovalRequest;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;

test('duration attribute return carbon interval between from_date to to_date', function () {
    $approvalRequest = ApprovalRequest::factory()
        ->date(CarbonPeriod::dates(today()->setTime(10, 0), today()->setTime(12, 0)))
        ->create();

    expect($approvalRequest->duration)->toBeInstanceOf(CarbonInterval::class);
    expect($approvalRequest->duration->totalSeconds)->toEqual(
        CarbonInterval::hour(2)->totalSeconds
    );
});
