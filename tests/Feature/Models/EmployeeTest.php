<?php

use App\Models\Department;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use Carbon\CarbonPeriod;

test('can assign an employee to direct manager', function () {
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['manager_id' => $manager->id]);

    expect($employee->manager->id)->toBe($manager->id);
});

test('can assign an employee to manager via department', function () {
    $manager = Employee::factory()
        ->for($this->tenant)
        ->create();

    $department = Department::factory()
        ->for($this->tenant)
        ->create([
            'manager_id' => $manager->id,
        ]);

    $employee = Employee::factory()
        ->for($this->tenant)
        ->create([
            'department_id' => $department->id,
        ]);

    expect($employee->manager->id)->toBe($manager->id);
});

test(
    'when an employee assigned to direct manager and via department, direct manager will be current manager',
    function () {
        $departmentManager = Employee::factory()
            ->for($this->tenant)
            ->create();
        $directManager = Employee::factory()
            ->for($this->tenant)
            ->create();

        $department = Department::factory()
            ->for($this->tenant)
            ->create([
                'manager_id' => $departmentManager->id,
            ]);

        $employee = Employee::factory()
            ->for($this->tenant)
            ->create([
                'manager_id' => $directManager->id,
                'department_id' => $department->id,
            ]);

        expect($employee->manager->id)->toBe($directManager->id);
    }
);

test('upcomingShifts scope return only today and future shifts', function () {
    // arrange

    # set time to morning and not start of day
    $this->travelTo(today()->setTime(10, 0));

    $permanentShift = Shift::factory()
        ->for($this->tenant)
        ->create();
    $temporaryShift = Shift::factory()
        ->for($this->tenant)
        ->create();

    $pastPeriod = CarbonPeriod::dates(
        today()->subDays(4)->startOfDay(),
        today()->subDay()->endOfDay()
    );
    $pastAcrossTodayPeriod = CarbonPeriod::dates(
        today()->subDays(4)->startOfDay(),
        today()->endOfDay()
    );
    $todayPeriod = CarbonPeriod::dates(today()->startOfDay(), today()->addDays(2)->endOfDay());
    $futurePeriod = CarbonPeriod::dates(
        today()->addDay()->startOfDay(),
        today()->addDays(4)->endOfDay()
    );

    /* @type Employee $employee */
    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentShift($permanentShift)
        ->temporaryShift($temporaryShift, $pastPeriod)
        ->temporaryShift($temporaryShift, $pastAcrossTodayPeriod)
        ->temporaryShift($temporaryShift, $todayPeriod)
        ->temporaryShift($temporaryShift, $futurePeriod)
        ->create();

    // assert
    $expected = collect([
        [
            'shift_id' => $permanentShift->id,
            'permanent' => 1,
        ],
        [
            'shift_id' => $temporaryShift->id,
            'permanent' => 0,
            'start_at' => $pastAcrossTodayPeriod->getStartDate(),
            'end_at' => $pastAcrossTodayPeriod->getEndDate(),
        ],
        [
            'shift_id' => $temporaryShift->id,
            'permanent' => 0,
            'start_at' => $todayPeriod->getStartDate(),
            'end_at' => $todayPeriod->getEndDate(),
        ],
        [
            'shift_id' => $temporaryShift->id,
            'permanent' => 0,
            'start_at' => $futurePeriod->getStartDate(),
            'end_at' => $futurePeriod->getEndDate(),
        ],
    ]);

    expect(
        $employee->upcomingShifts
            ->pluck('pivot')
            ->select('shift_id', 'permanent', 'start_at', 'end_at')
            ->toArray()
    )
        ->toHaveCount(count($expected))
        ->toMatchArray($expected);
});

test('upcomingLocations scope return only today and future locations', function () {
    // arrange

    # set time to morning and not start of day
    $this->travelTo(today()->setTime(10, 0));

    $permanentLocation = Location::factory()
        ->for($this->tenant)
        ->create();
    $temporaryLocation = Location::factory()
        ->for($this->tenant)
        ->create();

    $pastPeriod = CarbonPeriod::dates(
        today()->subDays(4)->startOfDay(),
        today()->subDay()->endOfDay()
    );
    $pastAcrossTodayPeriod = CarbonPeriod::dates(
        today()->subDays(4)->startOfDay(),
        today()->endOfDay()
    );
    $todayPeriod = CarbonPeriod::dates(today()->startOfDay(), today()->addDays(2)->endOfDay());
    $futurePeriod = CarbonPeriod::dates(
        today()->addDay()->startOfDay(),
        today()->addDays(4)->endOfDay()
    );

    /* @type Employee $employee */
    $employee = Employee::factory()
        ->for($this->tenant)
        ->permanentLocation($permanentLocation)
        ->temporaryLocation($temporaryLocation, $pastPeriod)
        ->temporaryLocation($temporaryLocation, $pastAcrossTodayPeriod)
        ->temporaryLocation($temporaryLocation, $todayPeriod)
        ->temporaryLocation($temporaryLocation, $futurePeriod)
        ->create();

    // assert
    $expected = collect([
        [
            'location_id' => $permanentLocation->id,
            'permanent' => 1,
        ],
        [
            'location_id' => $temporaryLocation->id,
            'permanent' => 0,
            'start_date' => $pastAcrossTodayPeriod->getStartDate(),
            'end_date' => $pastAcrossTodayPeriod->getEndDate(),
        ],
        [
            'location_id' => $temporaryLocation->id,
            'permanent' => 0,
            'start_date' => $todayPeriod->getStartDate(),
            'end_date' => $todayPeriod->getEndDate(),
        ],
        [
            'location_id' => $temporaryLocation->id,
            'permanent' => 0,
            'start_date' => $futurePeriod->getStartDate(),
            'end_date' => $futurePeriod->getEndDate(),
        ],
    ]);

    expect(
        $employee->upcomingLocations
            ->pluck('pivot')
            ->select('location_id', 'permanent', 'start_date', 'end_date')
            ->toArray()
    )
        ->toHaveCount(count($expected))
        ->toMatchArray($expected);
});
