<?php

use App\Models\Employee;
use App\Models\Role;
use Database\Seeders\RolesAndPermissions;
use Laravel\Passport\Passport;

beforeEach(function () {
    $this->employee = Employee::factory()->create();

    Passport::actingAs($this->employee);

    $this->seed(RolesAndPermissions::class);

    $this->startRole = Role::first();

    $this->employee->assignRole($this->startRole);
});

it('can create new token', function () {
    $payload = ['name' => 'new token'];

    $this->postJson('api/v1/tenant/tokens', $payload)->assertOk();

    $this->assertEquals(1, currentTenant()->personalAccessTokens()->count());
});

it('can get all his tokens', function () {
    currentTenant()->createToken('new token');

    $this->getJson('api/v1/tenant/tokens')->assertOk()->assertJsonCount(1, 'data.data');
});

it('can delete his token', function () {
    $id = currentTenant()->createToken('new token')->accessToken->id;

    $this->deleteJson("api/v1/tenant/tokens/$id")
        ->assertOk()
        ->assertJson(['message' => __('token has been deleted successfully')]);

    $this->assertEquals(0, currentTenant()->personalAccessTokens()->count());
});

it('can not create token without provided name', function () {
    $payload = ['name' => null];

    $this->postJson('api/v1/tenant/tokens', $payload)
        ->assertStatus(422)
        ->assertJsonValidationErrors('name');
});
