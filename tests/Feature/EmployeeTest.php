<?php

use App\Enums\EmployeePreferredLanguage;
use App\Models\Employee;
use App\Models\Role;
use App\Models\Tenant;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

it('can get their data with tenant', function () {
    $response = $this->actingAsStartAdmin()->getJson('api/v1/tenant/current-employee')->assertOk();

    expect($response->json('data'))
        ->first_name->toBe($this->employee->first_name)
        ->last_name->toBe($this->employee->last_name)
        ->email->toBe($this->employee->email)
        ->position->toBe($this->employee->position)
        ->number->toBe($this->employee->number)
        ->phone->toBe($this->employee->phone->formatE164())
        ->preferred_language->toBe($this->employee->preferred_language->value)
        ->is_active->toBe($this->employee->is_active)
        ->department->id->toBe($this->employee->department->id)
        ->tenant->id->toBe($this->employee->tenant->id);
});

it('can get all employees', function () {
    $tenant = Tenant::factory()->create();

    Employee::factory()
        ->count(3)
        ->create(['tenant_id' => $tenant->id]);

    $this->actingAsStartAdmin()
        ->getJson('api/v1/tenant/employees')
        ->assertOk()
        ->assertJsonCount(3, 'data');
});

it('can create new employees', function (array $payload) {
    $response = $this->actingAsStartAdmin()
        ->postJson('api/v1/tenant/employees/employee/new', $payload)
        ->assertOk()
        ->assertJson(['message' => __('New employee  has been created successfully')]);

    $employee = Employee::firstWhere('email', $payload['email']);

    $rolesInResponse = array_column($response->json('data.roles'), 'id');

    $assignedRoleIds = $employee->roles->pluck('uuid')->toArray();

    expect($rolesInResponse)
        ->toBe($assignedRoleIds)
        ->and($response->json('data'))
        ->id->toBe($employee->id)
        ->first_name->toBe($employee->first_name)
        ->last_name->toBe($employee->last_name);
})->with('full payload');

it('can update his preferred lang', function () {
    $updateLang = ['language' => EmployeePreferredLanguage::English];

    $this->actingAsStartAdmin()
        ->putJson('api/v1/tenant/employees/preferred-language', $updateLang)
        ->assertOk()
        ->assertJson(['message' => __('employee preferred language updated successfully')]);

    expect($this->employee->fresh())->preferred_language->toBe($updateLang['language']);
});

it('can update employee data', function () {
    $roles = Role::get();

    $newEmployee = Employee::factory()->create(['tenant_id' => $this->employee->tenant->id]);

    $updatedPayload = [
        'first_name' => fake()->firstName(),
        'role_ids' => $roles->pluck('uuid')->toArray(),
        'email' => fake()->email(),
        'tags' => [
            ['name' => 'tag1', 'color' => '#9289bd'],
            ['name' => 'tag2', 'color' => '#674EA7'],
        ],
    ];

    $response = $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/employees/{$newEmployee['id']}/update", $updatedPayload)
        ->assertOk();

    $rolesInResponse = array_column($response->json('data.roles'), 'id');

    $assignedRoleIds = $newEmployee->roles->pluck('uuid')->toArray();

    expect($rolesInResponse)
        ->toBe($assignedRoleIds)
        ->and($newEmployee->fresh())
        ->first_name->toBe($updatedPayload['first_name'])
        ->email->toBe($updatedPayload['email']);
});

it('can update employee direct manager', function () {
    $newEmployee = Employee::factory()->create(['tenant_id' => $this->employee->tenant->id]);
    $directManager = Employee::factory()->create(['tenant_id' => $this->employee->tenant->id]);

    $updatedPayload = [
        'manager_id' => $directManager->id,
    ];

    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/employees/{$newEmployee['id']}/update", $updatedPayload)
        ->assertOk();

    expect($newEmployee->fresh())->directManager->id->toBe($directManager->id);
});

it('cannot update employee data that do not belong  to his tenant', function () {
    $newEmployee = Employee::factory()->create();

    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/employees/{$newEmployee['id']}/update", ['first_name' => 'wali'])
        ->assertNotFound();
});

it('can retrieve details of a single employee record', function () {
    $newEmployee = Employee::factory()->create(['tenant_id' => $this->employee->tenant->id]);

    $response = $this->actingAsStartAdmin()
        ->getJson("api/v1/tenant/employees/{$newEmployee['id']}/show")
        ->assertOk();

    expect($response->json('data'))
        ->id->toBe($newEmployee['id'])
        ->first_name->toBe($newEmployee->first_name)
        ->last_name->toBe($newEmployee->last_name);
});

it(
    'cannot  retrieve details of a single employee record that don not belong to their tenant',
    function () {
        $newEmployee = Employee::factory()->create();

        $this->actingAsStartAdmin()
            ->getJson("api/v1/tenant/employees/{$newEmployee['id']}/show")
            ->assertNotFound();
    }
);

it('can activate single employee ', function () {
    $newEmployee = Employee::factory()->create([
        'tenant_id' => $this->employee->tenant->id,
        'is_active' => false,
    ]);

    $updatedData = ['is_active' => true];

    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/employees/{$newEmployee['id']}/activate", $updatedData)
        ->assertOk();

    expect($newEmployee->fresh())->is_active->toBeTruthy();
});

it('cannot activate employee do not belong to their tenant ', function () {
    $newEmployee = Employee::factory()->create(['is_active' => false]);

    $updatedData = ['is_active' => true];

    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/employees/{$newEmployee['id']}/activate", $updatedData)
        ->assertNotFound();
});

it('can deactivate single employee ', function () {
    $newEmployee = Employee::factory()->create([
        'tenant_id' => $this->employee->tenant->id,
        'is_active' => true,
    ]);

    $updatedData = ['is_active' => false];

    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/employees/{$newEmployee['id']}/deactivate", $updatedData)
        ->assertOk();

    expect($newEmployee->fresh())->is_active->toBeFalsy();
});

it('cannot deactivate  employee do not belong to their tenant', function () {
    $newEmployee = Employee::factory()->create(['is_active' => false]);

    $updatedData = ['is_active' => true];

    $this->actingAsStartAdmin()
        ->putJson("api/v1/tenant/employees/{$newEmployee['id']}/deactivate", $updatedData)
        ->assertNotFound();
});

it('can download the employee template', function () {
    $this->actingAsStartAdmin()
        ->getJson('api/v1/tenant/employees/employee/download-template')
        ->assertOk()
        ->assertHeader(
            'Content-Type',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        ->assertDownload();
});

it('only fetches the current tenant employees', function () {
    $anotherTenant = Tenant::factory()->create();

    Employee::factory()
        ->count(3)
        ->create(['tenant_id' => $this->employee->tenant->id]);

    Employee::factory()
        ->count(3)
        ->create(['tenant_id' => $anotherTenant->id]);

    $this->actingAsStartAdmin()
        ->getJson('api/v1/tenant/employees')
        ->assertOk()
        ->assertJsonCount(3, 'data');
});
describe('employees import from excel sheet', function () {
    it('can mock import employees from an Excel file', function () {
        Excel::fake();

        $file = UploadedFile::fake()->create('employees.xlsx', 20);

        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import', ['file' => $file])
            ->assertOk()
            ->assertJson([
                'message' => __('CSV file imported successfully'),
            ]);

        Excel::assertImported('employees.xlsx');
    });

    it('fails when the file is not provided', function () {
        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import')
            ->assertStatus(422)
            ->assertJsonValidationErrors('file');
    });

    it('can upload employee form excel file', function (array $payloads) {
        Storage::fake('local');

        $file = createTestExcelFile([$payloads]);

        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import', [
                'file' => $file,
            ])
            ->assertOk()
            ->assertJson([
                'message' => __('CSV file imported successfully'),
            ]);

        $this->assertDatabaseHas('employees', [
            'email' => $payloads['email'],
            'number' => $payloads['number'],
        ]);
    })->with('payload for excel');

    it('fails to upload employee form if required fields are missing', function (array $payload) {
        Storage::fake('local');

        $file = createTestExcelFile([$payload]);

        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import', [
                'file' => $file,
            ])
            ->assertStatus(422)
            ->assertJsonStructure(['message', 'errors']);
    })->with('payload for excel without first name');

    it('handles duplicate employee records gracefully', function (array $payload) {
        Storage::fake('local');

        $employeeWithEmptyEmployees = Employee::factory()->create();

        $this->actingAsStartAdmin($employeeWithEmptyEmployees)
            ->postJson('api/v1/tenant/employees/employee/import', [
                'file' => createTestExcelFile([$payload]),
            ])
            ->assertOk()
            ->assertJson([
                'message' => __('CSV file imported successfully'),
            ]);

        // 1 employee is already created in the setup + 1 employee in the Excel
        expect($employeeWithEmptyEmployees->tenant->employees()->count())->toBe(2);
    })->with('payload for excel');

    it('rejects invalid file types', function () {
        Storage::fake('local');

        $file = UploadedFile::fake()->create('test-file.png', 100); // Non-Excel file

        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import', [
                'file' => $file,
            ])
            ->assertStatus(422)
            ->assertJsonValidationErrors(['file']);
    });

    it('rejects file with malformed data', function (array $payload) {
        Storage::fake('local');

        $file = createTestExcelFile([$payload]);

        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import', [
                'file' => $file,
            ])
            ->assertStatus(422)
            ->assertJsonStructure(['errors']);
    })->with('payload for excel without first name');

    it('handles empty file upload gracefully', function () {
        Storage::fake('local');

        $file = UploadedFile::fake()->create('empty-file.xlsx', 0);

        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import', [
                'file' => $file,
            ])
            ->assertStatus(422)
            ->assertJsonValidationErrors('file');
    });

    it('can assign employee to direct manager', function (array $payload) {
        Storage::fake('local');

        $file = createTestExcelFile([$payload]);

        $this->actingAsStartAdmin()
            ->postJson('api/v1/tenant/employees/employee/import', [
                'file' => $file,
            ])
            ->assertOk()
            ->assertJson([
                'message' => __('CSV file imported successfully'),
            ]);

        $employee = currentTenant()
            ->employees()
            ->where('email', $payload['email'])
            ->first();

        $this->assertEquals($this->employee->id, $employee->manager_id);
    })->with('payload for excel');
});
