<?php

use App\Enums\ProofMethod;
use App\Enums\ProofStatus;
use App\Enums\RandomProofOfAttendanceDeadline;
use App\Listeners\CreateProofRecord;
use App\Models\Employee;
use App\Models\Team;
use App\Notifications\ProofAttendanceRequested;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\Events\NotificationSent;

test('works', function (ProofMethod $proofMethod) {
    $team = Team::factory()
        ->randomProofNotificationConfig(
            enabled: true,
            deadline: RandomProofOfAttendanceDeadline::fifteen
        )
        ->create();

    $employee = Employee::factory()->for($team)->create();

    $listener = new CreateProofRecord();

    $notificationRecord = DatabaseNotification::create([
        'id' => 1,
        'notifiable_id' => $employee->id,
        'notifiable_type' => Employee::class,
        'data' => ['method' => $proofMethod->value],
        'type' => ProofAttendanceRequested::class,
    ]);

    $notification = new ProofAttendanceRequested($proofMethod);
    $notification->id = $notificationRecord->id;

    $listener->handle(
        new NotificationSent(
            notifiable: $employee,
            notification: $notification,
            channel: 'NotificationChannels\OneSignal\OneSignalChannel'
        )
    );

    $expire = now()->addMinutes(15);

    expect($employee->proofs()->count())->toBe(1);

    $proof = $employee->proofs()->first();

    expect($proof->team_id)->toBe($team->id);
    expect($proof->employee_id)->toBe($employee->id);
    expect($proof->method)->toBe($proofMethod);
    expect($proof->status)->toEqual(ProofStatus::Sent);

    expect($proof->expire_at->toDateTimeString())->toEqual($expire);
})->with(ProofMethod::cases());
