<?php

use App\Events\EmployeeCheckedIn;
use App\Listeners\ScheduleCheckoutReminder;
use App\Models\Activity;
use App\Notifications\CheckoutReminderNotification;
use Illuminate\Support\Carbon;

test('by flexible hours end', function (Activity $activity, ?Carbon $expectedReminderTime) {
    Notification::fake();

    $this->travelTo($activity->created_at);

    (new ScheduleCheckoutReminder())->handle(new EmployeeCheckedIn($activity));

    if (!$expectedReminderTime) {
        Notification::assertNothingSent();
    } else {
        Notification::assertSentTo(
            [$activity->employee],
            CheckoutReminderNotification::class,
            fn(CheckoutReminderNotification $notification) => $expectedReminderTime->equalTo(
                $notification->delay
            )
        );
    }
})->with('by flexible hours end');

test('by check in', function (Activity $activity, ?Carbon $expectedReminderTime) {
    Notification::fake();

    $this->travelTo($activity->created_at);

    (new ScheduleCheckoutReminder())->handle(new EmployeeCheckedIn($activity));

    if (!$expectedReminderTime) {
        Notification::assertNothingSent();
    } else {
        Notification::assertSentTo(
            [$activity->employee],
            CheckoutReminderNotification::class,
            fn(CheckoutReminderNotification $notification) => $expectedReminderTime->equalTo(
                $notification->delay
            )
        );
    }
})->with('by check in');

test('by shift end', function (Activity $activity) {
    Notification::fake();

    $this->travelTo($activity->created_at);

    (new ScheduleCheckoutReminder())->handle(new EmployeeCheckedIn($activity));

    Notification::assertNothingSent();
})->with('by shift end');
