<?php

use App\Enums\RandomProofOfAttendanceDeadline;
use App\Events\EmployeeCheckedIn;
use App\Listeners\ScheduleRandomNotifications;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use Carbon\CarbonImmutable;
use Illuminate\Notifications\SendQueuedNotifications;

/**
 * Helper function to set up test data for notification tests.
 *
 * @param  array  $overrides  Key-value pairs to override the default setup.
 * @return array{team: Team, department: Department, employee: Employee, attendance: Attendance, activity: Activity, now: CarbonImmutable}
 */
function setupNotificationTestData(array $overrides = []): array
{
    $now = CarbonImmutable::now();

    $team = Team::factory()->randomProofNotificationConfig(
        enabled: true,
        count: $overrides['team_count'] ?? 8,
        deadline: RandomProofOfAttendanceDeadline::fortyFive
    );

    $department = Department::factory()
        ->for($team)
        ->randomProofNotificationConfig(
            enabled: true,
            inherited: $overrides['department_inherited'] ?? true,
            count: $overrides['department_count'] ?? 5
        )
        ->create();

    $employee = Employee::factory()
        ->for($team)
        ->for($department)
        ->randomProofNotificationConfig(
            enabled: $overrides['employee_enabled'] ?? true,
            inherited: $overrides['employee_inherited'] ?? false,
            count: $overrides['employee_count'] ?? 2
        )
        ->create();

    $attendance = Attendance::factory()
        ->for($employee)
        ->for($team)
        ->present(checkinDate: $now->setTime(8, 0), onDuty: true)
        ->date(date: $now, shiftFrom: '08:00', shiftTo: '16:00')
        ->create([
            'random_notifications' => false,
        ]);

    $activity = Activity::factory()->for($team)->for($employee)->create();

    return [
        'employee' => $employee,
        'attendance' => $attendance,
        'activity' => $activity,
        'now' => $now,
    ];
}

test('it works - using employee config', function (int $count) {
    Queue::fake();

    [
        'employee' => $employee,
        'attendance' => $attendance,
        'activity' => $activity,
        'now' => $now,
    ] = setupNotificationTestData([
        'employee_count' => $count,
        'employee_inherited' => false, // Use employee-specific values
    ]);

    $listener = new ScheduleRandomNotifications();
    $listener->handle(new EmployeeCheckedIn($activity));

    $start = $now->setTime(8, 0);
    $end = $now->setTime(16, 0);

    $jobs = collect(Queue::pushedJobs()['Illuminate\Notifications\SendQueuedNotifications'])
        ->pluck('job')
        ->unique(fn(SendQueuedNotifications $job) => $job->delay->format('Y-m-d H:i:s'));

    expect($jobs)->toHaveCount($count);

    $jobs->each(function (SendQueuedNotifications $job) use ($start, $end, $employee) {
        static $seenDelays = [];

        expect(in_array($job->delay->format('Y-m-d H:i:s'), $seenDelays))->toBeFalse();
        $seenDelays[] = $job->delay->format('Y-m-d H:i:s');

        expect($job->notifiables[0]->id)->toBe($employee->id);
        expect($job->delay)->toBeBetween($start, $end);
    });

    $attendance->refresh();
    expect($attendance->random_notifications)->toBeTrue();
})->with([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]);

test('it works - using department config', function (int $count) {
    Queue::fake();

    [
        'employee' => $employee,
        'attendance' => $attendance,
        'activity' => $activity,
        'now' => $now,
    ] = setupNotificationTestData([
        'department_count' => $count,
        'department_inherited' => false, // Use department-specific values
        'employee_inherited' => true, // Inherit from department
    ]);

    $listener = new ScheduleRandomNotifications();
    $listener->handle(new EmployeeCheckedIn($activity));

    $start = $now->setTime(8, 0);
    $end = $now->setTime(16, 0);

    $jobs = collect(Queue::pushedJobs()['Illuminate\Notifications\SendQueuedNotifications'])
        ->pluck('job')
        ->unique(fn(SendQueuedNotifications $job) => $job->delay->format('Y-m-d H:i:s'));

    expect($jobs)->toHaveCount($count);

    $jobs->each(function (SendQueuedNotifications $job) use ($start, $end, $employee) {
        static $seenDelays = [];

        expect(in_array($job->delay->format('Y-m-d H:i:s'), $seenDelays))->toBeFalse();
        $seenDelays[] = $job->delay->format('Y-m-d H:i:s');

        expect($job->notifiables[0]->id)->toBe($employee->id);
        expect($job->delay)->toBeBetween($start, $end);
    });

    $attendance->refresh();
    expect($attendance->random_notifications)->toBeTrue();
})->with([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]);

test('it works - using team config', function (int $count) {
    Queue::fake();

    [
        'employee' => $employee,
        'attendance' => $attendance,
        'activity' => $activity,
        'now' => $now,
    ] = setupNotificationTestData([
        'team_count' => $count,
        'department_inherited' => true, // Inherit from team
        'employee_inherited' => true, // Inherit from department
    ]);

    $listener = new ScheduleRandomNotifications();
    $listener->handle(new EmployeeCheckedIn($activity));

    $start = $now->setTime(8, 0);
    $end = $now->setTime(16, 0);

    $jobs = collect(Queue::pushedJobs()['Illuminate\Notifications\SendQueuedNotifications'])
        ->pluck('job')
        ->unique(fn(SendQueuedNotifications $job) => $job->delay->format('Y-m-d H:i:s'));

    expect($jobs)->toHaveCount($count);

    $jobs->each(function (SendQueuedNotifications $job) use ($start, $end, $employee) {
        static $seenDelays = [];

        expect(in_array($job->delay->format('Y-m-d H:i:s'), $seenDelays))->toBeFalse();
        $seenDelays[] = $job->delay->format('Y-m-d H:i:s');

        expect($job->notifiables[0]->id)->toBe($employee->id);
        expect($job->delay)->toBeBetween($start, $end);
    });

    $attendance->refresh();
    expect($attendance->random_notifications)->toBeTrue();
})->with([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]);

test('dont send notification if already sent', function () {
    Queue::fake();

    [
        'attendance' => $attendance,
        'activity' => $activity,
    ] = setupNotificationTestData([
        'employee_count' => 1,
        'employee_inherited' => false, // Use employee-specific values
    ]);

    $attendance->update(['random_notifications' => true]);

    $listener = new ScheduleRandomNotifications();
    $listener->handle(new EmployeeCheckedIn($activity));

    expect(
        collect(
            Queue::pushedJobs()['Illuminate\Notifications\SendQueuedNotifications'] ?? []
        )->pluck('job')
    )->toBeEmpty();
});

test('filter out notifications before checkin', function () {
    Queue::fake();

    [
        'employee' => $employee,
        'attendance' => $attendance,
        'activity' => $activity,
        'now' => $now,
    ] = setupNotificationTestData([
        'employee_count' => 5, // by default, should send 5 notifications
        'employee_inherited' => false, // Use employee-specific values
    ]);

    // but employee is late, check in at 10:00
    $attendance->update(['check_in' => $attendance->check_in->setTime(10, 0)]);

    $listener = new ScheduleRandomNotifications();
    $listener->handle(new EmployeeCheckedIn($activity));

    $start = $now->setTime(8, 0);
    $end = $now->setTime(16, 0);

    $jobs = collect(Queue::pushedJobs()['Illuminate\Notifications\SendQueuedNotifications'])
        ->pluck('job')
        ->unique(fn(SendQueuedNotifications $job) => $job->delay->format('Y-m-d H:i:s'));

    expect($jobs)->toHaveCount(3); // only 3 notifications should be sent

    $jobs->each(function (SendQueuedNotifications $job) use ($start, $end, $employee) {
        static $seenDelays = [];

        expect(in_array($job->delay->format('Y-m-d H:i:s'), $seenDelays))->toBeFalse();
        $seenDelays[] = $job->delay->format('Y-m-d H:i:s');

        expect($job->notifiables[0]->id)->toBe($employee->id);
        expect($job->delay)->toBeBetween($start, $end);
    });

    $attendance->refresh();
    expect($attendance->random_notifications)->toBeTrue();
});

test('dont send notification if not enabled', function () {
    Queue::fake();

    [
        'attendance' => $attendance,
        'activity' => $activity,
    ] = setupNotificationTestData([
        'employee_count' => 1,
        'employee_inherited' => false, // Use employee-specific values
        'employee_enabled' => false,
    ]);

    $attendance->update(['random_notifications' => true]);

    $listener = new ScheduleRandomNotifications();
    $listener->handle(new EmployeeCheckedIn($activity));

    expect(
        collect(
            Queue::pushedJobs()['Illuminate\Notifications\SendQueuedNotifications'] ?? []
        )->pluck('job')
    )->toBeEmpty();
});
