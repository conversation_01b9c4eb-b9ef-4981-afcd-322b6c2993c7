<?php

use App\Enums\AttendanceInType;
use App\Enums\AttendanceStatus;
use App\Enums\CheckoutReminderConfig;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Team;
use Illuminate\Support\Carbon;

function setup(
    CheckoutReminderConfig $reminderConfig,
    string $flexibleHoursInMinutes,
    Carbon $checkIn
): Activity {
    $tenant = Team::factory()
        ->enterprise()
        ->create([
            'active' => true,
            'checkout_reminder_config' => $reminderConfig,
        ]);

    $workingHours = [
        'weekdays' => collect([
            'sunday',
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'saturday',
        ])->mapWithKeys(
            fn($day) => [
                $day => [
                    'from' => '08:00',
                    'to' => '16:00',
                ],
            ]
        ),
        'flexible_hours' => $flexibleHoursInMinutes,
    ];

    $defaultShift = Shift::factory()
        ->for($tenant)
        ->createOne([
            'working_hours' => $workingHours,
            'force_checkout' => Carbon::today()->setTime(20, 0),
            'is_default' => true,
        ]);

    $employee = Employee::factory()
        ->for($tenant)
        ->defaultShift()
        ->createOne(['is_active' => true]);

    $location = Location::factory()->for($tenant)->create();
    $employee->locations()->attach($location, ['permanent' => true]);

    $activity = Activity::factory()
        ->for($tenant)
        ->for($employee)
        ->for($location)
        ->createOne(['action' => AttendanceInType::CHECK_IN, 'created_at' => $checkIn]);

    Attendance::factory()
        ->for($tenant)
        ->for($employee)
        ->date(today())
        ->createOne([
            'status' => AttendanceStatus::PRESENT,
            'shift_id' => $defaultShift->id,
            'check_in' => $checkIn,
            'check_out' => null,
            'net_hours' => today()->startOfDay(),
            'in_type' => $activity->action,
            'out_type' => null,
            'is_weekend' => false,
            'is_holiday' => false,
            'on_duty' => true,
            'active_until' => $defaultShift['force_checkout'],
            'is_adjusted' => false,
            'check_in_location_id' => $activity->location_id,
            'check_out_location_id' => null,
            'have_location' => true,
            'flexible_hours' => $flexibleHoursInMinutes,
        ]);

    return $activity;
}

dataset('by flexible hours end', [
    '1 hour of flexible hours - at 7:00 AM - on time - receive checkout reminder at 2:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(7, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(14, 45),
    ],
    '1 hour of flexible hours - at 8:00 AM - on time - receive checkout reminder at 3:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(8, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(15, 45),
    ],
    '1 hour of flexible hours - at 9:00 AM - on time - receive checkout reminder at 4:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(9, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(16, 45),
    ],
    '1 hour of flexible hours - at 10:00 AM - late - receive checkout reminder at 4:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(10, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(16, 45),
    ],
    '1 hour of flexible hours - at 4:50 PM - before 10 minutes of shift end - do not receive checkout reminder' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(16, 50)
        ),
        'expected_reminder_time' => null,
    ],
    'non-flexible hours - at 8:00 AM - on time - receive checkout reminder at 3:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 0,
            checkIn: Carbon::today()->setTime(8, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(15, 45),
    ],
    'non-flexible hours - at 10:00 AM - late - receive checkout reminder at 3:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 0,
            checkIn: Carbon::today()->setTime(10, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(15, 45),
    ],
    'non-flexible hours - at 3:50 PM - before 10 minutes of shift end - do not receive checkout reminder' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByFlexibleHoursEnd,
            flexibleHoursInMinutes: 0,
            checkIn: Carbon::today()->setTime(15, 50)
        ),
        'expected_reminder_time' => null,
    ],
]);

dataset('by check in', [
    '1 hour of flexible hours - at 7:00 AM - on time - receive checkout reminder at 2:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(7, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(14, 45),
    ],
    '1 hour of flexible hours - at 8:00 AM - on time - receive checkout reminder at 3:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(8, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(15, 45),
    ],
    '1 hour of flexible hours - at 9:00 AM - on time - receive checkout reminder at 4:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(9, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(16, 45),
    ],
    '1 hour of flexible hours - at 10:00 AM - late - receive checkout reminder at 5:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(10, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(17, 45),
    ],
    '1 hour of flexible hours - at 4:50 PM - before 10 minutes of shift end - receive checkout reminder at 12:35 AM next day' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 60,
            checkIn: Carbon::today()->setTime(16, 50)
        ),
        'expected_reminder_time' => Carbon::today()->addDay()->setTime(0, 35),
    ],
    'non-flexible hours - at 8:00 AM - on time - receive checkout reminder at 3:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 0,
            checkIn: Carbon::today()->setTime(8, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(15, 45),
    ],
    'non-flexible hours - at 10:00 AM - late - receive checkout reminder at 5:45 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 0,
            checkIn: Carbon::today()->setTime(10, 0)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(17, 45),
    ],
    'non-flexible hours - at 3:50 PM - before 10 minutes of shift end - receive checkout reminder at 11:35 PM' => fn() => [
        'activity' => setup(
            reminderConfig: CheckoutReminderConfig::ByCheckin,
            flexibleHoursInMinutes: 0,
            checkIn: Carbon::today()->setTime(15, 50)
        ),
        'expected_reminder_time' => Carbon::today()->setTime(23, 35),
    ],
]);

dataset('by shift end', [
    '1 hour of flexible hours - at 7:00 AM - on time - do not receive checkout reminder' => fn() => setup(
        reminderConfig: CheckoutReminderConfig::ByShiftEnd,
        flexibleHoursInMinutes: 60,
        checkIn: Carbon::today()->setTime(7, 0)
    ),
    'non-flexible hours - at 8:00 AM - on time - do not receive checkout reminder' => fn() => setup(
        reminderConfig: CheckoutReminderConfig::ByShiftEnd,
        flexibleHoursInMinutes: 0,
        checkIn: Carbon::today()->setTime(8, 0)
    ),
]);
