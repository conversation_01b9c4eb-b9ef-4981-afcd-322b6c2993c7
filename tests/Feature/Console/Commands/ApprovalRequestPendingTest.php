<?php

use App\Enums\DelegationType;
use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Delegation;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Notifications\NotifyManagersOfApprovalRequests;

it('send a notification for manager when has a pending approval request', function () {
    Notification::fake();

    $tenant = Team::factory()
        ->enterprise()
        ->create(['active' => true, 'approval_request' => true]);

    $manager = Employee::factory()->for($tenant)->create();

    $department = Department::factory()
        ->for($tenant)
        ->create(['manager_id' => $manager->id]);

    $managedEmployee = Employee::factory()->for($tenant)->for($department)->create();

    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending]);

    $this->artisan('attendance:notify-managers');

    Notification::assertSentTo(
        $manager,
        NotifyManagersOfApprovalRequests::class,
        fn(NotifyManagersOfApprovalRequests $notification) => $notification->count === 1
    );
});

test('test cases where some requests are delegated', function () {
    Notification::spy();

    $tenant = Team::factory()
        ->enterprise()
        ->create(['active' => true, 'approval_request' => true]);

    $manager = Employee::factory()->for($tenant)->create();

    $department = Department::factory()
        ->for($tenant)
        ->create(['manager_id' => $manager->id]);

    $managedEmployee = Employee::factory()->for($tenant)->for($department)->create();

    $anotherManagedEmployee = Employee::factory()->for($tenant)->for($department)->create();

    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending, 'type' => ApprovalRequest::REGULARIZATION]);

    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending, 'type' => ApprovalRequest::REMOTE_WORK]);
    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending, 'type' => ApprovalRequest::REMOTE_WORK]);

    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending, 'type' => ApprovalRequest::PERMISSION]);
    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending, 'type' => ApprovalRequest::PERMISSION]);
    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending, 'type' => ApprovalRequest::PERMISSION]);

    delegation::create([
        'type' => DelegationType::PermissionRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $managedEmployee->id,
    ]);
    delegation::create([
        'type' => DelegationType::RemoteWorkRequest,
        'delegatee_id' => $manager->id,
        'delegated_id' => $anotherManagedEmployee->id,
    ]);

    $this->artisan('attendance:notify-managers')->assertOk();

    Notification::assertSentTo(
        $manager,
        NotifyManagersOfApprovalRequests::class,
        fn(NotifyManagersOfApprovalRequests $notification) => $notification->count === 1
    );
    Notification::assertSentTo(
        $managedEmployee,
        NotifyManagersOfApprovalRequests::class,
        fn(NotifyManagersOfApprovalRequests $notification) => $notification->count === 3
    );
    Notification::assertSentTo(
        $anotherManagedEmployee,
        NotifyManagersOfApprovalRequests::class,
        fn(NotifyManagersOfApprovalRequests $notification) => $notification->count === 2
    );
});

it('does not send a notification when tenant is not active', function () {
    Notification::fake();

    $notActiveTenant = Team::factory()->create(['active' => false]);

    $manager = Employee::factory()->for($notActiveTenant)->create();

    $department = Department::factory()
        ->for($notActiveTenant)
        ->create(['manager_id' => $manager->id]);

    ApprovalRequest::factory()
        ->for($notActiveTenant)
        ->for($department)
        ->create(['status' => RequestStatus::Pending]);

    $this->artisan('attendance:notify-managers');

    Notification::assertNotSentTo($manager, NotifyManagersOfApprovalRequests::class);
});

it('does not send a notification when approval request is not pending', function () {
    Notification::fake();

    $tenant = Team::factory()
        ->enterprise()
        ->create(['active' => true, 'approval_request' => true]);

    $manager = Employee::factory()->for($tenant)->create();

    $department = Department::factory()
        ->for($tenant)
        ->create(['manager_id' => $manager->id]);

    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->create(['status' => ApprovalRequest::APPROVED]);

    $this->artisan('attendance:notify-managers');

    Notification::assertNotSentTo($manager, NotifyManagersOfApprovalRequests::class);
});

it('does not send a notification for non-manager employees', function () {
    Notification::fake();

    $tenant = Team::factory()
        ->enterprise()
        ->create(['active' => true, 'approval_request' => true]);

    $employee = Employee::factory()->for($tenant)->create();

    $manager = Employee::factory()->for($tenant)->create();

    $department = Department::factory()
        ->for($tenant)
        ->create(['manager_id' => $manager->id]);

    $managedEmployee = Employee::factory()->for($tenant)->for($department)->create();

    ApprovalRequest::factory()
        ->for($tenant)
        ->for($department)
        ->for($managedEmployee)
        ->create(['status' => RequestStatus::Pending]);

    $this->artisan('attendance:notify-managers');

    Notification::assertNotSentTo($employee, NotifyManagersOfApprovalRequests::class);
    Notification::assertSentTo($manager, NotifyManagersOfApprovalRequests::class);
});
