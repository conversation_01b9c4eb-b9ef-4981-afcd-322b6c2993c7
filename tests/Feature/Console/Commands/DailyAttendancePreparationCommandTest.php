<?php

use App\Enums\AttendanceStatus;
use App\Enums\CheckoutReminderConfig;
use App\Enums\RequestStatus;
use App\Jobs\SendEmployeeStatementIfApplicableJob;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\Leave;
use App\Models\Shift;
use App\Models\Team;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use function Pest\Laravel\artisan;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;
use function Pest\Laravel\travelTo;
use function PHPUnit\Framework\assertTrue;

test('normal', function () {
    $team = Team::factory()
        ->enterprise()
        ->create([
            'checkout_reminder_config' => CheckoutReminderConfig::ByShiftEnd,
        ]);

    $employee = Employee::factory()->for($team)->defaultShift()->create();

    artisan('attendance:prepare')->assertSuccessful();

    expect($employee->activeAttendanceRecord)->not->toBeNull();
});

test('team has employee statement', function () {
    Queue::fake([SendEmployeeStatementIfApplicableJob::class]);

    $team = Team::factory()
        ->enterprise()
        ->create([
            'checkout_reminder_config' => CheckoutReminderConfig::ByShiftEnd,
            'employee_statement_config' => [
                'enabled' => true,
                'prevent_requests_enabled' => true,
                'days_before_preventing_requests' => 1,
                'late_checkin_buffer_minutes' => 15,
                'early_checkout_buffer_minutes' => 15,
            ],
        ]);

    $employee = Employee::factory()->for($team)->defaultShift()->create();

    artisan('attendance:prepare')->assertSuccessful();

    expect($employee->activeAttendanceRecord)->not->toBeNull();
    expect($employee->activeAttendanceRecord->employee_statement_enabled)->toBeTrue();

    Queue::assertPushed(SendEmployeeStatementIfApplicableJob::class);
});

test('team has employee statement - dont send in leave', function () {
    Queue::fake([SendEmployeeStatementIfApplicableJob::class]);

    $team = Team::factory()
        ->enterprise()
        ->create([
            'checkout_reminder_config' => CheckoutReminderConfig::ByShiftEnd,
            'employee_statement_config' => [
                'enabled' => true,
                'prevent_requests_enabled' => true,
                'days_before_preventing_requests' => 1,
                'late_checkin_buffer_minutes' => 15,
                'early_checkout_buffer_minutes' => 15,
            ],
        ]);

    $employee = Employee::factory()->for($team)->defaultShift()->create();

    $leave = Leave::factory()
        ->for($team)
        ->for($employee)
        ->create([
            'from_date' => now()->subDay(),
            'to_date' => now()->addDay(),
            'status' => RequestStatus::Approved,
        ]);

    artisan('attendance:prepare')->assertSuccessful();

    expect($employee->activeAttendanceRecord)->not->toBeNull();

    Queue::assertNotPushed(SendEmployeeStatementIfApplicableJob::class);
});

test('team has employee statement - dont send in weekend', function () {
    Queue::fake([SendEmployeeStatementIfApplicableJob::class]);

    $team = Team::factory()
        ->enterprise()
        ->create([
            'checkout_reminder_config' => CheckoutReminderConfig::ByShiftEnd,
            'employee_statement_config' => [
                'enabled' => true,
                'prevent_requests_enabled' => true,
                'days_before_preventing_requests' => 1,
                'late_checkin_buffer_minutes' => 15,
                'early_checkout_buffer_minutes' => 15,
            ],
        ]);

    $shift = Shift::factory()->allDaysWeekend()->for($team)->create();

    $employee = Employee::factory()->for($team)->permanentShift($shift)->create();

    artisan('attendance:prepare')->assertSuccessful();

    expect($employee->activeAttendanceRecord)->not->toBeNull();

    Queue::assertNotPushed(SendEmployeeStatementIfApplicableJob::class);
});

test('team does not have employee statement', function () {
    Queue::fake([SendEmployeeStatementIfApplicableJob::class]);

    $team = Team::factory()
        ->enterprise()
        ->create([
            'checkout_reminder_config' => CheckoutReminderConfig::ByShiftEnd,
            'employee_statement_config' => [
                'enabled' => false,
                'prevent_requests_enabled' => true,
                'days_before_preventing_requests' => 1,
                'late_checkin_buffer_minutes' => 15,
                'early_checkout_buffer_minutes' => 15,
            ],
        ]);

    $employee = Employee::factory()->for($team)->defaultShift()->create();

    artisan('attendance:prepare')->assertSuccessful();

    expect($employee->activeAttendanceRecord)->not->toBeNull();
    expect($employee->activeAttendanceRecord->employee_statement_enabled)->toBeFalse();

    Queue::assertNotPushed(SendEmployeeStatementIfApplicableJob::class);
});

test('fixable hours get appended to attendance in attendance preparation', function () {
    // prepare
    $flexibleShiftFactory = Shift::factory()
        ->for($this->tenant)
        ->fixableHours();

    $inflexibleShiftFactory = Shift::factory()->for($this->tenant);

    $flexibleEmployee = Employee::factory()
        ->for($this->tenant)
        ->has($flexibleShiftFactory)
        ->create();

    $inflexibleEmployee = Employee::factory()
        ->for($this->tenant)
        ->has($inflexibleShiftFactory)
        ->create();

    artisan('attendance:prepare')->run();

    expect($flexibleEmployee->shift->attendances()->first()->flexible_hours)->not->toEqual(0);
    expect($inflexibleEmployee->shift->attendances()->first()->flexible_hours)->toEqual(0);
});

describe('holiday status management', function () {
    test(
        'is_holiday field must be false and status field must not be HOLIDAY for attendance of all employees of all tenants if today is not a holiday in any tenant',
        function () {
            // prepare
            $firstTenant = Team::factory()->create();
            $secondTenant = Team::factory()->create();

            Employee::factory(5)->for($firstTenant)->create();
            Employee::factory(5)->for($secondTenant)->create();

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseMissing('attendances', ['is_holiday' => true]);
            assertDatabaseMissing('attendances', ['status' => AttendanceStatus::HOLIDAY->value]);
        }
    );

    test(
        'is_holiday field must be false and status field must not be HOLIDAY for attendance of all employees of a tenant if today is a holiday in another tenant',
        function () {
            // prepare
            $tenant = Team::factory()->create();
            $anotherTenant = Team::factory()->create();

            Holiday::factory()
                ->for($anotherTenant)
                ->create([
                    'start_date' => today()->subDays(2)->toDateString(),
                    'end_date' => today()->toDateString(),
                ]);

            Employee::factory(5)->for($tenant)->create();
            Employee::factory(5)->for($anotherTenant)->create();

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseMissing('attendances', ['is_holiday' => true, 'team_id' => $tenant->id]);
            assertDatabaseMissing('attendances', [
                'status' => AttendanceStatus::HOLIDAY->value,
                'team_id' => $tenant->id,
            ]);
        }
    );

    test(
        'is_holiday field must be false and status field must not be HOLIDAY for attendance of all employees of a tenant if yesterday is a holiday in that tenant',
        function () {
            // prepare
            $tenant = Team::factory()->create();

            Holiday::factory()
                ->for($tenant)
                ->create([
                    'start_date' => today()->subDays(3)->toDateString(),
                    'end_date' => today()->subDay()->toDateString(),
                ]);

            Employee::factory(5)->for($tenant)->create();

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseMissing('attendances', ['is_holiday' => true, 'team_id' => $tenant->id]);
            assertDatabaseMissing('attendances', [
                'status' => AttendanceStatus::HOLIDAY->value,
                'team_id' => $tenant->id,
            ]);
        }
    );

    test(
        'is_holiday field must be false and status field must not be HOLIDAY for attendance of all employees of a tenant if tomorrow is a holiday in that tenant',
        function () {
            // prepare
            $tenant = Team::factory()->create();

            Holiday::factory()
                ->for($tenant)
                ->create([
                    'start_date' => today()->addDay()->toDateString(),
                    'end_date' => today()->addDays(2)->toDateString(),
                ]);

            Employee::factory(5)->for($tenant)->create();

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseMissing('attendances', ['is_holiday' => true, 'team_id' => $tenant->id]);
            assertDatabaseMissing('attendances', [
                'status' => AttendanceStatus::HOLIDAY->value,
                'team_id' => $tenant->id,
            ]);
        }
    );

    test(
        'is_holiday field must be true and status field must be HOLIDAY for attendance of all employees of a tenant if today is a holiday and weekday in that tenant',
        function () {
            // prepare
            $tenant = Team::factory()->create();
            $anotherTenant = Team::factory()->create();

            Holiday::factory()
                ->for($tenant)
                ->create([
                    'start_date' => today()->subDays(2)->toDateString(),
                    'end_date' => today()->toDateString(),
                ]);

            $shiftFactory = Shift::factory()->for($tenant);

            Employee::factory(5)->for($tenant)->has($shiftFactory)->create();
            Employee::factory(5)->for($anotherTenant)->has($shiftFactory)->create();

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseMissing('attendances', ['is_holiday' => false, 'team_id' => $tenant->id]);
            assertTrue(
                Attendance::where('status', '!=', AttendanceStatus::HOLIDAY->value)
                    ->where('team_id', $tenant->id)
                    ->doesntExist(),
                'status should be HOLIDAY for attendances in a holiday'
            );

            assertDatabaseMissing('attendances', [
                'is_holiday' => true,
                'team_id' => $anotherTenant->id,
            ]);
            assertTrue(
                Attendance::where('status', '=', AttendanceStatus::HOLIDAY->value)
                    ->where('team_id', $anotherTenant->id)
                    ->doesntExist(),
                'status should be HOLIDAY for attendances in a holiday'
            );
        }
    );

    test(
        'is_holiday field must be true and status field must be HOLIDAY and is_weekend field must be false for attendance of all employees of a tenant if today is a holiday and weekend in that tenant',
        function () {
            // prepare
            $tenant = Team::factory()->create();
            $anotherTenant = Team::factory()->create();

            Holiday::factory()
                ->for($tenant)
                ->create([
                    'start_date' => today()->subDays(2)->toDateString(),
                    'end_date' => today()->toDateString(),
                ]);

            $shift = Shift::factory()->for($tenant)->create();

            $workingHours = $shift['working_hours'];
            $workingHours['weekdays'][strtolower(Carbon::today()->locale('en')->dayName)] = false;
            $shift['working_hours'] = $workingHours;
            $shift->save();

            Employee::factory(5)->for($tenant)->create();
            Employee::factory(5)->for($anotherTenant)->create();

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseMissing('attendances', ['is_holiday' => false, 'team_id' => $tenant->id]);
            assertTrue(
                Attendance::where('status', '!=', AttendanceStatus::HOLIDAY->value)
                    ->where('team_id', $tenant->id)
                    ->doesntExist(),
                'status should be HOLIDAY for attendances in a holiday'
            );
            assertDatabaseMissing('attendances', ['is_weekend' => true, 'team_id' => $tenant->id]);

            assertDatabaseMissing('attendances', [
                'is_holiday' => true,
                'team_id' => $anotherTenant->id,
            ]);
            assertTrue(
                Attendance::where('status', '=', AttendanceStatus::HOLIDAY->value)
                    ->where('team_id', $anotherTenant->id)
                    ->doesntExist(),
                'status should not be HOLIDAY for attendances in a holiday of another tenant'
            );
        }
    );

    test(
        'is_holiday field must be false and status field must be LEAVE for attendance of an employee of a tenant
if today is a holiday and weekday in that tenant and employee has vacation',
        function () {
            Holiday::factory()
                ->for($this->tenant)
                ->create([
                    'start_date' => today()->subDays(2)->toDateString(),
                    'end_date' => today()->toDateString(),
                ]);

            $employee = Employee::factory()
                ->for($this->tenant)
                ->has(Shift::factory()->for($this->tenant))
                ->create();

            // employee has vacation
            Leave::factory()
                ->for($this->tenant)
                ->for($employee)
                ->create([
                    'from_date' => now()->subDay(),
                    'to_date' => now()->addDays(2),
                    'status' => RequestStatus::Approved,
                ]);

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseHas('attendances', [
                'employee_id' => $employee->id,
                'is_holiday' => false,
                'team_id' => $this->tenant->id,
            ]);
            assertDatabaseHas('attendances', [
                'employee_id' => $employee->id,
                'status' => AttendanceStatus::LEAVE->value,
                'team_id' => $this->tenant->id,
            ]);
        }
    );

    test(
        'is_weekend field must be false and status field must be LEAVE for attendance of an employee of a tenant
if today is weekend in that tenant and employee has vacation and tenant policy is "weekend is counted in employee vacation"',
        function () {
            // prepare
            $tenant = Team::factory()
                ->enterprise()
                ->create(['vacation_weekend' => true]);

            $employee = Employee::factory()
                ->for($tenant)
                ->has(Shift::factory()->for($tenant))
                ->create();

            $shift = $employee->shift;

            // weekday shift
            $workingHours = $shift['working_hours'];
            $workingHours['weekdays'][strtolower(Carbon::today()->locale('en')->dayName)] = false;
            $shift['working_hours'] = $workingHours;
            $shift->save();

            Leave::factory()
                ->for($tenant)
                ->for($employee)
                ->create([
                    'from_date' => today()->subDay(),
                    'to_date' => today()->addDay(),
                    'status' => RequestStatus::Approved,
                ]);

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseHas('attendances', [
                'employee_id' => $employee->id,
                'is_weekend' => false,
                'team_id' => $tenant->id,
            ]);
            assertDatabaseHas('attendances', [
                'employee_id' => $employee->id,
                'status' => AttendanceStatus::LEAVE->value,
                'team_id' => $tenant->id,
            ]);
        }
    );

    test(
        'is_weekend field must be true and status field must be WEEKEND for attendance of an employee of a tenant
if today is weekend in that tenant and employee has vacation and tenant policy is "weekend is not counted in employee vacation"',
        function () {
            // prepare
            $tenant = Team::factory()
                ->enterprise()
                ->create(['vacation_weekend' => false]);

            $employee = Employee::factory()
                ->for($tenant)
                ->has(Shift::factory()->for($tenant))
                ->create();

            $shift = $employee->shift;

            // weekday shift
            $workingHours = $shift['working_hours'];
            $workingHours['weekdays'][strtolower(Carbon::today()->locale('en')->dayName)] = false;
            $shift['working_hours'] = $workingHours;
            $shift->save();

            Leave::factory()
                ->for($tenant)
                ->for($employee)
                ->create([
                    'from_date' => today()->subDay(),
                    'to_date' => today()->addDay(),
                    'status' => RequestStatus::Approved,
                ]);

            artisan('attendance:prepare')->assertSuccessful();

            // assert
            assertDatabaseHas('attendances', [
                'employee_id' => $employee->id,
                'is_weekend' => true,
                'team_id' => $tenant->id,
            ]);
            assertDatabaseHas('attendances', [
                'employee_id' => $employee->id,
                'status' => AttendanceStatus::WEEKEND->value,
                'team_id' => $tenant->id,
            ]);
        }
    );
});

test(
    'attendance status should be LEAVE when created record for employee who have leave and register absent does not override it',
    function () {
        $tenant = Team::factory()
            ->enterprise()
            ->create(['vacation_weekend' => true]);
        $employee = Employee::factory()
            ->for($tenant)
            ->has(Shift::factory()->for($tenant))
            ->create();

        // Plan is [1 day leave, 2 days yet, 2 days leave, 1 day yet, 1 day leave]

        // 2024-10-20 to 2024-10-26
        $attendancePeriod = CarbonPeriod::create('2024-10-20', '2024-10-26');

        // 2024-10-20 is leave
        $firstLeavePeriod = CarbonPeriod::create('2024-10-20', '2024-10-20');

        // 2024-10-21 is yet
        // 2024-10-22 is yet

        // 2024-10-23 is leave
        // 2024-10-24 is leave
        $secondLeavePeriod = CarbonPeriod::create('2024-10-23', '2024-10-24');

        // 2024-10-25 is yet

        // 2024-10-26 is leave
        $thirdLeavePeriod = CarbonPeriod::create('2024-10-26', '2024-10-26');

        // Create the first leave
        $leaves = Leave::factory()
            ->for($employee)
            ->for($tenant)
            ->date($firstLeavePeriod)
            ->approved()
            ->create();

        // Create the second leave
        $leaves = Leave::factory()
            ->for($employee)
            ->for($tenant)
            ->date($secondLeavePeriod)
            ->approved()
            ->create();

        // Create the third leave
        $leaves = Leave::factory()
            ->for($employee)
            ->for($tenant)
            ->date($thirdLeavePeriod)
            ->approved()
            ->create();

        // when creating attendances, it should be LEAVE for leave period
        // and YET for other days
        foreach ($attendancePeriod as $date) {
            travelTo($date->addSeconds(fake()->numberBetween(0, 1000)));
            artisan('attendance:prepare')->assertSuccessful();
        }

        expect($employee->attendances->pluck('status')->toArray())->toBe([
            AttendanceStatus::LEAVE->value,
            AttendanceStatus::YET->value,
            AttendanceStatus::YET->value,
            AttendanceStatus::LEAVE->value,
            AttendanceStatus::LEAVE->value,
            AttendanceStatus::WEEKEND->value,
            AttendanceStatus::LEAVE->value,
        ]);

        // move after shifts for all attendances
        travelTo($attendancePeriod->getEndDate()->addHours(20));

        // when register absent, it should not override LEAVE status
        // and only update YET status
        artisan('attendance:register-absence')->assertSuccessful();

        expect($employee->refresh()->attendances->pluck('status')->toArray())->toBe([
            AttendanceStatus::LEAVE->value,
            AttendanceStatus::ABSENT->value,
            AttendanceStatus::ABSENT->value,
            AttendanceStatus::LEAVE->value,
            AttendanceStatus::LEAVE->value,
            AttendanceStatus::WEEKEND->value,
            AttendanceStatus::LEAVE->value,
        ]);
    }
);
