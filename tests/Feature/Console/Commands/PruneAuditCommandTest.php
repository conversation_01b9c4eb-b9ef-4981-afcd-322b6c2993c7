<?php

use App\Models\Employee;
use OwenIt\Auditing\Models\Audit;
use function Pest\Laravel\travelBack;
use function Pest\Laravel\travelTo;

test('audit records older than 3 months is deleted', function () {
    travelTo(now()->subMonths(4));

    $employee = Employee::factory()->create();

    $oldAudit = Audit::create([
        'event' => 'created',
        'auditable_id' => $employee->id,
        'auditable_type' => Employee::class,
    ]);

    travelBack();

    $newAudit = Audit::create([
        'event' => 'created',
        'auditable_id' => $employee->id,
        'auditable_type' => Employee::class,
    ]);

    $this->artisan('audit:prune')->assertSuccessful();

    expect(Audit::find($oldAudit->id))
        ->toBeNull()
        ->and(Audit::find($newAudit->id))
        ->not->toBeNull();
});
