<?php

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Leave;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use function Pest\Laravel\artisan;

test('ensure leaves are synced to attendances', function () {
    $employee = createDefaultEmployee();

    $now = CarbonImmutable::parse('2024-10-20');

    $notSyncedLeavePeriod = CarbonPeriod::create(
        $now->addDays(1),
        $now->addDays(3),
        CarbonPeriod::EXCLUDE_END_DATE
    );

    $attendancesForNotSyncedLeave = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->createFromPeriod($notSyncedLeavePeriod, ['status' => AttendanceStatus::ABSENT]);

    $notSyncedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->date($notSyncedLeavePeriod)
        ->approved()
        ->create([
            'synced_to_attendance_at' => null,
        ]);

    $syncedLeavePeriod = CarbonPeriod::create(
        $now->addDays(4),
        $now->addDays(6),
        CarbonPeriod::EXCLUDE_END_DATE
    );

    $attendancesForSyncedLeave = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->createFromPeriod($syncedLeavePeriod, ['status' => AttendanceStatus::LEAVE]);

    $syncedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->date($syncedLeavePeriod)
        ->approved()
        ->create([
            'synced_to_attendance_at' => now(),
            'updated_at' => null,
        ]);

    $notApprovedLeavePeriod = CarbonPeriod::create(
        $now->addDays(7),
        $now->addDays(9),
        CarbonPeriod::EXCLUDE_END_DATE
    );

    $attendancesForNotApprovedLeave = Attendance::factory()
        ->for($this->tenant)
        ->for($employee)
        ->createFromPeriod($notApprovedLeavePeriod, ['status' => AttendanceStatus::ABSENT]);

    $notApprovedLeave = Leave::factory()
        ->for($this->tenant)
        ->for($employee)
        ->date($notApprovedLeavePeriod)
        ->pending()
        ->create([
            'synced_to_attendance_at' => null,
        ]);

    artisan('leaves:sync-to-attendance')
        ->assertSuccessful()
        ->expectsOutput('Synced 1 leaves to attendances.');

    expect($notSyncedLeave->refresh()->synced_to_attendance_at)->not->toBeNull();

    expect($syncedLeave->refresh()->updated_at)->toBeNull();

    expect($notApprovedLeave->refresh()->synced_to_attendance_at)->toBeNull();

    $attendancesForNotSyncedLeave->each(
        fn(Attendance $attendance) => expect($attendance->refresh()->status)->toBe(
            AttendanceStatus::LEAVE->value
        )
    );
});
