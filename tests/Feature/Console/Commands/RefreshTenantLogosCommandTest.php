<?php

use App\Models\Tenant;

test('refresh logos', function () {
    $tenant = Tenant::factory()->create([
        'white_logo_url' => 'https://example.com/white.png',
        'colored_logo_url' => 'https://example.com/colored.png',
    ]);

    $this->artisan('refresh:tenant-logos')->assertSuccessful();

    $tenant->refresh();

    expect($tenant->white_logo_url)
        ->not->toBe('https://example.com/white.png')
        ->and($tenant->colored_logo_url)
        ->not->toBe('https://example.com/colored.png');
});
