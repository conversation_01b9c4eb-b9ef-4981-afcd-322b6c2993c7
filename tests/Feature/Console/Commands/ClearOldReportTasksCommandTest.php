<?php

use App\Models\ReportTask;
use Illuminate\Support\Facades\Storage;
use function Pest\Laravel\artisan;

it('deletes report tasks older than 15 days', function () {
    Storage::fake();

    $recentTask = ReportTask::factory()
        ->earlyLate()
        ->create([
            'created_at' => now()->subDays(10),
            'file_path' => fake()->filePath(),
        ]);

    $oldTask = ReportTask::factory()
        ->earlyLate()
        ->create([
            'created_at' => now()->subDays(16),
            'file_path' => fake()->filePath(),
        ]);

    artisan('clear:old-reports')->assertSuccessful();

    $this->assertDatabaseMissing('report_tasks', ['id' => $oldTask->id]);

    $this->assertDatabaseHas('report_tasks', ['id' => $recentTask->id]);

    Storage::assertMissing($oldTask->file_path);
});
