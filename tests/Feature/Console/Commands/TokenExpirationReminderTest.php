<?php

use App\Models\Employee;
use App\Models\Team;
use App\Notifications\TokenExpirationNotification;
use Carbon\Carbon;
use function Pest\Laravel\artisan;
use function Pest\Laravel\travelTo;

it('can send a reminder to users with expiring tokens - to developer if exist', function () {
    Notification::fake();

    travelTo(Carbon::parse('2021-01-01'));

    $team = Team::factory()->create(['name' => 'token-expiration-reminder']);

    $developer = Employee::factory()
        ->for($team)
        ->roles(['start-developer'])
        ->create();

    $admin = Employee::factory()
        ->for($team)
        ->roles(['start-admin'])
        ->create();

    // create a token that expire in 2022-01-01
    $this->actioningAsAttendanceDeveloper($admin)
        ->postJson('api/v1/frontend/tokens', ['name' => 'new token'])
        ->assertOk();

    // token expires in 2 weeks
    travelTo(Carbon::parse('2021-12-20'));

    artisan('app:token-expiration-reminder')->assertSuccessful();

    Notification::assertSentTo($developer, TokenExpirationNotification::class);

    Notification::assertNotSentTo($admin, TokenExpirationNotification::class);
});

it(
    'can send a reminder to users with expiring tokens - to admin if developers does not exist',
    function () {
        Notification::fake();

        travelTo(Carbon::parse('2021-01-01'));

        $team = Team::factory()->create(['name' => 'token-expiration-reminder']);

        $admin = Employee::factory()
            ->for($team)
            ->roles(['start-admin'])
            ->create();

        // create a token that expire in 2022-01-01
        $this->actioningAsAttendanceDeveloper($admin)
            ->postJson('api/v1/frontend/tokens', ['name' => 'new token'])
            ->assertOk();

        // token expires in 2 weeks
        travelTo(Carbon::parse('2021-12-20'));

        artisan('app:token-expiration-reminder')->assertSuccessful();

        Notification::assertSentTo($admin, TokenExpirationNotification::class);
    }
);

it('can not send a reminder to users with expiring tokens - if token does not exist', function () {
    Notification::fake();

    travelTo(Carbon::parse('2021-01-01'));

    $team = Team::factory()->create(['name' => 'token-expiration-reminder']);

    $admin = Employee::factory()
        ->for($team)
        ->roles(['start-admin'])
        ->create();

    // we did not create any token...

    // token expires in 2 weeks
    travelTo(Carbon::parse('2021-12-20'));

    artisan('app:token-expiration-reminder')->assertSuccessful();

    Notification::assertNothingSent();
});

it(
    'can not send a reminder to users with expiring tokens - if token does not expire within 2 weeks',
    function () {
        Notification::fake();

        travelTo(Carbon::parse('2021-01-01'));

        $team = Team::factory()->create(['name' => 'token-expiration-reminder']);

        $admin = Employee::factory()
            ->for($team)
            ->roles(['start-admin'])
            ->create();

        // create a token that expire in 2022-01-01
        $this->actioningAsAttendanceDeveloper($admin)
            ->postJson('api/v1/frontend/tokens', ['name' => 'new token'])
            ->assertOk();

        // token expires in 3 weeks
        travelTo(Carbon::parse('2021-12-05'));

        artisan('app:token-expiration-reminder')->assertSuccessful();

        Notification::assertNothingSent();
    }
);
