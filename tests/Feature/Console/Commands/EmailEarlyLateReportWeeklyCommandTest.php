<?php

use App\Enums\EarlyLateNestingPolicy;
use App\Enums\EarlyLatePeriodPolicy;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Workflows\GenerateEarlyLatePeriodicalMailWorkflow;
use Carbon\CarbonInterface;
use <PERSON><PERSON><PERSON><PERSON>\Venture\Facades\Workflow;

test('email early late report weekly', function () {
    Workflow::fake();

    $tenant = Team::factory()
        ->enterprise()
        ->earlyLateConfig(
            enabled: true,
            periodPolicy: EarlyLatePeriodPolicy::WeeklyMonthly,
            nestingPolicy: EarlyLateNestingPolicy::DirectDepartment
        )
        ->create();

    $departmentManager = Employee::factory()->for($tenant)->create();

    $tenantDepartment = Department::factory()
        ->for($tenant)
        ->create(['manager_id' => $departmentManager->id]);

    Employee::factory()
        ->for($tenant)
        ->for($tenantDepartment)
        ->create(['manager_id' => $departmentManager]);

    $this->artisan('reports:email-early-late-report-weekly')->assertSuccessful();

    Workflow::assertStarted(GenerateEarlyLatePeriodicalMailWorkflow::class, function (
        GenerateEarlyLatePeriodicalMailWorkflow $workflow
    ) use ($tenantDepartment, $departmentManager) {
        $data = $workflow->earlyLateMailData();
        return $data->manager->id === $departmentManager->id &&
            in_array($tenantDepartment->id, $data->departmentsIDs) &&
            $data->periodType === EarlyLatePeriodPolicy::Weekly &&
            $data->period->start->equalTo(
                now()
                    ->subDay()
                    ->startOfWeek(CarbonInterface::SUNDAY)
            ) &&
            $data->period->end->equalTo(
                now()
                    ->subDay()
                    ->endOfWeek(CarbonInterface::SATURDAY)
            );
    });
});
