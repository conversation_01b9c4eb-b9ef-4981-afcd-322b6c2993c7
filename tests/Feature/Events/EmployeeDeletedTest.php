<?php

use App\Events\EmployeeDeleted;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches employee deleted event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => '<PERSON>',
    ]);

    // Act
    event(new EmployeeDeleted($employee));

    // Assert
    Event::assertDispatched(EmployeeDeleted::class, function ($event) use ($employee) {
        return $event->employee->id === $employee->id;
    });
});

test('it sets correct properties on employee deleted event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => '<PERSON> Doe',
    ]);

    // Act
    $event = new EmployeeDeleted($employee);

    // Assert
    expect($event)->name->toBe('employee_deleted')->team_id->toBe(123)->employee->toBe($employee);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'id' => 999,
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);

    // Act
    $event = new EmployeeDeleted($employee);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('email', '<EMAIL>')
        ->toHaveKey('id', 999)
        ->toHaveKey('name', 'John Doe')
        ->toHaveKeys($employee->webhookPayload);
});
