<?php

use App\Enums\RequestStatus;
use App\Events\ApprovalRequestCreated;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches approval request created event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
    ]);

    // Act
    event(new ApprovalRequestCreated($approvalRequest));

    // Assert
    Event::assertDispatched(ApprovalRequestCreated::class, function ($event) use (
        $approvalRequest
    ) {
        return $event->approvalRequest->id === $approvalRequest->id;
    });
});

test('it sets correct properties on approval request created event', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'id' => 123,
    ]);

    // Act
    $event = new ApprovalRequestCreated($approvalRequest);

    // Assert
    expect($event)
        ->name->toBe('approvalRequest_created')
        ->id->toBe(123)
        ->team_id->toBe($team->id)
        ->approvalRequest->toBe($approvalRequest);
});

test('it contains all required attributes in payload', function (string $type) {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'status' => RequestStatus::DeprecatedPending->value,
        'type' => $type,
    ]);

    // Act
    $event = new ApprovalRequestCreated($approvalRequest);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('email', '<EMAIL>')
        ->toHaveKey('status', RequestStatus::Pending->value)
        ->toHaveKey('type', \Str::upper($type))
        ->toHaveKeys($approvalRequest->webhookPayload);
})->with([
    ApprovalRequest::REGULARIZATION,
    ApprovalRequest::PERMISSION,
    ApprovalRequest::REMOTE_WORK,
]);
