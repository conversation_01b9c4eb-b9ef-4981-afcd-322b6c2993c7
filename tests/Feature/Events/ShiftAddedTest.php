<?php

use App\Events\ShiftAdded;
use App\Models\Shift;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches shift added event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Morning Shift',
    ]);

    // Act
    event(new ShiftAdded($shift));

    // Assert
    Event::assertDispatched(ShiftAdded::class, function ($event) use ($shift) {
        return $event->shift->id === $shift->id;
    });
});

test('it sets correct properties on shift added event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Morning Shift',
    ]);

    // Act
    $event = new ShiftAdded($shift);

    // Assert
    expect($event)->name->toBe('shift_added')->team_id->toBe(123)->shift->toBe($shift);
});

test('it contains all required data in payload', function () {
    // Arrange
    $workingHours = [
        'weekdays' => [
            'monday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'tuesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'wednesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'thursday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'friday' => false,
            'saturday' => false,
            'sunday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
        ],
        'flexible_hours' => '0',
    ];

    $team = Team::factory()->create();
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Night Shift',
        'id' => 789,
        'working_hours' => $workingHours,
        'force_checkout' => true,
        'is_default' => true,
    ]);

    // Act
    $event = new ShiftAdded($shift);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('id', 789)
        ->toHaveKey('name', 'Night Shift')
        ->toHaveKey('working_hours');

    expect($event->payload['working_hours'])
        ->toBeArray()
        ->toHaveKey('weekdays')
        ->toHaveKey('flexible_hours', '0');

    expect($event->payload['working_hours']['weekdays'])
        ->toHaveKey('monday')
        ->toHaveKey('tuesday')
        ->toHaveKey('wednesday')
        ->toHaveKey('thursday')
        ->toHaveKey('friday', false)
        ->toHaveKey('saturday', false)
        ->toHaveKey('sunday');
});
