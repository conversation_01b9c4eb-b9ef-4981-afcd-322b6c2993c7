<?php

use App\Events\DepartmentUpdated;
use App\Models\Department;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches department updated event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Engineering',
    ]);

    // Act
    event(new DepartmentUpdated($department));

    // Assert
    Event::assertDispatched(DepartmentUpdated::class, function ($event) use ($department) {
        return $event->department->id === $department->id;
    });
});

test('it sets correct properties on department updated event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Engineering',
    ]);

    // Act
    $event = new DepartmentUpdated($department);

    // Assert
    expect($event)
        ->name->toBe('department_updated')
        ->team_id->toBe(123)
        ->department->toBe($department);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Old Department Name',
        'id' => 555,
    ]);

    // Update the department
    $department->name = 'New Department Name';
    $department->save();

    // Act
    $event = new DepartmentUpdated($department);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('id', 555)
        ->toHaveKey('name', 'New Department Name')
        ->toHaveKeys($department->webhookPayload);
});
