<?php

use App\Events\DepartmentAdded;
use App\Models\Department;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches department added event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Engineering',
    ]);

    // Act
    event(new DepartmentAdded($department));

    // Assert
    Event::assertDispatched(DepartmentAdded::class, function ($event) use ($department) {
        return $event->department->id === $department->id;
    });
});

test('it sets correct properties on department added event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Engineering',
    ]);

    // Act
    $event = new DepartmentAdded($department);

    // Assert
    expect($event)
        ->name->toBe('department_added')
        ->team_id->toBe(123)
        ->department->toBe($department);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Human Resources',
    ]);

    // Act
    $event = new DepartmentAdded($department);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('name', 'Human Resources')
        ->toHaveKeys($department->webhookPayload);
});
