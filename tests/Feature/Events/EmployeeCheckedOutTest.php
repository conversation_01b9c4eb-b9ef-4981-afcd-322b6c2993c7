<?php

use App\Events\EmployeeCheckedOut;
use App\Models\Activity;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches employee checked out event with activity', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $activity = Activity::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'action' => 'check_out',
        'lat' => '24.7136',
        'lng' => '46.6753',
    ]);

    // Act
    event(new EmployeeCheckedOut($activity));

    // Assert
    Event::assertDispatched(EmployeeCheckedOut::class, function ($event) use ($activity) {
        return $event->activity->id === $activity->id;
    });
});

test('it sets correct properties on employee checked out event', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $activity = Activity::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'action' => 'check_out',
        'lat' => '24.7136',
        'lng' => '46.6753',
    ]);

    // Act
    $event = new EmployeeCheckedOut($activity);

    // Assert
    expect($event)
        ->name->toBe('employee_checkout')
        ->team_id->toBe($team->id)
        ->activity->toBe($activity);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $activity = Activity::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'action' => 'check_out',
        'lat' => '24.7136',
        'lng' => '46.6753',
        'id' => 123,
        'created_at' => now(),
    ]);

    // Act
    $event = new EmployeeCheckedOut($activity);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKeys(['id', 'action', 'lat', 'lng', 'created_at', 'email'])
        ->email->toBe('<EMAIL>')
        ->action->toBe('check_out')
        ->lat->toBe('24.7136')
        ->lng->toBe('46.6753');
});

test('it sets correct team id from activity', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 999]);
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $activity = Activity::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'action' => 'check_out',
    ]);

    // Act
    $event = new EmployeeCheckedOut($activity);

    // Assert
    expect($event->team_id)->toBe(999);
});
