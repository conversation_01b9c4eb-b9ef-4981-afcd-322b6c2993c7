<?php

use App\Events\EmployeeAdded;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches employee added event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => '<PERSON>',
    ]);

    // Act
    event(new EmployeeAdded($employee));

    // Assert
    Event::assertDispatched(EmployeeAdded::class, function ($event) use ($employee) {
        return $event->employee->id === $employee->id;
    });
});

test('it sets correct properties on employee added event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => '<PERSON>',
    ]);

    // Act
    $event = new EmployeeAdded($employee);

    // Assert
    expect($event)->name->toBe('employee_added')->team_id->toBe(123)->employee->toBe($employee);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => 'John',
        'last_name' => 'Doe',
        'id' => 123,
    ]);

    // Act
    $event = new EmployeeAdded($employee);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('email', '<EMAIL>')
        ->toHaveKey('name', 'John Doe')
        ->toHaveKey('id', 123)
        ->toHaveKeys($employee->webhookPayload);
});
