<?php

use App\Enums\RequestStatus;
use App\Events\ApprovalRequestApproved;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches approval request approved event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
    ]);

    // Act
    event(new ApprovalRequestApproved($approvalRequest));

    // Assert
    Event::assertDispatched(ApprovalRequestApproved::class, function ($event) use (
        $approvalRequest
    ) {
        return $event->approvalRequest->id === $approvalRequest->id;
    });
});

test('it sets correct properties on approval request approved event', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'id' => 123,
    ]);

    // Act
    $event = new ApprovalRequestApproved($approvalRequest);

    // Assert
    expect($event)
        ->name->toBe('approvalRequest_approved')
        ->id->toBe(123)
        ->team_id->toBe($team->id)
        ->approvalRequest->toBe($approvalRequest);
});

test('it contains all required attributes in payload', function (string $type) {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'status' => RequestStatus::Approved->value,
        'type' => $type,
    ]);

    // Act
    $event = new ApprovalRequestApproved($approvalRequest);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('email', '<EMAIL>')
        ->toHaveKey('status', RequestStatus::Approved->value)
        ->toHaveKey('type', strtoupper($type))
        ->toHaveKeys($approvalRequest->webhookPayload);
})->with([
    ApprovalRequest::REGULARIZATION,
    ApprovalRequest::PERMISSION,
    ApprovalRequest::REMOTE_WORK,
]);
