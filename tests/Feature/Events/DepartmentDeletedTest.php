<?php

use App\Events\DepartmentDeleted;
use App\Models\Department;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches department deleted event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Engineering',
    ]);

    // Act
    event(new DepartmentDeleted($department));

    // Assert
    Event::assertDispatched(DepartmentDeleted::class, function ($event) use ($department) {
        return $event->department->id === $department->id;
    });
});

test('it sets correct properties on department deleted event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Engineering',
    ]);

    // Act
    $event = new DepartmentDeleted($department);

    // Assert
    expect($event)
        ->name->toBe('department_deleted')
        ->team_id->toBe(123)
        ->department->toBe($department);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $department = Department::factory()->create([
        'team_id' => $team->id,
        'name' => 'Finance',
        'id' => 999,
    ]);

    // Act
    $event = new DepartmentDeleted($department);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('name', 'Finance')
        ->toHaveKey('id', 999)
        ->toHaveKeys($department->webhookPayload);
});
