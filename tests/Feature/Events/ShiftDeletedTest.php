<?php

use App\Events\ShiftDeleted;
use App\Models\Shift;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches shift deleted event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Obsolete Shift',
    ]);

    // Act
    event(new ShiftDeleted($shift));

    // Assert
    Event::assertDispatched(ShiftDeleted::class, function ($event) use ($shift) {
        return $event->shift->id === $shift->id;
    });
});

test('it sets correct properties on shift deleted event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Obsolete Shift',
    ]);

    // Act
    $event = new ShiftDeleted($shift);

    // Assert
    expect($event)->name->toBe('shift_deleted')->team_id->toBe(123)->shift->toBe($shift);
});

test('it contains all shift data in payload', function () {
    // Arrange
    $workingHours = [
        'weekdays' => [
            'monday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'tuesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'wednesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'thursday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'friday' => false,
            'saturday' => false,
            'sunday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
        ],
        'flexible_hours' => '0',
    ];

    $team = Team::factory()->create();
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Obsolete Shift',
        'id' => 777,
        'working_hours' => $workingHours,
        'force_checkout' => true,
        'is_default' => false,
    ]);

    // Act
    $event = new ShiftDeleted($shift);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('id', 777)
        ->toHaveKey('name', 'Obsolete Shift')
        ->toHaveKey('working_hours');

    expect($event->payload['working_hours'])
        ->toBeArray()
        ->toHaveKey('weekdays')
        ->toHaveKey('flexible_hours', '0');

    expect($event->payload['working_hours']['weekdays'])
        ->toHaveKey('monday')
        ->toHaveKey('tuesday')
        ->toHaveKey('wednesday')
        ->toHaveKey('thursday')
        ->toHaveKey('friday', false)
        ->toHaveKey('saturday', false)
        ->toHaveKey('sunday');
});
