<?php

use App\Events\LocationUpdated;
use App\Models\Location;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches location updated event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Updated Office',
    ]);

    // Act
    event(new LocationUpdated($location));

    // Assert
    Event::assertDispatched(LocationUpdated::class, function ($event) use ($location) {
        return $event->location->id === $location->id;
    });
});

test('it sets correct properties on location updated event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Updated Office',
    ]);

    // Act
    $event = new LocationUpdated($location);

    // Assert
    expect($event)->name->toBe('location_updated')->team_id->toBe(123)->location->toBe($location);
});

test('it contains all location data in payload and reflects changes', function () {
    // Arrange
    $team = Team::factory()->create();
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Old Location Name',
        'id' => 777,
        'lat' => '24.0000',
        'lng' => '46.0000',
    ]);

    // Update location
    $location->name = 'New Location Name';
    $location->lat = '25.0000';
    $location->lng = '47.0000';
    $location->save();

    // Act
    $event = new LocationUpdated($location);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('id', 777)
        ->toHaveKey('name', 'New Location Name')
        ->toHaveKey('lat', '25.0000')
        ->toHaveKey('lng', '47.0000');
});
