<?php

use App\Enums\RequestStatus;
use App\Events\ApprovalRequestRejected;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches approval request rejected event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
    ]);

    // Act
    event(new ApprovalRequestRejected($approvalRequest));

    // Assert
    Event::assertDispatched(ApprovalRequestRejected::class, function ($event) use (
        $approvalRequest
    ) {
        return $event->approvalRequest->id === $approvalRequest->id;
    });
});

test('it sets correct properties on approval request rejected event', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'id' => 123,
    ]);

    // Act
    $event = new ApprovalRequestRejected($approvalRequest);

    // Assert
    expect($event)
        ->name->toBe('approvalRequest_rejected')
        ->id->toBe(123)
        ->team_id->toBe($team->id)
        ->approvalRequest->toBe($approvalRequest);
});

test('it contains all required attributes in payload', function (string $requestType) {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
    ]);

    $approvalRequest = ApprovalRequest::factory()->create([
        'team_id' => $team->id,
        'employee_id' => $employee->id,
        'rejection_reason' => 'Not approved due to policy violation',
        'status' => RequestStatus::Rejected->value,
        'type' => $requestType,
    ]);

    // Act
    $event = new ApprovalRequestRejected($approvalRequest);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('email', '<EMAIL>')
        ->toHaveKey('rejection_reason', 'Not approved due to policy violation')
        ->toHaveKey('status', RequestStatus::Rejected->value)
        ->toHaveKey('type', strtoupper($requestType))
        ->toHaveKeys($approvalRequest->webhookPayload);
})->with([
    ApprovalRequest::REGULARIZATION,
    ApprovalRequest::PERMISSION,
    ApprovalRequest::REMOTE_WORK,
]);
