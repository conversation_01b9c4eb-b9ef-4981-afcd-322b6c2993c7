<?php

use App\Events\ShiftUpdated;
use App\Models\Shift;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches shift updated event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Modified Shift',
    ]);

    // Act
    event(new ShiftUpdated($shift));

    // Assert
    Event::assertDispatched(ShiftUpdated::class, function ($event) use ($shift) {
        return $event->shift->id === $shift->id;
    });
});

test('it sets correct properties on shift updated event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Modified Shift',
    ]);

    // Act
    $event = new ShiftUpdated($shift);

    // Assert
    expect($event)->name->toBe('shift_updated')->team_id->toBe(123)->shift->toBe($shift);
});

test('it contains all shift data in payload and reflects changes', function () {
    // Arrange
    $oldWorkingHours = [
        'weekdays' => [
            'monday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'tuesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'wednesday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'thursday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'friday' => false,
            'saturday' => false,
            'sunday' => [
                'from' => '08:00',
                'to' => '16:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
        ],
        'flexible_hours' => '0',
    ];

    $team = Team::factory()->create();
    $shift = Shift::factory()->create([
        'team_id' => $team->id,
        'name' => 'Old Shift Name',
        'id' => 555,
        'working_hours' => $oldWorkingHours,
        'force_checkout' => false,
        'is_default' => false,
    ]);

    // Update shift with new working hours
    $newWorkingHours = [
        'weekdays' => [
            'monday' => [
                'from' => '10:00',
                'to' => '19:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'tuesday' => [
                'from' => '10:00',
                'to' => '19:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'wednesday' => [
                'from' => '10:00',
                'to' => '19:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'thursday' => [
                'from' => '10:00',
                'to' => '19:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'friday' => [
                'from' => '10:00',
                'to' => '19:00',
                'next_day_checkout' => false,
                'prevent_checkout_after' => null,
            ],
            'saturday' => false,
            'sunday' => false,
        ],
        'flexible_hours' => '1',
    ];

    $shift->name = 'New Shift Name';
    $shift->working_hours = $newWorkingHours;
    $shift->force_checkout = true;
    $shift->is_default = true;
    $shift->save();

    // Act
    $event = new ShiftUpdated($shift);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('id', 555)
        ->toHaveKey('name', 'New Shift Name')
        ->toHaveKey('working_hours');

    expect($event->payload['working_hours'])
        ->toBeArray()
        ->toHaveKey('weekdays')
        ->toHaveKey('flexible_hours', '1');

    expect($event->payload['working_hours']['weekdays'])
        ->toHaveKey('monday')
        ->toHaveKey('tuesday')
        ->toHaveKey('wednesday')
        ->toHaveKey('thursday')
        ->toHaveKey('friday')
        ->toHaveKey('saturday', false)
        ->toHaveKey('sunday', false);

    // Verify the changed values in working_hours
    expect($event->payload['working_hours']['weekdays']['monday']['from'])->toBe('10:00');
    expect($event->payload['working_hours']['weekdays']['monday']['to'])->toBe('19:00');
});
