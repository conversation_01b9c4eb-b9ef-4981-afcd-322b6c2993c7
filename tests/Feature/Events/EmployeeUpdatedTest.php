<?php

use App\Events\EmployeeUpdated;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches employee updated event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => '<PERSON>',
    ]);

    // Act
    event(new EmployeeUpdated($employee));

    // Assert
    Event::assertDispatched(EmployeeUpdated::class, function ($event) use ($employee) {
        return $event->employee->id === $employee->id;
    });
});

test('it sets correct properties on employee updated event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => '<PERSON> Doe',
    ]);

    // Act
    $event = new EmployeeUpdated($employee);

    // Assert
    expect($event)->team_id->toBe(123);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $employee = Employee::factory()->create([
        'team_id' => $team->id,
        'email' => '<EMAIL>',
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);

    // Act
    $event = new EmployeeUpdated($employee);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('id', $employee->id)
        ->toHaveKey('email', '<EMAIL>')
        ->toHaveKey('name', 'John Doe')
        ->toHaveKeys($employee->webhookPayload);
});
