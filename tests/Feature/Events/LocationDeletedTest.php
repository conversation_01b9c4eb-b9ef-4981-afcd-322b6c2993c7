<?php

use App\Events\LocationDeleted;
use App\Models\Location;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches location deleted event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Old Office',
    ]);

    // Act
    event(new LocationDeleted($location));

    // Assert
    Event::assertDispatched(LocationDeleted::class, function ($event) use ($location) {
        return $event->location->id === $location->id;
    });
});

test('it sets correct properties on location deleted event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Old Office',
    ]);

    // Act
    $event = new LocationDeleted($location);

    // Assert
    expect($event)->name->toBe('location_deleted')->team_id->toBe(123)->location->toBe($location);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Old Office',
        'id' => 555,
        'lat' => '24.5678',
        'lng' => '46.8765',
    ]);

    // Act
    $event = new LocationDeleted($location);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('id', 555)
        ->toHaveKey('name', 'Old Office')
        ->toHaveKey('lat', '24.5678')
        ->toHaveKey('lng', '46.8765')
        ->toHaveKeys($location->webhookPayload);
});
