<?php

use App\Events\LocationAdded;
use App\Models\Location;
use App\Models\Team;
use Illuminate\Support\Facades\Event;

test('it dispatches location added event', function () {
    // Arrange
    Event::fake();

    $team = Team::factory()->create();
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Headquarters',
        'lat' => '24.7136',
        'lng' => '46.6753',
    ]);

    // Act
    event(new LocationAdded($location));

    // Assert
    Event::assertDispatched(LocationAdded::class, function ($event) use ($location) {
        return $event->location->id === $location->id;
    });
});

test('it sets correct properties on location added event', function () {
    // Arrange
    $team = Team::factory()->create(['id' => 123]);
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Headquarters',
    ]);

    // Act
    $event = new LocationAdded($location);

    // Assert
    expect($event)->name->toBe('location_added')->team_id->toBe(123)->location->toBe($location);
});

test('it contains all required attributes in payload', function () {
    // Arrange
    $team = Team::factory()->create();
    $location = Location::factory()->create([
        'team_id' => $team->id,
        'name' => 'Branch Office',
        'lat' => '25.1234',
        'lng' => '45.6789',
        'id' => 789,
    ]);

    // Act
    $event = new LocationAdded($location);

    // Assert
    expect($event->payload)
        ->toBeArray()
        ->toHaveKey('name', 'Branch Office')
        ->toHaveKey('lat', '25.1234')
        ->toHaveKey('lng', '45.6789')
        ->toHaveKey('id', 789)
        ->toHaveKeys($location->webhookPayload);
});
