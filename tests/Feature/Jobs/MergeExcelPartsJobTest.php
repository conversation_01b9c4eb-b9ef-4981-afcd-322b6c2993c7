<?php

use App\Enums\ReportTaskStatus;
use App\Interfaces\ExcelPartsMerger;
use App\Jobs\MergeExcelPartsJob;
use App\Models\ReportTask;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;

beforeEach(function () {
    Storage::fake('local');
    Storage::fake('minio');
    Redis::spy();
});

test('merges excel parts successfully', function () {
    $reportTask = ReportTask::factory()->earlyLate()->create();
    $excelPartsMerger = spy(ExcelPartsMerger::class);

    // Set up Redis test data
    $key = "{$reportTask->team_id}_{$reportTask->id}_0";

    Redis::shouldReceive('keys')
        ->with("{$reportTask->team_id}_{$reportTask->id}_*")
        ->once()
        ->andReturn([$key]);

    Redis::shouldReceive('pipeline')
        ->once()
        ->andReturn([json_encode(['test' => 'data'])]);

    Redis::shouldReceive('del')->with($key)->once();

    $mergeJob = new MergeExcelPartsJob(
        reportTask: $reportTask,
        excelPartsMerger: $excelPartsMerger
    );

    // Set up expectations
    $excelPartsMerger->shouldReceive('configureOptions')->once();
    $excelPartsMerger->shouldReceive('beforeMerging')->once();
    $excelPartsMerger->shouldReceive('mergePart')->once();

    $mergeJob->handle();

    // Verify file was stored in minio
    Storage::disk('minio')->assertExists($reportTask->fresh()->file_path);
});

test('cleans up temporary file after merging', function () {
    $reportTask = ReportTask::factory()->earlyLate()->create();
    $excelPartsMerger = spy(ExcelPartsMerger::class);

    $key = "{$reportTask->team_id}_{$reportTask->id}_0";

    Redis::shouldReceive('keys')
        ->with("{$reportTask->team_id}_{$reportTask->id}_*")
        ->once()
        ->andReturn([$key]);

    Redis::shouldReceive('pipeline')
        ->once()
        ->andReturn([json_encode(['test' => 'data'])]);

    Redis::shouldReceive('del')->with($key)->once();

    $mergeJob = new MergeExcelPartsJob(
        reportTask: $reportTask,
        excelPartsMerger: $excelPartsMerger
    );

    $mergeJob->handle();

    // Verify no temporary files remain in local storage
    $tempFiles = Storage::disk('local')->files('temp');
    expect($tempFiles)->toBeEmpty();
});

test('marks report task as completed with correct file path', function () {
    $reportTask = ReportTask::factory()->earlyLate()->create();
    $excelPartsMerger = spy(ExcelPartsMerger::class);

    $key = "{$reportTask->team_id}_{$reportTask->id}_0";

    Redis::shouldReceive('keys')
        ->with("{$reportTask->team_id}_{$reportTask->id}_*")
        ->once()
        ->andReturn([$key]);

    Redis::shouldReceive('pipeline')
        ->once()
        ->andReturn([json_encode(['test' => 'data'])]);

    Redis::shouldReceive('del')->with($key)->once();

    $mergeJob = new MergeExcelPartsJob(
        reportTask: $reportTask,
        excelPartsMerger: $excelPartsMerger
    );

    $mergeJob->handle();

    $reportTask->refresh();

    expect($reportTask->file_path)
        ->not->toBeNull()
        ->and($reportTask->status)
        ->toBe(ReportTaskStatus::Success)
        ->and(Storage::disk('minio')->exists($reportTask->file_path))
        ->toBeTrue();
});

test('handles empty parts list gracefully', function () {
    $reportTask = ReportTask::factory()->earlyLate()->create();
    $excelPartsMerger = spy(ExcelPartsMerger::class);

    Redis::shouldReceive('keys')
        ->with("{$reportTask->team_id}_{$reportTask->id}_*")
        ->once()
        ->andReturn([]);

    $mergeJob = new MergeExcelPartsJob(
        reportTask: $reportTask,
        excelPartsMerger: $excelPartsMerger
    );

    $mergeJob->handle();

    // Should still create and store an empty file
    expect($reportTask->fresh()->file_path)
        ->not->toBeNull()
        ->and(Storage::disk('minio')->exists($reportTask->fresh()->file_path))
        ->toBeTrue();
});
