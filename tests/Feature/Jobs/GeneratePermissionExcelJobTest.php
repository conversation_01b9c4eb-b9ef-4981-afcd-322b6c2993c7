<?php

use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Jobs\GeneratePermissionExcelJob;
use App\Models\ApprovalRequest;
use App\Models\Employee;
use App\Models\ReportTask;
use Carbon\CarbonPeriod;
use function Pest\Laravel\assertDatabaseHas;

test('run permission report job correctly', function ($payload) {
    Excel::fake();

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->permission()
        ->count(3)
        ->create();

    $reportTask = ReportTask::factory()->permission()->create();

    Str::createUuidsUsing(fn() => 'uuid');

    createPermissionJob($payload, $this->user, $reportTask)->handle();

    assertDatabaseHas('report_tasks', [
        'id' => $reportTask->id,
        'status' => ReportTaskStatus::Success,
    ]);

    Excel::assertStored($reportTask->report->name->path('uuid', 'xlsx'), 'minio');

    Str::createUuidsNormally();
})->with('monthly daily');

it('fetch approval requests correctly - date', function ($payload) {
    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $outOfRangePeriod = CarbonPeriod::create(
        $payload['startDate']->subDay(),
        $payload['endDate']->subDay()
    );

    $inRange = ApprovalRequest::factory()
        ->for($this->tenant)
        ->permission()
        ->count(3)
        ->date($period)
        ->create();

    $outOfRange = ApprovalRequest::factory()
        ->for($this->tenant)
        ->permission()
        ->count(3)
        ->date($outOfRangePeriod)
        ->create();

    $job = createPermissionJob($payload);

    $actualPermissions = $job->getPermissions()->pluck('id');

    expect($inRange->count())->toBe(3);

    expect($inRange->pluck('id')->every(fn($id) => $actualPermissions->contains($id)))->toBeTrue();

    expect($actualPermissions)->not->toContain($outOfRange->pluck('id'));
})->with('monthly daily');

it('fetch approval requests correctly - department/employee ids', function ($payload) {
    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $includedDepartment = createDefaultDepartment();

    $includedEmployee = createDefaultEmployee([
        'department_id' => $includedDepartment->id,
    ]);

    $excludedDepartment = createDefaultDepartment();

    $excludedEmployee = createDefaultEmployee([
        'department_id' => $excludedDepartment->id,
    ]);

    $inRange = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($includedDepartment)
        ->for($includedEmployee)
        ->permission()
        ->count(3)
        ->date($period)
        ->create();

    $outOfRange = ApprovalRequest::factory()
        ->for($this->tenant)
        ->for($excludedDepartment)
        ->for($excludedEmployee)
        ->permission()
        ->count(3)
        ->date($period)
        ->create();

    $payload['departments_ids'] = [$includedDepartment->id];
    $payload['employees_ids'] = [$includedEmployee->id];

    $job = createPermissionJob($payload);

    $actualPermissions = $job->getPermissions()->pluck('id');

    expect($inRange->pluck('id')->sort())->toEqual($actualPermissions->sort());

    expect($actualPermissions)->not->toContain($outOfRange->pluck('id'));
})->with('monthly daily');

it('fetch approval requests correctly - permissions only', function ($payload) {
    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $permission = ApprovalRequest::factory()
        ->for($this->tenant)
        ->permission()
        ->count(3)
        ->date($period)
        ->create();

    ApprovalRequest::factory()
        ->for($this->tenant)
        ->remoteWork()
        ->count(3)
        ->date($period)
        ->create();

    $job = createPermissionJob($payload);

    $actualPermissions = $job->getPermissions()->pluck('id');

    expect(
        $permission->pluck('id')->every(fn($id) => $actualPermissions->contains($id))
    )->toBeTrue();
})->with('monthly daily');

function createPermissionJob(
    array $data,
    Employee $employee = null,
    ReportTask $reportTask = null
): GeneratePermissionExcelJob {
    $data = [
        'start_date' => $data['startDate'],
        'end_date' => $data['endDate'] ?? null,
        'type' => $data['type'],
        'departments_ids' => $data['departments_ids'] ?? null,
        'employees_ids' => $data['employees_ids'] ?? null,
        'tags' => $data['tags'] ?? null,
        'locations' => $data['locations'] ?? null,
        'shifts' => $data['shifts'] ?? null,
        'show_inactive_employees' => $data['show_inactive_employees'] ?? null,
        'sheet_mode' => SheetMode::SingleSheet->value,
    ];

    $reportTask?->update(['data' => $data]);

    return new GeneratePermissionExcelJob(
        employee: $employee ?? test()->user,
        reportTask: $reportTask ?? ReportTask::factory()->mood()->withData($data)->create()
    );
}
