<?php

use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Jobs\GenerateUsageRateExcelJob;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Models\Tag;
use function Pest\Laravel\assertDatabaseHas;

test('run usage rate report job correctly', function ($payload) {
    Excel::fake();

    $reportTask = ReportTask::factory()->usageRate()->create();

    Str::createUuidsUsing(fn() => 'uuid');

    createUsageRateJob($payload, $this->user, $reportTask)->handle();

    assertDatabaseHas('report_tasks', [
        'id' => $reportTask->id,
        'status' => ReportTaskStatus::Success,
    ]);

    Excel::assertStored($reportTask->report->name->path('uuid', 'xlsx'), 'minio');

    Str::createUuidsNormally();
})->with('monthly daily');

it('fetch employees correctly - employee ids', function ($payload) {
    $includedEmployee = createDefaultEmployee();

    $excludedEmployee = createDefaultEmployee();

    $payload['employees_ids'] = [$includedEmployee->id];

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - department ids', function ($payload) {
    $includedDepartment = createDefaultDepartment();

    $includedEmployee = createDefaultEmployee([
        'department_id' => $includedDepartment->id,
    ]);

    $excludedDepartment = createDefaultDepartment();

    $excludedEmployee = createDefaultEmployee([
        'department_id' => $excludedDepartment->id,
    ]);

    $payload['departments_ids'] = [$includedDepartment->id];

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - shifts ids', function ($payload) {
    $includedShift = createDefaultShift();

    $includedEmployee = Employee::factory()
        ->for(test()->tenant)
        ->permanentShift($includedShift)
        ->create();

    $excludedShift = createDefaultShift();

    $excludedEmployee = Employee::factory()
        ->for(test()->tenant)
        ->permanentShift($excludedShift)
        ->create();

    $payload['shifts'] = [$includedShift->id];

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - locations ids', function ($payload) {
    $includedLocations = createDefaultLocation();

    $includedEmployee = Employee::factory()
        ->for(test()->tenant)
        ->permanentLocation($includedLocations)
        ->create();

    $excludedLocations = createDefaultLocation();

    $excludedEmployee = Employee::factory()
        ->for(test()->tenant)
        ->permanentLocation($excludedLocations)
        ->create();

    $payload['locations'] = [$includedLocations->id];

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - tags', function ($payload) {
    $includedTag = Tag::factory()
        ->for(test()->tenant)
        ->create();

    $includedEmployee = Employee::factory()
        ->for(test()->tenant)
        ->hasAttached($includedTag)
        ->create();

    $excludedTag = Tag::factory()
        ->for(test()->tenant)
        ->create();

    $excludedEmployee = Employee::factory()
        ->for(test()->tenant)
        ->hasAttached($excludedTag)
        ->create();

    $payload['tags'] = [$includedTag->id];

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));
    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - usage status is "used"', function ($payload) {
    $includedEmployee = createDefaultEmployee(['last_activity_at' => now()]);

    $excludedEmployee = createDefaultEmployee(['last_activity_at' => null]);

    $payload['usage_status'] = true;

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - usage status is "not used"', function ($payload) {
    $includedEmployee = createDefaultEmployee(['last_activity_at' => null]);
    $excludedEmployee = $this->user;
    $excludedEmployee->update(['last_activity_at' => now()]);

    $payload['usage_status'] = false;

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - no usage status filter', function ($payload) {
    $includedEmployees = collect([
        test()->user,
        createDefaultEmployee(['last_activity_at' => now()]),
        createDefaultEmployee(['last_activity_at' => null]),
    ]);

    $job = createUsageRateJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toHaveCount($includedEmployees->count());

    foreach ($includedEmployees as $includedEmployee) {
        expect($actualEmployees)->toContain($includedEmployee->id);
    }
})->with('monthly daily');

function createUsageRateJob(
    array $data,
    Employee $employee = null,
    ReportTask $reportTask = null
): GenerateUsageRateExcelJob {
    $data = [
        'start_date' => $data['startDate'],
        'end_date' => $data['endDate'] ?? null,
        'type' => $data['type'],
        'departments_ids' => $data['departments_ids'] ?? null,
        'employees_ids' => $data['employees_ids'] ?? null,
        'tags' => $data['tags'] ?? null,
        'locations' => $data['locations'] ?? null,
        'shifts' => $data['shifts'] ?? null,
        'usage_status' => $data['usage_status'] ?? null,
        'show_inactive_employees' => $data['show_inactive_employees'] ?? null,
        'sheet_mode' => SheetMode::SingleSheet->value,
    ];

    $reportTask?->update(['data' => $data]);

    return new GenerateUsageRateExcelJob(
        employee: $employee ?? test()->user,
        reportTask: $reportTask ?? ReportTask::factory()->mood()->withData($data)->create()
    );
}
