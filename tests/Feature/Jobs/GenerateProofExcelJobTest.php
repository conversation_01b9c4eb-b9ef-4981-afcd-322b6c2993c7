<?php

use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Jobs\GenerateProofStatusExcelJob;
use App\Models\Employee;
use App\Models\Proof;
use App\Models\ReportTask;
use Carbon\CarbonPeriod;
use function Pest\Laravel\assertDatabaseHas;

test('run proof report job correctly', function ($payload) {
    Excel::fake();

    Proof::factory(3)
        ->for($this->tenant)
        ->create();

    $reportTask = ReportTask::factory()->proofStatus()->create();

    Str::createUuidsUsing(fn() => 'uuid');

    createProofJob($payload, $this->user, $reportTask)->handle();

    assertDatabaseHas('report_tasks', [
        'id' => $reportTask->id,
        'status' => ReportTaskStatus::Success,
    ]);

    Excel::assertStored($reportTask->report->name->path('uuid', 'xlsx'), 'minio');

    Str::createUuidsNormally();
})->with('monthly daily');

it('fetch proofs correctly - date', function ($payload) {
    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $outOfRangePeriod = CarbonPeriod::create(
        $payload['startDate']->subYear(),
        $payload['endDate']->subYear()
    );

    $inRange = Proof::factory()
        ->for($this->tenant)
        ->createFromPeriod($period)
        ->sortByDesc('created_at');

    $outOfRange = Proof::factory()
        ->for($this->tenant)
        ->createFromPeriod($outOfRangePeriod);

    $job = createProofJob($payload);

    $actualProofs = $job->getProofs()->pluck('id');

    expect($inRange->pluck('id'))->toEqual($actualProofs);

    expect($actualProofs)->not->toContain($outOfRange->pluck('id'));
})->with('monthly daily');

it('fetch proofs correctly - department/employee ids', function ($payload) {
    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $includedDepartment = createDefaultDepartment();

    $includedEmployee = createDefaultEmployee([
        'department_id' => $includedDepartment->id,
    ]);

    $excludedDepartment = createDefaultDepartment();

    $excludedEmployee = createDefaultEmployee([
        'department_id' => $excludedDepartment->id,
    ]);

    $inRange = Proof::factory()
        ->for($this->tenant)
        ->for($includedEmployee)
        ->createFromPeriod($period)
        ->sortByDesc('created_at');

    $outOfRange = Proof::factory()
        ->for($this->tenant)
        ->for($excludedEmployee)
        ->createFromPeriod($period);

    $payload['departments_ids'] = [$includedDepartment->id];
    $payload['employees_ids'] = [$includedEmployee->id];

    $job = createProofJob($payload);

    $actualProofs = $job->getProofs()->pluck('id');

    expect($inRange->pluck('id'))->toEqual($actualProofs);

    expect($actualProofs)->not->toContain($outOfRange->pluck('id'));
})->with('monthly daily');

function createProofJob(
    array $data,
    Employee $employee = null,
    ReportTask $reportTask = null
): GenerateProofStatusExcelJob {
    $data = [
        'start_date' => $data['startDate'],
        'end_date' => $data['endDate'] ?? null,
        'type' => $data['type'],
        'departments_ids' => $data['departments_ids'] ?? null,
        'employees_ids' => $data['employees_ids'] ?? null,
        'tags' => $data['tags'] ?? null,
        'locations' => $data['locations'] ?? null,
        'shifts' => $data['shifts'] ?? null,
        'show_inactive_employees' => $data['show_inactive_employees'] ?? null,
        'sheet_mode' => SheetMode::SingleSheet->value,
    ];

    $reportTask?->update(['data' => $data]);

    return new GenerateProofStatusExcelJob(
        employee: $employee ?? test()->user,
        reportTask: $reportTask ?? ReportTask::factory()->mood()->withData($data)->create()
    );
}
