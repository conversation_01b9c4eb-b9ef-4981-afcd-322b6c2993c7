<?php

use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Jobs\GenerateMoodExcelJob;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use Carbon\CarbonPeriod;
use function Pest\Laravel\assertDatabaseHas;

test('run mood report job correctly', function ($payload) {
    Excel::fake();

    $inRangeAttendances = Attendance::factory()
        ->for($this->tenant)
        ->present()
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->present()
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $reportTask = ReportTask::factory()->mood()->create();

    Str::createUuidsUsing(fn() => 'uuid');

    createMoodJob($payload, $this->user, $reportTask)->handle();

    assertDatabaseHas('report_tasks', [
        'id' => $reportTask->id,
        'status' => ReportTaskStatus::Success,
    ]);

    Excel::assertStored($reportTask->report->name->path('uuid', 'xlsx'), 'minio');

    Str::createUuidsNormally();
})->with('monthly daily');

it('fetch attendance records correctly - date', function ($payload) {
    $inRangeAttendances = Attendance::factory()
        ->for($this->tenant)
        ->present()
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->present()
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    assertAttendanceRecords(
        actualAttendances: createMoodJob($payload)->getAttendances(),
        expectedInRange: $inRangeAttendances,
        expectedOutOfRange: $outOfRangeAttendance
    );
})->with('monthly daily');

function createMoodJob(
    array $data,
    Employee $employee = null,
    ReportTask $reportTask = null
): GenerateMoodExcelJob {
    $data = [
        'start_date' => $data['startDate'],
        'end_date' => $data['endDate'] ?? null,
        'type' => $data['type'],
        'departments_ids' => $data['departments_ids'] ?? null,
        'employees_ids' => $data['employees_ids'] ?? null,
        'tags' => $data['tags'] ?? null,
        'locations' => $data['locations'] ?? null,
        'shifts' => $data['shifts'] ?? null,
        'show_inactive_employees' => $data['show_inactive_employees'] ?? null,
        'sheet_mode' => SheetMode::SingleSheet->value,
    ];

    $reportTask?->update(['data' => $data]);

    return new GenerateMoodExcelJob(
        employee: $employee ?? test()->user,
        reportTask: $reportTask ?? ReportTask::factory()->mood()->withData($data)->create()
    );
}
