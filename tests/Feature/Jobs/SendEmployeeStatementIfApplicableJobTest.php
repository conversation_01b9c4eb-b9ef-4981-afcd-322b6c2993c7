<?php

use App\DTOs\EmployeeStatementConfig;
use App\Enums\AttendanceStatus;
use App\Enums\EmployeeStatementType;
use App\Jobs\SendEmployeeStatementIfApplicableJob;
use App\Models\Attendance;
use App\Models\Team;
use App\Notifications\EmployeeStatementNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;
use function Pest\Laravel\travelTo;

test('no notification sent when employee statement is disabled', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(enabled: false),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'check_in' => Carbon::create('2021-01-01 09:16:00'),
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertNothingSent();
    expect($attendance->refresh()->employeeStatement)->toBeNull();
});

test('sends absent notification', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            absentEnabled: true
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'status' => AttendanceStatus::ABSENT->value,
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertSentTo($attendance->employee, function (
        EmployeeStatementNotification $notification
    ) {
        expect($notification->employeeStatementType)->toBe(EmployeeStatementType::Absent);

        return true;
    });

    $attendance->refresh();
    expect($attendance->employeeStatement)
        ->not->toBeNull()
        ->and($attendance->employeeStatement->type)
        ->toBe(EmployeeStatementType::Absent)
        ->and($attendance->employeeStatement->employee_id)
        ->toBe($attendance->employee_id);
});

test('sends absent notification when attendance is yet but shift has ended', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            absentEnabled: true
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 10:00:00'),
            'status' => AttendanceStatus::YET->value,
        ]);

    travelTo('2021-01-01 11:00:00');

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertSentTo($attendance->employee, function (
        EmployeeStatementNotification $notification
    ) {
        expect($notification->employeeStatementType)->toBe(EmployeeStatementType::Absent);

        return true;
    });

    $attendance->refresh();
    expect($attendance->employeeStatement)
        ->not->toBeNull()
        ->and($attendance->employeeStatement->type)
        ->toBe(EmployeeStatementType::Absent)
        ->and($attendance->employeeStatement->employee_id)
        ->toBe($attendance->employee_id);
});

test('does not send absent notification if absent is disabled', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            absentEnabled: false
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'status' => AttendanceStatus::ABSENT->value,
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertNothingSent();

    expect($attendance->refresh()->employeeStatement)->toBeNull();
});

test('sends late check-in notification', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            lateCheckinBufferMinutes: 15
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 17:00:00'),
            'check_in' => Carbon::create('2021-01-01 09:16:00'), // 16 minutes late
            'check_out' => Carbon::create('2021-01-01 17:00:00'),
        ]);

    travelTo('2021-01-01 09:16:00');

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertSentTo(
        $attendance->employee,
        fn(EmployeeStatementNotification $notification) => $notification->employeeStatementType ===
            EmployeeStatementType::LateCheckin
    );

    $attendance->refresh();
    expect($attendance->employeeStatement)
        ->not->toBeNull()
        ->and($attendance->employeeStatement->type)
        ->toBe(EmployeeStatementType::LateCheckin)
        ->and($attendance->employeeStatement->employee_id)
        ->toBe($attendance->employee_id);
});

test('sends early checkout notification', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            lateCheckinBufferMinutes: 15
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->present()
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 17:00:00'),
            'check_in' => Carbon::create('2021-01-01 09:00:00'),
            'check_out' => Carbon::create('2021-01-01 16:40:00'), // 20 minutes early
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertSentTo(
        $attendance->employee,
        fn(EmployeeStatementNotification $notification) => $notification->employeeStatementType ===
            EmployeeStatementType::EarlyCheckout
    );

    $attendance->refresh();

    expect($attendance->employeeStatement)
        ->not->toBeNull()
        ->and($attendance->employeeStatement->type)
        ->toBe(EmployeeStatementType::EarlyCheckout)
        ->and($attendance->employeeStatement->employee_id)
        ->toBe($attendance->employee_id);
});

test('sends late checkin and early checkout notification', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            lateCheckinBufferMinutes: 15
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 17:00:00'),
            'check_in' => Carbon::create('2021-01-01 09:20:00'), // 20 minutes late
            'check_out' => Carbon::create('2021-01-01 16:40:00'), // 20 minutes early
        ]);

    travelTo('2021-01-01 09:20:00');

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertSentTo(
        $attendance->employee,
        fn(EmployeeStatementNotification $notification) => $notification->employeeStatementType ===
            EmployeeStatementType::LateCheckinAndEarlyCheckout
    );

    $attendance->refresh();
    expect($attendance->employeeStatement)
        ->not->toBeNull()
        ->and($attendance->employeeStatement->type)
        ->toBe(EmployeeStatementType::LateCheckinAndEarlyCheckout)
        ->and($attendance->employeeStatement->employee_id)
        ->toBe($attendance->employee_id);
});

test('no notification sent when within buffer time', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            lateCheckinBufferMinutes: 15
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 17:00:00'),
            'check_in' => Carbon::create('2021-01-01 09:10:00'), // 10 minutes late (within buffer)
            'check_out' => Carbon::create('2021-01-01 16:50:00'), // 10 minutes early (within buffer)
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertNothingSent();
    expect($attendance->refresh()->employeeStatement)->toBeNull();
});

test('uses flexible hours instead of buffer when available', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            lateCheckinBufferMinutes: 15
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 45,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 17:00:00'),
            'check_in' => Carbon::create('2021-01-01 09:40:00'), // 40 minutes late
            'check_out' => Carbon::create('2021-01-01 16:20:00'), // 20 minutes early
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertNothingSent();
    expect($attendance->refresh()->employeeStatement)->toBeNull();
});

test('no notification when check_in is null', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            lateCheckinBufferMinutes: 15
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 17:00:00'),
            'check_in' => null,
            'check_out' => Carbon::create('2021-01-01 17:00:00'),
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertNothingSent();
    expect($attendance->refresh()->employeeStatement)->toBeNull();
});

test('no notification when check_out is null', function () {
    Notification::fake();

    $team = Team::factory()->create([
        'employee_statement_config' => new EmployeeStatementConfig(
            enabled: true,
            lateCheckinBufferMinutes: 15
        ),
    ]);

    $attendance = Attendance::factory()
        ->for($team)
        ->create([
            'flexible_hours' => 0,
            'shift_from' => Carbon::create('2021-01-01 09:00:00'),
            'shift_to' => Carbon::create('2021-01-01 17:00:00'),
            'check_in' => Carbon::create('2021-01-01 09:00:00'),
            'check_out' => null,
        ]);

    (new SendEmployeeStatementIfApplicableJob($attendance))->handle();

    Notification::assertNothingSent();
    expect($attendance->refresh()->employeeStatement)->toBeNull();
});
