<?php

use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Jobs\GenerateLeaveExcelJob;
use App\Models\Employee;
use App\Models\Leave;
use App\Models\ReportTask;
use Carbon\CarbonPeriod;
use function Pest\Laravel\assertDatabaseHas;

test('run leave report job correctly', function ($payload) {
    Excel::fake();

    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $inRangeLeaves = Leave::factory()
        ->for($this->tenant)
        ->date($period)
        ->create();

    $outOfRangeLeave = Leave::factory()
        ->for($this->tenant)
        ->date(CarbonPeriod::create($payload['startDate']->subDay(), $payload['endDate']->subDay()))
        ->count(1)
        ->create();

    $reportTask = ReportTask::factory()->leave()->create();

    Str::createUuidsUsing(fn() => 'uuid');

    createLeaveJob($payload, $this->user, $reportTask)->handle();

    assertDatabaseHas('report_tasks', [
        'id' => $reportTask->id,
        'status' => ReportTaskStatus::Success,
    ]);

    Excel::assertStored($reportTask->report->name->path('uuid', 'xlsx'), 'minio');

    Str::createUuidsNormally();
})->with('monthly daily');

it('fetch employees correctly - employee ids', function ($payload) {
    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $includedEmployee = createDefaultEmployee();

    $excludedEmployee = createDefaultEmployee();

    $includedLeave = Leave::factory()
        ->for($this->tenant)
        ->date($period)
        ->for($includedEmployee)
        ->create();

    $excludedLeave = Leave::factory()
        ->for($this->tenant)
        ->date($period)
        ->for($excludedEmployee)
        ->create();

    $payload['employees_ids'] = [$includedEmployee->id];

    $job = createLeaveJob($payload);

    $actualLeaves = $job->leaves()->collect()->pluck('id');

    expect($actualLeaves)->toEqual(collect($includedLeave->id));

    expect($actualLeaves)->not->toContain($excludedLeave->id);
})->with('monthly daily');

it('fetch employees correctly - department ids', function ($payload) {
    $period = CarbonPeriod::create($payload['startDate'], $payload['endDate']);

    $includedDepartment = createDefaultDepartment();

    $includedEmployee = createDefaultEmployee(['department_id' => $includedDepartment->id]);

    $includedLeave = Leave::factory()
        ->for($this->tenant)
        ->date($period)
        ->for($includedEmployee)
        ->for($includedDepartment)
        ->create();

    $excludedLeave = Leave::factory()
        ->for($this->tenant)
        ->date($period)
        ->create();

    $payload['departments_ids'] = [$includedDepartment->id];

    $job = createLeaveJob($payload);

    $actualLeaves = $job->leaves()->collect()->pluck('id');

    expect($actualLeaves)->toEqual(collect($includedLeave->id));

    expect($actualLeaves)->not->toContain($excludedLeave->id);
})->with('monthly daily');

function createLeaveJob(
    array $data,
    Employee $employee = null,
    ReportTask $reportTask = null
): GenerateLeaveExcelJob {
    $data = [
        'start_date' => $data['startDate'],
        'end_date' => $data['endDate'] ?? null,
        'type' => $data['type'],
        'departments_ids' => $data['departments_ids'] ?? null,
        'employees_ids' => $data['employees_ids'] ?? null,
        'tags' => $data['tags'] ?? null,
        'locations' => $data['locations'] ?? null,
        'shifts' => $data['shifts'] ?? null,
        'show_inactive_employees' => $data['show_inactive_employees'] ?? null,
        'sheet_mode' => SheetMode::SingleSheet->value,
    ];

    $reportTask?->update(['data' => $data]);

    return new GenerateLeaveExcelJob(
        employee: $employee ?? test()->user,
        reportTask: $reportTask ?? ReportTask::factory()->mood()->withData($data)->create()
    );
}
