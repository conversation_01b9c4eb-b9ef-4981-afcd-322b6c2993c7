<?php

use App\DTOs\EarlyLateMailData;
use App\Enums\DelegationType;
use App\Enums\EarlyLatePeriodPolicy;
use App\Jobs\SendEarlyLateReportMailJob;
use App\Mail\EarlyLateReportMail;
use App\Models\Delegation;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Mail;

test('sends early late report mail', function () {
    Mail::fake();

    $manager = createDefaultEmployee();

    $period = CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth());

    $mailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: [],
        period: $period,
        periodType: EarlyLatePeriodPolicy::Weekly,
        reportTasksIds: collect([1, 2, 3])
    );

    $job = new SendEarlyLateReportMailJob($mailData);

    // Act
    $job->handle();

    // Assert
    Mail::assertQueued(
        EarlyLateReportMail::class,
        fn(EarlyLateReportMail $mail) => $mail->hasTo($manager->email)
    );
});

test('sends early late report mail - to delegated employee', function () {
    Mail::fake();

    $manager = createDefaultEmployee();

    $delegatedEmployee = createDefaultEmployee();

    Delegation::factory()->create([
        'type' => DelegationType::EarlyLate,
        'delegated_id' => $delegatedEmployee->id,
        'delegatee_id' => $manager->id,
    ]);

    $period = CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth());

    $mailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: [],
        period: $period,
        periodType: EarlyLatePeriodPolicy::Weekly,
        reportTasksIds: collect([1, 2, 3])
    );

    $job = new SendEarlyLateReportMailJob($mailData);

    // Act
    $job->handle();

    // Assert
    Mail::assertQueued(
        EarlyLateReportMail::class,
        fn(EarlyLateReportMail $mail) => $mail->hasTo($delegatedEmployee->email)
    );
});
