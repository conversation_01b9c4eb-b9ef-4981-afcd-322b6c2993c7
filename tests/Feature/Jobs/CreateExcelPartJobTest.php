<?php

use App\Interfaces\ExcelPart;
use App\Interfaces\ExcelPartCreator;
use App\Jobs\CreateExcelPartJob;
use App\Models\ReportTask;
use Illuminate\Support\Facades\Redis;

test('stores excel part data in redis with correct parameters', function () {
    Redis::spy();

    // Arrange
    $index = 1;
    $partData = ['sheet1' => [['col1' => 'value1']]];

    $reportTask = ReportTask::factory()->earlyLate()->create();

    $part = mock(ExcelPart::class);

    $excelPartCreator = mock(ExcelPartCreator::class);
    $excelPartCreator
        ->shouldReceive('createPart')
        ->once()
        ->with($reportTask, $part)
        ->andReturn($partData);

    $job = new CreateExcelPartJob(
        index: 1,
        part: $part,
        excelPartCreator: $excelPartCreator,
        reportTask: $reportTask
    );

    // Act
    $job->handle();

    // Assert
    Redis::shouldHaveReceived('set')
        ->once()
        ->withArgs(function ($key, $value, $expireResolution, $expireTTL) use (
            $reportTask,
            $index,
            $partData
        ) {
            return $key === "{$reportTask->team_id}_{$reportTask->id}_{$index}" &&
                $value === json_encode($partData) &&
                $expireResolution === 'EX' &&
                $expireTTL === 7200;
        });
});
