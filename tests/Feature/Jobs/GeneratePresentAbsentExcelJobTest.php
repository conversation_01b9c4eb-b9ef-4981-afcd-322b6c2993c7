<?php

use App\Enums\ReportTaskStatus;
use App\Enums\SheetMode;
use App\Jobs\GeneratePresentAbsentExcelJob;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use Carbon\CarbonPeriod;
use function Pest\Laravel\assertDatabaseHas;

test('run present absent report job correctly', function ($payload) {
    Excel::fake();

    $inRangeAttendances = Attendance::factory()
        ->for($this->tenant)
        ->createFromPeriod(CarbonPeriod::create($payload['startDate'], $payload['endDate']));

    $outOfRangeAttendance = Attendance::factory()
        ->for($this->tenant)
        ->date($payload['startDate']->subDay())
        ->count(1)
        ->create();

    $reportTask = ReportTask::factory()->presentAbsent()->create();

    Str::createUuidsUsing(fn() => 'uuid');

    createPresentAbsentJob($payload, $this->user, $reportTask)->handle();

    assertDatabaseHas('report_tasks', [
        'id' => $reportTask->id,
        'status' => ReportTaskStatus::Success,
    ]);

    Excel::assertStored($reportTask->report->name->path('uuid', 'xlsx'), 'minio');

    Str::createUuidsNormally();
})->with('monthly daily');

it('fetch employees correctly - employee ids', function ($payload) {
    $includedEmployee = createDefaultEmployee();

    $excludedEmployee = createDefaultEmployee();

    $payload['employees_ids'] = [$includedEmployee->id];

    $job = createPresentAbsentJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

it('fetch employees correctly - department ids', function ($payload) {
    $includedDepartment = createDefaultDepartment();

    $includedEmployee = createDefaultEmployee([
        'department_id' => $includedDepartment->id,
    ]);

    $excludedDepartment = createDefaultDepartment();

    $excludedEmployee = createDefaultEmployee([
        'department_id' => $excludedDepartment->id,
    ]);

    $payload['departments_ids'] = [$includedDepartment->id];

    $job = createPresentAbsentJob($payload);

    $actualEmployees = $job->getEmployees()->pluck('id');

    expect($actualEmployees)->toEqual(collect($includedEmployee->id));

    expect($actualEmployees)->not->toContain($excludedEmployee->id);
})->with('monthly daily');

function createPresentAbsentJob(
    array $data,
    Employee $employee = null,
    ReportTask $reportTask = null
): GeneratePresentAbsentExcelJob {
    $data = [
        'start_date' => $data['startDate'],
        'end_date' => $data['endDate'] ?? null,
        'type' => $data['type'],
        'departments_ids' => $data['departments_ids'] ?? null,
        'employees_ids' => $data['employees_ids'] ?? null,
        'tags' => $data['tags'] ?? null,
        'locations' => $data['locations'] ?? null,
        'shifts' => $data['shifts'] ?? null,
        'show_inactive_employees' => $data['show_inactive_employees'] ?? null,
        'sheet_mode' => SheetMode::SingleSheet->value,
    ];

    $reportTask?->update(['data' => $data]);

    return new GeneratePresentAbsentExcelJob(
        employee: $employee ?? test()->user,
        reportTask: $reportTask ?? ReportTask::factory()->mood()->withData($data)->create()
    );
}
