<?php

use App\Enums\RequestStatus;
use App\Enums\WorkdayType;
use App\Jobs\GenerateEmployeeWorkScheduleRecordsJob;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\Leave;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleRecord;
use Carbon\Carbon;
use function Pest\Laravel\assertDatabaseCount;
use function Pest\Laravel\assertDatabaseHas;

beforeEach(function () {
    $this->employee = Employee::factory()
        ->for($this->tenant)
        ->create();
    $this->workday = Workday::factory()
        ->for($this->tenant)
        ->create();
});

test('generates records for fixed schedule with specific days distribution', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
            'specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);

    $workSchedule->workdays()->attach($this->workday->id);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should create records for ALL days in the next 6 months (new behavior)
    $expectedRecords =
        Carbon::parse($workSchedule->start_date)->diffInDays(
            Carbon::parse($workSchedule->start_date)->addMonths(6)
        ) + 1;

    assertDatabaseCount('work_schedule_records', $expectedRecords);

    // Check that weekday records are created correctly for selected days
    $mondayDate = Carbon::parse($workSchedule->start_date);
    while ($mondayDate->format('l') !== 'Monday') {
        $mondayDate->addDay();
    }

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $mondayDate->toDateString(),
        'workday_type' => WorkdayType::Weekday->value,
    ]);

    // Check that weekend records are created for unselected days
    $sundayDate = Carbon::parse($workSchedule->start_date);
    while ($sundayDate->format('l') !== 'Sunday') {
        $sundayDate->addDay();
    }

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $sundayDate->toDateString(),
        'workday_type' => WorkdayType::Weekend->value,
    ]);
});

test('generates records for fixed schedule with number of days distribution', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->numberOfDays()
        ->create([
            'start_date' => now()->addDays(1),
            'off_days_after_each_repetition' => 2,
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'work_days_number' => 5,
        'off_days_number' => 2,
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should create records for the next 6 months (every day)
    $expectedRecords =
        Carbon::parse($workSchedule->start_date)->diffInDays(
            Carbon::parse($workSchedule->start_date)->addMonths(6)
        ) + 1;

    assertDatabaseCount('work_schedule_records', $expectedRecords);
});

test('correctly identifies holiday records', function () {
    $holidayDate = now()->addDays(5);

    Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => $holidayDate,
            'end_date' => $holidayDate,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
            'specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);

    $workSchedule->workdays()->attach($this->workday->id);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Check that the holiday is correctly identified
    if (
        in_array(strtolower($holidayDate->format('l')), [
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
        ])
    ) {
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'date' => $holidayDate->toDateString(),
            'workday_type' => WorkdayType::Holiday->value,
        ]);
    }
});

test('correctly identifies leave records', function () {
    $leaveDate = now()->addDays(7);

    Leave::factory()
        ->for($this->tenant)
        ->create([
            'employee_id' => $this->employee->id,
            'from_date' => $leaveDate,
            'to_date' => $leaveDate,
            'status' => RequestStatus::Approved,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
            'specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);

    $workSchedule->workdays()->attach($this->workday->id);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Check that the leave is correctly identified
    if (
        in_array(strtolower($leaveDate->format('l')), [
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
        ])
    ) {
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'date' => $leaveDate->toDateString(),
            'workday_type' => WorkdayType::Leave->value,
        ]);
    }
});

test(
    'creates records for all days including unselected days in specific days distribution',
    function () {
        $workSchedule = WorkSchedule::factory()
            ->for($this->tenant)
            ->fixed()
            ->specificDays()
            ->create([
                'start_date' => now()->addDays(1),
                'specific_days' => ['monday', 'tuesday', 'wednesday'], // Thursday-Sunday are not selected
            ]);

        $workSchedule->workdays()->attach($this->workday->id);

        GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

        // Should create records for ALL days in the 6-month period
        $expectedRecords =
            Carbon::parse($workSchedule->start_date)->diffInDays(
                Carbon::parse($workSchedule->start_date)->addMonths(6)
            ) + 1;

        assertDatabaseCount('work_schedule_records', $expectedRecords);

        // Find an unselected day (Thursday, Friday, Saturday, or Sunday) and verify it has a record
        $currentDate = Carbon::parse($workSchedule->start_date);
        $endDate = $currentDate->copy()->addMonths(6);

        $foundUnselectedDay = false;
        while ($currentDate->lte($endDate)) {
            $dayName = strtolower($currentDate->format('l'));
            if (!in_array($dayName, ['monday', 'tuesday', 'wednesday'])) {
                // This day SHOULD have a record (new behavior - records for all days)
                assertDatabaseHas('work_schedule_records', [
                    'work_schedule_id' => $workSchedule->id,
                    'employee_id' => $this->employee->id,
                    'date' => $currentDate->toDateString(),
                    'workday_type' => WorkdayType::Weekend->value, // Unselected days are weekends
                ]);
                $foundUnselectedDay = true;
                break; // Just check one unselected day
            }
            $currentDate->addDay();
        }

        expect($foundUnselectedDay)->toBeTrue();
    }
);

test('correctly identifies weekend records for number of days distribution', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->numberOfDays()
        ->create([
            'start_date' => now()->addDays(1),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'work_days_number' => 5,
        'off_days_number' => 2,
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // For number of days distribution, all days get records
    // Weekend determination is based on the isWeekendDay logic
    $recordCount = WorkScheduleRecord::where('work_schedule_id', $workSchedule->id)
        ->where('employee_id', $this->employee->id)
        ->count();

    expect($recordCount)->toBeGreaterThan(0);
});

test('generates records for rotational schedule with multiple workdays', function () {
    $workday1 = Workday::factory()
        ->for($this->tenant)
        ->create();
    $workday2 = Workday::factory()
        ->for($this->tenant)
        ->create();

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->numberOfDays()
        ->create([
            'start_date' => now()->addDays(1),
            'off_days_after_each_repetition' => 1,
        ]);

    $workSchedule->workdays()->attach($workday1->id, [
        'work_days_number' => 3,
        'off_days_number' => 1,
        'repetitions_number' => 2,
    ]);

    $workSchedule->workdays()->attach($workday2->id, [
        'work_days_number' => 2,
        'off_days_number' => 2,
        'repetitions_number' => 1,
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should create records for the next 6 months
    $expectedRecords =
        Carbon::parse($workSchedule->start_date)->diffInDays(
            Carbon::parse($workSchedule->start_date)->addMonths(6)
        ) + 1;

    assertDatabaseCount('work_schedule_records', $expectedRecords);

    // Check that both workdays are used
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $workday1->id,
    ]);

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $workday2->id,
    ]);
});

test('deletes existing future records before generating new ones', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
            'specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);

    $workSchedule->workdays()->attach($this->workday->id);

    // Create some existing records with different dates
    for ($i = 10; $i < 15; $i++) {
        WorkScheduleRecord::factory()->create([
            'team_id' => $this->tenant->id,
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $this->workday->id,
            'date' => now()->addDays($i),
        ]);
    }

    $initialCount = WorkScheduleRecord::count();
    expect($initialCount)->toBe(5);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should have deleted the old records and created new ones
    $finalCount = WorkScheduleRecord::count();
    expect($finalCount)->toBeGreaterThan(5); // Should have more records for 6 months
});

test('does not generate records for past dates', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->subDays(10), // Past date
            'specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);

    $workSchedule->workdays()->attach($this->workday->id);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should start from today, not from the past start_date
    $earliestRecord = WorkScheduleRecord::where('work_schedule_id', $workSchedule->id)
        ->where('employee_id', $this->employee->id)
        ->orderBy('date')
        ->first();

    expect($earliestRecord->date->toDateString())->toBeGreaterThanOrEqual(now()->toDateString());
});

test('handles empty workdays gracefully', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
            'specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        ]);

    // Don't attach any workdays

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should not create any records
    assertDatabaseCount('work_schedule_records', 0);
});
