<?php

use App\Calculations\EarlyLatePeriodicalMailCalculations;
use App\DTOs\EarlyLateMailData;
use App\Enums\AttendanceStatus;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use Carbon\CarbonPeriod;

test('attendance, early/late, employees stats is working', function (array $period) {
    $manager = createDefaultEmployee();

    $departments = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->count(2)
        ->create();

    $firstEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($manager, 'directManager')
        ->create();

    $secondEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($departments->first())
        ->create();

    $attendancePeriod = CarbonPeriod::create('2021-08-01', '2021-08-07');

    // Case 1: an employee that attendances (good employee):
    Attendance::factory()
        ->for($firstEmployee)
        ->for($this->tenant)
        ->createFromPeriod(
            period: $attendancePeriod,
            statuses: [
                '2021-08-01' => '08:00-16:00', // Sunday - on time
                '2021-08-02' => '06:00-16:00', // Monday - 1 hours early in
                '2021-08-03' => '08:00-18:30', // Tuesday - 1.5 hours late out
                '2021-08-04' => '08:00-17:00', // Wednesday - 1 hour early out
                '2021-08-05' => AttendanceStatus::LEAVE, // Thursday
                '2021-08-06' => AttendanceStatus::WEEKEND, // Friday
                '2021-08-07' => AttendanceStatus::WEEKEND, // Saturday
            ]
        );

    // Case 2: an employee that attendances (bad employee):
    Attendance::factory()
        ->for($secondEmployee)
        ->for($this->tenant)
        ->createFromPeriod(
            period: $attendancePeriod,
            statuses: [
                '2021-08-01' => '08:00-16:00', // Sunday - on time
                '2021-08-02' => '09:30-16:00', // Monday - 1.5 hours late in
                '2021-08-03' => '08:00-14:00', // Tuesday - 2 hours early out
                '2021-08-04' => AttendanceStatus::ABSENT, // Wednesday
                '2021-08-05' => AttendanceStatus::ABSENT, // Thursday
                '2021-08-06' => AttendanceStatus::WEEKEND, // Friday
                '2021-08-07' => AttendanceStatus::WEEKEND, // Saturday
            ]
        );

    ApprovalRequest::factory()
        ->for($firstEmployee)
        ->for($this->tenant)
        ->approved()
        ->permission()
        ->date($attendancePeriod)
        ->create();

    $earlyLateMailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: $departments->pluck('id')->toArray(),
        period: $period['period'],
        periodType: $period['periodType']
    );

    $data = (new EarlyLatePeriodicalMailCalculations($earlyLateMailData))->calculate();

    // Assert the stats for each day of the week
    expect($data['attendancesStats'])
        ->sunday->toMatchObject(['present' => 100])
        ->monday->toMatchObject(['present' => 100])
        ->tuesday->toMatchObject(['present' => 100])
        ->wednesday->toMatchObject(['present' => 50, 'absent' => 50])
        ->thursday->toMatchObject(['leave' => 50, 'absent' => 50])
        ->friday->toMatchObject(['weekend' => 100])
        ->saturday->toMatchObject(['weekend' => 100])
        ->sum->toMatchObject([
            'present' => 50,
            'absent' => 14, // 2 days of absence from 14 days
            'leave' => 7, // 1 day of leave from 14 days
            'weekend' => 29, // 4 days of weekend from 14 days
        ]);

    expect($data['earlyLateStats'])
        ->sunday->toMatchObject(['onTime' => 100])
        ->monday->toMatchObject(['onTime' => 0, 'earlyIn' => 50, 'lateIn' => 50])
        ->tuesday->toMatchObject(['onTime' => 0, 'earlyOut' => 50, 'lateOut' => 50])
        ->wednesday->toMatchObject(['onTime' => 100])
        ->thursday->toMatchObject(['onTime' => 100])
        ->friday->toMatchObject(['onTime' => 100])
        ->saturday->toMatchObject(['onTime' => 100])
        ->sum->toMatchObject([
            'onTime' => 71, // 10 days of on time from 14 days
            'earlyIn' => 7, // 1 day of early in from 14 days
            'lateIn' => 7, // 1 day of late in from 14 days
            'earlyOut' => 7, // 1 day of early out from 14 days
            'lateOut' => 7, // 1 day of late out from 14 days
        ]);

    expect($data['employeesStats'])->toMatchObject([
        'total' => 2,
        'employeesHasAdditionalHoursCount' => 1,
        'employeesHasPermissionRequestsCount' => 1,
    ]);
})->with('weekly monthly period');

test('employees count is working', function (array $period) {
    $manager = createDefaultEmployee();

    $departments = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->count(2)
        ->create();

    $firstEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($manager, 'directManager')
        ->create();

    $secondEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($departments->first())
        ->create();

    $anotherManager = createDefaultEmployee();

    $anotherManagerDepartments = Department::factory()
        ->for($this->tenant)
        ->for($anotherManager, 'manager')
        ->count(2)
        ->create();

    $anotherManagerFirstEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($anotherManager, 'directManager')
        ->create();

    $anotherManagerSecondEmployee = Employee::factory()
        ->for($this->tenant)
        ->for($anotherManagerDepartments->first())
        ->create();

    $attendancePeriod = CarbonPeriod::create('2021-08-01', '2021-08-07');

    // Case 1: an employee that attendances (good employee):
    Attendance::factory()
        ->for($firstEmployee)
        ->for($this->tenant)
        ->createFromPeriod(period: $attendancePeriod);

    // Case 2: an employee that attendances (bad employee):
    Attendance::factory()
        ->for($secondEmployee)
        ->for($this->tenant)
        ->createFromPeriod(period: $attendancePeriod);

    // Case 1: an employee that attendances (good employee):
    Attendance::factory()
        ->for($anotherManagerFirstEmployee)
        ->for($this->tenant)
        ->createFromPeriod(period: $attendancePeriod);

    // Case 2: an employee that attendances (bad employee):
    Attendance::factory()
        ->for($anotherManagerSecondEmployee)
        ->for($this->tenant)
        ->createFromPeriod(period: $attendancePeriod);

    $earlyLateMailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: $departments->pluck('id')->toArray(),
        period: $period['period'],
        periodType: $period['periodType']
    );

    $data = (new EarlyLatePeriodicalMailCalculations($earlyLateMailData))->calculate();

    expect($data['employeesCount'])->toBe(2);
})->with('weekly monthly period');
