<?php

use App\Calculations\EarlyLatePeriodicalMailCalculations;
use App\DTOs\EarlyLateMailData;
use App\DTOs\Stats\MostLeastCommittedStats;
use App\Enums\AttendanceStatus;
use App\Enums\EarlyLatePeriodPolicy;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use Carbon\CarbonPeriod;

test('most/least committed attendance is working', function () {
    // Set up manager and departments
    $manager = createDefaultEmployee();
    $departments = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->count(2)
        ->create();

    // Create 10 employees (5 most committed, 5 least committed)
    $employees = Employee::factory()
        ->count(10)
        ->sequence(
            fn($sequence) => [
                'first_name' =>
                    $sequence->index < 5
                        ? 'Most Committed Employee #' . ($sequence->index + 1)
                        : 'Least Committed Employee #' . ($sequence->index - 4),
                'manager_id' => $sequence->index < 5 ? $manager->id : null,
                'department_id' => $sequence->index >= 5 ? $departments->random()->id : null,
            ]
        )
        ->for($this->tenant)
        ->create();

    // Create attendance records for most committed employees
    $mostCommittedPeriods = mostCommittedPeriods();

    $period = CarbonPeriod::create('2021-08-01', '2021-08-13');

    foreach ($employees->take(5) as $i => $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod(period: $period, statuses: $mostCommittedPeriods[$i]);
    }

    // Create attendance records for least committed employees
    $leastCommittedPeriods = leastCommittedPeriods();

    foreach ($employees->slice(5) as $i => $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod(period: $period, statuses: $leastCommittedPeriods[$i - 5]);
    }

    $earlyLateMailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: $departments->pluck('id')->toArray(),
        period: CarbonPeriod::create('2021-08-01', '2021-08-30'),
        periodType: EarlyLatePeriodPolicy::Monthly
    );

    $data = (new EarlyLatePeriodicalMailCalculations($earlyLateMailData))->calculate();

    $mostCommitted = $data['mostLeastCommitted']->mostCommittedList()->map(
        fn(MostLeastCommittedStats $item) => [
            'name' => $item->employee->first_name,
            'missingHours' => $item->missingHours->totalHours,
            'attendanceRatePercentage' => $item->attendanceRatePercentage,
        ]
    );

    $leastCommitted = $data['mostLeastCommitted']->leastCommittedList()->map(
        fn(MostLeastCommittedStats $item) => [
            'name' => $item->employee->first_name,
            'missingHours' => $item->missingHours->totalHours,
            'attendanceRatePercentage' => $item->attendanceRatePercentage,
        ]
    );

    expect($mostCommitted)->toEqual(
        collect([
            [
                'name' => 'Most Committed Employee #1',
                'missingHours' => 0.5,
                'attendanceRatePercentage' => 99.0,
            ],
            [
                'name' => 'Most Committed Employee #2',
                'missingHours' => 1.0,
                'attendanceRatePercentage' => 99.0,
            ],
            [
                'name' => 'Most Committed Employee #3',
                'missingHours' => 1.5,
                'attendanceRatePercentage' => 98.0,
            ],
            [
                'name' => 'Most Committed Employee #4',
                'missingHours' => 2.0,
                'attendanceRatePercentage' => 98.0,
            ],
            [
                'name' => 'Most Committed Employee #5',
                'missingHours' => 2.5,
                'attendanceRatePercentage' => 97.0,
            ],
        ])
    );

    expect($leastCommitted)->toEqual(
        collect([
            [
                'name' => 'Least Committed Employee #1',
                'missingHours' => 80.0,
                'attendanceRatePercentage' => 0.0,
            ],
            [
                'name' => 'Least Committed Employee #2',
                'missingHours' => 15.0,
                'attendanceRatePercentage' => 81.0,
            ],
            [
                'name' => 'Least Committed Employee #3',
                'missingHours' => 12.0,
                'attendanceRatePercentage' => 85.0,
            ],
            [
                'name' => 'Least Committed Employee #4',
                'missingHours' => 9.0,
                'attendanceRatePercentage' => 89.0,
            ],
            [
                'name' => 'Least Committed Employee #5',
                'missingHours' => 3.0,
                'attendanceRatePercentage' => 96.0,
            ],
        ])
    );
});

test('most least committed - employee count is working', function () {
    // Set up manager and departments
    $manager = createDefaultEmployee();

    $departments = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->count(2)
        ->create();

    $anotherManager = createDefaultEmployee();

    $anotherManagerDepartments = Department::factory()
        ->for($this->tenant)
        ->for($anotherManager, 'manager')
        ->count(2)
        ->create();

    // Create 10 employees (5 most committed, 5 least committed)
    $employees = Employee::factory()
        ->count(10)
        ->sequence(
            fn($sequence) => [
                'first_name' =>
                    $sequence->index < 5
                        ? 'Most Committed Employee #' . ($sequence->index + 1)
                        : 'Least Committed Employee #' . ($sequence->index - 4),
                'manager_id' => $sequence->index < 5 ? $manager->id : null,
                'department_id' => $sequence->index >= 5 ? $departments->random()->id : null,
            ]
        )
        ->for($this->tenant)
        ->create();

    // Create 10 employees (5 most committed, 5 least committed)
    $anotherManagerEmployees = Employee::factory()
        ->count(10)
        ->sequence(
            fn($sequence) => [
                'first_name' =>
                    $sequence->index < 5
                        ? 'Most Committed Employee #' . ($sequence->index + 1)
                        : 'Least Committed Employee #' . ($sequence->index - 4),
                'manager_id' => $sequence->index < 5 ? $anotherManager->id : null,
                'department_id' =>
                    $sequence->index >= 5 ? $anotherManagerDepartments->random()->id : null,
            ]
        )
        ->for($this->tenant)
        ->create();

    // Create attendance records for most committed employees
    $committedAttendancePeriods = mostCommittedPeriods();

    $period = CarbonPeriod::create('2021-08-01', '2021-08-13');

    foreach ($employees->take(5) as $i => $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod(period: $period, statuses: $committedAttendancePeriods[$i]);
    }

    // Create attendance records for least committed employees
    $leastCommittedAttendancePeriods = leastCommittedPeriods();

    foreach ($employees->slice(5) as $i => $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod(period: $period, statuses: $leastCommittedAttendancePeriods[$i - 5]);
    }

    // Create attendance records for most committed employees
    $committedAttendancePeriods = mostCommittedPeriods();

    $period = CarbonPeriod::create('2021-08-01', '2021-08-13');

    foreach ($anotherManagerEmployees->take(5) as $i => $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod(period: $period, statuses: $committedAttendancePeriods[$i]);
    }

    // Create attendance records for least committed employees
    $leastCommittedAttendancePeriods = leastCommittedPeriods();

    foreach ($anotherManagerEmployees->slice(5) as $i => $employee) {
        Attendance::factory()
            ->for($employee)
            ->for($this->tenant)
            ->createFromPeriod(period: $period, statuses: $leastCommittedAttendancePeriods[$i - 5]);
    }

    $earlyLateMailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: $departments->pluck('id')->toArray(),
        period: CarbonPeriod::create('2021-08-01', '2021-08-30'),
        periodType: EarlyLatePeriodPolicy::Monthly
    );

    $data = (new EarlyLatePeriodicalMailCalculations($earlyLateMailData))->calculate();

    expect($data['employeesStats']->total)->toBe(10);
});

function mostCommittedPeriods(): array
{
    return [
        [
            // missing 0:30 hours
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '08:00-16:00',
            '2021-08-03' => '08:00-16:00',
            '2021-08-04' => '08:00-16:00',
            '2021-08-05' => '08:00-16:00',
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:30-16:00', // 7:30 hours
            '2021-08-09' => '08:00-16:00',
            '2021-08-10' => '08:00-16:00',
            '2021-08-11' => '08:00-16:00',
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // missing 1:00 hours
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '08:00-16:00',
            '2021-08-03' => '08:00-16:00',
            '2021-08-04' => '08:00-16:00',
            '2021-08-05' => '08:00-16:00',
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:30-16:00', // 7:30 hours
            '2021-08-09' => '08:30-16:00', // 7:30 hours
            '2021-08-10' => '08:00-16:00',
            '2021-08-11' => '08:00-16:00',
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // missing 1:30 hours
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '08:00-16:00',
            '2021-08-03' => '08:00-16:00',
            '2021-08-04' => '08:00-16:00',
            '2021-08-05' => '08:00-16:00',
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:30-16:00', // 7:30 hours
            '2021-08-09' => '08:30-16:00', // 7:30 hours
            '2021-08-10' => '08:30-16:00', // 7:30 hours
            '2021-08-11' => '08:00-16:00',
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // missing 2:00 hours
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '08:00-16:00',
            '2021-08-03' => '08:00-16:00',
            '2021-08-04' => '08:00-16:00',
            '2021-08-05' => '08:00-16:00',
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:30-16:00', // 7:30 hours
            '2021-08-09' => '08:30-16:00', // 7:30 hours
            '2021-08-10' => '08:30-16:00', // 7:30 hours
            '2021-08-11' => '08:30-16:00', // 7:30 hours
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // missing 2:30 hours
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '08:00-16:00',
            '2021-08-03' => '08:00-16:00',
            '2021-08-04' => '08:00-16:00',
            '2021-08-05' => '08:00-16:00',
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:30-16:00', // 7:30 hours
            '2021-08-09' => '08:30-16:00', // 7:30 hours
            '2021-08-10' => '08:30-16:00', // 7:30 hours
            '2021-08-11' => '08:30-16:00', // 7:30 hours
            '2021-08-12' => '08:30-16:00', // 7:30 hours
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
    ];
}

function leastCommittedPeriods(): array
{
    return [
        [
            // Least Committed Employee #1
            // missing all hours
            '2021-08-01' => AttendanceStatus::ABSENT,
            '2021-08-02' => AttendanceStatus::ABSENT,
            '2021-08-03' => AttendanceStatus::ABSENT,
            '2021-08-04' => AttendanceStatus::ABSENT,
            '2021-08-05' => AttendanceStatus::ABSENT,
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => AttendanceStatus::ABSENT,
            '2021-08-09' => AttendanceStatus::ABSENT,
            '2021-08-10' => AttendanceStatus::ABSENT,
            '2021-08-11' => AttendanceStatus::ABSENT,
            '2021-08-12' => AttendanceStatus::ABSENT,
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // Least Committed Employee #2
            // missing 15 hours
            '2021-08-01' => '11:00-16:00', // worked 5:00 hours
            '2021-08-02' => '11:00-16:00', // worked 5:00 hours
            '2021-08-03' => '11:00-16:00', // worked 5:00 hours
            '2021-08-04' => '11:00-16:00', // worked 5:00 hours
            '2021-08-05' => '11:00-16:00', // worked 5:00 hours
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:00-16:00',
            '2021-08-09' => '08:00-16:00',
            '2021-08-10' => '08:00-16:00',
            '2021-08-11' => '08:00-16:00',
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // Least Committed Employee #3
            // missing 12 hours
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '11:00-16:00', // worked 5:00 hours
            '2021-08-03' => '11:00-16:00', // worked 5:00 hours
            '2021-08-04' => '11:00-16:00', // worked 5:00 hours
            '2021-08-05' => '11:00-16:00', // worked 5:00 hours
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:00-16:00',
            '2021-08-09' => '08:00-16:00',
            '2021-08-10' => '08:00-16:00',
            '2021-08-11' => '08:00-16:00',
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // Least Committed Employee #4
            // missing 9
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '08:00-16:00',
            '2021-08-03' => '08:00-16:00',
            '2021-08-04' => '11:00-16:00', // worked 5:00 hours
            '2021-08-05' => '11:00-16:00', // worked 5:00 hours
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:00-16:00',
            '2021-08-09' => '08:00-16:00',
            '2021-08-10' => '11:00-16:00', // worked 5:00 hours
            '2021-08-11' => '08:00-16:00',
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
        [
            // Least Committed Employee #5
            // missing 3 hour
            '2021-08-01' => '08:00-16:00',
            '2021-08-02' => '08:00-16:00',
            '2021-08-03' => '08:00-16:00',
            '2021-08-04' => '08:00-16:00',
            '2021-08-05' => '11:00-16:00', // worked 5:00 hours
            '2021-08-06' => AttendanceStatus::WEEKEND,
            '2021-08-07' => AttendanceStatus::WEEKEND,
            '2021-08-08' => '08:00-16:00',
            '2021-08-09' => '08:00-16:00',
            '2021-08-10' => '08:00-16:00',
            '2021-08-11' => '08:00-16:00',
            '2021-08-12' => '08:00-16:00',
            '2021-08-13' => AttendanceStatus::WEEKEND,
        ],
    ];
}
