<?php

use App\Calculations\EarlyLatePeriodicalMailCalculations;
use App\DTOs\EarlyLateMailData;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;

test('average mood is working', function (array $case, array $period) {
    $manager = createDefaultEmployee();

    $departments = Department::factory()
        ->for($this->tenant)
        ->for($manager, 'manager')
        ->count(2)
        ->create();

    $employees = Employee::factory()
        ->for($this->tenant)
        ->for($manager, 'directManager')
        ->count(5)
        ->create();

    $anotherManager = createDefaultEmployee();

    $anotherManagerEmployees = Employee::factory()
        ->for($this->tenant)
        ->for($anotherManager, 'directManager')
        ->count(5)
        ->create();

    foreach ($case['moods']['in_mood'] as $key => $mood) {
        Attendance::factory()
            ->for($employees[$key])
            ->for($this->tenant)
            ->date('2021-08-01')
            ->create([
                'in_mood' => $mood,
                'out_mood' => $case['moods']['out_mood'][$key],
            ]);
    }

    foreach ($case['anotherManagerMoods']['in_mood'] as $key => $mood) {
        Attendance::factory()
            ->for($anotherManagerEmployees[$key])
            ->for($this->tenant)
            ->date('2021-08-01')
            ->create([
                'in_mood' => $mood,
                'out_mood' => $case['anotherManagerMoods']['out_mood'][$key],
            ]);
    }
    $earlyLateMailData = new EarlyLateMailData(
        manager: $manager,
        departmentsIDs: $departments->pluck('id')->toArray(),
        period: $period['period'],
        periodType: $period['periodType']
    );

    $data = (new EarlyLatePeriodicalMailCalculations($earlyLateMailData))->calculate();

    /** @var array{key: string, value: float, emoji: string, title: string, description: string} $inMood */
    $inMood = $data['inMood'];

    expect($inMood['value'])->toBe($case['result']['in']['value']);
    expect($inMood['title'])->toBe($case['result']['in']['title']);

    /** @var array{key: string, value: float, emoji: string, title: string, description: string} $inMood */
    $outMood = $data['outMood'];

    expect($outMood['value'])->toBe($case['result']['out']['value']);
    expect($outMood['title'])->toBe($case['result']['out']['title']);
})
    ->with([
        'very-happy' => fn() => [
            'moods' => [
                'in_mood' => [1, 1, 3, 4, 5],
                'out_mood' => [1, 1, 3, 4, 5],
            ],
            'anotherManagerMoods' => [
                'in_mood' => [5, 5, 5, 5, 5],
                'out_mood' => [5, 5, 5, 5, 5],
            ],
            'result' => [
                'in' => ['value' => 40.0, 'title' => __('very-happy')],
                'out' => ['value' => 40.0, 'title' => __('very-happy')],
            ],
        ],
        'happy' => fn() => [
            'moods' => [
                'in_mood' => [1, 2, 2, 4, 5],
                'out_mood' => [1, 2, 2, 4, 5],
            ],
            'anotherManagerMoods' => [
                'in_mood' => [5, 5, 5, 5, 5],
                'out_mood' => [5, 5, 5, 5, 5],
            ],
            'result' => [
                'in' => ['value' => 40.0, 'title' => __('happy')],
                'out' => ['value' => 40.0, 'title' => __('happy')],
            ],
        ],
        'neutral' => fn() => [
            'moods' => [
                'in_mood' => [1, 3, 3, 4, 5],
                'out_mood' => [1, 3, 3, 4, 5],
            ],
            'anotherManagerMoods' => [
                'in_mood' => [5, 5, 5, 5, 5],
                'out_mood' => [5, 5, 5, 5, 5],
            ],
            'result' => [
                'in' => ['value' => 40.0, 'title' => __('neutral')],
                'out' => ['value' => 40.0, 'title' => __('neutral')],
            ],
        ],
        'sad' => fn() => [
            'moods' => [
                'in_mood' => [1, 3, 4, 4, 5],
                'out_mood' => [1, 3, 4, 4, 5],
            ],
            'anotherManagerMoods' => [
                'in_mood' => [5, 5, 5, 5, 5],
                'out_mood' => [5, 5, 5, 5, 5],
            ],
            'result' => [
                'in' => ['value' => 40.0, 'title' => __('sad')],
                'out' => ['value' => 40.0, 'title' => __('sad')],
            ],
        ],
        'very-sad' => fn() => [
            'moods' => [
                'in_mood' => [1, 3, 4, 5, 5],
                'out_mood' => [1, 3, 4, 5, 5],
            ],
            'anotherManagerMoods' => [
                'in_mood' => [5, 5, 5, 5, 5],
                'out_mood' => [5, 5, 5, 5, 5],
            ],
            'result' => [
                'in' => ['value' => 40.0, 'title' => __('very-sad')],
                'out' => ['value' => 40.0, 'title' => __('very-sad')],
            ],
        ],
    ])
    ->with('weekly monthly period');
