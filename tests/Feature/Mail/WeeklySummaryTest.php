<?php

use App\Mail\WeeklySummary;
use App\Models\Attendance;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Mail\Mailable;
use function Pest\Laravel\travelTo;

test('it works', function () {
    $notifiable = createDefaultEmployee();

    travelTo(Carbon::parse('2021-05-05')); // random date in the middle of the period

    $includePeriod = CarbonPeriod::create('2021-04-01', '2021-06-30');
    $excludePeriod = CarbonPeriod::create('2021-07-01', '2021-07-01');

    $includedAttendances = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->createFromPeriod($includePeriod);

    $excludeAttendances = Attendance::factory()
        ->for($notifiable)
        ->for($this->tenant)
        ->createFromPeriod($excludePeriod);

    $mail = new WeeklySummary($notifiable);

    // run the build method to make sure it doesn't throw any exceptions
    expect($mail->build())->toBeInstanceOf(Mailable::class);

    // test fetch the attendances since all data depend on it
    $attendances = $mail->fetchAttendancesOfThreeMonths();

    expect($attendances->count())->toBe($includedAttendances->count());

    expect(
        $includedAttendances
            ->pluck('id')
            ->every(fn($id) => $attendances->pluck('id')->contains($id))
    )->toBeTrue();

    expect(
        $excludeAttendances
            ->pluck('id')
            ->every(fn($id) => $attendances->pluck('id')->doesntContain($id))
    )->toBeTrue();
});
