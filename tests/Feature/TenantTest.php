<?php

use App\Models\Employee;
use App\Models\Role;
use App\Models\Subscription;
use Database\Seeders\RolesAndPermissions;
use Laravel\Passport\Passport;
use Spatie\Permission\PermissionRegistrar;

beforeEach(function () {
    $this->employee = Employee::factory()->create();

    Passport::actingAs($this->employee);

    $this->app->make(PermissionRegistrar::class)->forgetCachedPermissions();

    $this->seed(RolesAndPermissions::class);

    $this->startRole = Role::first();

    $this->employee->assignRole($this->startRole);
});

it('can update tenant settings when they has start admin role', function () {
    $payload = ['name' => 'new tenant', 'color' => '#ffff66'];

    $response = $this->putJson('api/v1/tenant/settings/update', $payload)
        ->assertOk()
        ->assertJson(['message' => __('settings updated successfully')]);

    expect($response->json('data'))
        ->name->toBe($payload['name'])
        ->color->toBe($payload['color']);
});

it('can get current tenant team subscriptions', function () {
    Subscription::factory()->create(['tenant_id' => $this->employee->tenant->id]);

    $this->getJson('api/v1/tenant/subscription')->assertOk();
});
