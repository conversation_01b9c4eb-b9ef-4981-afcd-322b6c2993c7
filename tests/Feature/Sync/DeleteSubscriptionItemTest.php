<?php

use App\Models\Team;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseMissing;

test('delete a subscription item', function () {
    $team = Team::factory()->enterprise()->create();

    $item = $team->subscriptionItems()->first();

    SyncHandler::triggerMessage('tenant.subscription-item.delete', [
        'id' => $item->id,
        'tenant_id' => $item->team->nawart_uuid,
    ]);

    assertDatabaseMissing('subscription_items', [
        'id' => $item->id,
    ]);
});

test('delete a subscription item that is not synced', function () {
    $team = Team::factory()->create();

    SyncHandler::triggerMessage('tenant.subscription-item.delete', [
        'id' => 'non-existing-subscription-item-id',
        'tenant_id' => $team->nawart_uuid,
    ]);

    assertDatabaseMissing('subscription_items', [
        'team_id' => $team->id,
    ]);
});
