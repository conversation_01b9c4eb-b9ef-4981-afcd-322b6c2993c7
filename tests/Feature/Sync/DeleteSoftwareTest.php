<?php

use App\Models\SoftwarePackage;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseMissing;

test('delete software', function () {
    // software stuff is already seeded

    $softwarePackage = SoftwarePackage::first();

    SyncHandler::triggerMessage('tenant.software.delete', [
        'id' => 'software-id',
        'software_packages' => [['id' => $softwarePackage->id]],
    ]);

    assertDatabaseMissing('software_packages', [
        'id' => $softwarePackage->id,
    ]);

    assertDatabaseMissing('software_features', [
        'software_package_id' => $softwarePackage->id,
    ]);
});
