<?php

use App\DTOs\RandomProofNotificationConfig;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use App\Support\SyncHandler;

test('create a department', function () {
    $team = Team::factory()->enterprise()->create();

    $parentDepartment = Department::factory()->for($team)->create();

    $manager = Employee::factory()->for($team)->create();

    $payload = [
        'id' => Str::uuid(),
        'tenant_id' => $team->nawart_uuid,
        'name' => fake()->word(),
        'parent_id' => $parentDepartment->nawart_uuid,
        'manager_id' => $manager->nawart_uuid,
    ];

    SyncHandler::triggerMessage('tenant.department.create', $payload);

    $newDepartment = Department::findByNawartUuid($payload['id']);

    expect($newDepartment)->not->toBeNull();

    expect($newDepartment->name)->toBe($payload['name']);
    expect($newDepartment->team_id)->toBe($team->id);
    expect($newDepartment->parent_id)->toBe($parentDepartment->id);
    expect($newDepartment->manager_id)->toBe($manager->id);

    $defaultRandomProofNotificationConfig = new RandomProofNotificationConfig(
        enabled: $team->random_proof_notification_config->enabled,
        inherited: true,
        count: $team->random_proof_notification_config->count
    );

    expect($newDepartment->random_proof_notification_config->enabled)->toBe(
        $defaultRandomProofNotificationConfig->enabled
    );
    expect($newDepartment->random_proof_notification_config->inherited)->toBeTrue();
    expect($newDepartment->random_proof_notification_config->count)->toBe(
        $defaultRandomProofNotificationConfig->count
    );
});

test('update a department', function () {
    $team = Team::factory()->enterprise()->create();

    $parentDepartment = Department::factory()->for($team)->create();

    $manager = Employee::factory()->for($team)->create();

    $department = Department::factory()
        ->for($team)
        ->randomProofNotificationConfig(enabled: true, inherited: false, count: 2)
        ->create([
            'parent_id' => $parentDepartment->id,
            'manager_id' => $manager->id,
        ]);

    $newParentDepartment = Department::factory()->for($team)->create();

    $newManager = Employee::factory()->for($team)->create();

    $payload = [
        'id' => $department->nawart_uuid,
        'tenant_id' => $team->nawart_uuid,
        'name' => fake()->word(),
        'parent_id' => $newParentDepartment->nawart_uuid,
        'manager_id' => $newManager->nawart_uuid,
    ];

    SyncHandler::triggerMessage('tenant.department.update', $payload);

    $department->refresh();

    expect($department->name)->toBe($payload['name']);
    expect($department->team_id)->toBe($team->id);
    expect($department->parent_id)->toBe($newParentDepartment->id);
    expect($department->manager_id)->toBe($newManager->id);

    // The random_proof_notification_config should not be updated
    expect($department->random_proof_notification_config->enabled)->toBeTrue();
    expect($department->random_proof_notification_config->inherited)->toBeFalse();
    expect($department->random_proof_notification_config->count)->toBe(2);
});
