<?php

use App\Models\Subscription;
use App\Models\SubscriptionItem;
use App\Models\Team;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseHas;
use function Pest\Laravel\assertDatabaseMissing;

test('create a subscription', function () {
    $team = Team::factory()->create();

    $payload = [
        'id' => Str::uuid(),
        'tenant_id' => $team->nawart_uuid,
    ];

    SyncHandler::triggerMessage('tenant.subscription.create', $payload);

    $team->refresh();

    assertDatabaseHas('subscriptions', [
        'id' => $payload['id'],
        'team_id' => $team->id,
    ]);
});

test('replace a subscription', function () {
    $team = Team::factory()->create();

    $oldSubscription = Subscription::factory()->for($team)->create();

    $oldSubscriptionItem = SubscriptionItem::factory()->for($oldSubscription)->for($team)->create();

    $payload = [
        'id' => Str::uuid(),
        'tenant_id' => $team->nawart_uuid,
    ];

    SyncHandler::triggerMessage('tenant.subscription.create', $payload);

    $team->refresh();

    assertDatabaseMissing('subscriptions', [
        'id' => $oldSubscription->id,
    ]);

    assertDatabaseMissing('subscription_items', [
        'subscription_id' => $oldSubscription->id,
    ]);

    assertDatabaseHas('subscriptions', [
        'id' => $payload['id'],
        'team_id' => $team->id,
    ]);
});
