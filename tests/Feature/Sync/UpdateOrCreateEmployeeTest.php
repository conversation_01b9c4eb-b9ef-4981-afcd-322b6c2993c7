<?php

use App\DTOs\RandomProofNotificationConfig;
use App\Enums\PreferredLanguage;
use App\Jobs\PrepareEmployeeAttendanceRecord;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Team;
use App\Notifications\WelcomeNotification;
use App\Support\SyncHandler;

function payload(Team $team): array
{
    $department = Department::factory()->for($team)->create();

    return [
        'id' => Str::uuid(),
        'tenant_id' => $team->nawart_uuid,
        'department_id' => $department->nawart_uuid,
        'first_name' => fake()->firstName(),
        'last_name' => fake()->lastName(),
        'email' => fake()->email(),
        'phone' => fake()->numerify('050#######'),
        'roles' => ['attendance-hr'],
        'preferred_language' => fake()->randomElement(PreferredLanguage::class),
        'tags' => [
            ['name' => 'tag1', 'color' => '#000000'],
            ['name' => 'tag2', 'color' => '#000000'],
        ],
        'position' => fake()->word(),
        'number' => fake()->bothify('?????-#####'),
        'random_proof_notification_config' => new RandomProofNotificationConfig(
            enabled: $team->random_proof_notification_config->enabled,
            inherited: true,
            count: $team->random_proof_notification_config->count
        ),
    ];
}

function assertEmployee(Employee $employee, array $payload): void
{
    expect($employee)
        ->first_name->toBe($payload['first_name'])
        ->last_name->toBe($payload['last_name'])
        ->email->toBe($payload['email'])
        ->mobile->toBe($payload['phone'])
        ->position->toBe($payload['position'])
        ->number->toBe($payload['number'])
        ->is_active->toBe($payload['is_active'])
        ->is_ready->toBe($payload['is_ready'])
        ->preferred_language->toBe($payload['preferred_language']->value)
        ->department_id->toBe(Department::findByNawartUuid($payload['department_id'])->id)
        ->random_proof_notification_config->toEqual($payload['random_proof_notification_config']);
}

test('create a employee - employee active', function () {
    Queue::fake([PrepareEmployeeAttendanceRecord::class]);

    Notification::fake();

    $team = Team::factory()->enterprise()->create();

    $payload = [...payload($team), 'is_active' => true, 'is_ready' => true];

    $defaultShift = Shift::factory()->default()->for($team)->create();
    $defaultLocation = Location::factory()->default()->for($team)->create();

    SyncHandler::triggerMessage('tenant.employee.create', $payload);

    $employee = Employee::findByNawartUuid($payload['id']);

    assertEmployee(employee: $employee, payload: $payload);

    expect($employee->tags()->count())->toBe(2);

    expect($employee->shifts()->pluck('shifts.id'))->toEqual(collect([$defaultShift->id]));
    expect($employee->locations()->pluck('locations.id'))->toEqual(collect([$defaultLocation->id]));

    Notification::assertSentTo($employee, WelcomeNotification::class);

    Queue::assertPushed(PrepareEmployeeAttendanceRecord::class);
});

test('create a employee - employee inactive', function () {
    Queue::fake([PrepareEmployeeAttendanceRecord::class]);

    Notification::fake();

    $team = Team::factory()->enterprise()->create();

    $payload = [...payload($team), 'is_active' => false, 'is_ready' => false];

    SyncHandler::triggerMessage('tenant.employee.create', $payload);

    $employee = Employee::findByNawartUuid($payload['id']);

    assertEmployee(employee: $employee, payload: $payload);

    expect($employee->tags()->count())->toBe(2);

    expect($employee->shifts()->count())->toBe(0);
    expect($employee->locations()->count())->toBe(0);

    Notification::assertNotSentTo($employee, WelcomeNotification::class);

    Queue::assertNotPushed(PrepareEmployeeAttendanceRecord::class);
});

test('create a employee - employee active - team inactive', function () {
    Queue::fake([PrepareEmployeeAttendanceRecord::class]);

    Notification::fake();

    $team = Team::factory()->enterprise()->inactive()->create();

    $payload = [...payload($team), 'is_active' => true, 'is_ready' => false];

    SyncHandler::triggerMessage('tenant.employee.create', $payload);

    $employee = Employee::findByNawartUuid($payload['id']);

    assertEmployee(employee: $employee, payload: $payload);

    expect($employee->tags()->count())->toBe(2);

    expect($employee->shifts()->count())->toBe(0);
    expect($employee->locations()->count())->toBe(0);

    Notification::assertNotSentTo($employee, WelcomeNotification::class);

    Queue::assertNotPushed(PrepareEmployeeAttendanceRecord::class);
});

test('create a employee - employee active - team active but has no subscription', function () {
    Queue::fake([PrepareEmployeeAttendanceRecord::class]);

    Notification::fake();

    $team = Team::factory()->create();

    $payload = [...payload($team), 'is_active' => true, 'is_ready' => false];

    SyncHandler::triggerMessage('tenant.employee.create', $payload);

    $employee = Employee::findByNawartUuid($payload['id']);

    assertEmployee($employee, $payload);

    expect($employee->tags()->count())->toBe(2);

    expect($employee->shifts()->count())->toBe(0);
    expect($employee->locations()->count())->toBe(0);

    Notification::assertNotSentTo($employee, WelcomeNotification::class);

    Queue::assertNotPushed(PrepareEmployeeAttendanceRecord::class);
});

test('update a employee', function () {
    $team = Team::factory()->enterprise()->create();

    $employee = Employee::factory()
        ->for($team)
        ->create(['is_ready' => true]);

    $payload = [
        ...payload($team),
        'id' => $employee->nawart_uuid,
        'is_active' => true,
        'is_ready' => true,
        'random_proof_notification_config' => $employee->random_proof_notification_config,
    ];

    SyncHandler::triggerMessage('tenant.employee.update', $payload);

    $employee->refresh();

    assertEmployee(employee: $employee, payload: $payload);

    expect($employee->tags()->count())->toBe(2);
});
