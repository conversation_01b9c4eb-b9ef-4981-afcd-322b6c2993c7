<?php

use App\Enums\SoftwareCode;
use App\Enums\SoftwareFeatureCode;
use App\Enums\SoftwarePackageCode;
use App\Models\SoftwareFeature;
use App\Models\SoftwarePackage;
use App\Support\SyncHandler;
use function Pest\Laravel\artisan;

test('create attendance software', function () {
    artisan('migrate:fresh');

    $payload = [
        'id' => Str::uuid()->toString(),
        'code' => SoftwareCode::Attendance->value,
        'software_packages' => [
            [
                'id' => ($package1Id = Str::uuid()->toString()),
                'is_active' => fake()->boolean(),
                'name' => 'Attendance',
                'code' => SoftwarePackageCode::Basic1,
                'software_features' => [
                    [
                        'id' => Str::uuid()->toString(),
                        'is_active' => fake()->boolean(),
                        'code' => SoftwareFeatureCode::MultiLocation,
                        'software_package_id' => $package1Id,
                    ],
                    [
                        'id' => Str::uuid()->toString(),
                        'is_active' => fake()->boolean(),
                        'code' => SoftwareFeatureCode::MultiShift,
                        'software_package_id' => $package1Id,
                    ],
                ],
            ],
        ],
    ];

    SyncHandler::triggerMessage('tenant.software.create', $payload);

    $softwarePackage = SoftwarePackage::find($package1Id);

    $packagePayload = $payload['software_packages'][0];

    expect($softwarePackage)
        ->is_active->toBe($packagePayload['is_active'])
        ->name->toBe($packagePayload['name'])
        ->code->toBe($packagePayload['code']);

    $feature1Payload = $packagePayload['software_features'][0];

    expect($softwarePackage->softwareFeatures->find($feature1Payload['id']))
        ->is_active->toBe($feature1Payload['is_active'])
        ->code->toBe($feature1Payload['code']);

    $feature2Payload = $packagePayload['software_features'][1];

    expect($softwarePackage->softwareFeatures->find($feature2Payload['id']))
        ->is_active->toBe($feature2Payload['is_active'])
        ->code->toBe($feature2Payload['code']);

    // trigger the same payload again, should not create duplicate records
    SyncHandler::triggerMessage('tenant.software.create', $payload);

    expect(SoftwarePackage::count())->toBe(1);

    expect(SoftwareFeature::count())->toBe(2);

    $payload['code'] = SoftwareCode::Visitors->value;

    // trigger the payload with non-attendance code, should not create any records
    SyncHandler::triggerMessage('tenant.software.create', $payload);

    expect(SoftwarePackage::count())->toBe(1);
});
