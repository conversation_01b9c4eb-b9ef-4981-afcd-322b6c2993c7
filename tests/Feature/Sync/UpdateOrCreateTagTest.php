<?php

use App\Models\Tag;
use App\Models\Team;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseHas;

test('create a tag', function () {
    $team = Team::factory()->enterprise()->create();

    $payload = [
        'id' => Str::uuid(),
        'tenant_id' => $team->nawart_uuid,
        'name' => fake()->word(),
        'color' => fake()->hexColor(),
    ];
    SyncHandler::triggerMessage('tenant.tag.create', $payload);

    assertDatabaseHas('tags', [
        'name' => $payload['name'],
        'team_id' => $team->id,
        'color' => $payload['color'],
    ]);
});

test('update a tag', function () {
    $team = Team::factory()->enterprise()->create();

    $tag = Tag::factory()->for($team)->create();

    $payload = [
        'id' => $tag->id,
        'tenant_id' => $team->nawart_uuid,
        'name' => $tag->name, // Same name
        'color' => fake()->hexColor(),
    ];

    SyncHandler::triggerMessage('tenant.tag.update', $payload);

    assertDatabaseHas('tags', [
        'id' => $tag->id,
        'name' => $payload['name'],
        'team_id' => $team->id,
        'color' => $payload['color'],
    ]);
});
