<?php

use App\Models\Team;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseMissing;

test('delete a subscription', function () {
    $team = Team::factory()->enterprise()->create();

    $subscription = $team->subscription;

    SyncHandler::triggerMessage('tenant.subscription.delete', [
        'id' => $subscription->id,
        'tenant_id' => $subscription->team->nawart_uuid,
    ]);

    assertDatabaseMissing('subscriptions', [
        'id' => $subscription->id,
    ]);

    assertDatabaseMissing('subscription_items', [
        'subscription_id' => $subscription->id,
    ]);
});

test('delete a subscription that is not synced', function () {
    $team = Team::factory()->create();

    SyncHandler::triggerMessage('tenant.subscription.delete', [
        'id' => 'non-existing-subscription-id',
        'tenant_id' => $team->nawart_uuid,
    ]);

    assertDatabaseMissing('subscriptions', [
        'team_id' => $team->id,
    ]);
});
