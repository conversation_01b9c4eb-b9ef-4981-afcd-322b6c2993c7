<?php

use App\Models\ApprovalRequest;
use App\Models\Leave;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseEmpty;
use function Pest\Laravel\assertDatabaseMissing;

test('delete a department', function () {
    $department = createDefaultDepartment();

    $employee = createDefaultEmployee([
        'department_id' => $department->id,
    ]);

    ApprovalRequest::factory()
        ->for($department->team)
        ->for($department)
        ->for($employee)
        ->create();

    Leave::factory()
        ->for($department->team)
        ->for($department)
        ->for($employee)
        ->create();

    // remove department_id from employee
    $employee->update(['department_id' => null]);

    $childDepartment = createDefaultDepartment([
        'parent_id' => $department->id,
    ]);

    $grandChildDepartment = createDefaultDepartment([
        'parent_id' => $childDepartment->id,
    ]);

    SyncHandler::triggerMessage('tenant.department.delete', [
        'id' => $department->nawart_uuid,
        'tenant_id' => $department->team->nawart_uuid,
    ]);

    assertDatabaseMissing('departments', [
        'id' => $department->id,
    ]);

    assertDatabaseEmpty('approval_requests');
    assertDatabaseEmpty('leaves');

    expect($childDepartment->refresh()->parent_id)->toBeNull();

    expect($grandChildDepartment->refresh()->parent_id)->toBe($childDepartment->id);
});
