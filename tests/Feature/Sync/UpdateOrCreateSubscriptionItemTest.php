<?php

use App\Enums\SoftwareCode;
use App\Enums\SoftwarePackageCode;
use App\Models\SoftwarePackage;
use App\Models\Subscription;
use App\Models\Team;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseHas;

test('can not create subscription item without subscription', function () {
    $team = Team::factory()->create();

    $payload = [
        'tenant_id' => $team->nawart_uuid,
        'software' => [
            'code' => SoftwareCode::Attendance->value,
        ],
    ];

    SyncHandler::triggerMessage('tenant.subscription-item.create', $payload);
})->throws(Exception::class);

test('does not create subscription item for non-attendance', function () {
    $team = Team::factory()->create();

    $subscription = Subscription::factory()->for($team)->create();

    $payload = [
        'id' => Str::uuid()->toString(),
        'tenant_id' => $team->nawart_uuid,
        'software' => [
            'code' => SoftwareCode::Visitors->value,
        ],
    ];

    SyncHandler::triggerMessage('tenant.subscription-item.create', $payload);

    expect($team->subscriptionItems()->count())->toBe(0);
});

test('create subscription item for attendance', function () {
    $team = Team::factory()->create();

    $subscription = Subscription::factory()->for($team)->create();

    $softwarePackage = SoftwarePackage::findByCode(SoftwarePackageCode::Enterprise1);

    $payload = [
        'id' => Str::uuid()->toString(),
        'tenant_id' => $team->nawart_uuid,
        'subscription_id' => $subscription->id,
        'software' => [
            'code' => SoftwareCode::Attendance->value,
        ],
        'software_package' => [
            'id' => $softwarePackage->id,
            'code' => $softwarePackage->code,
        ],
    ];

    SyncHandler::triggerMessage('tenant.subscription-item.create', $payload);

    assertDatabaseHas('subscription_items', [
        'id' => $payload['id'],
        'subscription_id' => $subscription->id,
        'team_id' => $team->id,
        'software_package_id' => $softwarePackage->id,
    ]);
});
