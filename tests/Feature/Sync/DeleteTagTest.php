<?php

use App\Models\Tag;
use App\Support\SyncHandler;
use function Pest\Laravel\assertDatabaseEmpty;
use function Pest\Laravel\assertDatabaseMissing;

test('delete a tag', function () {
    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();

    SyncHandler::triggerMessage('tenant.tag.delete', [
        'id' => $tag->nawart_uuid,
        'name' => $tag->name,
        'tenant_id' => $tag->team->nawart_uuid,
    ]);

    assertDatabaseMissing('tags', [
        'id' => $tag->id,
    ]);
});

test('delete a tag that is not synced', function () {
    SyncHandler::triggerMessage('tenant.tag.delete', [
        'id' => 'non-existing-tag-id',
        'name' => 'non-existing-tag-name',
        'tenant_id' => $this->tenant->nawart_uuid,
    ]);

    assertDatabaseEmpty('tags');
});

test('delete a tag for a tenant that is not synced', function () {
    $tag = Tag::factory()
        ->for($this->tenant)
        ->create();

    SyncHandler::triggerMessage('tenant.tag.delete', [
        'id' => $tag->nawart_uuid,
        'name' => $tag->name,
        'tenant_id' => 'non-existing-tenant-id',
    ]);
})->throws(
    Exception::class,
    'Tenant Id non-existing-tenant-id Could not be found, it exists in `Start` but not in the local database.'
);
