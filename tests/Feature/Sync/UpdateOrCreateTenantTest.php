<?php

use App\DTOs\EarlyLateConfig;
use App\Enums\ApprovalType;
use App\Models\Team;
use App\Support\SyncHandler;

test('create a tenant', function () {
    $payload = [
        'id' => Str::uuid(),
        'name' => fake()->company(),
        'is_active' => fake()->boolean(),
        'color' => fake()->hexColor(),
        'colored_logo_url' => fake()->url(),
        'white_logo_url' => fake()->url(),
    ];

    SyncHandler::triggerMessage('tenant.create', $payload);

    $team = Team::findByNawartUuid($payload['id']);

    expect($team)
        ->id->toBe($team->id)
        ->name->toBe($payload['name'])
        ->active->toBe($payload['is_active'])
        ->remote_work->toBe('not_allowed')
        ->free_checkout->toBe('not_allowed')
        ->primary_color->toBe($payload['color'])
        ->colored_logo_url->toBe($payload['colored_logo_url'])
        ->white_logo_url->toBe($payload['white_logo_url'])
        ->map_report_thresholds->toEqual(collect(['yellow' => ['min' => 60, 'max' => 80]]))
        ->early_late_config->toEqual(EarlyLateConfig::makeDefault())
        ->approval_type->toEqual(ApprovalType::OneLayer);
});

test('update a tenant', function () {
    $team = Team::factory()->enterprise()->create();

    $payload = [
        'id' => $team->nawart_uuid,
        'name' => fake()->company(),
        'is_active' => fake()->boolean(),
        'color' => fake()->hexColor(),
        'colored_logo_url' => fake()->url(),
        'white_logo_url' => fake()->url(),
    ];

    SyncHandler::triggerMessage('tenant.update', $payload);

    $team->refresh();

    expect($team)
        ->id->toBe($team->id)
        ->name->toBe($payload['name'])
        ->active->toBe($payload['is_active'])
        ->primary_color->toBe($payload['color'])
        ->colored_logo_url->toBe($payload['colored_logo_url'])
        ->white_logo_url->toBe($payload['white_logo_url']);

    // these should not be updated
    expect($team)
        ->remote_work->toBe($team->remote_work)
        ->free_checkout->toBe($team->free_checkout)
        ->map_report_thresholds->toEqual($team->map_report_thresholds)
        ->early_late_config->toEqual($team->early_late_config);
});
