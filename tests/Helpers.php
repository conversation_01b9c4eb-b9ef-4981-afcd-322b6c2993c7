<?php

use App\Enums\DurationStatus;
use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Attendance;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

function createDefaultEmployee(array $attributes = [])
{
    return Employee::factory()
        ->for(test()->tenant)
        ->create($attributes);
}

function createDefaultDepartment(array $attributes = [])
{
    return Department::factory()
        ->for(test()->tenant)
        ->create($attributes);
}

function createDefaultLocation(array $attributes = [])
{
    return Location::factory()
        ->for(test()->tenant)
        ->create($attributes);
}

function createDefaultShift(array $attributes = [])
{
    return Shift::factory()
        ->for(test()->tenant)
        ->create($attributes);
}

function assertAttendanceRecords(
    Collection $actualAttendances,
    Collection $expectedInRange,
    Collection $expectedOutOfRange
): void {
    $actualAttendances = $actualAttendances->pluck('id');

    // assert all in range and filtered attendances are included
    expect(
        $expectedInRange->pluck('id')->every(fn($id) => $actualAttendances->contains($id))
    )->toBeTrue();

    // assert out of range attendances are not included
    expect($actualAttendances)->not->toContain($expectedOutOfRange->pluck('id'));
}

function durationStatusPayload(DurationStatus $type): array
{
    return [
        'type' => $type,
        'start_date' => $type === DurationStatus::TEMPORARY ? now()->format('Y-m-d') : null,
        'end_date' =>
            $type === DurationStatus::TEMPORARY ? now()->addDays(5)->format('Y-m-d') : null,
    ];
}

function createApprovalRequests(
    Employee $employee,
    RequestStatus $status,
    string $type,
    string $attendanceType = ApprovalRequest::CHECK_IN_OUT
) {
    $period = CarbonPeriod::create('2020-01-01 08:00:00', '2020-01-03 16:00:00');

    return Collection::make($period->toArray())->map(function (Carbon $date) use (
        $attendanceType,
        $type,
        $status,
        $employee
    ) {
        $approvalRequest = ApprovalRequest::factory()
            ->for($employee->team)
            ->for($employee)
            ->date(
                CarbonPeriod::create(
                    $date->copy()->setTime(8, 0, 0),
                    $date->copy()->setTime(16, 0, 0)
                )
            )
            ->create([
                'status' => $status->value,
                'type' => $type,
                'attendance_type' => $attendanceType,
            ]);

        Attendance::factory()
            ->for($employee->team)
            ->for($employee)
            ->date($date)
            ->yet()
            ->create();

        return $approvalRequest;
    });
}

function createLocalFile(string $path): string
{
    $fullPath =
        pathinfo($path, PATHINFO_DIRNAME) .
        '/' .
        pathinfo($path, PATHINFO_FILENAME) .
        // we need to add random string to avoid race condition when running parallel tests
        Str::random() .
        '.' .
        pathinfo($path, PATHINFO_EXTENSION);

    File::put($fullPath, '');

    return $fullPath;
}
