<?php

namespace Tests;

use Illuminate\Foundation\Auth\User;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\ChainedFormatter;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\JoseEncoder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Rsa\Sha256;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token\Builder;
use function config;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        $this->defineEnvironment();
    }

    protected function jwtActingAs(User $user, array $roles): TestCase
    {
        $jwt = $this->issueToken(
            sub: $user->nawart_uuid,
            roles: $roles,
            customClaims: [
                'employee' => [
                    'tenant_id' => $user->team->nawart_uuid,
                    'id' => $user->nawart_uuid,
                    'email' => $user->email,
                ],
            ]
        );

        return $this->withToken($jwt);
    }

    protected function jwtActingAsAttendanceHR(User $user): TestCase
    {
        return $this->jwtActingAs($user, ['attendance-hr']);
    }

    protected function actioningAsAttendanceDeveloper(User $user): TestCase
    {
        return $this->jwtActingAs($user, ['attendance-developer']);
    }

    protected function jwtActingAsAttendanceDashboardViewer(User $user): TestCase
    {
        return $this->jwtActingAs($user, ['attendance-dashboard-viewer']);
    }

    protected function jwtActingAsMobile(User $user): TestCase
    {
        $jwt = $this->issueToken(
            sub: $user->nawart_uuid,
            customClaims: [
                'employee' => [
                    'tenant_id' => $user->team->nawart_uuid,
                    'id' => $user->nawart_uuid,
                    'email' => $user->email,
                ],
            ]
        );

        return $this->withToken($jwt)->withHeader('X-Device-Id', $user->device_id);
    }

    protected function defineEnvironment(): void
    {
        config()->set(
            'externaljwtguard.authorization_servers.default.public_key',
            $this->getPublicKey()
        );
    }

    protected function issueToken(string $sub, array $roles = [], array $customClaims = []): string
    {
        $tokenBuilder = new Builder(new JoseEncoder(), ChainedFormatter::default());
        $algorithm = new Sha256();
        $signingKey = InMemory::plainText($this->getPrivateKey());

        $now = now()->toDateTimeImmutable();
        $token = $tokenBuilder
            // Configures the issuer (iss claim)
            ->issuedBy('http://example.com')
            // Configures the audience (aud claim)
            ->permittedFor('http://example.org')
            // Configures the subject of the token (sub claim)
            ->relatedTo($sub)
            // Configures the id (jti claim)
            ->identifiedBy('4f1g23a12aa')
            // Configures the time that the token was issue (iat claim)
            ->issuedAt($now)
            // Configures the time that the token can be used (nbf claim)
            ->canOnlyBeUsedAfter($now->modify('-5 minute'))
            // Configures the expiration time of the token (exp claim)
            ->expiresAt($now->modify('+2 hour'))
            // add roles claim
            ->withClaim('roles', $roles)
            // Configures a new header, called "foo"
            ->withHeader('foo', 'bar');

        foreach ($customClaims as $claimKey => $claim) {
            $token = $token->withClaim($claimKey, $claim);
        }

        // Builds a new token
        $token = $token->getToken($algorithm, $signingKey);

        return $token->toString();
    }

    protected function getPublicKey()
    {
        return '-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA2EqMc2SXqPhyer0L4DUf
rdxh5TNptD7XXLmJJE2UjeiVbsRkg25z5SPQ3sGBU6cb8w4k5hFenwJ6pywt4Dll
PNiwSmf8LqlOHKxRtIXO7h1vI20El3q0f/pJi0oftv1/RTtmttw4H4Y52Dz47VmJ
kmiuE9IlUqTHsIVflX0P3o4zvOS3G5WiTf+/He+egpDFtheeNhe3lEFLIvM3l0VL
IAXrLkX9kmSbdw6JzLg2Ud2SfzK5tGjmWWDxQTaTy569CDEPxgyo9k8CMijhoi2p
Q04TIT1jbTqzvEiTVBZOc+xngl9t4SY8P3M/i5LfLqaP0vEjX7rY8rccxIjJw28i
7SK2hJ1Ri2pKIivns+8RbFRvTLny3iJwBWRHMSyONKYqJqmIDFTdpgFFJSrLhhba
I26zivHLFamfFWSrIc+6G/LN5Bq827bbAJO/KnS1CKZQG2M3eVrTeA5SpEVJVsxq
r708VrPL6HgwKh3llqG3dhSybKyET+qlos1HvvKmg4WVxNKSw+Ilmmd3lARhixtd
wngBk3ItVLwA1GyGLfaeaiORbvYkqKe20EsbHDTy6q5NaemCfcnL75NKjttPiKkE
a5Pcs+k+sKZVZddYvNOnkwDiLxYOzo7B95MIxTAYuzQ5SjeaUsSRnPDchxW/rExP
K2xaD2WCCfLVnD1sHaPExSUCAwEAAQ==
-----END PUBLIC KEY-----';
    }

    protected function getPrivateKey()
    {
        return '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    }
}
