<?php

use App\Http\Controllers\Mobile\V2\AttendanceController;
use App\Models\Attendance;
use App\Notifications\ApprovalRequestApprovedNotification;
use App\Notifications\ApprovalRequestRejectedNotification;
use App\Notifications\NewApprovalRequestNotification;
use App\Notifications\NewLeaveRequestNotification;

arch()->preset()->php();
arch()->preset()->security();
arch()
    ->preset()
    ->laravel()
    ->ignoring([
        // until we refactor it
        AttendanceController::class,
        // because of http resource
        ApprovalRequestApprovedNotification::class,
        ApprovalRequestRejectedNotification::class,
        NewLeaveRequestNotification::class,
        NewApprovalRequestNotification::class,

        // to allow ddFormatted
        Attendance::class,
    ]);

test('models should implement Auditable interface and use Auditable trait')
    ->expect('App\Models')
    ->toImplement('OwenIt\Auditing\Contracts\Auditable')
    ->ignoring([
        App\Models\EmployeeManagerView::class,
        App\Models\EmployeeShift::class,
        App\Models\SoftwareFeature::class,
        App\Models\SoftwarePackage::class,
        App\Models\Report::class,
    ])
    ->toUse('OwenIt\Auditing\Auditable');
