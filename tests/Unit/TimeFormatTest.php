<?php

use App\Support\IntervalFormat;
use Carbon\CarbonInterval;

dataset(
    'timeInterval provider',
    fn() => [
        'Case 1: show hours and minutes only - less than minute' => [
            'interval' => CarbonInterval::create(0, 0, 0, 0, 0, 0, 22),
            'expectedOutput' => '00:00',
        ],
        'Case 2: show hours and minutes only - less than hour' => [
            'interval' => CarbonInterval::create(0, 0, 0, 0, 0, 39, 22),
            'expectedOutput' => '00:39',
        ],
        'Case 3: show hours and minutes only - less than day' => [
            'interval' => CarbonInterval::create(0, 0, 0, 0, 23, 39, 22),
            'expectedOutput' => '23:39',
        ],
        'Case 4: show hours and minutes only - exactly one day' => [
            'interval' => CarbonInterval::create(0, 0, 0, 1, 0, 0, 0),
            'expectedOutput' => '24:00',
        ],
        'Case 5: show hours and minutes only - more than day' => [
            'interval' => CarbonInterval::create(0, 0, 0, 1, 1, 39, 22),
            'expectedOutput' => '25:39',
        ],
    ]
);

test('show carbon interval in the format h:m', function (
    CarbonInterval $interval,
    string $expectedOutput
) {
    expect(IntervalFormat::toHoursMinutes($interval))->toEqual($expectedOutput);
})->with('timeInterval provider');
