<?php

use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;

test('test macro carbon', function () {
    Carbon::setTestNow(Carbon::createFromDate(2024, 12, 4));

    $startOfWeek = Carbon::createFromDate(2024, 12, 1)->setTime(0, 0);
    $endOfWeek = Carbon::createFromDate(2024, 12, 7)->setTime(0, 0);

    expect(today()->localStartOfWeek())
        ->toEqual($startOfWeek)
        ->and(today()->localEndOfWeek())
        ->toEqual($endOfWeek)
        ->and(today()->isLocalCurrentWeek())
        ->toBeTrue()
        ->and(today()->subWeek()->isLocalCurrentWeek())
        ->toBeFalse()
        ->and(today()->addWeek()->isLocalCurrentWeek())
        ->toBeFalse();
});

test('test macro carbonImmutable', function () {
    Carbon::setTestNow(Carbon::createFromDate(2024, 12, 4));

    $startOfWeek = Carbon::createFromDate(2024, 12, 1)->setTime(0, 0)->toImmutable();
    $endOfWeek = Carbon::createFromDate(2024, 12, 7)->setTime(0, 0)->toImmutable();

    $date = today()->toImmutable();

    expect($date->localStartOfWeek())
        ->toEqual($startOfWeek)
        ->and($date->localEndOfWeek())
        ->toEqual($endOfWeek)
        ->and($date->isLocalCurrentWeek())
        ->toBeTrue()
        ->and($date->subWeek()->isLocalCurrentWeek())
        ->toBeFalse()
        ->and(today()->addWeek()->isLocalCurrentWeek())
        ->toBeFalse();
});

test('test macro carbon period', function () {
    Carbon::setTestNow(Carbon::createFromDate(2024, 12, 4));

    $startOfWeek = Carbon::createFromDate(2024, 12, 1)->setTime(0, 0);
    $endOfWeek = Carbon::createFromDate(2024, 12, 7)->setTime(0, 0);

    $period = CarbonPeriod::localWeek();

    expect($period->getStartDate())
        ->toEqual($startOfWeek)
        ->and($period->getEndDate())
        ->toEqual($endOfWeek);
});
