<?php

declare(strict_types=1);

use <PERSON>\CodingStyle\Rector\If_\NullableCompareToNullRector;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector;
use <PERSON>\TypeDeclaration\Rector\StmtsAwareInterface\DeclareStrictTypesRector;

return RectorConfig::configure()
    ->withPaths([
        __DIR__ . '/app',
        __DIR__ . '/bootstrap',
        __DIR__ . '/config',
        __DIR__ . '/lang',
        __DIR__ . '/operations',
        __DIR__ . '/public',
        __DIR__ . '/resources',
        __DIR__ . '/routes',
        __DIR__ . '/tests',
    ])
    ->withPhpSets()
    ->withImportNames(removeUnusedImports: true)
    ->withPreparedSets(
        deadCode: true,
        codeQuality: true,
        codingStyle: true,
        typeDeclarations: true,
        earlyReturn: true,
        rectorPreset: true
    )
    ->withSkip([
        AddOverrideAttributeToOverriddenMethodsRector::class,
        NullableCompareToNullRector::class,
        DeclareStrictTypesRector::class,
    ]);
