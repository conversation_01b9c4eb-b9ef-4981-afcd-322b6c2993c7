#!/usr/bin/env sh
set -e

container_mode=${CONTAINER_MODE:-"http"}
octane_server=${OCTANE_SERVER}
running_migrations_and_seeders=${RUNNING_MIGRATIONS_AND_SEEDERS:-"false"}

echo "Container mode: $container_mode"

initialStuff() {
    php artisan storage:link; \
    php artisan scribe:generate --force || true; \
    php artisan optimize:clear; \
    php artisan event:cache; \
    php artisan config:cache; \
    php artisan route:cache; \
    php artisan view:cache;
}

if [ "$1" != "" ]; then
    exec "$@"
elif [ ${container_mode} = "http" ]; then
    echo "Octane Server: $octane_server"
    initialStuff
    if [ ${octane_server}  = "frankenphp" ]; then
        exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.frankenphp.conf
    elif [ ${octane_server}  = "swoole" ]; then
        exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.swoole.conf
    elif [ ${octane_server}  = "roadrunner" ]; then
        exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.roadrunner.conf
    else
        echo "Invalid Octane server supplied."
        exit 1
    fi
elif [ ${container_mode} = "horizon" ]; then
    initialStuff
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.horizon.conf
elif [ ${container_mode} = "scheduler" ]; then
    initialStuff
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.scheduler.conf
#elif [ ${container_mode} = "worker" ]; then
#    initialStuff
#    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.worker.conf
else
    echo "Container mode mismatched."
    exit 1
fi
