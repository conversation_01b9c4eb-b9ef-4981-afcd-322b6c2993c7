version: '2.7'
rpc:
  listen: 'tcp://127.0.0.1:6001'
server:
  relay: pipes
http:
  middleware: [ "static", "gzip", "headers" ]
  max_request_size: 20
  static:
    dir: "public"
    forbid: [ ".php", ".htaccess" ]
  uploads:
    forbid: [".php", ".exe", ".bat", ".sh"]
  pool:
    allocate_timeout: 10s
    destroy_timeout: 10s
    supervisor:
      max_worker_memory: 128
      exec_ttl: 60s
logs:
  mode: production
  level: debug
  encoding: json
status:
    address: localhost:2114
