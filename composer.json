{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "abublihi/laravel-external-jwt-guard": "^3.0", "berkayk/onesignal-laravel": "v2.4.1", "guzzlehttp/guzzle": "^7.8.1", "imumz/nova-4-field-map": "^1.0", "knuckleswtf/scribe": "*", "laravel-notification-channels/onesignal": "v2.7.0", "laravel/framework": "^11.0", "laravel/helpers": "^1.7.0", "laravel/horizon": "^5.21.4", "laravel/nova": "~4.0", "laravel/octane": "^2.3", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8.2", "league/flysystem-aws-s3-v3": "^3.22.0", "maatwebsite/excel": "^3.1", "mobiledetect/mobiledetectlib": "^4.8", "muhammed-alkhudiry/venture": "^5.4", "openspout/openspout": "^4.28", "owen-it/laravel-auditing": "^13.6", "predis/predis": "^2.1", "prwnr/laravel-streamer": "^4.1", "sentry/sentry-laravel": "^4.1.1", "spatie/laravel-query-builder": "^6.2", "spatie/simple-excel": "^3.7", "spomky-labs/otphp": "^11.3", "staudenmeir/belongs-to-through": "^2.16", "staudenmeir/laravel-adjacency-list": "^1.0", "symfony/http-client": "^7.0.0", "symfony/postmark-mailer": "^7.0.0", "timokoerber/laravel-one-time-operations": "^1.4"}, "require-dev": {"fakerphp/faker": "^1.9.1", "jasonmccreary/laravel-test-assertions": "^2.1", "larastan/larastan": "^2.0", "laravel/sail": "^1.16", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "pestphp/pest": "^v3.2.4", "pestphp/pest-plugin-faker": "^3.0.0", "pestphp/pest-plugin-laravel": "^3.0.0", "pestphp/pest-plugin-type-coverage": "^3.0", "phpstan/phpstan": "^1.11.5", "phpunit/phpunit": "^11.3.6", "rector/rector": "^1.1.1", "spatie/laravel-horizon-watcher": "^1.1", "spatie/laravel-ignition": "^2.3.2", "spatie/laravel-ray": "^1.33"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "pestphp/pest-plugin": true}}, "repositories": {"nova": {"type": "composer", "url": "https://nova.laravel.com"}}}