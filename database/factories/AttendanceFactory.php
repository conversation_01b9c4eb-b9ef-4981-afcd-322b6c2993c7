<?php

namespace Database\Factories;

use App\Enums\AttendanceStatus;
use App\Models\Activity;
use App\Models\Employee;
use App\Models\Shift;
use App\Models\Team;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Collection;
use InvalidArgumentException;

class AttendanceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        # TODO: produce more accurate records
        return [
            'team_id' => Team::factory(),
            'employee_id' => fn(array $attribute) => Employee::factory()->create([
                'team_id' => $attribute['team_id'],
            ]),
            'timezone' => fake()->timezone(),
            'date' => fake()->dateTime(),
            'status' => AttendanceStatus::YET->value,
            'shift_id' => fn(array $attribute) => Shift::factory()->create([
                'team_id' => $attribute['team_id'],
            ]),
            'shift_from' => fake()->dateTime(),
            'shift_to' => fake()->dateTime(),
            'check_in' => fake()->dateTime(),
            'check_out' => fake()->dateTime(),
            'net_hours' => '00:00',
            'is_weekend' => false,
            'is_holiday' => fake()->boolean(),
            'on_duty' => fake()->boolean(),
            'random_notifications' => fake()->boolean(),
            'active_until' => fake()->dateTime(),
            'in_mood' => fake()->numberBetween(1, 5),
            'out_mood' => fake()->numberBetween(1, 5),
            'is_adjusted' => fake()->boolean(),
            'have_location' => fake()->boolean(),
            'flexible_hours' => 0,
        ];
    }

    public function date(
        DateTimeInterface|string $date,
        DateTimeInterface|string $shiftFrom = '08:00:00',
        DateTimeInterface|string $shiftTo = '16:00:00',
        int $flexibleHours = 60
    ): AttendanceFactory {
        $date = Carbon::parse($date);
        $shiftFrom = Carbon::parse($shiftFrom);
        $shiftTo = Carbon::parse($shiftTo);

        return $this->state(
            fn() => [
                'date' => $date,
                'shift_from' => $date
                    ->copy()
                    ->setTime($shiftFrom->hour, $shiftFrom->minute, $shiftFrom->second),
                'shift_to' => $date
                    ->copy()
                    ->setTime($shiftTo->hour, $shiftTo->minute, $shiftTo->second),
                'flexible_hours' => $flexibleHours,
                'active_until' => $date->copy()->addDay(),
            ]
        );
    }

    public function present(
        DateTimeInterface $checkinDate = null,
        bool $onDuty = false
    ): AttendanceFactory {
        return $this->state(
            fn() => [
                'status' => AttendanceStatus::PRESENT,
                'check_in' => $checkinDate ?? fake()->dateTime(),
                'on_duty' => $onDuty,
                'in_type' => Activity::CHECK_IN,
            ]
        );
    }

    public function yet(): static
    {
        return $this->state(
            fn() => [
                'status' => AttendanceStatus::YET,
                'check_in' => null,
                'check_out' => null,
                'on_duty' => false,
                'is_weekend' => false,
                'is_holiday' => false,
            ]
        );
    }

    public function absent(): AttendanceFactory
    {
        return $this->state(
            fn() => [
                'status' => AttendanceStatus::ABSENT,
                'check_in' => null,
                'check_out' => null,
            ]
        );
    }

    public function leave(): AttendanceFactory
    {
        return $this->state(fn() => ['status' => AttendanceStatus::LEAVE]);
    }

    /**
     * An associative array representing work statuses for specific dates.
     *
     * The keys of the array are dates in the format 'YYYY-MM-DD', and the values are
     * the corresponding work statuses, which can include working hours or absence types.
     *
     * Example of $statuses:
     * [
     *     '2021-08-01' => '08:00-16:00',            // Worked from 8 AM to 4 PM
     *     '2021-08-02' => '08:30-16:00',            // Worked from 8:30 AM to 4 PM
     *     '2021-08-03' => '08:00-16:30',            // Worked from 8 AM to 4:30 PM
     *     '2021-08-04' => AttendanceStatus::ABSENT,   // Absent from work
     *     '2021-08-05' => AttendanceStatus::LEAVE,    // On leave
     *     '2021-08-06' => AttendanceStatus::WEEKEND,  // Weekend (not a work day)
     *     '2021-08-07' => AttendanceStatus::WEEKEND,  // Weekend (not a work day)
     *     '2021-08-08' => '08:00-16:00',            // Worked from 8 AM to 4 PM
     * ]
     *
     * @param  CarbonPeriod  $period
     * @param  array|null  $attributes
     * @param  array<AttendanceStatus|string, string>  $statuses
     * @param  int  $flexibleHours
     * @return Collection
     */
    public function createFromPeriod(
        CarbonPeriod $period,
        array $attributes = null,
        array $statuses = null,
        int $flexibleHours = 60
    ): Collection {
        $records = collect();

        foreach ($period as $date) {
            if ($statuses) {
                $status = $statuses[$date->format('Y-m-d')] ?? null;

                if (!$status) {
                    throw new InvalidArgumentException("Status not provided for date: $date");
                }

                // if `leave` or `absent` format
                $attendanceStatus = $status instanceof AttendanceStatus ? $status : null;

                $attributes = [
                    ...$attributes ?? [],
                    'status' => $attendanceStatus,
                    'is_weekend' =>
                        $attendanceStatus instanceof AttendanceStatus &&
                        $attendanceStatus === AttendanceStatus::WEEKEND,
                    'check_in' => null,
                    'check_out' => null,
                    'in_type' => null,
                    'out_type' => null,
                    'net_hours' => '00:00',
                ];

                if (!$attendanceStatus) {
                    // if `08:00-16:00` format
                    [$start, $end] = explode('-', $status);

                    $attendanceStatus = AttendanceStatus::PRESENT;

                    $checkIn = $date->copy()->setTimeFromTimeString($start);
                    $checkOut = $date->copy()->setTimeFromTimeString($end);

                    $attributes = [
                        ...$attributes,
                        'status' => $attendanceStatus,
                        'check_in' => $checkIn,
                        'check_out' => $checkOut,
                        'in_type' => Activity::CHECK_IN,
                        'out_type' => Activity::CHECK_OUT,
                        'net_hours' => $checkIn->diff($checkOut)->format('%H:%I'),
                    ];
                }

                if (!$attendanceStatus) {
                    throw new InvalidArgumentException("Invalid status: $status");
                }
            }

            $records->push(
                $this->date(date: $date, flexibleHours: $flexibleHours)->create($attributes)
            );
        }

        return $records;
    }
}
