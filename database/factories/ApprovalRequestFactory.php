<?php

namespace Database\Factories;

use App\Enums\RequestStatus;
use App\Models\ApprovalRequest;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApprovalRequestFactory extends Factory
{
    protected $model = ApprovalRequest::class;

    public function definition(): array
    {
        return [
            'from_datetime' => Carbon::now(),
            'to_datetime' => Carbon::now(),
            'type' => ApprovalRequest::REGULARIZATION,
            'reason' => $this->faker->word(),
            'status' => ApprovalRequest::PENDING,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'attendance_type' => ApprovalRequest::CHECK_IN_OUT,
            'team_id' => Team::factory(),
            'department_id' => fn(array $attributes) => Department::factory()->create([
                'team_id' => $attributes['team_id'],
            ]),
            'employee_id' => fn(array $attributes) => Employee::factory()->create([
                'team_id' => $attributes['team_id'],
                'department_id' => $attributes['department_id'],
            ]),
        ];
    }

    public function date(CarbonPeriod $period): self
    {
        return $this->state(
            fn() => [
                'from_datetime' => $period->getStartDate(),
                'to_datetime' => $period->getEndDate(),
            ]
        );
    }

    public function approved(): self
    {
        return $this->state(fn() => ['status' => RequestStatus::Approved]);
    }

    public function pending(): static
    {
        return $this->state(fn() => ['status' => RequestStatus::Pending]);
    }

    public function rejected(): static
    {
        return $this->state(fn() => ['status' => RequestStatus::Rejected]);
    }

    public function permission(): self
    {
        return $this->state(fn() => ['type' => ApprovalRequest::PERMISSION]);
    }

    public function remoteWork(): self
    {
        return $this->state(fn() => ['type' => ApprovalRequest::REMOTE_WORK]);
    }

    public function regularization(): self
    {
        return $this->state(fn() => ['type' => ApprovalRequest::REGULARIZATION]);
    }

    public function checkIn(): self
    {
        return $this->state(fn() => ['attendance_type' => ApprovalRequest::CHECK_IN]);
    }

    public function checkInOut(): self
    {
        return $this->state(fn() => ['attendance_type' => ApprovalRequest::CHECK_IN_OUT]);
    }

    public function checkOut(): self
    {
        return $this->state(fn() => ['attendance_type' => ApprovalRequest::CHECK_OUT]);
    }
}
