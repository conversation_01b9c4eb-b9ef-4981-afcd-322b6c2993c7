<?php

namespace Database\Factories;

use App\Enums\DelegationType;
use App\Models\Delegation;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;

class DelegationFactory extends Factory
{
    protected $model = Delegation::class;

    public function definition(): array
    {
        return [
            'type' => fake()->randomElement(array_pluck(DelegationType::cases(), 'value')),
            'delegatee_id' => Employee::factory(),
            'delegated_id' => Employee::factory(),
        ];
    }
}
