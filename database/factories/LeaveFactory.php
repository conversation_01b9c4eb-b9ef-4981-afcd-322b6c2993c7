<?php

namespace Database\Factories;

use App\Enums\RequestStatus;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Team;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Leave>
 */
class LeaveFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'department_id' => fn(array $attributes) => Department::factory([
                'team_id' => $attributes['team_id'],
            ]),
            'employee_id' => fn(array $attributes) => Employee::factory([
                'team_id' => $attributes['team_id'],
                'department_id' => $attributes['department_id'],
            ]),
            'from_date' => fn() => now(),
            'to_date' => now()->addDays(random_int(1, 20)),
            'reason' => fake()->realTextBetween(10, 20),
            'status' => fake()->randomElement(array_pluck(RequestStatus::cases(), 'value')),
        ];
    }

    public function date(CarbonPeriod $period): LeaveFactory
    {
        return $this->state(
            fn() => [
                'from_date' => $period->getStartDate(),
                'to_date' => $period->getEndDate(),
            ]
        );
    }

    public function approved(): LeaveFactory
    {
        return $this->state(fn() => ['status' => RequestStatus::Approved]);
    }

    public function pending(): LeaveFactory
    {
        return $this->state(fn() => ['status' => RequestStatus::Pending]);
    }

    public function rejected(): LeaveFactory
    {
        return $this->state(
            fn() => [
                'status' => RequestStatus::Rejected,
                'rejection_reason' => fake()->text(),
            ]
        );
    }
}
