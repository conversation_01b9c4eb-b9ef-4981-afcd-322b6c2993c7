<?php

namespace Database\Factories;

use App\Enums\ProofMethod;
use App\Enums\ProofStatus;
use App\Models\Employee;
use App\Models\Proof;
use App\Models\Team;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProofFactory extends Factory
{
    protected $model = Proof::class;

    public function definition(): array
    {
        return [
            'notification_id' => fake()->uuid(),
            'method' => fake()->randomElement(array_pluck(ProofMethod::cases(), 'value')),
            'status' => fake()->randomElement(array_pluck(ProofStatus::cases(), 'value')),

            'score' => 0,

            'team_id' => Team::factory(),
            'employee_id' => Employee::factory(),
        ];
    }

    public function createFromPeriod(CarbonPeriod $period, array $attributes = null): Collection
    {
        $recordToCreate = collect($period)->map(
            fn($date) => [...$attributes ?? [], 'created_at' => $date]
        );

        return $this->createMany($recordToCreate);
    }
}
