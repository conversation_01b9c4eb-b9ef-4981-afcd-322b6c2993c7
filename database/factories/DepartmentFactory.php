<?php

namespace Database\Factories;

use App\DTOs\RandomProofNotificationConfig;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class DepartmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'name' => fake()->name(),
            'nawart_uuid' => fake()->uuid(),
            'parent_id' => null,
            'manager_id' => null,
            'random_proof_notification_config' => new RandomProofNotificationConfig(
                enabled: fake()->boolean(),
                inherited: fake()->boolean(),
                count: 5
            ),
        ];
    }

    public function randomProofNotificationConfig(
        bool $enabled = false,
        bool $inherited = false,
        int $count = 5
    ): static {
        return $this->state(
            fn(array $attributes) => [
                'random_proof_notification_config' => new RandomProofNotificationConfig(
                    enabled: $enabled,
                    inherited: $inherited,
                    count: $count
                ),
            ]
        );
    }
}
