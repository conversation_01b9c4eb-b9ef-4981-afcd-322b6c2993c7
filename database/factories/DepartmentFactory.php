<?php

namespace Database\Factories;

use App\Models\Department;
use App\Models\Employee;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\Factory;

class DepartmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Department::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'manager_id' => Employee::first()?->id,
            'tenant_id' => Tenant::factory(),
        ];
    }
}
