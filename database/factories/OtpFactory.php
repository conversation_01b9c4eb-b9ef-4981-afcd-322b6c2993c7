<?php

namespace Database\Factories;

use App\DTOs\Identifier;
use App\Models\Otp;
use Illuminate\Database\Eloquent\Factories\Factory;

class OtpFactory extends Factory
{
    protected $model = Otp::class;

    public function definition(): array
    {
        return [
            'value' => fake()->randomNumber(4),
            'expires_at' => now()->addSeconds(Otp::TIMEOUT),
            'sent_at' => now(),
        ];
    }

    public function identifier(Identifier $identifier): static
    {
        return $this->state(
            fn(array $attributes) => [
                'identifier' => $identifier->value(),
                'identifier_type' => $identifier->type(),
            ]
        );
    }
}
