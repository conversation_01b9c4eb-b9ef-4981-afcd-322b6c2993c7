<?php

namespace Database\Factories;

use App\Enums\EmployeeStatementType;
use App\Models\ApprovalRequest;
use App\Models\EmployeeStatement;
use App\Models\Leave;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmployeeStatementFactory extends Factory
{
    protected $model = EmployeeStatement::class;

    public function definition(): array
    {
        return [
            'type' => fake()->randomElement(EmployeeStatementType::class),
        ];
    }

    public function requestable(Leave|ApprovalRequest $request): EmployeeStatementFactory
    {
        return $this->state(
            fn() => [
                'requestable_id' => $request->id,
                'requestable_type' => $request::class,
            ]
        );
    }
}
