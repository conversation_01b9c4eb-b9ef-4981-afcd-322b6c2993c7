<?php

namespace Database\Factories;

use App\Models\Team;
use App\Models\Workday;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class WorkdayFactory extends Factory
{
    protected $model = Workday::class;

    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'start_time' => Carbon::now(),
            'end_time' => Carbon::now(),
            'color' => fake()->hexColor(),
            'flexible_time_before' => Carbon::now(),
            'flexible_time_after' => Carbon::now(),
            'prevent_checkout_after' => Carbon::now(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'team_id' => Team::factory(),
        ];
    }
}
