<?php

namespace Database\Factories;

use App\DTOs\RandomProofNotificationConfig;
use App\Enums\PreferredLanguage;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Shift;
use App\Models\Tag;
use App\Models\Team;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmployeeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'team_id' => Team::factory(),
            'department_id' => fn(array $attributes) => Department::factory()->create([
                'team_id' => $attributes['team_id'],
            ]),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'mobile' => fake()->numerify('05########'),
            'position' => fake()->jobTitle(),
            'is_active' => true,
            'nawart_uuid' => fake()->uuid(),
            'email' => fake()->unique()->safeEmail(),
            'number' => fake()->unique()->numerify('######'),
            'manager_id' => null,
            'preferred_language' => fake()->randomElement(
                array_pluck(PreferredLanguage::cases(), 'value')
            ),
            'device_id' => fake()->name(),
            'random_proof_notification_config' => new RandomProofNotificationConfig(
                enabled: fake()->boolean(),
                inherited: fake()->boolean(),
                count: 5
            ),
            'last_activity_at' => null,
            'first_login_at' => null,
        ];
    }

    public function managerByDepartment(Employee $manager): EmployeeFactory
    {
        return $this->state(
            fn(array $attributes) => [
                'department_id' => Department::factory()->create([
                    'team_id' => $manager->team_id,
                    'manager_id' => $manager->id,
                ])->id,
            ]
        );
    }

    public function permanentLocation(Location $location): EmployeeFactory
    {
        return $this->hasAttached($location, ['permanent' => true]);
    }

    public function temporaryLocation(Location $location, CarbonPeriod $period): EmployeeFactory
    {
        return $this->hasAttached($location, [
            'permanent' => false,
            'start_date' => $period->getStartDate(),
            'end_date' => $period->getEndDate(),
        ]);
    }

    public function permanentShift(Shift $shift): EmployeeFactory
    {
        return $this->hasAttached($shift, ['permanent' => true]);
    }

    public function temporaryShift(Shift $shift, CarbonPeriod $period): EmployeeFactory
    {
        return $this->hasAttached($shift, [
            'permanent' => false,
            'start_at' => $period->getStartDate(),
            'end_at' => $period->getEndDate(),
        ]);
    }

    public function tag(Tag $tag): EmployeeFactory
    {
        return $this->hasAttached($tag);
    }

    public function directManager(Employee $manager): EmployeeFactory
    {
        return $this->state(fn() => ['manager_id' => $manager->id]);
    }

    public function active(): EmployeeFactory
    {
        return $this->state(fn() => ['is_active' => true]);
    }

    public function inactive(): EmployeeFactory
    {
        return $this->state(fn() => ['is_active' => false]);
    }

    public function roles(array $roles): EmployeeFactory
    {
        return $this->state(fn() => ['roles' => $roles]);
    }

    public function defaultShift(): EmployeeFactory
    {
        return $this->afterCreating(function (Employee $employee) {
            $employee->shifts()->attach($employee->team->firstOrCreateDefaultShift());
        });
    }

    public function randomProofNotificationConfig(
        bool $enabled = false,
        bool $inherited = false,
        null|int $count = 5
    ): static {
        return $this->state(
            fn(array $attributes) => [
                'random_proof_notification_config' => new RandomProofNotificationConfig(
                    enabled: $enabled,
                    inherited: $inherited,
                    count: $count
                ),
            ]
        );
    }
}
