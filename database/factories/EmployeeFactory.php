<?php

namespace Database\Factories;

use App\DTOs\Identifier;
use App\Enums\EmployeePreferredLanguage;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class EmployeeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Employee::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $tenant = Tenant::factory()->create();

        return [
            'tenant_id' => $tenant->id,
            'first_name' => $this->faker->firstName,
            'last_name' => $this->faker->lastName,
            'email' => function (array $attributes) {
                return $this->faker->userName .
                    '@' .
                    Tenant::find($attributes['tenant_id'])->domain;
            },
            'password' => Hash::make('password'),
            'position' => $this->faker->word,
            'number' => $this->faker->bothify('?????-#####'),
            'department_id' => Department::factory()->for($tenant)->create()->id,
            'phone' => $this->faker->numerify('050#######'),
            'preferred_language' => $this->faker->randomElement(EmployeePreferredLanguage::cases()),
        ];
    }

    public function withIdentifier(Identifier $identifier): self
    {
        return $this->state(
            fn() => [
                $identifier->type()->value => $identifier->value(),
            ]
        );
    }

    public function inactive(): static
    {
        return $this->state(fn() => ['is_active' => false]);
    }
}
