<?php

namespace Database\Factories;

use App\Models\Shift;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShiftFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'name' => fake()->name(),
            'is_default' => false,
            'working_hours' => Shift::defaultWorkingHours(),
            'timezone' => fake()->timezone(),
        ];
    }

    public function fixableHours(int $minutes = 120): Factory
    {
        return $this->state(function (array $attributes) use ($minutes) {
            $working_hours = $attributes['working_hours'];
            $working_hours['flexible_hours'] = $minutes;
            return [
                'working_hours' => $working_hours,
            ];
        });
    }

    public static function workingHoursDefaultTemplate(
        string $preventCheckoutAfter = '20:00'
    ): array {
        return [
            'weekdays' => [
                'sunday' => [
                    'from' => '08:00',
                    'to' => '16:00',
                    'next_day_checkout' => false,
                    'prevent_checkout_after' => $preventCheckoutAfter,
                ],
                'monday' => [
                    'from' => '08:00',
                    'to' => '16:00',
                    'next_day_checkout' => false,
                    'prevent_checkout_after' => $preventCheckoutAfter,
                ],
                'tuesday' => [
                    'from' => '08:00',
                    'to' => '16:00',
                    'next_day_checkout' => false,
                    'prevent_checkout_after' => $preventCheckoutAfter,
                ],
                'wednesday' => [
                    'from' => '08:00',
                    'to' => '16:00',
                    'next_day_checkout' => false,
                    'prevent_checkout_after' => $preventCheckoutAfter,
                ],
                'thursday' => [
                    'from' => '08:00',
                    'to' => '16:00',
                    'next_day_checkout' => false,
                    'prevent_checkout_after' => $preventCheckoutAfter,
                ],
                'friday' => [
                    'from' => '08:00',
                    'to' => '16:00',
                    'next_day_checkout' => false,
                    'prevent_checkout_after' => $preventCheckoutAfter,
                ],
                'saturday' => [
                    'from' => '08:00',
                    'to' => '16:00',
                    'next_day_checkout' => false,
                    'prevent_checkout_after' => $preventCheckoutAfter,
                ],
            ],
            'flexible_hours' => '0',
        ];
    }

    public function workingHoursWithWeekend(int $flexibleHours = 0): Factory
    {
        return $this->state(
            fn(array $attributes) => [
                'working_hours' => [
                    'weekdays' => [
                        'sunday' => [
                            'from' => '08:00',
                            'to' => '16:00',
                            'next_day_checkout' => false,
                            'prevent_checkout_after' => '20:00',
                        ],
                        'monday' => [
                            'from' => '08:00',
                            'to' => '16:00',
                            'next_day_checkout' => false,
                            'prevent_checkout_after' => '20:00',
                        ],
                        'tuesday' => [
                            'from' => '08:00',
                            'to' => '16:00',
                            'next_day_checkout' => false,
                            'prevent_checkout_after' => '20:00',
                        ],
                        'wednesday' => [
                            'from' => '08:00',
                            'to' => '16:00',
                            'next_day_checkout' => false,
                            'prevent_checkout_after' => '20:00',
                        ],
                        'thursday' => [
                            'from' => '08:00',
                            'to' => '16:00',
                            'next_day_checkout' => false,
                            'prevent_checkout_after' => '20:00',
                        ],
                        'friday' => false,
                        'saturday' => false,
                    ],
                    'flexible_hours' => $flexibleHours,
                ],
            ]
        );
    }

    public function allDaysWeekend(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'working_hours' => [
                    'weekdays' => [
                        'sunday' => false,
                        'monday' => false,
                        'tuesday' => false,
                        'wednesday' => false,
                        'thursday' => false,
                        'friday' => false,
                        'saturday' => false,
                    ],
                    'flexible_hours' => '0',
                ],
            ]
        );
    }

    public function default(): static
    {
        return $this->state(fn() => ['is_default' => true]);
    }
}
