<?php

namespace Database\Factories;

use App\Enums\ReportName;
use App\Enums\ReportTaskStatus;
use App\Models\Employee;
use App\Models\Report;
use App\Models\ReportTask;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class ReportTaskFactory extends Factory
{
    protected $model = ReportTask::class;

    public function definition(): array
    {
        return [
            'status' => fake()->randomElement(ReportTaskStatus::cases()),
            'file_name' => $this->faker->name(),
            'report_id' => Report::byName(fake()->randomElement(ReportName::cases())),
            'completed_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),

            'team_id' => Team::factory(),
            'created_by_id' => Employee::factory(),
        ];
    }

    public function earlyLate(): static
    {
        return $this->state(fn() => ['report_id' => Report::byName(ReportName::EarlyLate)->id]);
    }

    public function presentAbsent(): static
    {
        return $this->state(fn() => ['report_id' => Report::byName(ReportName::PresentAbsent)->id]);
    }

    public function mood(): static
    {
        return $this->state(fn() => ['report_id' => Report::byName(ReportName::Mood)->id]);
    }

    public function permission(): static
    {
        return $this->state(fn() => ['report_id' => Report::byName(ReportName::Permission)->id]);
    }

    public function proofStatus(): static
    {
        return $this->state(fn() => ['report_id' => Report::byName(ReportName::ProofStatus)->id]);
    }

    public function usageRate(): static
    {
        return $this->state(fn() => ['report_id' => Report::byName(ReportName::UsageRate)->id]);
    }

    public function leave(): static
    {
        return $this->state(fn() => ['report_id' => Report::byName(ReportName::Leave)->id]);
    }

    public function withData(array $data = null): static
    {
        return $this->state(
            fn(array $attributes) => [
                'data' => [
                    ...[
                        'departments_ids' => [],
                        'employees_ids' => [],
                        'tags' => [],
                        'locations' => [],
                        'shifts' => [],
                        'show_inactive_employees' => false,
                        'start_date' => now()->subDays(7)->format('Y-m-d'),
                        'end_date' => now()->format('Y-m-d'),
                        'sheet_mode' => 'single_sheet',
                        'type' => 'monthly',
                    ],
                    ...$data ?? [],
                ],
            ]
        );
    }

    public function createdBy(Employee $employee): static
    {
        return $this->state(
            fn(array $attributes) => [
                'created_by_id' => $employee->id,
            ]
        );
    }

    public function pending(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'status' => ReportTaskStatus::Pending,
            ]
        );
    }
}
