<?php

namespace Database\Factories;

use App\Models\Software;
use App\Models\SoftwarePackage;
use App\Models\Subscription;
use App\Models\SubscriptionItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SubscriptionItem::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'subscription_id' => Subscription::factory(),
            'software_id' => Software::factory(),
            'software_package_id' => SoftwarePackage::factory(),
        ];
    }
}
