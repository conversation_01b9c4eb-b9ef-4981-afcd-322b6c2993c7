<?php

namespace Database\Factories;

use App\Models\SoftwarePackage;
use App\Models\SubscriptionItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionItemFactory extends Factory
{
    protected $model = SubscriptionItem::class;

    public function definition(): array
    {
        return [
            'id' => fake()->uuid(),
            'software_package_id' => fn() => SoftwarePackage::first() ?? SoftwarePackage::factory(),
        ];
    }
}
