<?php

namespace Database\Factories;

use App\Models\Team;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Holiday>
 */
class HolidayFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'team_id' => Team::factory(),
            'start_date' => fake()->date(max: '+6 months'),
            'end_date' => fn(array $attributes) => Carbon::parse($attributes['start_date'])
                ->addDays(fake()->numberBetween(1, 25))
                ->toDateString(),
        ];
    }

    public function date(CarbonPeriod $period): HolidayFactory
    {
        return $this->state(
            fn() => [
                'start_date' => $period->getStartDate(),
                'end_date' => $period->getEndDate(),
            ]
        );
    }
}
