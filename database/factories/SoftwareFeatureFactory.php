<?php

namespace Database\Factories;

use App\Models\SoftwareFeature;
use App\Models\SoftwarePackage;
use Illuminate\Database\Eloquent\Factories\Factory;

class SoftwareFeatureFactory extends Factory
{
    protected $model = SoftwareFeature::class;

    public function definition(): array
    {
        return [
            'id' => fake()->uuid(),
            'software_package_id' => SoftwarePackage::factory(),
            'code' => fake()->word(),
            'is_active' => true,
        ];
    }
}
