<?php

namespace Database\Factories;

use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class LocationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'name' => fake()->name(),
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
            'radius' => fake()->randomNumber(),
            'timezone' => fake()->timezone(),
            'is_default' => false,
            'automatic' => false,
            'check_out_radius' => 0,
        ];
    }

    public function default(): static
    {
        return $this->state(fn(array $attributes) => ['is_default' => true]);
    }

    public function automaticLocation(int $extraRadius): static
    {
        return $this->state(
            fn(array $attributes) => ['automatic' => true, 'check_out_radius' => $extraRadius]
        );
    }
}
