<?php

namespace Database\Factories;

use App\Enums\SoftwarePackageCode;
use App\Models\SoftwarePackage;
use Illuminate\Database\Eloquent\Factories\Factory;

class SoftwarePackageFactory extends Factory
{
    protected $model = SoftwarePackage::class;

    public function definition(): array
    {
        return [
            'id' => fake()->uuid(),
            'name' => fake()->name(),
            'code' => fake()->randomElement(array_pluck(SoftwarePackageCode::cases(), 'value')),
            'is_active' => true,
        ];
    }

    public function enterprise(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'name' => 'enterprise',
                'code' => SoftwarePackageCode::Enterprise1,
            ]
        );
    }

    public function basic(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'name' => 'basic',
                'code' => SoftwarePackageCode::Basic1,
            ]
        );
    }
}
