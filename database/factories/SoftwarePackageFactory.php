<?php

namespace Database\Factories;

use App\Models\Software;
use App\Models\SoftwarePackage;
use Illuminate\Database\Eloquent\Factories\Factory;

class SoftwarePackageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SoftwarePackage::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'code' => $this->faker->word,
            'software_id' => Software::factory(),
            'is_active' => $this->faker->boolean,
        ];
    }
}
