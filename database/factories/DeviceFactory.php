<?php

namespace Database\Factories;

use App\Enums\LocationSelection;
use App\Models\Location;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class DeviceFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'location_selection' => LocationSelection::DeviceLocation,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),

            'location_id' => null,
            'team_id' => Team::factory(),
        ];
    }

    public function existingLocation(?Location $location = null): static
    {
        return $this->state(
            fn(array $attributes) => [
                'location_selection' => LocationSelection::ExistingLocation->value,
                'location_id' =>
                    $location?->id ??
                    Location::factory()->createOne(['team_id' => $attributes['team_id']]),
            ]
        );
    }

    public function deviceLocation(array $latLng = null): static
    {
        return $this->state(
            fn(array $attributes) => [
                'location_selection' => LocationSelection::DeviceLocation->value,
                'location_id' => null,
                ...$latLng ?? [
                    'lat' => fake()->latitude(),
                    'lng' => fake()->longitude(),
                ],
            ]
        );
    }
}
