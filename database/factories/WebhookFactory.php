<?php

namespace Database\Factories;

use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class WebhookFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'team_id' => Team::factory(),
            'name' => fake()->name(),
            'url' => fake()->url(),
            'secret' => fake()->text(),
            'active' => fake()->boolean(),
            'event_name' => fake()->word(),
        ];
    }
}
