<?php

namespace Database\Factories;

use App\Models\Employee;
use App\Models\Location;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class ActivityFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'employee_id' => fn(array $attributes) => Employee::factory()->create([
                'team_id' => $attributes['team_id'],
            ]),
            'action' => fake()->word(),
            'lat' => fake()->latitude(),
            'lng' => fake()->longitude(),
            'location_id' => Location::factory(),
        ];
    }
}
