<?php

namespace Database\Seeders;

use App\Models\Tenant;
use App\Models\TenantAuthenticatorConfig;
use Illuminate\Database\Seeder;

class SampleSamlAuthenticatorConfig extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenant = Tenant::find('9a8c018c-76a2-479c-8b3a-ed0f72345f22');

        TenantAuthenticatorConfig::updateOrCreate([
            'tenant_id' => $tenant->id,
            'config' => [
                'idp_entity_id' => 'https://sample-saml-sso-with-ldap.ddev.site/saml/metadata',
                'idp_login_url' => 'https://sample-saml-sso-with-ldap.ddev.site/login',
                'name_id_format' => 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
                'idp_x509_cert' => 'MIIDazCCAlOgAwIBAgIUTBe6mFDqN9QvfC3ivNolLkMh/AMwDQYJKoZIhvcNAQEL
BQAwRTELMAkGA1UEBhMCQVUxEzARBgNVBAgMClNvbWUtU3RhdGUxITAfBgNVBAoM
GEludGVybmV0IFdpZGdpdHMgUHR5IEx0ZDAeFw0yMzEwMjMwODMzMzFaFw00MzEw
MTgwODMzMzFaMEUxCzAJBgNVBAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEw
HwYDVQQKDBhJbnRlcm5ldCBXaWRnaXRzIFB0eSBMdGQwggEiMA0GCSqGSIb3DQEB
AQUAA4IBDwAwggEKAoIBAQDG6DiTHvoIMLAbbkib5Wc/I8oU8xLyFj0YxO3/LKJz
ZE5Ru6Ff4/Ca2E6o/c5ZY52pGy9OMjdEiJOs18EqmJodXDnGGI0Hzxr2/GsqGEjr
KBgz5mQcDWsljJk+nKGa3fV3NBjTiHnAapWRc6HVU2XBn0dqMzYwGxFJ7leQKqLV
DwoL34x2K8hD3e8BhRHncWQJtICveAnyDBWrk/B+4KBZsdF5ybhdldfAr2oYPDJY
G0a0vCAMI6fyzVoOqi2RHztoGPwRWf0+h+fiCSc1/6v7+T641Z5y+RVQaKy0oWw+
XRPmcQ64YrlntzZvIRwEU37xjyrPzfFc3jzuprHW6rmdAgMBAAGjUzBRMB0GA1Ud
DgQWBBSv0EeLCh/tD9BCqUi15FOmII26CTAfBgNVHSMEGDAWgBSv0EeLCh/tD9BC
qUi15FOmII26CTAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBM
NTP7Ya4RRFNxT/lmhZcrWheR3+s1NXP6O4b+KihuZzjq0qdh98VgWBUTn3WmXLg/
10n0TIzbBZ4/xQsrMPQcePve8eAAQEGg+aVLi7uWAPl7CWxLWvvMbFpeD/iBFaiW
IVDiTL4pNZxeudyb3okrZI3xXLHne097CucFmbErpwR1Q0TT/BykanumELgXmdfn
7zROSj0M1mytyk/ubUbOL3bdlP6beIqmzEozVPMsPK/H0tvNiR4m2T1SJfc6cO2+
KKBjMD2Eqy0Wq35DR22BCPujhqTsyxS+RVSCV8mVqGnhCeZsOxR5sODGyh6Mu8V9
LpIFg6AoN+DW2qXkGpcq',
                'sign_message' => true,
                'email_claim' =>
                    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
                'use_name_id_for_authentication' => true,
                'first_name_claim' =>
                    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
            ],
            'authenticator_class' => \App\Authenticators\SamlAuthenticator::class,
        ]);
    }
}
