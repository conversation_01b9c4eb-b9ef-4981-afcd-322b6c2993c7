<?php

namespace Database\Seeders;

use App\Enums\SoftwareFeatureCode;
use App\Models\SoftwareFeature;
use App\Models\SoftwarePackage;
use Illuminate\Database\Seeder;

class SoftwarePackageSeeder extends Seeder
{
    public function run(): void
    {
        if (SoftwarePackage::count() !== 0) {
            return;
        }

        $enterpriseSoftwarePackage = SoftwarePackage::factory()->enterprise()->create();

        SoftwareFeature::factory()
            ->for($enterpriseSoftwarePackage)
            ->create(['code' => SoftwareFeatureCode::MultiLocation]);

        SoftwareFeature::factory()
            ->for($enterpriseSoftwarePackage)
            ->create(['code' => SoftwareFeatureCode::MultiShift]);

        SoftwarePackage::factory()->basic()->create();
    }
}
