<?php

namespace Database\Seeders;

use Artisan;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Database\Seeder;

class GenerateTestAttendanceSeeder extends Seeder
{
    public function run(): void
    {
        $start = now()->subYear()->startOfMonth();
        $end = now();

        $period = CarbonPeriod::create($start, $end);

        info(
            "creating attendances between {$period->start->toDateTimeString()} and {$period->end->toDateTimeString()}"
        );

        info('you should start horizon with: `php artisan horizon` to start the generation');

        foreach ($period as $date) {
            dispatch(function () use ($date) {
                Carbon::setTestNow($date);

                info("Generating test attendance for {$date->format('Y-m-d')}");
                Artisan::call('attendance:prepare');
            });
        }
    }
}
