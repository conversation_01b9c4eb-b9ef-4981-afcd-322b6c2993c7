<?php

namespace Database\Seeders;

use App\Models\Holiday;
use App\Models\Team;
use Illuminate\Database\Seeder;

class HolidaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (config('app.env') !== 'local') {
            $this->command->error('This seeder should not be run in a production env');

            return;
        }

        Holiday::factory(10)->for(Team::first())->create();
    }
}
