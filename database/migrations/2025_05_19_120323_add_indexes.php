<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('leaves', function (Blueprint $table) {
            $table->index('from_date');
            $table->index('to_date');
            $table->index('status');
        });

        Schema::table('approval_requests', function (Blueprint $table) {
            $table->index('status');
            $table->index('type');
            $table->index('from_datetime');
            $table->index('to_datetime');
        });
    }

    public function down(): void
    {
        Schema::table('leaves', function (Blueprint $table) {
            $table->dropIndex(['from_date']);
            $table->dropIndex(['to_date']);
            $table->dropIndex(['status']);
        });

        Schema::table('approval_requests', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropIndex(['type']);
            $table->dropIndex(['from_datetime']);
            $table->dropIndex(['to_datetime']);
        });
    }
};
