<?php

use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::dropIfExists('daily_plans');

        Schema::create('workdays', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Team::class);
            $table->string('name');
            $table->time('start_time');
            $table->time('end_time');
            $table->string('color', 10);
            $table->time('flexible_time_before');
            $table->time('flexible_time_after');
            $table->time('prevent_checkout_after');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('workdays');
    }
};
