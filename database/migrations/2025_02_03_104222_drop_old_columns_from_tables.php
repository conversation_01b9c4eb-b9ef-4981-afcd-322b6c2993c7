<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn([
                'random_notification',
                'periodical_early_late_report',
                'random_proof_of_attendance_count',
                'random_proof_of_attendance_deadline',
                'nested_periodical_report_policy',
                'early_late_periodical_report_sheet_mode',
            ]);
        });

        Schema::table('departments', function (Blueprint $table) {
            $table->dropColumn(['random_notification', 'random_proof_of_attendance_count']);
        });

        Schema::table('employees', function (Blueprint $table) {
            $table->dropColumn(['random_notification', 'random_proof_of_attendance_count']);
        });
    }
};
