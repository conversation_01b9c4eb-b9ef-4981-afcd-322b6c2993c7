<?php

use App\Models\Employee;
use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('decisions', function (Blueprint $table) {
            $table->id();
            $table->enum('status', ['PENDING', 'APPROVED', 'REJECTED']);
            $table->enum('layer', ['FIRST', 'SECOND']);

            $table->morphs('decidable');
            $table->foreignIdFor(Team::class)->constrained();
            $table->foreignIdFor(Employee::class)->constrained();
            $table->foreignIdFor(Employee::class, 'decider_id')->constrained();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('decisions');
    }
};
