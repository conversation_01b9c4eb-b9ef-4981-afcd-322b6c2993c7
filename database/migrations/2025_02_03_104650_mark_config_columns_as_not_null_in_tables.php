<?php

use App\DTOs\RandomProofNotificationConfig;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Department::whereNull('random_proof_notification_config')
            ->get()
            ->each(function ($department) {
                if ($department->random_proof_notification_config) {
                    return;
                }

                $department->update([
                    'random_proof_notification_config' => new RandomProofNotificationConfig(
                        enabled: $department->team->random_proof_notification_config->enabled,
                        inherited: true,
                        count: $department->team->random_proof_notification_config->count
                    ),
                ]);
            });

        Employee::withTrashed()
            ->whereNull('random_proof_notification_config')
            ->each(function (Employee $employee) {
                if ($employee->random_proof_notification_config) {
                    return;
                }

                $employee->update([
                    'random_proof_notification_config' => new RandomProofNotificationConfig(
                        enabled: $employee->team->random_proof_notification_config->enabled,
                        inherited: true,
                        count: $employee->team->random_proof_notification_config->count
                    ),
                ]);
            });

        Schema::table('teams', function (Blueprint $table) {
            $table->json('early_late_config')->nullable(false)->change();
            $table->json('random_proof_notification_config')->nullable(false)->change();
        });

        Schema::table('departments', function (Blueprint $table) {
            $table->json('random_proof_notification_config')->nullable(false)->change();
        });

        Schema::table('employees', function (Blueprint $table) {
            $table->json('random_proof_notification_config')->nullable(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('tables', function (Blueprint $table) {
            //
        });
    }
};
