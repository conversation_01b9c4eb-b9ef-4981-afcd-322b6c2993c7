<?php

use App\Models\Team;
use App\Models\WorkSchedule;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('work_schedule_assignments', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['department', 'employee', 'direct_manager', 'location', 'tag']);
            $table->json('value');
            $table->foreignIdFor(Team::class)->constrained();
            $table->foreignIdFor(WorkSchedule::class)->constrained();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('work_schedule_assignments');
    }
};
