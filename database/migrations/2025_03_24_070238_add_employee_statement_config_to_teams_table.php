<?php

use App\DTOs\EmployeeStatementConfig;
use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->json('employee_statement_config')->nullable();
        });

        Team::all()->each->update([
            'employee_statement_config' => new EmployeeStatementConfig(
                enabled: false,
                preventRequestsEnabled: false,
                daysBeforePreventingRequests: 3,
                lateCheckinBufferMinutes: 15,
                earlyCheckoutBufferMinutes: 15
            ),
        ]);

        Schema::table('teams', function (Blueprint $table) {
            $table->json('employee_statement_config')->nullable(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('employee_statement_config');
        });
    }
};
