<?php

use App\Models\Employee;
use App\Models\Team;
use App\Models\Workday;
use App\Models\WorkSchedule;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('work_schedule_records', function (Blueprint $table) {
            $table->id();
            $table->date('date')->index();
            $table->foreignIdFor(Team::class)->constrained();
            $table->foreignIdFor(WorkSchedule::class)->constrained();
            $table->foreignIdFor(Employee::class)->constrained();
            $table->foreignIdFor(Workday::class)->constrained();
            $table->enum('workday_type', ['WEEKDAY', 'WEEKEND', 'HOLIDAY', 'LEAVE']);
            $table->unique(['employee_id', 'date']);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('work_schedule_records');
    }
};
