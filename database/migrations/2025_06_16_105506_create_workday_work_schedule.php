<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('workday_work_schedule', function (Blueprint $table) {
            $table->foreignId('workday_id');
            $table->foreignId('work_schedule_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('workday_work_schedule');
    }
};
