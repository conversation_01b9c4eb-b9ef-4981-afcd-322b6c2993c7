<?php

use App\Models\Employee;
use Illuminate\Database\Migrations\Migration;
use libphonenumber\PhoneNumberFormat;

return new class extends Migration {
    public function up(): void
    {
        Employee::each(function (Employee $employee) {
            $phone = phone($employee->phone, 'SA');

            $employee->update([
                'phone' => $phone->isValid() ? $phone->format(PhoneNumberFormat::E164) : null,
            ]);
        });
    }
};
