<?php

use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        // for dev env, will delete this later
        if (Schema::hasColumn('attendances', 'has_employee_statement')) {
            Schema::table('attendances', function (Blueprint $table) {
                $table->renameColumn('has_employee_statement', 'employee_statement_enabled');
            });
        }

        Schema::create('employee_statements', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Team::class)->constrained();
            $table->foreignIdFor(Employee::class)->constrained();
            $table
                ->foreignIdFor(Attendance::class)
                ->unique()
                ->constrained();
            $table->enum('type', [
                'late_checkin',
                'early_checkout',
                'late_checkin_and_early_checkout',
                'absent',
            ]);
            $table
                ->enum('employee_action', ['leave', 'permission', 'regularization', 'remote_work'])
                ->nullable();

            $table->dateTime('employee_action_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('employee_statements');
    }
};
