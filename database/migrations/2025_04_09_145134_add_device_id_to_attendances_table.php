<?php

use App\Models\Device;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            $table
                ->foreignIdFor(Device::class, 'check_in_device_id')
                ->nullable()
                ->constrained();

            $table
                ->foreignIdFor(Device::class, 'check_out_device_id')
                ->nullable()
                ->constrained();
        });
    }

    public function down(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            $table->dropForeign(['check_in_device_id']);
            $table->dropForeign(['check_out_device_id']);
        });
    }
};
