<?php

use App\Models\Team;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('work_schedules', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['fixed', 'rotational']);
            $table->string('name');
            $table->enum('work_and_off_days_distribution_type', [
                'number_of_days',
                'specific_days',
            ]);

            $table->json('specific_days')->nullable();

            $table->foreignIdFor(Team::class);

            $table->unique(['team_id', 'name']);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('work_schedules');
    }
};
