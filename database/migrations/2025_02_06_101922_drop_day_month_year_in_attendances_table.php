<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            $table->dropForeign('attendances_employee_id_foreign');

            $table->dropUnique('attendances_employee_id_day_month_year_unique');

            $table->dropIndex('attendances_day_index');
            $table->dropIndex('attendances_month_index');
            $table->dropIndex('attendances_year_index');

            $table->dropColumn('day');
            $table->dropColumn('month');
            $table->dropColumn('year');
        });
    }
};
