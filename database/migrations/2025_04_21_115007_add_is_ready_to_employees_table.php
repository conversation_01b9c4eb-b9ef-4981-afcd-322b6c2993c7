<?php

use App\Models\Employee;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (Schema::hasColumn('employees', 'is_ready')) {
            return;
        }

        Schema::table('employees', function (Blueprint $table) {
            $table->boolean('is_ready')->default(false);
        });

        Employee::query()->update(['is_ready' => true]);
    }
};
