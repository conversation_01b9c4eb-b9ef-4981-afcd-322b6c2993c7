<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('employee_statements', function (Blueprint $table) {
            $table->nullableMorphs('requestable');

            $table->dropColumn('employee_action');
            $table->dropColumn('employee_action_at');
        });
    }

    public function down(): void
    {
        Schema::table('employee_statements', function (Blueprint $table) {
            $table->dropMorphs('requestable');
        });
    }
};
