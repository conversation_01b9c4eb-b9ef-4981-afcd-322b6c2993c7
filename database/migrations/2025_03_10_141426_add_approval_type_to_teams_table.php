<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->enum('approval_type', ['ONE_LAYER', 'TWO_LAYER'])->default('ONE_LAYER');
            $table->enum('approval_type', ['ONE_LAYER', 'TWO_LAYER'])->change();
        });
    }

    public function down(): void
    {
        Schema::table('teams', function (Blueprint $table) {
            $table->dropColumn('approval_type');
        });
    }
};
