<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default minimum iOS version
    |--------------------------------------------------------------------------
    |
    | This option controls the minimum acceptable version for iOS app to run.
    | if the iOS app version if less than the specified version, the user
    | will not be able to use the app before updating it to an acceptable version.
    |
    */

    'ios_minimum_version' => env('IOS_MINIMUM_VERSION', '1.0.0'),

    /*
    |--------------------------------------------------------------------------
    | Default minimum android version
    |--------------------------------------------------------------------------
    |
    | This option controls the minimum acceptable version for android app to run.
    | if the iOS app version if less than the specified version, the user
    | will not be able to use the app before updating it to an acceptable version.
    |
    */

    'android_minimum_version' => env('ANDROID_MINIMUM_VERSION', '1.0.0'),
];
