NOVA_TOKEN := $(shell grep -o '"password": "[^"]*"' auth.json | cut -d'"' -f4)

# Format all files using Prettier
format:
	npx prettier . --write

docker-up-style:
	docker build --tag 'attendance_backend_style' . --target style --build-arg NOVA_TOKEN=$(NOVA_TOKEN)

docker-up-test:
	docker build --tag 'attendance_backend_test' . --target testing --build-arg NOVA_TOKEN=$(NOVA_TOKEN)

docker-up-deploy:
	docker build --tag 'attendance_backend' . --target deployment --build-arg NOVA_TOKEN=$(NOVA_TOKEN) && docker run -p 8000:8000 --rm 'attendance_backend'

docker-up-deploy-run-bash:
	docker build --tag 'attendance_backend' . --target deployment --build-arg NOVA_TOKEN=$(NOVA_TOKEN) && docker run -it --rm 'attendance_backend' /bin/bash

