<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
        <exclude>
            <directory>app/Nova</directory>
        </exclude>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="XDEBUG_MODE" value="0"/>
        <env name="PCOV_ENABLED" value="1"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="mysql"/>
        <env name="DB_DATABASE" value="testing"/>

        <env name="MAIL_MAILER" value="array"/>
        <!-- useful for sending an email while testing (send email using fake data from tests-->
        <!--        <env name="MAIL_MAILER" value="smtp"/>-->
        <!--        <env name="MAIL_HOST" value="127.0.0.1"/>-->
        <!--        <env name="MAIL_PORT" value="1025"/>-->
        <!--        <env name="MAIL_USERNAME" value="null"/>-->
        <!--        <env name="MAIL_PASSWORD" value="null"/>-->
        <!--        <env name="MAIL_ENCRYPTION" value="null"/>-->
        <!--        <env name="MAIL_FROM_ADDRESS" value="<EMAIL>"/>-->
        <!--        <env name="MAIL_FROM_NAME" value="example"/>-->

        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="LOG_CHANNEL" value="null"/>
    </php>
</phpunit>
