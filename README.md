# Nawart Attendance

## Installation

Add `auth.json` file to the root of the project with the following content:

#### Note: you have to ask a team member for the password or get it from ci/cd variables in gitlab under name NOVA_TOKEN

```json
{
    "http-basic": {
        "nova.laravel.com": {
            "username": "s.alma<PERSON><PERSON>@tamkeentech.sa",
            "password": ""
        }
    }
}
```

1. Install [ddev](https://ddev.com).
2. `ddev composer install`
3. `ddev artisan migrate`

## Development

Start the DDEV project.

```shell
ddev start
```

Open the minio dashboard to create a minio bucket with the name `attendance`

```shell
ddev minio
```

Finally, ensure that the following environment variables are set in the `.env` file

```shell
# make sure it's set to minio
FILESYSTEM_DISK=minio

# minio credentials
MINIO_ENDPOINT=http://minio:10101
MINIO_ACCESS_KEY_ID=ddevminio
MINIO_SECRET_ACCESS_KEY=ddevminio
MINIO_DEFAULT_REGION=us-east-1
MINIO_BUCKET=attendance
```
