<!DOCTYPE html><html class=antialiased lang=en><head><meta charset=UTF-8><meta content="width=device-width" name=viewport><meta content="Astro v2.0.13" name=generator><title>LRD - Laravel Request Docs</title><link href=/request-docs/_astro/index.3f528d64.css rel=stylesheet /></head><body><style>astro-island,astro-slot{display:contents}</style><script>var l;(self.Astro=self.Astro||{}).only=t=>{(async()=>{await(await t())()})()},window.dispatchEvent(new Event("astro:only"));{const t={0:t=>t,1:t=>JSON.parse(t,e),2:t=>new RegExp(t),3:t=>new Date(t),4:t=>new Map(JSON.parse(t,e)),5:t=>new Set(JSON.parse(t,e)),6:t=>BigInt(t),7:t=>new URL(t),8:t=>new Uint8Array(JSON.parse(t)),9:t=>new Uint16Array(JSON.parse(t)),10:t=>new Uint32Array(JSON.parse(t))},e=(e,s)=>{if(""===e||!Array.isArray(s))return s;const[r,i]=s;return r in t?t[r](i):void 0};customElements.get("astro-island")||customElements.define("astro-island",((l=class extends HTMLElement{constructor(){super(...arguments),this.hydrate=()=>{if(!this.hydrator||this.parentElement&&this.parentElement.closest("astro-island[ssr]"))return;const t=this.querySelectorAll("astro-slot"),s={},r=this.querySelectorAll("template[data-astro-template]");for(const t of r){const e=t.closest(this.tagName);!e||!e.isSameNode(this)||(s[t.getAttribute("data-astro-template")||"default"]=t.innerHTML,t.remove())}for(const e of t){const t=e.closest(this.tagName);!t||!t.isSameNode(this)||(s[e.getAttribute("name")||"default"]=e.innerHTML)}const i=this.hasAttribute("props")?JSON.parse(this.getAttribute("props"),e):{};this.hydrator(this)(this.Component,i,s,{client:this.getAttribute("client")}),this.removeAttribute("ssr"),window.removeEventListener("astro:hydrate",this.hydrate),window.dispatchEvent(new CustomEvent("astro:hydrate"))}}connectedCallback(){!this.hasAttribute("await-children")||this.firstChild?this.childrenConnectedCallback():new MutationObserver(((t,e)=>{e.disconnect(),this.childrenConnectedCallback()})).observe(this,{childList:!0})}async childrenConnectedCallback(){window.addEventListener("astro:hydrate",this.hydrate);let t=this.getAttribute("before-hydration-url");t&&await import(t),this.start()}start(){const t=JSON.parse(this.getAttribute("opts")),e=this.getAttribute("client");void 0!==Astro[e]?Astro[e]((async()=>{const t=this.getAttribute("renderer-url"),[e,{default:s}]=await Promise.all([import(this.getAttribute("component-url")),t?import(t):()=>()=>{}]),r=this.getAttribute("component-export")||"default";if(r.includes(".")){this.Component=e;for(const t of r.split("."))this.Component=this.Component[t]}else this.Component=e[r];return this.hydrator=s,this.hydrate}),t,this):window.addEventListener(`astro:${e}`,(()=>this.start()),{once:!0})}attributeChangedCallback(){this.hydrator&&this.hydrate()}}).observedAttributes=["props"],l))}</script><astro-island client=only component-export=default component-url=/request-docs/_astro/App.e8f74b1b.js opts={&quot;name&quot;:&quot;App&quot;,&quot;value&quot;:true} props={} renderer-url=/request-docs/_astro/client.8c8eb78c.js ssr="" uid=Z26kMUL></astro-island></body></html>